#!/bin/bash

echo "=== Vero MongoDB Backup System Setup ==="
echo "This script will install required dependencies using Poetry"
echo

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "Poetry not found! Please install Poetry first:"
    echo "  curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

echo "Installing backup system dependencies..."
# Use --no-root to avoid the error about the project not being installable
poetry install --extras "backups" --no-root

echo
echo "=== Installation Complete ==="
echo "You can now run scheduled backups with rich console output."
echo "The server will automatically check for missed backups on startup."
echo
echo "To schedule daily backups, use the web interface at /database-backup"
echo "or make a POST request to /api/database-backup/schedule with hour and minute parameters."
echo 
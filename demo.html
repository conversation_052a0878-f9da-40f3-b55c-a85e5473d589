<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>The Art of Code</title>
  <style>
    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Arial', sans-serif; /* System font for simplicity; base64 fonts can be added */
      color: #333;
      line-height: 1.6;
      overflow-x: hidden;
      scroll-behavior: smooth;
    }

    h1, h2 {
      font-weight: bold;
    }

    /* Hero Section */
    .hero {
      height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
      color: white;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero h1 {
      font-size: 4rem;
      text-transform: uppercase;
      animation: fadeInUp 1s ease-out;
    }

    .hero p {
      font-size: 1.5rem;
      margin-top: 1rem;
      animation: fadeInUp 1s ease-out 0.5s forwards;
      opacity: 0;
    }

    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* General Section Styles */
    .section {
      min-height: 100vh;
      padding: 50px;
      opacity: 0;
      transform: translateY(50px);
      transition: opacity 0.5s ease, transform 0.5s ease;
    }

    .section.visible {
      opacity: 1;
      transform: translateY(0);
    }

    .section h2 {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .section p {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto 30px;
    }

    /* Gradients Section */
    .gradients {
      background: linear-gradient(to bottom, #ff9a9e, #fad0c4);
    }

    .gradient-showcase {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
    }

    .gradient-box {
      height: 150px;
      border-radius: 10px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .gradient-box:hover {
      transform: scale(1.05);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    /* Shapes Section */
    .shapes {
      background: linear-gradient(to bottom, #a18cd1, #fbc2eb);
      position: relative;
    }

    .shapes svg {
      display: block;
      margin: 0 auto;
      max-width: 100%;
    }

    /* Canvas Section */
    .canvas {
      background: linear-gradient(to bottom, #ffecd2, #fcb69f);
    }

    #interactive-canvas {
      width: 100%;
      height: 400px;
      max-width: 800px;
      margin: 0 auto;
      display: block;
      border: 2px solid #333;
      border-radius: 5px;
    }

    /* Typography Section */
    .typography {
      background: linear-gradient(to bottom, #d4fc79, #96e6a1);
    }

    .fancy-text {
      font-size: 3rem;
      font-weight: bold;
      color: #e91e63;
      text-transform: uppercase;
      letter-spacing: 2px;
      text-align: center;
      animation: textPulse 2s infinite;
    }

    @keyframes textPulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .text-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      text-align: center;
    }

    .text-grid p {
      padding: 10px;
      border-radius: 5px;
      background: rgba(255, 255, 255, 0.2);
      transition: transform 0.3s ease;
    }

    .text-grid p:hover {
      transform: translateY(-5px);
    }

    /* Conclusion */
    .conclusion {
      background: linear-gradient(to bottom, #cfd9df, #e2ebf0);
      text-align: center;
      padding: 50px;
    }

    .conclusion p {
      color: #666;
    }

    /* Back to Top Button */
    #back-to-top {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #6a11cb;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 50%;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    #back-to-top.visible {
      opacity: 1;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero h1 { font-size: 2.5rem; }
      .hero p { font-size: 1.2rem; }
      .section { padding: 30px; }
      .section h2 { font-size: 2rem; }
      .gradient-showcase { grid-template-columns: 1fr; }
      .text-grid { grid-template-columns: 1fr; }
      #interactive-canvas { height: 300px; }
    }

    @media (max-width: 480px) {
      .hero h1 { font-size: 2rem; }
      .section p { font-size: 1rem; }
    }
  </style>
</head>
<body>
  <!-- Hero Section -->
  <header class="hero">
    <h1>The Art of Code</h1>
    <p>Visual Storytelling Without Images</p>
  </header>

  <!-- Gradients Section -->
  <section class="section gradients">
    <h2>The Power of Gradients</h2>
    <p>Gradients breathe life into designs, creating depth and emotion without a single pixel of imagery.</p>
    <div class="gradient-showcase">
      <div class="gradient-box" style="background: linear-gradient(to right, #ff758c, #ff7eb3);"></div>
      <div class="gradient-box" style="background: radial-gradient(circle, #a18cd1, #fbc2eb);"></div>
      <div class="gradient-box" style="background: linear-gradient(45deg, #84fab0, #8fd3f4);"></div>
      <div class="gradient-box" style="background: linear-gradient(to bottom, #ffecd2, #fcb69f);"></div>
    </div>
  </section>

  <!-- Shapes Section -->
  <section class="section shapes">
    <h2>Shaping the Web</h2>
    <p>SVG and CSS combine to craft intricate illustrations, turning code into art.</p>
    <svg width="800" height="300" viewBox="0 0 800 300" role="img" aria-label="Abstract landscape">
      <rect x="0" y="150" width="800" height="150" fill="#4caf50" />
      <circle cx="400" cy="100" r="50" fill="#ffeb3b" id="sun" />
      <polygon points="200,150 250,100 300,150" fill="#795548" />
      <rect x="220" y="120" width="60" height="30" fill="#8bc34a" />
    </svg>
  </section>

  <!-- Canvas Section -->
  <section class="section canvas">
    <h2>Interactive Canvas</h2>
    <p>Move your mouse or tap to influence the particles below, creating dynamic patterns.</p>
    <canvas id="interactive-canvas"></canvas>
  </section>

  <!-- Typography Section -->
  <section class="section typography">
    <h2>Typographic Mastery</h2>
    <p class="fancy-text">Words as Art</p>
    <p>Typography transforms text into a visual experience, blending form and function.</p>
    <div class="text-grid">
      <p style="font-size: 2rem; color: #ff5722;">Bold Impact</p>
      <p style="font-style: italic; color: #3f51b5;">Graceful Slant</p>
      <p style="text-shadow: 2px 2px 4px #795548;">Shadowed Depth</p>
      <p style="letter-spacing: 5px; color: #009688;">Wide Expression</p>
    </div>
  </section>

  <!-- Conclusion -->
  <footer class="conclusion">
    <h2>Conclusion</h2>
    <p>Thank you for journeying through "The Art of Code." This experience showcases the boundless potential of HTML, CSS, and JavaScript.</p>
    <p>Share this creation and explore the code that powers it.</p>
  </footer>

  <!-- Back to Top Button -->
  <button id="back-to-top" aria-label="Back to Top">↑</button>

  <script>
    // Intersection Observer for Scroll Animations
    document.addEventListener('DOMContentLoaded', () => {
      const sections = document.querySelectorAll('.section');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, { threshold: 0.1 });
      sections.forEach(section => observer.observe(section));
    });

    // Back to Top Button
    const backToTop = document.getElementById('back-to-top');
    window.addEventListener('scroll', () => {
      if (window.scrollY > 300) {
        backToTop.classList.add('visible');
      } else {
        backToTop.classList.remove('visible');
      }
    });
    backToTop.addEventListener('click', () => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // Canvas Interactivity
    const canvas = document.getElementById('interactive-canvas');
    const ctx = canvas.getContext('2d');

    // Responsive Canvas Sizing
    function resizeCanvas() {
      canvas.width = Math.min(800, window.innerWidth - 60);
      canvas.height = window.innerHeight < 768 ? 300 : 400;
    }
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    // Particle System
    const particles = [];
    for (let i = 0; i < 100; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 5 + 1,
        color: `hsl(${Math.random() * 360}, 100%, 50%)`
      });
    }

    let mouseX = canvas.width / 2;
    let mouseY = canvas.height / 2;

    canvas.addEventListener('mousemove', (e) => {
      const rect = canvas.getBoundingClientRect();
      mouseX = e.clientX - rect.left;
      mouseY = e.clientY - rect.top;
    });

    canvas.addEventListener('touchmove', (e) => {
      e.preventDefault();
      const rect = canvas.getBoundingClientRect();
      mouseX = e.touches[0].clientX - rect.left;
      mouseY = e.touches[0].clientY - rect.top;
    });

    function updateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      particles.forEach(p => {
        p.x += p.vx;
        p.y += p.vy;

        // Bounce off walls
        if (p.x < p.size || p.x > canvas.width - p.size) p.vx *= -1;
        if (p.y < p.size || p.y > canvas.height - p.size) p.vy *= -1;

        // Attraction to mouse
        const dx = mouseX - p.x;
        const dy = mouseY - p.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance < 100) {
          p.vx += dx * 0.01;
          p.vy += dy * 0.01;
        }

        // Draw particle
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
        ctx.fillStyle = p.color;
        ctx.fill();

        // Draw connection to mouse
        if (distance < 150) {
          ctx.beginPath();
          ctx.moveTo(p.x, p.y);
          ctx.lineTo(mouseX, mouseY);
          ctx.strokeStyle = p.color;
          ctx.globalAlpha = 1 - distance / 150;
          ctx.stroke();
          ctx.globalAlpha = 1;
        }
      });
      requestAnimationFrame(updateParticles);
    }
    updateParticles();

    // SVG Scroll Animation
    const sun = document.getElementById('sun');
    window.addEventListener('scroll', () => {
      const scrollMax = document.body.scrollHeight - window.innerHeight;
      const scrollPos = window.scrollY;
      const progress = scrollPos / scrollMax;
      const maxMove = 400;
      sun.setAttribute('cx', 400 + progress * maxMove);
    });
  </script>
</body>
</html>
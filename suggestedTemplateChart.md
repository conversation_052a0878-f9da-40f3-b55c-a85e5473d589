# Comprehensive Backtesting Visualization Template

This template provides a detailed guide for creating sophisticated backtesting visualizations using matplotlib. It includes multiple chart types and layouts to effectively communicate trading strategy performance.

## Core Chart Types for Backtesting

### 1. Price and Signal Charts

#### Candlestick Charts
```python
# Create candlestick chart
from matplotlib.dates import date2num
from mplfinance.original_flavor import candlestick_ohlc

# Convert dates to matplotlib format
ohlc = df[['Open', 'High', 'Low', 'Close']].copy()
ohlc.index = date2num(ohlc.index.to_pydatetime())
ohlc = ohlc.reset_index()

# Plot candlesticks
fig, ax = plt.subplots(figsize=(12, 6))
candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='green', colordown='red', alpha=0.8)
```

#### OHLC Charts
```python
from mplfinance.original_flavor import plot_day_summary_ohlc

# Plot OHLC bars
fig, ax = plt.subplots(figsize=(12, 6))
plot_day_summary_ohlc(ax, ohlc.values, colorup='green', colordown='red')
```

#### Hollow Candles (Trend Visualization)
```python
# Create hollow candles (white body when close > open, filled when close < open)
def create_hollow_candles(ax, df):
    for i, (idx, row) in enumerate(df.iterrows()):
        if row['Close'] >= row['Open']:
            # Bullish candle (hollow)
            rect = plt.Rectangle((i-0.3, row['Open']), 0.6, row['Close']-row['Open'], 
                                 fill=False, edgecolor='green', linewidth=1)
            ax.add_patch(rect)
            # Wicks
            ax.plot([i, i], [row['Low'], row['Open']], color='green', linewidth=1)
            ax.plot([i, i], [row['Close'], row['High']], color='green', linewidth=1)
        else:
            # Bearish candle (filled)
            rect = plt.Rectangle((i-0.3, row['Open']), 0.6, row['Close']-row['Open'], 
                                 color='red', edgecolor='red', linewidth=1)
            ax.add_patch(rect)
            # Wicks
            ax.plot([i, i], [row['Low'], row['Close']], color='red', linewidth=1)
            ax.plot([i, i], [row['Open'], row['High']], color='red', linewidth=1)
```

#### Heikin-Ashi Candles (Trend Smoothing)
```python
# Calculate Heikin-Ashi candles
df_ha = df.copy()
df_ha['HA_Close'] = (df['Open'] + df['High'] + df['Low'] + df['Close']) / 4
df_ha['HA_Open'] = df['Open'].copy()
for i in range(1, len(df)):
    df_ha.loc[df.index[i], 'HA_Open'] = (df_ha.loc[df.index[i-1], 'HA_Open'] + 
                                         df_ha.loc[df.index[i-1], 'HA_Close']) / 2
df_ha['HA_High'] = df_ha[['High', 'HA_Open', 'HA_Close']].max(axis=1)
df_ha['HA_Low'] = df_ha[['Low', 'HA_Open', 'HA_Close']].min(axis=1)
```

### 2. Volume Analysis Charts

#### Volume Bars with Color Coding
```python
# Plot volume bars colored by price direction
colors = ['green' if close >= open else 'red' for open, close in zip(df['Open'], df['Close'])]
ax.bar(df.index, df['Volume'], color=colors, alpha=0.5)

# Add moving average of volume
ax.plot(df.index, df['Volume'].rolling(20).mean(), color='blue', linewidth=1.5)
```

#### Volume Profile (Price vs Volume Distribution)
```python
# Create volume profile
def volume_profile(ax, df, bins=100):
    price_range = df['Close'].max() - df['Close'].min()
    bin_size = price_range / bins
    price_bins = np.arange(df['Close'].min(), df['Close'].max() + bin_size, bin_size)
    
    # Group volumes by price bins
    vol_by_price = {}
    for i, row in df.iterrows():
        bin_idx = int((row['Close'] - df['Close'].min()) / bin_size)
        if bin_idx >= len(price_bins) - 1:
            bin_idx = len(price_bins) - 2
        price_bin = price_bins[bin_idx]
        vol_by_price[price_bin] = vol_by_price.get(price_bin, 0) + row['Volume']
    
    # Plot horizontal bars
    prices = list(vol_by_price.keys())
    volumes = list(vol_by_price.values())
    ax.barh(prices, volumes, height=bin_size*0.8, alpha=0.3, color='blue')
```

#### VWAP (Volume-Weighted Average Price)
```python
# Calculate and plot VWAP
df['VWAP'] = (df['Volume'] * (df['High'] + df['Low'] + df['Close']) / 3).cumsum() / df['Volume'].cumsum()
ax.plot(df.index, df['VWAP'], color='purple', linewidth=1.5, label='VWAP')
```

### 3. Technical Indicator Charts

#### Moving Averages (Multiple Types)
```python
# Simple Moving Average (SMA)
df['SMA_50'] = df['Close'].rolling(window=50).mean()
ax.plot(df.index, df['SMA_50'], color='blue', linewidth=1.5, label='50-day SMA')

# Exponential Moving Average (EMA)
df['EMA_20'] = df['Close'].ewm(span=20, adjust=False).mean()
ax.plot(df.index, df['EMA_20'], color='orange', linewidth=1.5, label='20-day EMA')

# Weighted Moving Average (WMA)
def weighted_moving_average(data, window):
    weights = np.arange(1, window+1)
    return data.rolling(window).apply(lambda x: np.sum(weights*x) / weights.sum(), raw=True)

df['WMA_20'] = weighted_moving_average(df['Close'], 20)
ax.plot(df.index, df['WMA_20'], color='green', linewidth=1.5, label='20-day WMA')
```

#### Bollinger Bands
```python
# Calculate and plot Bollinger Bands
window = 20
df['SMA_20'] = df['Close'].rolling(window=window).mean()
df['STD_20'] = df['Close'].rolling(window=window).std()
df['Upper_Band'] = df['SMA_20'] + (df['STD_20'] * 2)
df['Lower_Band'] = df['SMA_20'] - (df['STD_20'] * 2)

ax.plot(df.index, df['Upper_Band'], color='red', linestyle='--', linewidth=1, label='Upper BB')
ax.plot(df.index, df['SMA_20'], color='blue', linewidth=1, label='20-day SMA')
ax.plot(df.index, df['Lower_Band'], color='green', linestyle='--', linewidth=1, label='Lower BB')
ax.fill_between(df.index, df['Upper_Band'], df['Lower_Band'], color='gray', alpha=0.1)
```

#### MACD (Moving Average Convergence Divergence)
```python
# Calculate MACD
df['EMA_12'] = df['Close'].ewm(span=12, adjust=False).mean()
df['EMA_26'] = df['Close'].ewm(span=26, adjust=False).mean()
df['MACD'] = df['EMA_12'] - df['EMA_26']
df['Signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
df['Histogram'] = df['MACD'] - df['Signal']

# Plot MACD
ax2.plot(df.index, df['MACD'], color='blue', linewidth=1.5, label='MACD')
ax2.plot(df.index, df['Signal'], color='red', linewidth=1, label='Signal')
ax2.bar(df.index, df['Histogram'], color=['green' if h > 0 else 'red' for h in df['Histogram']], alpha=0.5)
```

#### RSI (Relative Strength Index)
```python
# Calculate RSI
delta = df['Close'].diff()
gain = delta.where(delta > 0, 0)
loss = -delta.where(delta < 0, 0)
avg_gain = gain.rolling(window=14).mean()
avg_loss = loss.rolling(window=14).mean()
rs = avg_gain / avg_loss
df['RSI'] = 100 - (100 / (1 + rs))

# Plot RSI
ax3.plot(df.index, df['RSI'], color='purple', linewidth=1.5)
ax3.axhline(70, color='red', linestyle='--', alpha=0.5)
ax3.axhline(30, color='green', linestyle='--', alpha=0.5)
ax3.fill_between(df.index, df['RSI'], 70, where=(df['RSI'] >= 70), color='red', alpha=0.3)
ax3.fill_between(df.index, df['RSI'], 30, where=(df['RSI'] <= 30), color='green', alpha=0.3)
```

### 4. Trade Visualization Elements

#### Buy/Sell Markers with Annotations
```python
# Plot buy signals
for idx in buy_signals:
    ax.scatter(idx, df.loc[idx, 'Close'], color='green', s=100, marker='^', zorder=5)
    ax.annotate(f"Buy\n${df.loc[idx, 'Close']:.2f}", 
                (idx, df.loc[idx, 'Close']), 
                xytext=(0, 30), 
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='green'),
                ha='center', fontsize=8)

# Plot sell signals
for idx in sell_signals:
    ax.scatter(idx, df.loc[idx, 'Close'], color='red', s=100, marker='v', zorder=5)
    ax.annotate(f"Sell\n${df.loc[idx, 'Close']:.2f}", 
                (idx, df.loc[idx, 'Close']), 
                xytext=(0, -30), 
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='red'),
                ha='center', fontsize=8)
```

#### Position Holding Periods
```python
# Highlight holding periods
for start, end in zip(buy_signals, sell_signals):
    if start < end:  # Valid trade
        ax.axvspan(start, end, alpha=0.2, color='lightgreen')
```

#### Drawdown Visualization
```python
# Calculate and plot drawdowns
df['Peak'] = df['Portfolio_Value'].cummax()
df['Drawdown'] = (df['Portfolio_Value'] - df['Peak']) / df['Peak'] * 100

ax4.fill_between(df.index, df['Drawdown'], 0, color='red', alpha=0.3)
ax4.set_ylabel('Drawdown (%)')
```

### 5. Performance Metrics Visualization

#### Equity Curve with Benchmark Comparison
```python
# Plot equity curves
ax.plot(df.index, df['Strategy_Equity'], color='green', linewidth=2, label='Strategy')
ax.plot(df.index, df['Benchmark_Equity'], color='gray', linestyle='--', linewidth=1.5, label='Buy & Hold')
```

#### Underwater Plot (Drawdowns Over Time)
```python
# Calculate running maximum
df['Running_Max'] = df['Strategy_Equity'].cummax()
# Calculate drawdown percentage
df['Drawdown_Pct'] = (df['Strategy_Equity'] - df['Running_Max']) / df['Running_Max'] * 100

# Plot underwater chart
ax.fill_between(df.index, df['Drawdown_Pct'], 0, color='red', alpha=0.3)
ax.set_ylabel('Drawdown (%)')
ax.set_title('Underwater Plot (Drawdowns Over Time)')
```

#### Rolling Performance Metrics
```python
# Calculate rolling Sharpe ratio (252 trading days per year)
returns = df['Strategy_Equity'].pct_change()
rolling_sharpe = (returns.rolling(window=63).mean() * 252) / (returns.rolling(window=63).std() * np.sqrt(252))

# Plot rolling metrics
ax.plot(df.index, rolling_sharpe, color='purple', linewidth=1.5)
ax.axhline(y=1, color='red', linestyle='--', alpha=0.5)
ax.axhline(y=2, color='green', linestyle='--', alpha=0.5)
ax.set_ylabel('Rolling Sharpe Ratio (3-month)')
```

#### Performance Metrics Box
```python
# Create metrics box
def add_metrics_box(fig, metrics_dict, pos=(0.02, 0.97)):
    metrics_text = "\n".join([f"{k}: {v}" for k, v in metrics_dict.items()])
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
    fig.text(pos[0], pos[1], metrics_text, transform=fig.transFigure, fontsize=9,
             verticalalignment='top', bbox=props)

metrics = {
    'Total Return': f"{total_return:.2f}%",
    'Annualized Return': f"{annual_return:.2f}%",
    'Sharpe Ratio': f"{sharpe_ratio:.2f}",
    'Max Drawdown': f"{max_drawdown:.2f}%",
    'Win Rate': f"{win_rate:.2f}%",
    'Profit Factor': f"{profit_factor:.2f}"
}

add_metrics_box(fig, metrics)
```

## Layout Templates

### 1. Basic 4-Panel Backtesting Layout
```python
def create_basic_backtest_chart(df, ticker, strategy_name):
    # Create figure and subplots
    fig = plt.figure(figsize=(12, 10))
    gs = gridspec.GridSpec(4, 1, height_ratios=[3, 1, 1, 1])
    
    # Price and signals chart
    ax1 = plt.subplot(gs[0])
    ax1.plot(df.index, df['Close'], color='blue', linewidth=1.5)
    ax1.set_title(f"{strategy_name} - {ticker}")
    
    # Volume chart
    ax2 = plt.subplot(gs[1], sharex=ax1)
    ax2.bar(df.index, df['Volume'], color='gray', alpha=0.5)
    ax2.set_ylabel('Volume')
    
    # Strategy returns chart
    ax3 = plt.subplot(gs[2], sharex=ax1)
    ax3.plot(df.index, df['Strategy_Returns'], color='green', linewidth=1.5)
    ax3.plot(df.index, df['Benchmark_Returns'], color='gray', linestyle='--', linewidth=1)
    ax3.set_ylabel('Returns (%)')
    
    # Drawdown chart
    ax4 = plt.subplot(gs[3], sharex=ax1)
    ax4.fill_between(df.index, df['Drawdown'], 0, color='red', alpha=0.3)
    ax4.set_ylabel('Drawdown (%)')
    ax4.set_xlabel('Date')
    
    # Format x-axis
    plt.setp(ax1.get_xticklabels(), visible=False)
    plt.setp(ax2.get_xticklabels(), visible=False)
    plt.setp(ax3.get_xticklabels(), visible=False)
    
    plt.tight_layout()
    return fig
```

### 2. Advanced Multi-Panel Technical Analysis Layout
```python
def create_advanced_technical_chart(df, ticker, strategy_name):
    # Create figure and subplots
    fig = plt.figure(figsize=(14, 12))
    gs = gridspec.GridSpec(5, 4, height_ratios=[3, 1, 1, 1, 1])
    
    # Main price chart with indicators
    ax_price = plt.subplot(gs[0, :])
    # Volume chart
    ax_vol = plt.subplot(gs[1, :], sharex=ax_price)
    # MACD
    ax_macd = plt.subplot(gs[2, :], sharex=ax_price)
    # RSI
    ax_rsi = plt.subplot(gs[3, :], sharex=ax_price)
    # Equity curve and drawdown
    ax_equity = plt.subplot(gs[4, :2], sharex=ax_price)
    ax_dd = plt.subplot(gs[4, 2:], sharex=ax_price)
    
    # Hide x-axis labels for all but bottom charts
    for ax in [ax_price, ax_vol, ax_macd, ax_rsi]:
        plt.setp(ax.get_xticklabels(), visible=False)
    
    plt.tight_layout()
    return fig
```

### 3. Performance Dashboard Layout
```python
def create_performance_dashboard(df, ticker, strategy_name):
    # Create figure and subplots
    fig = plt.figure(figsize=(15, 10))
    gs = gridspec.GridSpec(3, 3)
    
    # Equity curve
    ax_equity = plt.subplot(gs[0, :])
    # Drawdown
    ax_dd = plt.subplot(gs[1, 0])
    # Monthly returns heatmap
    ax_monthly = plt.subplot(gs[1, 1:])
    # Rolling Sharpe
    ax_sharpe = plt.subplot(gs[2, 0])
    # Win/loss distribution
    ax_dist = plt.subplot(gs[2, 1])
    # Trade duration
    ax_duration = plt.subplot(gs[2, 2])
    
    plt.tight_layout()
    return fig
```

## Saving and Returning the Chart

```python
def create_chart(df, ticker='AAPL', unique_id='12345'):
    """
    Create a comprehensive backtesting chart and return the file path.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing OHLCV data and strategy signals
    ticker : str
        Ticker symbol
    unique_id : str
        Unique identifier for the chart file
        
    Returns:
    --------
    str
        Path to the saved chart file
    """
    # Create the chart (using any of the templates above)
    fig = create_basic_backtest_chart(df, ticker, "50-day MA Crossover Strategy")
    
    # Save the chart
    file_path = os.path.join('software/library/images', f'backtest_strategy_{ticker.lower()}_{unique_id}.png')
    plt.savefig(file_path, dpi=100, bbox_inches='tight')
    plt.close(fig)
    
    # Return the file path
    return file_path
```

## Complete Example Implementation

```python
def create_chart(df):
    """
    Create a comprehensive backtesting visualization for a moving average crossover strategy.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with OHLCV data
        
    Returns:
    --------
    str
        Path to the saved chart file
    """
    # Calculate indicators and signals
    df['MA_50'] = df['Close'].rolling(window=50).mean()
    
    # Generate signals
    df['Signal'] = 0
    df['Signal'] = np.where(df['Close'] > df['MA_50'], 1, 0)
    df['Position'] = df['Signal'].diff()
    
    # Find buy and sell signals
    buy_signals = df[df['Position'] == 1].index
    sell_signals = df[df['Position'] == -1].index
    
    # Calculate strategy returns
    df['Strategy_Return'] = df['Close'].pct_change() * df['Signal'].shift(1)
    df['Cumulative_Return'] = (1 + df['Strategy_Return']).cumprod() - 1
    df['Buy_Hold_Return'] = (df['Close'] / df['Close'].iloc[50] - 1)  # Start at same point as strategy
    
    # Calculate drawdowns
    df['Peak'] = df['Cumulative_Return'].cummax()
    df['Drawdown'] = (df['Cumulative_Return'] - df['Peak']) * 100
    
    # Calculate performance metrics
    total_return = df['Cumulative_Return'].iloc[-1] * 100
    sharpe_ratio = df['Strategy_Return'].mean() / df['Strategy_Return'].std() * np.sqrt(252)
    max_drawdown = df['Drawdown'].min()
    
    # Calculate equity curves
    initial_investment = 10000
    df['Strategy_Equity'] = initial_investment * (1 + df['Cumulative_Return'])
    df['Buy_Hold_Equity'] = initial_investment * (1 + df['Buy_Hold_Return'])
    
    # Create figure and subplots
    fig = plt.figure(figsize=(10, 8))
    gs = gridspec.GridSpec(2, 1, height_ratios=[2, 1])
    
    # Price and signals chart
    ax1 = plt.subplot(gs[0])
    ax1.plot(df.index, df['Close'], color='blue', linewidth=1.5, label='Close Price')
    ax1.plot(df.index, df['MA_50'], color='orange', linewidth=1.5, label='50-day MA')
    
    # Add buy/sell markers
    for idx in buy_signals:
        ax1.scatter(idx, df.loc[idx, 'Close'], color='green', s=100, marker='^', zorder=5)
    for idx in sell_signals:
        ax1.scatter(idx, df.loc[idx, 'Close'], color='red', s=100, marker='v', zorder=5)
    
    # Highlight holding periods
    for i in range(len(buy_signals)):
        if i < len(sell_signals):
            ax1.axvspan(buy_signals[i], sell_signals[i], alpha=0.2, color='lightgreen')
    
    ax1.set_title(f"50-day MA Crossover Strategy - AAPL", fontsize=14)
    ax1.set_ylabel('Price ($)', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # Add metrics box
    metrics = {
        'Total Return': f"{total_return:.2f}%",
        'Sharpe Ratio': f"{sharpe_ratio:.2f}",
        'Max Drawdown': f"{max_drawdown:.2f}%",
        'Final $10K': f"${df['Strategy_Equity'].iloc[-1]:.2f}"
    }
    
    metrics_text = "\n".join([f"{k}: {v}" for k, v in metrics.items()])
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
    ax1.text(0.02, 0.98, metrics_text, transform=ax1.transAxes, fontsize=10,
             verticalalignment='top', bbox=props)
    
    # Returns comparison chart
    ax2 = plt.subplot(gs[1], sharex=ax1)
    ax2.plot(df.index, df['Strategy_Equity'], color='green', linewidth=2, label='Strategy')
    ax2.plot(df.index, df['Buy_Hold_Equity'], color='gray', linestyle='--', linewidth=1.5, label='Buy & Hold')
    ax2.axhline(y=initial_investment, color='black', linestyle='-', linewidth=0.5)
    ax2.set_ylabel('Portfolio Value ($)', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    # Hide x-axis labels for top chart
    plt.setp(ax1.get_xticklabels(), visible=False)
    
    plt.tight_layout()
    
    # Save the chart
    ticker = 'aapl'
    unique_id = ''.join(random.choices('0123456789abcdef', k=8))
    file_path = os.path.join('software/library/images', f'backtest_strategy_{ticker}_{unique_id}.png')
    plt.savefig(file_path, dpi=100, bbox_inches='tight')
    plt.close(fig)
    
    # Return the file path
    return file_path
```

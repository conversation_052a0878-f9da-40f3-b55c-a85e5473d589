<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Backup Recovery</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="backup-recovery-component" id="backupComponent">
        <div class="controls">
            <input type="datetime-local" id="dateTimeInput" step="1">
        </div>

        <div class="timeline-navigation">
             <button class="nav-arrow left" id="navLeft" aria-label="Previous Backup">
                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
             </button>
             <div class="timeline-container" id="timelineContainer">
                 <div class="timeline-track" id="timelineTrack">
                     <!-- Backup nodes will be injected here by JS -->
                 </div>
             </div>
             <button class="nav-arrow right" id="navRight" aria-label="Next Backup">
                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
             </button>
        </div>


        <div class="backup-details" id="backupDetails">
            <p class="details-placeholder">Select a backup point</p>
            <div class="details-content">
                <div class="details-header">
                     <h3 id="detailsType"></h3>
                     <button id="restoreButton" class="restore-button">Restore</button>
                </div>
                <p><strong>ID:</strong> <span id="detailsId"></span></p>
                <p><strong>Date:</strong> <time id="detailsDate"></time></p>
                <p><strong>Details:</strong> <span id="detailsInfo"></span></p>
            </div>
        </div>
    </div>

    <!-- Restore Action Overlay -->
    <div class="restore-overlay" id="restoreOverlay">
        <div class="overlay-content">
            <div class="spinner" id="restoreSpinner"></div>
            <svg class="checkmark" id="restoreCheckmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>
                <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
            </svg>
            <p id="restoreStatusText"></p>
        </div>
    </div>


    <script src="backup.js"></script>
</body>
</html>
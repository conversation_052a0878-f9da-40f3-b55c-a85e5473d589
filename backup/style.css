@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes draw-checkmark {
    0% { stroke-dashoffset: 30; } /* Length of the checkmark path */
    100% { stroke-dashoffset: 0; }
}

@keyframes draw-circle {
     0% { stroke-dashoffset: 157; } /* Circumference: 2 * pi * 25 */
     100% { stroke-dashoffset: 0; }
}

@keyframes fade-in-scale {
  0% { opacity: 0; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes fade-out {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes pulse-ring {
    0% {
        box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px currentColor;
    }
    50% {
        box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 6px currentColor; /* Slightly expand the ring */
    }
    100% {
        box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px currentColor;
    }
}

/* Keyframes for the moving gradient line */
@keyframes move-gradient {
  0% { background-position: 100% 0; } /* Start position */
  100% { background-position: -100% 0; } /* End position */
}

/* Add new keyframes for breathing glow */
@keyframes breathing-glow {
  0% {
    box-shadow: 0 0 3px 0px hsla(var(--primary), 0.4);
  }
  50% {
    box-shadow: 0 0 8px 2px hsla(var(--primary), 0.7); /* Stronger glow */
  }
  100% {
    box-shadow: 0 0 3px 0px hsla(var(--primary), 0.4);
  }
}

/* Keyframes for pulsing text opacity */
@keyframes pulse-text {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Keyframes for success background flash */
@keyframes flash-bg {
  0%, 100% { background-color: hsl(var(--card)); }
  50% { background-color: hsla(var(--primary-success), 0.1); } /* Brief flash of success color */
}

:root {
    /* Shadcn Inspired Palette (Default - Slate/Blue) */
    --background: 210 20% 98%; /* Lighter background hsl(210 20% 98%) */
    --foreground: 224 71% 4%; /* Darker foreground hsl(224 71% 4%) */

    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 221.2 83.2% 53.3%; /* Blue */
    --primary-foreground: 210 40% 98%;
    --primary-hover: 221.2 83.2% 48.3%;
    --primary-focus-ring: 217, 91%, 96%;
    --primary-success: 142.1 70.6% 45.3%; /* Green 600 for success */
    --primary-success-foreground: #ffffff;

    --secondary: 215 25% 95%; /* Lighter secondary hsl(215 25% 95%) */
    --secondary-foreground: 224 71% 4%;

    --muted: 215 25% 95%;
    --muted-foreground: 215 20% 65%; /* Lighter muted fg hsl(215 20% 65%) */

    --accent: 215 25% 95%;
    --accent-foreground: 224 71% 4%;

    --destructive: 0 72% 51%; /* Red 600 */
    --destructive-foreground: 0 0% 100%;

    --border: 215 28% 90%; /* Slightly softer border hsl(215 28% 90%) */
    --input: 215 28% 90%;
    --ring: 217, 91%, 96%;

    --radius: 0.5rem;
    --radius-sm: 0.3rem;
    --radius-lg: 0.75rem;

    /* --- Updated Node Colors (shadcn inspired) --- */
    --manual-color: 201, 94%, 51%;     /* Bright blue */
    --scheduled-color: 142, 76%, 45%;  /* Green */
    --missed-color: 0, 84%, 60%;       /* Red */

    --node-size: 10px; /* Increased slightly */
    --node-size-active: 14px;
    --timeline-height: 48px; /* Increased slightly */

    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    --transition-curve-elastic: cubic-bezier(0.68, -0.55, 0.27, 1.55);
    --transition-curve-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --transition-snap-duration: 0.35s;
    --transition-hover-duration: 0.15s;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    --shadow-md: 0 3px 5px -1px rgba(0, 0, 0, 0.04), 0 2px 3px -1px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 8px 13px -3px rgba(0, 0, 0, 0.05), 0 3px 5px -2px rgba(0, 0, 0, 0.04);
    --shadow-inset: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
     -webkit-font-smoothing: antialiased;
     -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-sans);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 1.5rem;
    line-height: 1.5;
}

.backup-recovery-component {
    width: 100%;
    max-width: 1100px; /* Slightly reduced max width */
    padding: 1.5rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    background-color: hsl(var(--card));
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.25rem;
    position: relative; /* Needed if overlay is positioned absolutely relative to this */
    overflow: hidden; /* Clip potential overflows from animations */
}

.controls {
    width: auto;
}

#dateTimeInput {
    font-family: var(--font-sans);
    padding: 0.5rem 0.75rem;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    transition: border-color var(--transition-hover-duration) ease-in-out, box-shadow var(--transition-hover-duration) ease-in-out;
    min-width: 240px;
}

#dateTimeInput:focus {
    outline: none;
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 2.5px hsla(var(--ring), 0.4);
}

.timeline-navigation {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    gap: 0.75rem;
}

.nav-arrow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    background-color: hsl(var(--card));
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    transition: background-color var(--transition-hover-duration) ease-in-out, color var(--transition-hover-duration) ease-in-out, border-color var(--transition-hover-duration) ease-in-out, transform var(--transition-hover-duration) ease-in-out;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.nav-arrow:hover:not(:disabled) {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
    transform: translateY(-1px);
    border-color: hsl(var(--border));
}

.nav-arrow:active:not(:disabled) {
    transform: translateY(0px);
}

.nav-arrow:focus {
    outline: none;
     box-shadow: 0 0 0 2.5px hsla(var(--ring), 0.4);
     border-color: hsl(var(--primary));
}

.nav-arrow:disabled {
    color: hsl(var(--muted-foreground));
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
    cursor: not-allowed;
    opacity: 0.5;
    box-shadow: none;
}

.nav-arrow svg {
    display: block;
    width: 1rem;
    height: 1rem;
}

.timeline-container {
    flex-grow: 1;
    height: var(--timeline-height);
    overflow: hidden;
    position: relative;
    cursor: grab;
    user-select: none;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--muted)); /* Changed from secondary to muted */
    box-shadow: var(--shadow-inset);
}

.timeline-container:active {
    cursor: grabbing;
}

.timeline-track {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    transform: translateX(0);
    transition: transform var(--transition-snap-duration) var(--transition-curve-smooth);
}

/* --- Improved Timeline Track Style --- */
/* Base static line with animated glow */
.timeline-track::before {
    content: '';
    position: absolute;
    left: calc(var(--node-size) / 2);
    right: calc(var(--node-size) / 2);
    top: 50%;
    height: 2px; /* Revert to 2px thickness */
    /* Use a darker, solid base color for reliable visibility */
    background-color: hsl(var(--muted-foreground) / 0.5);
    transform: translateY(-50%);
    z-index: 0;
    border-radius: 2px; /* Match height */
    /* Apply the breathing glow animation via box-shadow */
    animation: breathing-glow 2.5s ease-in-out infinite;
}

.backup-node {
    position: absolute;
    top: 50%;
    width: var(--node-size);
    height: var(--node-size);
    border-radius: 50%;
    box-shadow: 0 0 0 2px hsl(var(--secondary)); /* Background cutout */
    transform: translate(-50%, -50%);
    cursor: pointer;
    transition: transform var(--transition-hover-duration) var(--transition-curve-smooth),
                width var(--transition-snap-duration) var(--transition-curve-elastic),
                height var(--transition-snap-duration) var(--transition-curve-elastic),
                box-shadow var(--transition-snap-duration) var(--transition-curve-smooth),
                background-color var(--transition-hover-duration) ease-in-out;
    z-index: 2;
    flex-shrink: 0;
}

.backup-node:hover {
    transform: translate(-50%, -50%) scale(1.6);
    z-index: 3;
    /* Ring using currentColor on hover */
    box-shadow: 0 0 0 2px hsl(var(--secondary)), /* Cutout */
                0 0 0 3.5px currentColor;
}

/* --- Refined Selected Node Style with Ring --- */
.backup-node.selected {
    width: var(--node-size-active);
    height: var(--node-size-active);
    transform: translate(-50%, -50%);
    /* Simplified & clearer ring effect */
    box-shadow:
        /* 1. Inner ring matching page background (cutout) */
        0 0 0 2px hsl(var(--background)),
        /* 2. Prominent colored ring matching node type */
        0 0 0 4px currentColor;
    z-index: 4;
    /* Add pulsing animation */
    animation: pulse-ring 1.5s ease-in-out infinite;
}

/* Apply NEW type colors using HSL variables */
.backup-node.manual { background-color: hsl(var(--manual-color)); color: hsl(var(--manual-color)); }
.backup-node.scheduled { background-color: hsl(var(--scheduled-color)); color: hsl(var(--scheduled-color)); }
.backup-node.missed { background-color: hsl(var(--missed-color)); color: hsl(var(--missed-color)); }


.backup-details {
    margin-top: 1rem;
    padding: 1rem 1.25rem; /* Slightly reduced padding */
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius); /* Revert to standard radius */
    width: 100%;
    min-height: 100px; /* Adjusted min-height */
    background-color: hsl(var(--card));
    transition: background-color var(--transition-hover-duration), box-shadow var(--transition-hover-duration);
    box-shadow: var(--shadow-sm); /* Reduced shadow intensity */
    position: relative;
    overflow: hidden;
}

.backup-details .details-placeholder {
    color: hsl(var(--muted-foreground));
    font-style: normal;
    font-size: 0.875rem; /* Standard text size */
    padding: 1rem 0; /* Standard padding */
    display: flex;
    align-items: center;
    justify-content: center;
    /* Calculate height based on expected content lines + header */
    height: calc( (0.875rem * 1.5 * 3) + 1rem + 0.875rem); /* Approx 3 lines of text + header space */
    text-align: center;
    position: absolute;
    inset: 0;
    opacity: 1;
    transition: opacity 0.2s ease-out; /* Faster fade */
}

.backup-details .details-content {
    display: block;
    text-align: left;
    opacity: 0;
    /* Faster fade in */
    transition: opacity 0.25s ease-in-out 0.05s;
    position: relative;
    z-index: 1;
}

.backup-details.has-data .details-placeholder {
    opacity: 0;
    pointer-events: none;
}

.backup-details.has-data .details-content {
    opacity: 1;
}

.details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem; /* Reduced margin */
    /* Removed bottom border */
    min-height: auto; /* Remove min-height */
    padding-bottom: 0; /* Remove padding */
}

.backup-details h3 {
    font-size: 1rem; /* Standard size */
    font-weight: 500; /* Regular weight */
    color: hsl(var(--card-foreground));
    margin: 0;
    text-transform: capitalize;
    line-height: 1.4; /* Adjust line height */
}

.backup-details p {
    font-size: 0.875rem;
    margin-bottom: 0.375rem; /* Reduced spacing */
    color: hsl(var(--foreground)); /* Use standard foreground for values */
    line-height: 1.4;
    display: grid;
    grid-template-columns: 50px 1fr; /* Slightly narrower label column */
    gap: 0.375rem; /* Reduced gap */
}
.backup-details p:last-child {
    margin-bottom: 0;
}

.backup-details p strong {
    color: hsl(var(--muted-foreground));
    font-weight: 400; /* Lighter weight */
    text-align: right;
    font-size: 0.8rem; /* Slightly smaller label */
    line-height: 1.5; /* Align baseline better */
}

.restore-button {
    font-family: var(--font-sans);
    /* Use foreground for dark bg, primary-foreground for light text */
    background-color: hsl(var(--foreground));
    color: hsl(var(--primary-foreground));
    border: 1px solid hsl(var(--border)); /* Add subtle border */
    padding: 0.4rem 0.9rem; /* Adjusted padding */
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500; /* Regular weight */
    cursor: pointer;
    /* Refined transition */
    transition: background-color var(--transition-hover-duration) ease-in-out,
                color var(--transition-hover-duration) ease-in-out,
                border-color var(--transition-hover-duration) ease-in-out,
                transform var(--transition-hover-duration) ease-in-out,
                box-shadow var(--transition-hover-duration) ease-in-out;
    white-space: nowrap;
    letter-spacing: normal; /* Remove letter spacing */
    box-shadow: var(--shadow-sm);
}

.restore-button:hover {
    /* Slightly lighter dark background on hover */
    background-color: hsl(var(--foreground) / 0.9);
    border-color: hsl(var(--input)); /* Use input border color on hover */
    color: hsl(var(--primary-foreground)); /* Keep text color */
    transform: translateY(0); /* Remove hover transform */
    box-shadow: var(--shadow-sm); /* Keep shadow consistent */
}
.restore-button:active {
     /* Subtle scale down on active */
     transform: scale(0.98);
     background-color: hsl(var(--foreground) / 0.9); /* Keep hover bg */
     box-shadow: var(--shadow-sm);
}

.restore-button:focus {
    outline: none;
    /* Focus ring using foreground color */
    box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--foreground));
}


/* --- Restore Overlay Styles --- */
.restore-overlay {
    position: fixed; /* Cover viewport */
    inset: 0;
    background-color: hsla(var(--background), 0.85); /* Semi-transparent bg */
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s var(--transition-curve-smooth), visibility 0s linear 0.3s;
}

.restore-overlay.visible {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s var(--transition-curve-smooth), visibility 0s linear 0s;
}

.overlay-content {
    background-color: hsl(var(--card));
    padding: 2.5rem 3rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    text-align: center;
    color: hsl(var(--card-foreground));
    opacity: 0;
    transform: scale(0.9);
    /* Faster initial pop-in */
    transition: opacity 0.25s ease-out, transform 0.25s ease-out;
}

.restore-overlay.visible .overlay-content {
    opacity: 1;
    transform: scale(1);
    /* Apply transition delay after overlay is visible */
    transition: opacity 0.25s ease-out 0.1s, transform 0.25s ease-out 0.1s;
}


.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid hsl(var(--border));
    border-top-color: hsl(var(--primary));
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin: 0 auto 1rem auto;
    display: block;
    /* Add transition for fade out */
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
    opacity: 1;
    transform: scale(1);
}

.checkmark {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    display: block; /* Keep as block, control via opacity/transform */
    stroke-width: 3;
    stroke: hsl(var(--primary-success));
    stroke-miterlimit: 10;
    margin: 0 auto 1rem auto;
    box-shadow: inset 0px 0px 0px hsl(var(--primary-success));
    /* Add transition for scale/fade in */
    transition: opacity 0.2s ease-in 0.1s, transform 0.3s var(--transition-curve-elastic) 0.1s;
    opacity: 0;
    transform: scale(0.5); /* Start smaller */
}

.checkmark-circle {
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    stroke: hsl(var(--primary-success));
    fill: none;
    /* Animation triggered by .success class */
}

.checkmark-check {
    transform-origin: 50% 50%;
    stroke-dasharray: 30;
    stroke-dashoffset: 30;
    stroke-linecap: round;
     /* Animation triggered by .success class */
}

/* States for overlay content */
.restore-overlay.processing .spinner {
    opacity: 1;
    transform: scale(1);
}
.restore-overlay.processing .checkmark {
    opacity: 0;
    transform: scale(0.5);
}
/* Remove text ellipsis animation, use pulsing opacity */
.restore-overlay.processing #restoreStatusText {
    animation: pulse-text 1.5s ease-in-out infinite;
}

/* Remove previous dots animation */
/* @keyframes dots { from { width: 0; } to { width: 1.2em; }} */

/* --- Enhanced Success State --- */
.restore-overlay.success .overlay-content {
    /* Apply short background flash */
    animation: flash-bg 0.5s ease-out;
}

.restore-overlay.success .spinner {
    /* Quickly hide spinner */
    opacity: 0;
    transform: scale(0.7);
    transition-duration: 0.1s; /* Faster transition out */
}
.restore-overlay.success .checkmark {
    /* Pop in checkmark */
    opacity: 1;
    transform: scale(1);
}
.restore-overlay.success .checkmark-circle {
     /* Slightly faster, more elastic circle draw */
     animation: draw-circle 0.4s var(--transition-curve-elastic) forwards;
}
.restore-overlay.success .checkmark-check {
     /* Slightly faster, more elastic check draw, starts sooner */
     animation: draw-checkmark 0.3s var(--transition-curve-elastic) 0.2s forwards;
}

#restoreStatusText {
    font-size: 1rem;
    font-weight: 500;
    color: hsl(var(--muted-foreground));
    min-height: 1.5em;
    transition: color 0.3s ease-in-out; /* Smooth color change */
}
.restore-overlay.success #restoreStatusText {
     color: hsl(var(--primary-success));
}


/* Responsive Adjustments */
@media (max-width: 768px) {
    body {
        padding: 1rem;
        align-items: flex-start;
        overflow-y: auto;
    }

    .backup-recovery-component {
        padding: 1rem;
        gap: 1rem;
        border-radius: var(--radius);
    }

    .timeline-navigation {
        gap: 0.5rem;
    }

    .backup-details {
        padding: 0.8rem 1rem;
        min-height: 85px;
    }
    .details-header {
        margin-bottom: 0.6rem;
    }
     .backup-details h3 { font-size: 0.9rem; }
     .backup-details p { font-size: 0.8rem; }
     .restore-button { font-size: 0.75rem; padding: 0.35rem 0.8rem;}

     .overlay-content { padding: 2rem; }
}

@media (max-width: 480px) {
    :root { --timeline-height: 40px; --node-size: 8px; --node-size-active: 12px; }
    body { padding: 0.5rem; }
    .backup-recovery-component { padding: 0.75rem; gap: 0.75rem; }
    #dateTimeInput { font-size: 0.8rem; padding: 0.4rem 0.6rem; min-width: 180px;}
    .nav-arrow { width: 1.8rem; height: 1.8rem; }
    .nav-arrow svg { width: 0.9rem; height: 0.9rem; }
    .backup-details { padding: 0.75rem; min-height: 80px;}
    .details-header { flex-direction: row; align-items: center; gap: 0.5rem; margin-bottom: 0.6rem;}
    .restore-button { align-self: center; }

    .overlay-content { padding: 1.5rem; }
    #restoreStatusText { font-size: 0.9rem;}
    .spinner { width: 30px; height: 30px; border-width: 3px;}
    .checkmark { width: 40px; height: 40px; stroke-width: 2.5;}
}
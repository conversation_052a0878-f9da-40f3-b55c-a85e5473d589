document.addEventListener('DOMContentLoaded', () => {
    const backupComponent = document.getElementById('backupComponent');
    const timelineContainer = document.getElementById('timelineContainer');
    const timelineTrack = document.getElementById('timelineTrack');
    const dateTimeInput = document.getElementById('dateTimeInput');
    const backupDetailsEl = document.getElementById('backupDetails');
    const detailsContent = backupDetailsEl.querySelector('.details-content');
    const detailsType = document.getElementById('detailsType');
    const detailsId = document.getElementById('detailsId');
    const detailsDate = document.getElementById('detailsDate');
    const detailsInfo = document.getElementById('detailsInfo');
    const restoreButton = document.getElementById('restoreButton');
    const navLeft = document.getElementById('navLeft');
    const navRight = document.getElementById('navRight');
    const restoreOverlay = document.getElementById('restoreOverlay');
    const restoreStatusText = document.getElementById('restoreStatusText');
    const restoreSpinner = document.getElementById('restoreSpinner'); // Needed? Maybe just use class
    const restoreCheckmark = document.getElementById('restoreCheckmark'); // Needed? Maybe just use class

    const nodeSpacing = 90; // Adjusted slightly
    const restoreDuration = 1500; // Simulate restore time in ms
    const successDuration = 1200; // Time success message stays visible

    const backupData = [
        { id: 'bkp_001', timestamp: '2023-10-26T10:00:00Z', type: 'manual', details: 'Pre-deployment backup' },
        { id: 'bkp_002', timestamp: '2023-10-27T13:00:00Z', type: 'scheduled', details: 'Daily scheduled backup' },
        { id: 'bkp_003', timestamp: '2023-10-28T15:30:00Z', type: 'manual', details: 'After critical update' },
        { id: 'bkp_004', timestamp: '2023-10-29T13:05:00Z', type: 'missed', details: 'Ran after server restart' },
        { id: 'bkp_005', timestamp: '2023-10-30T13:00:00Z', type: 'scheduled', details: 'Daily scheduled backup' },
        { id: 'bkp_006', timestamp: '2023-10-30T18:45:00Z', type: 'manual', details: 'User requested backup' },
        { id: 'bkp_007', timestamp: '2023-10-31T13:00:00Z', type: 'scheduled', details: 'Daily scheduled backup' },
        { id: 'bkp_008', timestamp: '2023-11-01T09:15:00Z', type: 'manual', details: 'Routine check backup' },
        { id: 'bkp_009', timestamp: '2023-11-01T13:00:00Z', type: 'scheduled', details: 'Daily scheduled backup' },
        { id: 'bkp_010', timestamp: '2025-05-15T11:00:00Z', type: 'manual', details: 'Future test backup' },
        { id: 'bkp_011', timestamp: '2025-05-16T13:00:00Z', type: 'scheduled', details: 'Future scheduled' },
    ].map((b, index) => ({ ...b, date: new Date(b.timestamp), originalIndex: index }))
     .sort((a, b) => a.date - b.date)
     .map((b, index) => ({ ...b, sortedIndex: index }));

    let trackWidth = 0;
    let isDragging = false;
    let startX, currentTranslateX = 0;
    let timelineRect;
    let nodes = [];
    let selectedIndex = -1;
    let isRestoring = false; // Prevent multiple restore clicks

    function initializeTimeline() {
        if (!backupData || backupData.length === 0) {
            timelineTrack.innerHTML = '';
            backupDetailsEl.classList.remove('has-data');
            navLeft.disabled = true;
            navRight.disabled = true;
            return;
        };

        timelineRect = timelineContainer.getBoundingClientRect();
        const padding = timelineRect.width / 2;
        trackWidth = (backupData.length - 1) * nodeSpacing + padding * 2;
        timelineTrack.style.width = `${trackWidth}px`;
        timelineTrack.innerHTML = '';
        nodes = [];

        backupData.forEach((backup) => {
            const nodeEl = document.createElement('div');
            nodeEl.classList.add('backup-node', backup.type);
            nodeEl.dataset.id = backup.id;
            nodeEl.dataset.index = backup.sortedIndex;
            const positionPx = padding + backup.sortedIndex * nodeSpacing;
            nodeEl.style.left = `${positionPx}px`;
            nodeEl.addEventListener('click', () => handleNodeClick(backup.sortedIndex));
            timelineTrack.appendChild(nodeEl);
            nodes.push({ element: nodeEl, data: backup });
        });

        addEventListeners();
        selectNodeByIndex(backupData.length - 1, true);
    }

    function calculateCenteringTranslateX(targetIndex) {
        const containerWidth = timelineRect.width;
        const padding = containerWidth / 2;
        const nodeCenterPx = padding + targetIndex * nodeSpacing;
        let targetTranslateX = containerWidth / 2 - nodeCenterPx;
        const maxTranslateX = 0;
        const minTranslateX = Math.min(0, containerWidth - trackWidth);
        targetTranslateX = Math.max(minTranslateX, Math.min(maxTranslateX, targetTranslateX));
        return targetTranslateX;
    }

    function setTimelinePosition(targetTranslateX, immediate = false) {
        currentTranslateX = targetTranslateX;
        // Use CSS variable for transition duration/curve
        timelineTrack.style.transition = immediate ? 'none' : `transform var(--transition-snap-duration) var(--transition-curve-smooth)`;
        timelineTrack.style.transform = `translateX(${currentTranslateX}px)`;
    }

    function updateDateTimeInput(timestamp) {
        if (!timestamp) {
            dateTimeInput.value = ''; return;
        }
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        dateTimeInput.value = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    }

    function displayBackupDetails(backup) {
        if (!backup) {
            backupDetailsEl.classList.remove('has-data');
            detailsContent.style.opacity = 0;
            setTimeout(() => {
                if (!backupDetailsEl.classList.contains('has-data')) {
                    detailsContent.style.display = 'none';
                }
            }, 200);
            return;
        }
        detailsType.textContent = backup.type + ' Backup';
        detailsId.textContent = backup.id;
        detailsDate.textContent = backup.date.toLocaleString();
        detailsDate.setAttribute('datetime', backup.timestamp);
        detailsInfo.textContent = backup.details;
        detailsContent.style.display = 'block';
        backupDetailsEl.classList.add('has-data');
        requestAnimationFrame(() => { detailsContent.style.opacity = 1; });
    }

     function findClosestNodeByTime(targetTimestamp) {
        if (!backupData || backupData.length === 0) return null;
        let closestBackup = null;
        let minDiff = Infinity;
        backupData.forEach((backup) => {
            const diff = Math.abs(backup.date.getTime() - targetTimestamp);
            if (diff < minDiff) { minDiff = diff; closestBackup = backup; }
        });
        return closestBackup;
    }

    function findClosestNodeByPosition(targetTranslateX) {
        if (!nodes || nodes.length === 0) return -1;
        const containerCenterPx = timelineRect.width / 2;
        const trackCenterPx = containerCenterPx - targetTranslateX;
        let closestIndex = -1;
        let minDiff = Infinity;
        const padding = timelineRect.width / 2;
        nodes.forEach((node, index) => {
            const nodeCenterPx = padding + index * nodeSpacing;
            const diff = Math.abs(nodeCenterPx - trackCenterPx);
            if (diff < minDiff) { minDiff = diff; closestIndex = index; }
        });
        return closestIndex;
    }

    function selectNodeByIndex(index, immediate = false) {
        if (index < 0 || index >= nodes.length) {
            if (selectedIndex !== -1) { setTimelinePosition(calculateCenteringTranslateX(selectedIndex), immediate); }
            return;
        }
        if (index === selectedIndex && !immediate) {
             setTimelinePosition(calculateCenteringTranslateX(selectedIndex), immediate);
             return;
        }
        const previousIndex = selectedIndex;
        if (previousIndex !== -1 && nodes[previousIndex]) {
            nodes[previousIndex].element.classList.remove('selected');
        }
        selectedIndex = index;
        const selectedNode = nodes[selectedIndex];
        selectedNode.element.classList.add('selected');
        const backup = selectedNode.data;
        if (previousIndex !== selectedIndex || immediate) {
             displayBackupDetails(backup);
             updateDateTimeInput(backup.date.getTime());
        }
        const targetTranslateX = calculateCenteringTranslateX(selectedIndex);
        setTimelinePosition(targetTranslateX, immediate);
        navLeft.disabled = selectedIndex === 0;
        navRight.disabled = selectedIndex === nodes.length - 1;
    }

    function handleNodeClick(index) {
        if (isRestoring) return; // Prevent interaction during restore
        selectNodeByIndex(index);
    }

    function startDrag(e) {
        if (nodes.length <= 1 || isRestoring) return;
        isDragging = true;
        startX = (e.touches ? e.touches[0].clientX : e.clientX) - currentTranslateX;
        timelineContainer.style.cursor = 'grabbing';
        timelineTrack.style.transition = 'none';
    }

    function drag(e) {
        if (!isDragging || isRestoring) return;
        e.preventDefault();
        const currentX = e.touches ? e.touches[0].clientX : e.clientX;
        let newTranslateX = currentX - startX;
        const minValidTranslateX = calculateCenteringTranslateX(nodes.length - 1);
        const maxValidTranslateX = calculateCenteringTranslateX(0);
        const buffer = timelineRect.width * 0.3;
        const minClamp = minValidTranslateX - buffer;
        const maxClamp = maxValidTranslateX + buffer;
        newTranslateX = Math.max(minClamp, Math.min(maxClamp, newTranslateX));
        currentTranslateX = newTranslateX;
        timelineTrack.style.transform = `translateX(${currentTranslateX}px)`;
    }

    function endDrag() {
        if (!isDragging || isRestoring) return; // Ensure isDragging is true before proceeding
        isDragging = false;
        timelineContainer.style.cursor = 'grab';
        const closestIndex = findClosestNodeByPosition(currentTranslateX);
        if (closestIndex !== -1) {
            selectNodeByIndex(closestIndex);
        } else if (nodes.length > 0) {
            selectNodeByIndex(nodes.length - 1);
        }
    }


    function navigate(direction) {
        if (nodes.length === 0 || isRestoring) return;
        let newIndex = selectedIndex + direction;
        newIndex = Math.max(0, Math.min(nodes.length - 1, newIndex));
        selectNodeByIndex(newIndex);
    }

    // --- Updated Restore Click Handler ---
    function handleRestoreClick() {
        if (selectedIndex === -1 || !nodes[selectedIndex] || isRestoring) {
            return; // No node selected or already restoring
        }

        const backupToRestore = nodes[selectedIndex].data;
        console.log("Initiating restore for:", backupToRestore.id);
        isRestoring = true;
        restoreButton.disabled = true; // Disable button during restore

        // Show Overlay - Processing State
        restoreOverlay.classList.remove('success'); // Ensure success state is removed
        restoreOverlay.classList.add('processing', 'visible');
        restoreStatusText.textContent = `Restoring backup from ${backupToRestore.date.toLocaleTimeString()}`;

        // Simulate restore process
        setTimeout(() => {
            console.log("Restore supposedly complete for:", backupToRestore.id);

            // Transition to Success State
            restoreOverlay.classList.remove('processing');
            restoreOverlay.classList.add('success');
            restoreStatusText.textContent = 'Restore Successful!';

            // Hide overlay after a delay
            setTimeout(() => {
                restoreOverlay.classList.remove('visible');
                isRestoring = false;
                restoreButton.disabled = false;
                // Optional: Reset overlay classes fully after fade out
                 setTimeout(() => {
                    restoreOverlay.classList.remove('success');
                 }, 300); // Match overlay fade-out time
            }, successDuration);

        }, restoreDuration);
    }


    function handleKeyDown(e) {
         if (isRestoring) { // Block all keyboard interaction during restore
            e.preventDefault();
            return;
         }

        if (document.activeElement === dateTimeInput || document.activeElement === restoreButton) {
             if(document.activeElement === dateTimeInput && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) { return; }
             if (document.activeElement !== dateTimeInput || (e.key !== 'ArrowLeft' && e.key !== 'ArrowRight')) { return; }
        }

        switch (e.key) {
            case 'ArrowLeft': e.preventDefault(); navigate(-1); break;
            case 'ArrowRight': e.preventDefault(); navigate(1); break;
            case ' ': case 'Enter':
                 if (backupDetailsEl.classList.contains('has-data') && document.activeElement !== restoreButton) {
                      e.preventDefault();
                      handleRestoreClick();
                 }
                 break;
        }
    }

    function addEventListeners() {
        timelineContainer.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);
        timelineContainer.addEventListener('mouseleave', endDrag);
        timelineContainer.addEventListener('touchstart', startDrag, { passive: false });
        document.addEventListener('touchmove', drag, { passive: false });
        document.addEventListener('touchend', endDrag);
        document.addEventListener('touchcancel', endDrag);
        navLeft.addEventListener('click', () => navigate(-1));
        navRight.addEventListener('click', () => navigate(1));
        restoreButton.addEventListener('click', handleRestoreClick);
        dateTimeInput.addEventListener('blur', handleDateTimeBlur);
        dateTimeInput.addEventListener('keydown', (e) => {
             if (e.key === 'Enter') { handleDateTimeBlur(); dateTimeInput.blur(); }
        });
        document.addEventListener('keydown', handleKeyDown);
        window.addEventListener('resize', debounce(handleResize, 200));
    }

    function handleDateTimeBlur() {
         if (isRestoring) return;
         try {
            const inputVal = dateTimeInput.value;
             if (!inputVal) {
                 if(selectedIndex !== -1) updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
                 return;
             };
            const inputDate = new Date(inputVal);
             if (isNaN(inputDate.getTime())) {
                 if(selectedIndex !== -1) updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
                 return;
             };
             const closestBackup = findClosestNodeByTime(inputDate.getTime());
             if (closestBackup) {
                 selectNodeByIndex(closestBackup.sortedIndex);
             } else if (selectedIndex !== -1) {
                  updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
             }
        } catch (e) {
            console.error("Error parsing date on blur:", e);
            if(selectedIndex !== -1) updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
        }
    }

    function handleResize() {
        if (!backupData || backupData.length === 0 || isRestoring) return;
         timelineRect = timelineContainer.getBoundingClientRect();
         const padding = timelineRect.width / 2;
         trackWidth = (backupData.length - 1) * nodeSpacing + padding * 2;
         timelineTrack.style.width = `${trackWidth}px`;
         nodes.forEach((node, index) => {
             const positionPx = padding + index * nodeSpacing;
             node.element.style.left = `${positionPx}px`;
         });
        if (selectedIndex !== -1) {
            const targetTranslateX = calculateCenteringTranslateX(selectedIndex);
            setTimelinePosition(targetTranslateX, true);
        }
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => { clearTimeout(timeout); func(...args); };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    initializeTimeline();
});
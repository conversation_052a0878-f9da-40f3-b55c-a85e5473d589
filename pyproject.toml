[tool.poetry]
name = "vero"
version = "0.1.0"
package-mode = false
description = "Vero application with database backup features"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10,<3.14"
fastapi = "0.115.9"
uvicorn = {extras = ["standard"], version = "^0.34.0"}
motor = "^3.3.1"
python-dotenv = "^1.0.0"
pymongo = "^4.5.0"

# Optional dependencies for database backups
aioschedule = {version = "^0.5.2", optional = true}
rich = {version = "^13.5.2", optional = true}
nest-asyncio = "^1.6.0"
langgraph-cli = {extras = ["inmem"], version = "^0.1.80"}
starlette = "0.44.0"
langgraph = "^0.3.20"
langgraph-prebuilt = "^0.1.7"
langchain-nvidia-ai-endpoints = "^0.3.9"
langchain-google-genai = "^2.1.2"
alpaca-py = "^0.39.1"
chromadb = "^1.0.4"
langchain-community = "^0.3.21"
langchain-openai = "^0.3.12"
tavily-python = "^0.5.4"
beautifulsoup4 = "^4.13.3"
requests = "^2.32.3"
langchain-anthropic = "^0.3.10"
langchain-deepseek = "^0.1.3"
trustcall = {path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/trustcall/dist/trustcall-0.0.38.tar.gz"}
langchain-cerebras = "^0.5.0"
langchain-mistralai = "^0.2.10"
torch = "^2.6.0"
tenacity = "^9.1.2"
google-genai = "^1.11.0"
wikipedia = "^1.4.0"
duckduckgo-search = "^8.0.1"
langgraph-supervisor = "^0.0.17"
protobuf = "^5.0.0"
nltk = "^3.9.1"
letta-client = "^0.1.124"
matplotlib = "^3.10.1"
letta = "^0.7.0"
langchain-mcp-adapters = "^0.0.9"
edgartools = "^4.0.0"
rizaio = "^0.11.0"
langchain-tavily = "^0.1.6"
hf-xet = "^1.1.0"
stable-baselines3 = "^2.6.0"
scikit-optimize = "^0.10.2"
mplfinance = "^0.12.10b0"
pandas-ta = "^0.3.14b0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"

[tool.poetry.extras]
backups = ["aioschedule", "rich"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

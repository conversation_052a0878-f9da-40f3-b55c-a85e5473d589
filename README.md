# Vero

Vero is an advanced AI-driven hedge fund management system designed to predict and capitalize on the next big opportunities in the stock market. Inspired by the analytical prowess of <PERSON>, Vero leverages cutting-edge machine learning algorithms and deep learning techniques to provide accurate, data-driven investment insights.

## Requirements

- Python >=3.10,<3.14 (supports Python 3.13)
- Poetry for dependency management

## Dependencies
- Python >=3.10,<3.14
- protobuf==4.25.6
- Other dependencies listed in pyproject.toml

## Environment Variables
- PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (required for protobuf compatibility)

## System Architecture

The system is organized into several key components:

### Core Components
- `ai/`: Advanced AI models and tools for market analysis
  - `ai/tools/`: Custom tool implementations for AI agents
    - `base_tool.py`: Async-first base class for all tools (inherits from LangChain's StructuredTool)
    - `TodayDateTool.py`: Async tool for getting current date and time information
    - `SimpleWorkflowTool.py`: Async tool for stock analysis workflow automation
    - `SimpleJoinWorkflowTool.py`: Async tool for joining financial data from multiple sources, analyzing relationships between different tickers or data types, and generating price forecasts. Creates structured markdown summaries of data samples with column types for optimal join decisions across multiple workflow steps. Uses LangChain PromptTemplate for flexible and maintainable LLM guidance. Preserves DatetimeIndex throughout model prediction and report generation to maintain proper time series forecasting including future dates. **Uses the correct main ticker in the report metadata.**
    - `ExtractWorkflowTool.py`: Async tool for extracting small samples (10-20 rows) of historical market data with technical indicators, including data windows around specific dates for event analysis
    - `registry.py`: Registry for managing and loading async tools dynamically
    - `edgar.py`: ReAct agent for SEC EDGAR filings. Simulates edgartools usage interactively, verifies logic with `execute_code` (pass code_list as a list of code lines), and outputs a minimal Python method for the user's business question.
  - `ai/graph/`
    ```
    software/ai/graph/
    ├── director_state.py           # Pydantic models (including FinalReport, AnalystState with defaults) and console utilities
    ├── delegate_graph.py           # Workflow: create_plan (using FIN_LANG), create_personas (using enhanced system prompt for AnalystList tool adherence), analyst_tool_agent (sends tasks to agents), collect_reports, feature_requestor
    ├── summarize_report_graph.py   # Workflow: get_structured_report (formats AnalysisReports as markdown with tools args), write_final_report (creates final markdown with plan, analysts), schedule_revisit (schedules forecast revisits for verification)
    ├── director_memory_graph.py    # Director memory retrieval subgraph
    ├── search_scope_graph.py       # Production-ready subgraph for search scope operations using Wikipedia and DuckDuckGo to find promising S&P 500 companies for swing trading
    ├── simple_agent_tool_graph.py  # Subgraph for agent tool operation. Includes supervisor_vp (Vice President of Data Analysis) managing data_forecaster -> embed_successful_tools -> retrieve_successful_tools -> synthesize_response flow. All nodes have been stripped of explicit validation, fallbacks (like default values or error handling returns), and potentially unnecessary hardcoding (like similarity/chunking thresholds, time decay logic) to rely purely on expected data flow and prompt adherence. **Ensures analysis_report_id is always a valid UUID4 in synthesize_response.**
    └── memzoom_agent.py          # Skeleton for a reusable agent pattern (MemZoomAgent) featuring multi-resolution chunking and advanced retrieval (recency, reranking, zoom-in) for effective large context handling.
    ```
  - `ai/llm/`: LLM connection and management
    - `llm_connect.py`: Handles fetching and caching LLM/Embedding connections (BaseChatModel, Embeddings) based on configurations stored in the database (LLMRepository). Provides methods to get models by ID (`get_llm_by_id`), by graph stage (`get_llm_by_graph_stage`), randomly from a list (`get_random_llm_from_list`), by director stage (`get_llm_for_stage`), or default (`get_llm`, `get_embedding`). Includes invocation and embedding helpers (`ainvoke`, `embed_documents`, `embed_query`).
    - `get_default_llm_sync`: Synchronous method to fetch default LLM config from the database, minimal query, no hardcoding or fallbacks.
  - `data/`: Data loaders management and sources
    - `base_data.py`: Base class for all data loaders with common functionality
    - `StockPrice10YrDaily.py`: Data loader for 10 years of daily stock price data using FMP API
    - `CompanyNewsLastYearLoader.py`: Data loader for company news sentiment and volume from the past year using Finnhub API and TextBlob, tracking both sentiment scores and daily news frequency
    - `StockMarket1M1D.py`: Data loader for 1-minute interval stock price data using yfinance API
    - `StockMarket24M1D.py`: Minimalistic data loader for 24 months of daily stock price data using direct Alpaca API requests with minimal error handling
    - `StockMarket30D1H.py`: Data loader for 30 days of hourly stock price data using direct Alpaca API requests
    - `SixMonthHistoricalDataLoader.py`: Data loader for 6 months of historical stock price data using yfinance API, with built-in data validation and retry mechanism to handle API failures
    - `Media3M.py`: Data loader for 3 months of news articles using direct Alpaca API requests. Provides comprehensive daily metrics including article counts, sentiment analysis (average/median), symbol statistics, headline/summary length analysis, image counts, publication time patterns, source variety, and tech giant company co-mentions. Features low variance thresholds optimized for news data characteristics.
    - `Media1Y.py`: Data loader for 1 year of news articles using direct Alpaca API requests. Provides the same comprehensive daily metrics as Media3M but with a longer timeframe.
- `features/`: Technical indicators and feature engineering modules
- `api/`: Backend API routes and server logic
  - `api/routers/`: API endpoints and route handlers
    - `pages.py`: Frontend page routes and rendering
    - `server_restart.py`: Backend endpoints for server restart and shutdown (minimalistic, SIGTERM for shutdown).
    - `add_data_loader.py`: Endpoints for data loader creation
    - `forecast_revisits.py`: API endpoints for tracking and validating stock price forecasts against actual market performance. Provides strict validation to prevent checking actuals before the scheduled date, with a "Check All Due" option for batch-processing forecasts that have reached their scheduled dates.
    - `research_agents.py`: Endpoints for managing AI research directors and deploying analysis tasks. Features a continuous auto-deploy mode that automatically starts new analysis tasks when previous ones complete, with persistent state maintained in MongoDB so tasks continue even if the browser is closed. Includes a task counter that displays total, completed, failed, and in-progress tasks with statistics that persist across browser sessions.
  - `database.py`: Database initialization and scheduled tasks manager

### Frontend Components
- `frontend/`: Frontend UI files and templates
  - `base.html`: Base template for all pages with responsive dark mode toggle, server restart button, and minimalistic shutdown button (now uses Material Design power icon SVG)
    - `restart.js`: JavaScript for handling server restart functionality - minimalistic implementation with tailwind styling
    - `shutdown.js`: JavaScript for handling server shutdown functionality - minimalistic implementation
  - `forecast-revisits/`: UI for tracking forecast accuracy and reinforcement learning data collection
    - `index.html`: Tabbed interface for viewing pending forecast revisits and historical accuracy metrics. Features a sidebar to filter forecasts by director, showing director stats and total reports. Validates scheduled dates and disables "Check Actuals" button until the scheduled date has been reached. Includes a "Check All Due" button to process all due forecasts at once.
    - `js/forecast-chart.js`: Interactive visualization for forecast performance using Highcharts. Displays a scatter chart with forecast horizon dates on x-axis and residual values on y-axis. Features include:
      - Zoomable chart (drag in plot area to zoom in on x-axis)
      - Pending forecasts shown as open circles (transparent fill with colored border)
      - Completed forecasts as filled circles with their actual residual percentage
      - Tooltips with detailed forecast information including the date
      - Color-coded by recommendation type (Buy/Sell/Hold)
      - Interactive ticker filters with proper styling that correctly updates the active filter background

### Database Collections
- `research_analysis`: Stores AI director analysis tasks and reports
- `forecast_revisits`: Tracks forecasted prices and scheduled revisits for comparison with actual market performance. Uses the data registry to fetch actual prices when validating forecasts. Handles case variations in column names (close/Close/CLOSE) with detailed logging for troubleshooting. Shows all pending revisits regardless of scheduled date to ensure visibility of future forecasts. Uses original dates from the structured report without timezone manipulation to ensure accurate forecast horizon dates. Includes validation to prevent checking actuals before the scheduled date (one day after forecast horizon) and automated daily checks for forecasts that are due.

## Recent Changes
- Enhanced RL training system with smart Bayesian hyperparameter optimization that produces a single optimized model
- Simplified the smart hyperparameters optimizer UI to always use Bayesian optimization by default
- Modified the backend to run multiple hyperparameter trials internally and return only the best model
- Ensured compatibility with recurring finetuning by tracking the best hyperparameters
- Added Letta feature - a powerful LLM engine integrated with Vero for managing and deploying LLM servers
- Replaced Tavily search with Wikipedia tool in `search_scope_graph.py` for finding S&P 500 companies
- Enhanced `search_scope_graph.py` to focus specifically on S&P 500 companies for swing trading with improved output format
- Simplified system_prompt in `synthesize_response` function to be more minimalistic and effective
- Fixed `TypeError: unhashable type: 'dict'` in `synthesize_response` return statement (corrected `{{}}` to `{}`).
- Enhanced prompts in `retrieve_successful_tools` to use `AnalysisReport` schema, improving relevance of both key term extraction (for vector search) and RAG answer generation (for synthesizing the final report).
- Enhanced `synthesize_response` system prompt in `simple_agent_tool_graph.py` with more explicit negative constraints (no markdown, no extra text), f-string syntax correction (using `{{}}`), and included the exact `AnalysisReport` model schema in the prompt to improve zero-shot structured output generation.
- Fixed `ValidationError` in `synthesize_response` node of `simple_agent_tool_graph.py` by ensuring `analysis_report_id` is always generated as a valid UUID4 if missing or invalid in the LLM response.
- Updated system prompt in `search_scope_graph.py` to explicitly instruct the agent to ignore the Wikipedia summary and extract the first company/ticker from the list/table content.
- Updated `SYSTEM_MESSAGE` in `software/ai/tools/edgar.py` to require the final output to be a Python method named `edgartool_repot`, using `execute_code` for intermediate logic verification.

## Edgar ReAct Agent Simulation
- The agent simulates SEC EDGAR queries using `edgartools`.
- All code is verified interactively using the `execute_code` tool.
- To use `execute_code`, pass a single string of Python code. All output should be handled via print statements in the code.
- The agent prints all output to simulate rich window displays.
- After simulation, the agent outputs a minimal Python method answering the business question.

# File Tree (core AI)
```ai/
  ├── graph/         # agent graphs, director state, retrieval
  ├── llm/           # LLM/embedding connection logic
      └── llm_connect.py  # LLM connection, sync+async db fetch
  └── tools/         # tool implementations


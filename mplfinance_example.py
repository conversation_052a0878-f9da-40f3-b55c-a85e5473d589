import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import mplfinance as mpf
import os
import uuid

# Create sample data
def create_sample_data():
    # Create date range
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')

    # Create OHLCV data
    np.random.seed(42)
    close = np.random.normal(100, 5, len(dates))
    close = np.cumsum(np.random.normal(0, 1, len(dates))) + 100

    # Create OHLC data
    high = close + np.random.uniform(0, 3, len(dates))
    low = close - np.random.uniform(0, 3, len(dates))
    open_price = low + np.random.uniform(0, high-low, len(dates))
    volume = np.random.uniform(1000000, 5000000, len(dates))

    # Create DataFrame
    df = pd.DataFrame({
        'Open': open_price,
        'High': high,
        'Low': low,
        'Close': close,
        'Volume': volume
    }, index=dates)

    return df

# Create chart
def create_chart(df):
    # Calculate moving average
    df['MA_50'] = df['Close'].rolling(window=20).mean()

    # Generate buy/sell signals
    df['Signal'] = 0
    df.loc[df['Close'] > df['MA_50'], 'Signal'] = 1
    df.loc[df['Close'] < df['MA_50'], 'Signal'] = -1

    # Find crossover points
    df['Position_Change'] = df['Signal'].diff()

    # Get buy/sell signal indices
    buy_signals = df[df['Position_Change'] == 1].index
    sell_signals = df[df['Position_Change'] == -1].index

    # Create addplot for MA
    ma_plot = mpf.make_addplot(df['MA_50'], color='orange', width=1.5)

    # Create empty list for additional plots
    addplots = [ma_plot]

    # Add buy signals if any exist
    if len(buy_signals) > 0:
        # Create a Series for buy signals
        buy_markers = pd.Series(np.nan, index=df.index)
        buy_markers.loc[buy_signals] = df.loc[buy_signals, 'High']
        # Add the plot
        addplots.append(mpf.make_addplot(buy_markers, type='scatter', marker='^', markersize=100, color='g'))

    # Add sell signals if any exist
    if len(sell_signals) > 0:
        # Create a Series for sell signals
        sell_markers = pd.Series(np.nan, index=df.index)
        sell_markers.loc[sell_signals] = df.loc[sell_signals, 'Low']
        # Add the plot
        addplots.append(mpf.make_addplot(sell_markers, type='scatter', marker='v', markersize=100, color='r'))

    # Calculate performance metrics
    initial_investment = 10000

    # Calculate strategy returns
    df['Returns'] = df['Close'].pct_change()
    df['Strategy_Returns'] = df['Signal'].shift(1) * df['Returns']

    # Calculate cumulative returns
    df['Cumulative_Returns'] = (1 + df['Returns']).cumprod()
    df['Strategy_Cumulative_Returns'] = (1 + df['Strategy_Returns']).cumprod()

    # Calculate metrics
    total_return = (df['Strategy_Cumulative_Returns'].iloc[-1] - 1) * 100
    buy_hold_return = (df['Cumulative_Returns'].iloc[-1] - 1) * 100

    # Create figure and save
    title = f'Moving Average Crossover Strategy'

    # Create a custom style
    mc = mpf.make_marketcolors(
        up='g', down='r',
        edge='inherit',
        wick='inherit',
        volume='inherit'
    )

    s = mpf.make_mpf_style(
        marketcolors=mc,
        gridstyle='-',
        y_on_right=False
    )

    # Plot the chart
    fig, axes = mpf.plot(
        df,
        type='candle',
        style=s,
        title=title,
        ylabel='Price ($)',
        volume=True,
        figsize=(12, 8),
        panel_ratios=(4, 1),
        addplot=addplots,
        returnfig=True
    )

    # Add text box with performance metrics
    ax = axes[0]
    textstr = f'Strategy Return: {total_return:.2f}%\nBuy & Hold Return: {buy_hold_return:.2f}%'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=props)

    # Save the chart
    unique_id = str(uuid.uuid4())[:8]
    file_path = os.path.join('software/library/images', f'mplfinance_example_{unique_id}.png')
    plt.savefig(file_path, dpi=100, bbox_inches='tight')
    plt.close(fig)

    return file_path

if __name__ == "__main__":
    # Create sample data
    df = create_sample_data()

    # Create chart
    file_path = create_chart(df)

    print(f"Chart saved to: {file_path}")

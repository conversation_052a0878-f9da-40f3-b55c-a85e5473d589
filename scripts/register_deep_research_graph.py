"""
<PERSON><PERSON><PERSON> to register the deep_research_graph in the database.
"""
import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from software.db.graph_repository import GraphRepository

async def register_graph():
    """Register the deep_research_graph in the database."""
    graph_repo = GraphRepository()

    # Define the graph data
    graph_data = {
        "name": "deep_research_graph",
        "description": "A graph for deep research on investment opportunities",
        "stages": [
            {"name": "critical_thinking", "description": "Analyze investment strategy"},
            {"name": "search_scope", "description": "Search for investment opportunities"},
            {"name": "coordinator", "description": "Coordinate research tasks"},
            {"name": "supervisor", "description": "Supervise research agents"},
            {"name": "process_results", "description": "Process research results"},
            {"name": "synthesize_findings", "description": "Synthesize findings into a recommendation"}
        ]
    }

    # Create or update the graph
    graph_id = await graph_repo.create_graph(graph_data)
    print(f"Registered deep_research_graph with ID: {graph_id}")

if __name__ == "__main__":
    asyncio.run(register_graph())

# Jesse Algorithmic Trading Framework

Jesse is a Python-based open-source framework for developing, backtesting, and executing algorithmic trading strategies. This document outlines the key components, methods, and options available in the Jesse framework to help you implement your own backtesting system.

## Table of Contents

1. [Strategy Structure](#strategy-structure)
2. [Entry and Exit Methods](#entry-and-exit-methods)
3. [Position Management](#position-management)
4. [Risk Management](#risk-management)
5. [Technical Indicators](#technical-indicators)
6. [Backtesting](#backtesting)
7. [Optimization](#optimization)
8. [Utilities](#utilities)

## Strategy Structure

### Basic Strategy Class

Every trading strategy in Jesse inherits from the `Strategy` base class:

```python
from jesse.strategies import Strategy
import jesse.indicators as ta
from jesse import utils

class MyStrategy(Strategy):
    def should_long(self):
        return False

    def should_short(self):
        return False

    def should_cancel_entry(self):
        return False

    def go_long(self):
        pass

    def go_short(self):
        pass
        
    def update_position(self):
        pass
        
    def filters(self):
        return []
```

### Required Methods

- `should_long()`: Returns a boolean indicating whether to enter a long position
- `should_short()`: Returns a boolean indicating whether to enter a short position
- `should_cancel_entry()`: Returns a boolean indicating whether to cancel a pending entry order
- `go_long()`: Defines entry, stop-loss, and take-profit for long positions
- `go_short()`: Defines entry, stop-loss, and take-profit for short positions

### Optional Methods

- `update_position()`: Called on each new candle when a position is open
- `filters()`: Returns a list of filter methods to validate trade entries
- `on_close_position(order)`: Called when a position is closed
- `on_reduced_position(order)`: Called when a position is partially closed
- `watch_list()`: Returns a list of values to monitor during live trading
- `hyperparameters()`: Defines parameters for optimization
- `dna()`: Returns a DNA string for optimization

## Entry and Exit Methods

### Long Position Entry

```python
def go_long(self):
    qty = 1
    entry_price = self.price
    stop_loss_price = self.low - 10
    take_profit_price = self.high + 10
    
    self.buy = qty, entry_price
    self.stop_loss = qty, stop_loss_price
    self.take_profit = qty, take_profit_price
```

### Short Position Entry

```python
def go_short(self):
    qty = 1
    entry_price = self.price
    stop_loss_price = self.high + 10
    take_profit_price = self.low - 10
    
    self.sell = qty, entry_price
    self.stop_loss = qty, stop_loss_price
    self.take_profit = qty, take_profit_price
```

### Multiple Entry Points

```python
def go_long(self):
    qty = 1
    
    # open position at multiple price points
    self.buy = [
        (qty/2, 120),
        (qty/2, 140)
    ]
    self.stop_loss = qty, 100
    self.take_profit = qty, 160
```

### Multiple Take-Profit Points

```python
def go_long(self):
    qty = 1
    
    self.buy = qty, 100
    self.stop_loss = qty, 80
    
    # take-profit at multiple price points
    self.take_profit = [
        (qty/2, 120),
        (qty/2, 140)
    ]
```

### Position Liquidation

```python
def update_position(self):
    if self.is_long and ta.rsi(self.candles) == 100:
        self.liquidate()
```

## Position Management

### Accessing Position Properties

```python
# Current position
position = self.position
entry_price = self.position.entry_price
qty = self.position.qty
pnl = self.position.pnl
pnl_percentage = self.position.pnl_percentage

# Check position type
if self.is_long:
    # do something for long positions
elif self.is_short:
    # do something for short positions
```

### Updating Stop-Loss and Take-Profit

```python
def update_position(self):
    # Move stop-loss to breakeven when in 10% profit
    if self.position.pnl_percentage >= 10:
        self.stop_loss = self.position.qty, self.position.entry_price
```

### Trailing Stop-Loss

```python
def update_position(self):
    # Update trailing stop-loss only if in profit
    if self.position.pnl > 0:
        if self.is_long:
            self.stop_loss = self.position.qty, self.price - self.atr * 2
        else:
            self.stop_loss = self.position.qty, self.price + self.atr * 2
```

### Increasing Position Size

```python
def update_position(self):
    # Add to position if certain conditions are met
    if self.is_long and self.position.pnl_percentage > 5 and ta.rsi(self.candles) < 30:
        # Double the size of the already open position
        self.buy = self.position.qty, self.price
```

### Multi-Symbol Position Management

```python
# Assuming multiple trading routes
btc_position = self.all_positions['BTC-USDT']
eth_position = self.all_positions['ETH-USDT']
```

## Risk Management

### Position Sizing with Risk Percentage

```python
def position_size(self, entry, stop):
    # Risk 10% of balance per trade
    risk_qty = utils.risk_to_qty(self.balance, 10, entry, stop, fee_rate=self.fee_rate)
    
    # Never risk more than 25% of capital
    max_qty = utils.size_to_qty(0.25 * self.balance, entry, precision=6, fee_rate=self.fee_rate)
    
    qty = min(risk_qty, max_qty)
    return qty
```

### Kelly Criterion for Position Sizing

```python
def kelly_qty(self, entry, stop):
    if not self.metrics or self.metrics['total'] < 20:
        win_rate = 0.46
        avg_win_ratio = 1.1
        avg_loss_ratio = 0.5
    else:
        win_rate = self.metrics['win_rate']
        avg_win_ratio = self.metrics['avg_win_percentage'] / 100
        avg_loss_ratio = self.metrics['avg_loss_percentage'] / 100
        
    kc = utils.kelly_criterion(win_rate, avg_win_ratio, avg_loss_ratio) * 100
    
    if not kc or kc <= 0:
        raise ValueError("Bad Kelly criterion.")
        
    risk_qty = utils.risk_to_qty(self.available_margin, kc, entry, stop, self.fee_rate)
    
    # Never risk more than 25%
    max_qty = utils.size_to_qty(0.25 * self.available_margin, entry, precision=6, fee_rate=self.fee_rate)
    qty = min(risk_qty, max_qty)
    
    return qty
```

### Limiting Stop-Loss Based on Risk

```python
# Adjust stop-loss to limit risk
adjusted_stop = utils.limit_stop_loss(entry_price, stop_price, trade_type, max_allowed_risk_percentage)
```

## Technical Indicators

Jesse provides a comprehensive set of technical indicators through the `ta` module:

### Trend Indicators

```python
# Moving Averages
sma = ta.sma(self.candles, period=20)
ema = ta.ema(self.candles, period=20)
wma = ta.wma(self.candles, period=20)
hma = ta.hma(self.candles, period=20)
kama = ta.kama(self.candles, period=30, fast_length=2, slow_length=30)
mama = ta.mama(self.candles, fastlimit=0.5, slowlimit=0.05)
vwma = ta.vwma(self.candles, period=20)
alma = ta.alma(self.candles, period=9, sigma=6.0, distribution_offset=0.85)
jma = ta.jma(self.candles, period=7, phase=50, power=2)

# Bollinger Bands
bb = ta.bollinger_bands(self.candles, period=20, devup=2, devdn=2)
bb_width = ta.bollinger_bands_width(self.candles, period=20, mult=2)

# Ichimoku Cloud
ichimoku = ta.ichimoku_cloud(self.candles, conversion_line_period=9, base_line_period=26, lagging_line_period=52, displacement=26)

# SuperTrend
supertrend = ta.supertrend(self.candles, period=10, factor=3)
```

### Momentum Indicators

```python
# RSI and Stochastic
rsi = ta.rsi(self.candles, period=14)
stoch = ta.stoch(self.candles, fastk_period=14, slowk_period=3, slowk_matype=0, slowd_period=3, slowd_matype=0)
stochf = ta.stochf(self.candles, fastk_period=5, fastd_period=3, fastd_matype=0)
srsi = ta.srsi(self.candles, period=14)

# MACD
macd = ta.macd(self.candles, fast_period=12, slow_period=26, signal_period=9)

# Other Momentum Indicators
roc = ta.roc(self.candles, period=10)
willr = ta.willr(self.candles, period=14)
ift_rsi = ta.ift_rsi(self.candles, rsi_period=5, wma_period=9)
```

### Volatility Indicators

```python
# ATR
atr = ta.atr(self.candles, period=14)

# True Range
trange = ta.trange(self.candles)

# Chande Kroll Stop
cksp = ta.cksp(self.candles, p=10, x=1.0, q=9)

# Chandelier Exits
chande = ta.chande(self.candles, period=22, mult=3.0, direction="long")
```

### Volume Indicators

```python
# Accumulation/Distribution
ad = ta.ad(self.candles)

# On-Balance Volume
obv = ta.obv(self.candles)

# Money Flow Index
mfi = ta.mfi(self.candles, period=14)

# Volume Weighted Average Price
vwap = ta.vwap(self.candles)
```

## Backtesting

### Basic Backtest Execution

```python
import jesse.helpers as jh
from jesse.strategies import Strategy
from jesse import utils
from jesse.research import backtest, candles_from_close_prices

# Generate fake candles
prices = [10, 11, 12, 12, 11, 13, 14, 12, 11, 15]
fake_candles = candles_from_close_prices(prices)

# Define strategy
class ResearchStrategy(Strategy):
    def should_long(self):
        return True

    def should_short(self):
        return False

    def should_cancel_entry(self):
        return True

    def go_long(self):
        entry_price = self.price
        qty = utils.size_to_qty(self.balance * 0.5, entry_price)
        self.buy = qty, entry_price

    def go_short(self):
        pass

# Prepare inputs
exchange_name = 'Fake Exchange'
symbol = 'BTC-USDT'
timeframe = '4h'
config = {
    'starting_balance': 10_000,
    'fee': 0,
    'type': 'futures',
    'futures_leverage': 2,
    'futures_leverage_mode': 'cross',
    'exchange': exchange_name,
    'warm_up_candles': 0
}
routes = [
    {'exchange': exchange_name, 'strategy': ResearchStrategy, 'symbol': symbol, 'timeframe': timeframe}
]
extra_routes = []
candles = {
    jh.key(exchange_name, symbol): {
        'exchange': exchange_name,
        'symbol': symbol,
        'candles': fake_candles,
    },
}

# Execute backtest
result = backtest(
    config,
    routes,
    extra_routes,
    candles,
    generate_charts=True
)

# Access results
metrics = result['metrics']
charts = result['charts']
logs = result['logs']
```

### Production-Ready Backtest Function

```python
def execute_strategy(
        strategy_name: str,
        exchange_name: str,
        symbol: str,
        timeframe: str,
        config: dict,
        start_date_str: str,
        finish_date_str: str
):
    warmup_candles, trading_candles = get_candles(
        exchange_name, symbol, timeframe, jh.date_to_timestamp(start_date_str), jh.date_to_timestamp(finish_date_str),
        config['warm_up_candles'], caching=True, is_for_jesse=True
    )

    routes = [
        {'exchange': exchange_name, 'strategy': strategy_name, 'symbol': symbol, 'timeframe': timeframe}
    ]

    trading_candles = {
        jh.key(exchange_name, symbol): {
            'exchange': exchange_name,
            'symbol': symbol,
            'candles': trading_candles,
        },
    }
    warmup_candles = {
        jh.key(exchange_name, symbol): {
            'exchange': exchange_name,
            'symbol': symbol,
            'candles': warmup_candles,
        },
    }

    # Execute backtest
    result = backtest(
        config,
        routes,
        [],
        candles=trading_candles,
        warmup_candles=warmup_candles,
        generate_charts=True,
        generate_equity_curve=True,
        generate_csv=True,
        generate_json=True,
        generate_logs=True,
        fast_mode=True
    )

    # Print result as a JSON string
    print(json.dumps(result, ignore_nan=True, cls=NpEncoder))
```

## Optimization

### Defining Hyperparameters

```python
def hyperparameters(self):
    return [
        {'name': 'slow_sma_period', 'type': int, 'min': 150, 'max': 210, 'default': 200},
        {'name': 'fast_sma_period', 'type': int, 'min': 20, 'max': 100, 'default': 50},
    ]
```

### Using DNA for Optimization

```python
def dna(self):
    return 't4'
```

## Utilities

### Accessing Candle Data

```python
# Current candle data
timestamp = self.current_candle[0]
open_price = self.current_candle[1]
close_price = self.current_candle[2]
high_price = self.current_candle[3]
low_price = self.current_candle[4]
volume = self.current_candle[5]

# Shorthand properties
open_price = self.open
close_price = self.close
high_price = self.high
low_price = self.low
volume = self.volume
price = self.price  # Same as close_price
```

### Accessing Candles from Different Timeframes

```python
@property
def big_trend(self):
    """
    Uses the SRSI indicator to determine the bigger trend of the market.
    The trading timeframe is "4h" so we use "1D" timeframe as the anchor timeframe.
    """
    k, d = ta.srsi(self.get_candles(self.exchange, self.symbol, '1D'))

    if k > d:
        return 1
    elif k < d:
        return -1
    else:
        return 0
```

### Detecting Crosses

```python
# Detect when price crosses above a moving average
crossed_above = utils.crossed(self.candles[:, 2], self.sma, 'above')

# Detect when price crosses below a moving average
crossed_below = utils.crossed(self.candles[:, 2], self.sma, 'below')

# Alternative implementation for detecting crosses
@property
def long_cross(self):
    return self.price > self.bb.middleband[-1] and self.candles[:, 2][-2] <= self.bb.middleband[-2]
```

### Debugging

```python
def update_position(self):
    self.log(f'pnl_percentage: {self.position.pnl_percentage}')

    if self.position.pnl_percentage > 2:
        self.log('if statement is True, liquidate is called')
        self.liquidate()
```

### Adding Chart Annotations

```python
def after(self) -> None:
    self.add_line_to_candle_chart('supertrend', ta.supertrend(self.candles).trend)
    self.add_line_to_candle_chart('ema5', ta.ema(self.candles, 50))
    # Resistance and support lines
    self.add_horizontal_line_to_candle_chart('resistance', 18266, 'red')
    self.add_horizontal_line_to_candle_chart('support', 17756, 'green')
```

import os
import logging
from typing import List, Dict, Any, TypedDict
import json
from dotenv import load_dotenv
from langgraph.graph import StateGraph, END
from langchain_groq import ChatGroq

from data_preparation import prepare_data, DataContext
from utils.constants import DEFAULT_TICKER
from utils.console_output import ConsoleOutput
from main import StockAnalysisApp
from models import MODEL_CLASSES
from utils.report_utils import calculate_model_score

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")

# Initialize LLM
llm = ChatGroq(model_name="llama-3.2-11b-vision-preview", temperature=0, api_key=GROQ_API_KEY)

class GraphState(TypedDict):
    ticker: str
    prepared_data: Dict[str, Dict[str, Any]]
    current_node: str
    llm_responses: List[Dict[str, Any]]
    best_model_results: Dict[str, Any]

class Researcher:
    @staticmethod
    def prepare_data(state: GraphState) -> GraphState:
        state['current_node'] = 'analyze_data'
        return state

    @staticmethod
    def analyze_data(state: GraphState) -> GraphState:
        llm_responses = []
        for loader_name, data in state['prepared_data'].items():
            context = data['context']
            prompt = Researcher.prepare_llm_prompt(context)

            try:
                response = llm.invoke(prompt)
                try:
                    parsed_response = json.loads(response.content)
                    llm_responses.append({
                        'loader_name': loader_name,
                        'prediction': parsed_response
                    })
                except json.JSONDecodeError:
                    ConsoleOutput.print_error(f"Error parsing JSON for {loader_name}")
                    llm_responses.append({
                        'loader_name': loader_name,
                        'prediction': {'error': 'Invalid JSON response'}
                    })

            except Exception as e:
                ConsoleOutput.print_error(f"Error invoking LLM for {loader_name}: {str(e)}")
                llm_responses.append({
                    'loader_name': loader_name,
                    'prediction': {'error': f"Unable to generate analysis: {str(e)}"}
                })

        state['llm_responses'] = llm_responses
        state['current_node'] = 'run_models'
        return state

    @staticmethod
    def run_models(state: GraphState) -> GraphState:
        best_results: Dict[str, Dict] = {}

        for response in state['llm_responses']:
            prediction = response['prediction']
            target = prediction.get('predict')
            horizon = prediction.get('forecast_horizon')
            loader_name = response['loader_name']

            if not target or not horizon:
                continue

            app = state['prepared_data'][loader_name]['app']

            ConsoleOutput.print_info(f"Running models for {loader_name} with target {target} and horizon {horizon}")

            # Debug: Print data info
            ConsoleOutput.print_debug(f"Data columns: {app.data.columns.tolist()}")
            ConsoleOutput.print_debug(f"Data shape: {app.data.shape}")
            ConsoleOutput.print_debug(f"Data types: {app.data.dtypes}")

            # Check if the target column exists
            if target not in app.data.columns:
                ConsoleOutput.print_error(f"Target column '{target}' not found in data. Available columns: {app.data.columns.tolist()}")
                continue

            loader_results: List[Dict] = []

            for model_class in MODEL_CLASSES:
                try:
                    result, metrics = app.run_model(model_class, predict=str(target), forecast_horizon=int(horizon))
                    score = calculate_model_score(result, target)['Composite Score']

                    model_result = {
                        'model': model_class.__name__,
                        'target': target,
                        'horizon': horizon,
                        'score': score,
                        'metrics': metrics
                    }
                    loader_results.append(model_result)

                    ConsoleOutput.print_success(f"{model_class.__name__} for {loader_name}: Score = {score:.2f}")
                except Exception as e:
                    ConsoleOutput.print_error(f"Error running {model_class.__name__} for {target}: {str(e)}")

            if loader_results:
                best_model = max(loader_results, key=lambda x: x['score'])
                best_results[loader_name] = best_model
                ConsoleOutput.print_info(f"Best model for {loader_name}: {best_model['model']} with score {best_model['score']:.2f}")

        state['best_model_results'] = best_results
        state['current_node'] = END
        return state

    @staticmethod
    def prepare_llm_prompt(context: DataContext) -> str:
        prompt = f"Analyze the following stock data from {context['loader_name']} and recommend a target variable for prediction and an appropriate time horizon. Provide your response in JSON format only, with no additional text or explanation.\n\n"
        prompt += "Columns:\n"
        for col in context['columns']:
            description = context['descriptions'].get(col, "No description available")
            sample_data = context['sample_data'].get(col, [])
            prompt += f"- {col}: {description}\n"
            prompt += f"  Sample values: {', '.join(map(str, sample_data[:5]))}\n"
        prompt += "\nBased on this data, provide your recommendations in the following JSON format:\n"
        prompt += "{\n"
        prompt += '  "predict": "name_of_target_variable",\n'
        prompt += '  "forecast_horizon": number_of_days\n'
        prompt += "}\n"
        prompt += "\nEnsure that 'predict' is a string matching one of the column names and 'forecast_horizon' is an integer number of days. Do not include any explanations or additional text outside of the JSON object."
        return prompt

class ResearchWorkflow:
    def __init__(self, ticker: str):
        self.ticker = ticker
        self.workflow = StateGraph(GraphState)

    def setup_workflow(self):
        self.workflow.add_node("prepare_data", Researcher.prepare_data)
        self.workflow.add_node("analyze_data", Researcher.analyze_data)
        self.workflow.add_node("run_models", Researcher.run_models)

        self.workflow.add_edge("prepare_data", "analyze_data")
        self.workflow.add_edge("analyze_data", "run_models")

        self.workflow.set_entry_point("prepare_data")

    def run(self):
        self.setup_workflow()
        graph = self.workflow.compile()

        # Use prepare_data function to load and process data
        prepared_data = prepare_data(self.ticker)

        initial_state: GraphState = {
            "ticker": self.ticker,
            "prepared_data": prepared_data,
            "current_node": "prepare_data",
            "llm_responses": [],
            "best_model_results": {}
        }

        final_output = None
        for output in graph.stream(initial_state):
            final_output = output

        return final_output

def main():
    ticker = DEFAULT_TICKER
    workflow = ResearchWorkflow(ticker)
    final_state = workflow.run()

    ConsoleOutput.print_success("Research completed. Best model results:")
    if final_state is not None:
        best_results = final_state.get('best_model_results', {})
        if best_results:
            for loader_name, result in best_results.items():
                print(f"\nBest model for {loader_name}:")
                print(json.dumps(result, indent=2, default=str))
        else:
            print("No valid model results found.")
    else:
        print("Workflow execution failed to produce a final state.")

if __name__ == "__main__":
    main()

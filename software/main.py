import logging
import os
import pandas as pd
import numpy as np
import traceback
from typing import Type, Tuple, Dict, Any
from data import *
from reporting.create_html_data import create_html_file, StockReportGenerator
from reporting.lightweight_report import LightweightReportGenerator
from utils.constants import DEFAULT_TICKER
from utils.feature_list import DEFAULT_FEATURES
from utils.console_output import ConsoleOutput
from utils.report_utils import calculate_model_score
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from models.base_model import BaseModel
from db.report_repository import ReportRepository
import json
from bson import json_util
from datetime import datetime  # Import datetime module

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class StockAnalysisApp:
    """
    Main application class for stock analysis.

    This class orchestrates the entire process of loading data,
    calculating features, running models, and generating reports.
    """

    def __init__(self, ticker=DEFAULT_TICKER):
        """
        Initialize the StockAnalysisApp.

        Args:
            ticker (str): The stock ticker to analyze. Defaults to DEFAULT_TICKER.
        """
        self.ticker = ticker
        self.data = None
        self.features = {}
        self.selected_features = []
        self.model_evaluation = None
        self.model_name = None  # Add model_name attribute

    def load_data(self, loader_class, sample_size=None):
        """
        Load data using the specified loader class.

        Args:
            loader_class: The class of the data loader to use.
        """
        loader = loader_class(self.ticker)
        self.data = loader.load_historical_data()

        if sample_size:
            self.data = self.data.head(sample_size)

        self.data_loader_name = loader_class.__name__

    def add_features(self, feature_names):
        """
        Add and calculate specified features, handling incompatibilities silently.

        This method attempts to add each specified feature to the data,
        skipping incompatible features without raising errors or printing messages.

        Args:
            feature_names (list): List of feature names to add.

        Raises:
            ValueError: If data is not loaded before adding features.
        """
        if self.data is None:
            raise ValueError("Data not loaded. Call load_data() first.")

        # Get essential columns from the data based on columns present
        # Focus on data integrity and only drop rows that are truly problematic
        essential_cols_present = list(self.data.select_dtypes(include=['number']).columns)

        for name in feature_names:
            if name in FEATURE_CLASSES:
                feature = FEATURE_CLASSES[name]()
                try:
                    result = feature.calculate(self.data)
                    if isinstance(result, pd.DataFrame):
                        self.data = pd.concat([self.data, result], axis=1)
                    elif isinstance(result, pd.Series):
                        self.data[name] = result
                    elif isinstance(result, np.ndarray):
                        if result.ndim == 1:
                            self.data[name] = result
                        else:
                            for i in range(result.shape[1]):
                                self.data[f"{name}_{i}"] = result[:, i]
                    self.features[name] = feature
                except Exception:
                    pass

        if not isinstance(self.data.index, pd.DatetimeIndex):
            try:
                self.data.index = pd.to_datetime(self.data.index)
            except Exception:
                pass

        # Only drop rows where data is completely unusable
        initial_rows = len(self.data)
        
        # Check if index has NaT values and fix them
        if pd.isna(self.data.index).any():
            self.data = self.data[~pd.isna(self.data.index)]
            index_rows_dropped = initial_rows - len(self.data)
            if index_rows_dropped > 0:
                ConsoleOutput.print_warning(f"Dropped {index_rows_dropped} rows with NaT values in index.")
                initial_rows = len(self.data)
        
        # Count NaN values in each row before dropping
        rows_with_all_nans = self.data.isna().all(axis=1).sum()
        if rows_with_all_nans > 0:
            self.data = self.data[~self.data.isna().all(axis=1)]
            ConsoleOutput.print_warning(f"Removed {rows_with_all_nans} rows with all NaN values.")
        
        # Count rows with ANY NaN values for information purposes only
        rows_with_some_nans = self.data.isna().any(axis=1).sum()
        if rows_with_some_nans > 0:
            ConsoleOutput.print_info(f"{rows_with_some_nans} rows contain some NaN values but are kept in the dataset.")

    def rerun_features(self, feature_names):
        if self.data is None:
            raise ValueError("Data not loaded. Call load_data() first.")

        # Get essential columns from the data based on columns present
        # Focus on data integrity and only drop rows that are truly problematic
        essential_cols_present = list(self.data.select_dtypes(include=['number']).columns)

        self.features = {}
        for name in feature_names:
            if name in FEATURE_CLASSES:
                feature = FEATURE_CLASSES[name]()
                try:
                    result = feature.calculate(self.data)
                    if isinstance(result, pd.DataFrame):
                        self.data = pd.concat([self.data, result], axis=1)
                    elif isinstance(result, pd.Series):
                        self.data[name] = result
                    elif isinstance(result, np.ndarray):
                        if result.ndim == 1:
                            self.data[name] = result
                        else:
                            for i in range(result.shape[1]):
                                self.data[f"{name}_{i}"] = result[:, i]
                    self.features[name] = feature
                except Exception:
                    pass

        if not isinstance(self.data.index, pd.DatetimeIndex):
            try:
                self.data.index = pd.to_datetime(self.data.index)
            except Exception:
                pass

        # Only drop rows where data is completely unusable
        initial_rows = len(self.data)
        
        # Check if index has NaT values and fix them
        if pd.isna(self.data.index).any():
            self.data = self.data[~pd.isna(self.data.index)]
            index_rows_dropped = initial_rows - len(self.data)
            if index_rows_dropped > 0:
                ConsoleOutput.print_warning(f"Dropped {index_rows_dropped} rows with NaT values in index.")
                initial_rows = len(self.data)
        
        # Count NaN values in each row before dropping
        rows_with_all_nans = self.data.isna().all(axis=1).sum()
        if rows_with_all_nans > 0:
            self.data = self.data[~self.data.isna().all(axis=1)]
            ConsoleOutput.print_warning(f"Removed {rows_with_all_nans} rows with all NaN values.")
        
        # Count rows with ANY NaN values for information purposes only
        rows_with_some_nans = self.data.isna().any(axis=1).sum()
        if rows_with_some_nans > 0:
            ConsoleOutput.print_info(f"{rows_with_some_nans} rows contain some NaN values but are kept in the dataset.")

    def run_model(self, model_class: Type[BaseModel], predict: str = 'Close', forecast_horizon: int = 1) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Run a specified model on the data.

        Args:
            model_class: The class of the model to run.
            predict (str): The target variable to predict.
            forecast_horizon (int): Number of future time steps to forecast.

        Returns:
            tuple: Predictions and evaluation metrics, or (None, None) if the model is incompatible.
        """
        if self.data is None:
            raise ValueError("Data not loaded. Call load_data() first.")

        if predict not in self.data.columns:
            logging.warning(f"Column '{predict}' not found in data.")
            return pd.DataFrame(), {}

        try:
            model = model_class()
            result, _ = model.run(self.data, predict, forecast_horizon)
            self.data = result

            # Calculate performance metrics using calculate_model_score
            self.model_evaluation = calculate_model_score(result, predict)
            self.model_name = model_class.__name__  # Set the model name
            self.prediction_column = predict  # Set the prediction column
            self.forecast_horizon = forecast_horizon  # Set the forecast horizon
            return result, self.model_evaluation
        except ValueError as e:
            # Model is incompatible, print error and skip
            ConsoleOutput.print_error(f"Model is incompatible: {str(e)}")
            return pd.DataFrame(), {}
        except Exception as e:
            # Log unexpected errors without printing to console
            logging.error(f"An unexpected error occurred while running {model_class.__name__}: {str(e)}")
            return pd.DataFrame(), {}

    def generate_report(self):
        """
        Generate an HTML report of the analysis.

        Returns:
            str: Path to the generated HTML report.
        """
        if self.data is None:
            raise ValueError("No data available. Load data and run analysis first.")

        if self.model_evaluation is None:
            ConsoleOutput.print_warning("No model evaluation available. Report may be incomplete.")

        report_generator = StockReportGenerator(self.data, self.ticker, self.model_evaluation)
        html_path = report_generator.generate_html_report()
        ConsoleOutput.print_success(f"Generated report for {self.ticker}: {html_path}")
        return html_path

    async def generate_report_new(self):
        """
        Generate a comprehensive report data without storing it.

        This method creates a report containing all the analysis data
        and model performance.

        Returns:
            dict: Report data dictionary
        """
        if self.data is None:
            raise ValueError("No data available. Load data and run analysis first.")

        try:
            logging.info(f"Generating report for {self.ticker}")
            logging.info(f"Data shape: {self.data.shape}")
            logging.info(f"Data columns: {self.data.columns.tolist()}")

            # Ensure prediction_column and forecast_horizon are set
            if not hasattr(self, 'prediction_column') or not hasattr(self, 'forecast_horizon'):
                raise AttributeError("Prediction column or forecast horizon not set. Ensure run_model is called before generating the report.")

            # Ensure the DataFrame's index is named 'Date'
            self.data.index.name = 'Date'

            # Prepare report data
            report_data = {
                'type': 'simple',
                'ticker': self.ticker,
                'workflow': {
                    'data_loader': getattr(self, 'data_loader_name', 'Unknown'),
                    'features': list(self.features.keys()),
                    'start_date': str(self.data.index.min()),
                    'end_date': str(self.data.index.max()),
                    'model': {
                        'name': self.model_name,
                        'prediction_column': self.prediction_column,
                        'forecast_horizon': self.forecast_horizon
                    }
                },
                'performance': self.model_evaluation or {},
                'data': {
                    'timeseries': self.data.reset_index().to_dict('records')
                }
            }

            ConsoleOutput.print_success(f"Generated report data for {self.ticker}")
            return report_data

        except Exception as e:
            error_msg = f"Error generating report: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)
            ConsoleOutput.print_error(error_msg)
            raise

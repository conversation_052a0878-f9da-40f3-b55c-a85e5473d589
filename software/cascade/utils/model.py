"""
GroqModel: Handles AI model interactions for Cascade.
"""

import os
import sys
import logging
from typing import Optional, Dict, Any
from groq import Groq
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))

class GroqModel:
    """Handles interactions with the Groq AI model for code generation."""
    
    def __init__(self):
        """Initialize the Groq model with API credentials."""
        try:
            api_key = os.getenv('GROQ_API_KEY')
            if not api_key:
                raise ValueError("GROQ_API_KEY not found in environment variables")
                
            self.client = Groq(api_key=api_key)
            self.model = "mixtral-8x7b-32768"  # Use the "llama-3.2-11b-vision-preview" model
            logger.debug(f"GroqModel initialized with model: {self.model}")
            
        except Exception as e:
            logger.error(f"Error initializing GroqModel: {str(e)}")
            raise

    def generate_code(self, prompt: str) -> str:
        """
        Generate code using the Groq API.
        
        Args:
            prompt (str): Prompt for code generation
            
        Returns:
            str: Generated code
        """
        try:
            # Create messages for the chat
            messages = [
                {"role": "system", "content": "You are a Python code generator. Generate only Python code without any comments, explanations, or markdown. Never add any text before or after the code."},
                {"role": "user", "content": prompt}
            ]
            
            print("\n=== Debug: Generating Code ===")
            print(f"Model: {self.model}")
            print(f"Prompt:\n{prompt}")
            
            # Generate code using Groq
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0,  # Lower temperature for more focused code generation
                max_tokens=2048,
                top_p=1.0,  # Use 1.0 for code generation to consider all tokens
                stop=None
            )
            
            # Extract the generated code
            generated_code = response.choices[0].message.content.strip()
            print("\n=== Debug: Generated Response ===")
            print(f"Raw output:\n{generated_code}")
            print("================================\n")
            
            return generated_code
            
        except Exception as e:
            logger.error(f"Error generating code: {str(e)}")
            raise

# Create a singleton instance
_model = None

def get_model() -> GroqModel:
    """Get or create the GroqModel instance."""
    global _model
    if _model is None:
        _model = GroqModel()
    return _model

def generate_code(prompt: str) -> str:
    """
    Generate code using the Groq model.
    
    This is the main interface for code generation. It handles getting the model
    instance and generating the code.
    
    Args:
        prompt (str): Prompt for code generation
        
    Returns:
        str: Generated code
    """
    model = get_model()
    return model.generate_code(prompt)

"""Terminal UI module for Cascade using Rich."""
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.text import Text
from rich.theme import Theme
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
import logging
from typing import Optional, List
import os

# Create custom theme
theme = Theme({
    "info": "cyan",
    "warning": "yellow",
    "error": "red",
    "success": "green",
    "file": "blue",
    "code": "magenta"
})

# Create console with theme
console = Console(theme=theme)

def setup_logging() -> None:
    """Configure Rich logging handler."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(message)s",
        handlers=[RichHandler(console=console, rich_tracebacks=True)]
    )

def display_welcome() -> None:
    """Display welcome message."""
    welcome_text = Text()
    welcome_text.append("Welcome to ", style="cyan")
    welcome_text.append("Cascade", style="magenta bold")
    welcome_text.append(" - AI Code Editor!", style="cyan")
    
    help_text = """
Type 'exit', 'q', or 'Q' to quit.

Example commands:
• Add error handling to the calculate_sum function
• Add logging to the MathOperations class methods
• Improve the documentation of multiply_numbers function
"""
    
    panel = Panel(
        Text.from_markup(help_text),
        title=welcome_text,
        border_style="cyan"
    )
    console.print(panel)

def display_active_file(file_path: Optional[str], line: Optional[int] = None) -> None:
    """Display currently active file information."""
    if not file_path:
        return
        
    file_name = os.path.basename(file_path)
    location = f"{file_name}:{line}" if line else file_name
    
    panel = Panel(
        f"📁 Working on: [file]{location}[/file]",
        border_style="blue",
        title="Active File",
        title_align="left"
    )
    console.print(panel)

def display_open_files(files: List[str]) -> None:
    """Display list of open files."""
    if not files:
        return
        
    file_list = "\n".join(f"• [file]{os.path.basename(f)}[/file]" for f in files)
    panel = Panel(
        file_list,
        border_style="blue",
        title="Open Files",
        title_align="left"
    )
    console.print(panel)

def display_code_item(code_item: Optional[str]) -> None:
    """Display active code item."""
    if not code_item:
        return
        
    panel = Panel(
        f"🔍 [code]{code_item}[/code]",
        border_style="magenta",
        title="Active Code Item",
        title_align="left"
    )
    console.print(panel)

def display_error(error: str) -> None:
    """Display error message."""
    panel = Panel(
        f"❌ {error}",
        border_style="red",
        title="Error",
        title_align="left"
    )
    console.print(panel)

def display_success(message: str) -> None:
    """Display success message."""
    panel = Panel(
        f"✅ {message}",
        border_style="green",
        title="Success",
        title_align="left"
    )
    console.print(panel)

def display_input_prompt() -> str:
    """Display input prompt and return user input."""
    return console.input("\n[cyan]Enter your request[/cyan]: ")

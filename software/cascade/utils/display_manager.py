"""
Display Manager: Handles all console output using Rich library
"""

from rich.console import Console
from rich.panel import Panel
from rich.layout import Layout
from rich.table import Table
from rich.syntax import Syntax
from rich.text import Text
from rich.padding import Padding
from rich.box import ROUNDED
from typing import Dict, Any, List, Optional

console = Console()

class DisplayManager:
    """Manages all console output using Rich."""
    
    def __init__(self):
        self.console = Console()
        
    def display_agent_planning(self, request: str, tasks: List[Dict[str, Any]]):
        """Display agent planning phase."""
        panel = Panel(
            Text.assemble(
                ("🤖 Agent Planner\n\n", "bold magenta"),
                ("Request: ", "bold"),
                (f"{request}\n\n", "cyan"),
                ("Breaking down into tasks:\n", "bold"),
                *[f"• {task['description']}\n" for task in tasks]
            ),
            title="Planning Phase",
            border_style="magenta",
            box=ROUNDED
        )
        self.console.print(panel)
        
    def display_tool_execution(self, task: Dict[str, Any], capability_name: str):
        """Display tool execution phase."""
        panel = Panel(
            Text.assemble(
                ("🔧 Tool Executor\n\n", "bold yellow"),
                ("Capability: ", "bold"),
                (f"{capability_name}\n", "yellow"),
                ("Task: ", "bold"),
                (f"{task['description']}\n", "yellow"),
                ("\nParameters:\n", "bold"),
                *[f"• {k}: {v}\n" for k, v in task['parameters'].items()]
            ),
            title="Tool Execution",
            border_style="yellow",
            box=ROUNDED
        )
        self.console.print(panel)
        
    def display_capability_output(self, capability_name: str, output: Any):
        """Display capability output."""
        panel = Panel(
            Text.assemble(
                ("✨ Capability Output\n\n", "bold green"),
                ("Capability: ", "bold"),
                (f"{capability_name}\n\n", "green"),
                str(output)
            ),
            title="Capability Result",
            border_style="green",
            box=ROUNDED
        )
        self.console.print(panel)
        
    def display_code(self, code: str, language: str = "python"):
        """Display code with syntax highlighting."""
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        self.console.print(syntax)
        
    def display_error(self, error_msg: str):
        """Display error message."""
        panel = Panel(
            Text.assemble(
                ("❌ Error\n\n", "bold red"),
                error_msg
            ),
            title="Error",
            border_style="red",
            box=ROUNDED
        )
        self.console.print(panel)
        
    def display_welcome(self):
        """Display welcome message and available capabilities."""
        from capabilities import CapabilityRegistry
        
        # Create capabilities table
        table = Table(title="Available Capabilities", box=ROUNDED, border_style="blue")
        table.add_column("Capability", style="cyan")
        table.add_column("Description", style="white")
        
        # Add registered capabilities
        for name, capability in CapabilityRegistry._capabilities.items():
            description = capability.__class__.__doc__.split('\n')[0] if capability.__class__.__doc__ else "No description"
            table.add_row(name, description)
        
        # Create welcome panel with header
        header = Text.assemble(
            ("Welcome to Cascade!\n\n", "bold magenta"),
            ("A focused Python code editor with AI capabilities.\n\n", "italic")
        )
        
        # Create layout with header and table
        layout = Layout()
        layout.split_column(
            Layout(header, size=5),
            Layout(table)
        )
        
        # Wrap in panel
        panel = Panel(
            layout,
            title="🚀 Cascade",
            border_style="blue",
            box=ROUNDED
        )
        self.console.print(panel)
        
    def display_completion(self):
        """Display completion message."""
        panel = Panel(
            Text.assemble(
                ("✅ Task Complete\n\n", "bold green"),
                "All tasks have been executed successfully!"
            ),
            title="Complete",
            border_style="green",
            box=ROUNDED
        )
        self.console.print(panel)

# Global display manager instance
display = DisplayManager()

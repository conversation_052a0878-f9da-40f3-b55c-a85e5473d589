"""
LangGraph workflow for Cascade code editor.
Implements workflow: agent_planner → agent_tools → capabilities
"""

from typing import List, Dict, Any, Union, Optional
from langchain.schema import HumanMessage, SystemMessage
from langgraph.graph import END, StateGraph
import logging
from utils.model import get_model
from agents.agent_planner import AgentPlanner, TaskState
from agents import agent_tools
from capabilities import CapabilityRegistry

logger = logging.getLogger(__name__)

def create_workflow(workspace_dir: str, model: Optional[Any] = None) -> StateGraph:
    """Create the workflow graph.
    
    Workflow:
    1. agent_planner: Plans tasks based on user request
    2. agent_tools: Executes tasks using capabilities
    3. Should continue? If yes, back to planner. If no, end.
    """
    if not model:
        model = get_model()
        
    # Initialize tools and get capability descriptions
    agent_tools.initialize_tools(workspace_dir)
    capabilities = CapabilityRegistry._capabilities
    capability_docs = []
    for name, cap in capabilities.items():
        doc = cap.__doc__ or ""
        capability_docs.append(f"{name}: {doc}")
    
    # Create workflow
    workflow = StateGraph(TaskState)
    
    # Create planner node
    def plan_node(state: TaskState) -> Union[TaskState, str]:
        """Plan next tasks or get next task."""
        planner = AgentPlanner(model=model, workspace_dir=workspace_dir)
        
        # Pass capability docstrings to planner
        state["capabilities"] = capabilities
        state["capability_docs"] = capability_docs
        
        # If no tasks and no current task, plan new tasks
        if not state["tasks"] and not state["current_task"]:
            state = planner.plan_tasks(state)
            
        # If no current task, get next task
        if not state["current_task"]:
            next_task = planner.get_next_task(state)
            if next_task:
                state["current_task"] = next_task
                return state
            else:
                return END
                
        return state
        
    # Create tools node
    def tools_node(state: TaskState) -> TaskState:
        """Execute current task using agent_tools and capabilities."""
        task = state["current_task"]
        if not task:
            return state
            
        try:
            # Get capability and tool info
            capability_name = task.get("capability")
            tool_name = task.get("tool")
            
            if not capability_name or not tool_name:
                raise ValueError("Task missing required capability or tool name")
                
            # Get capability
            capability = capabilities[capability_name]
            
            # Get tool from registry
            tools = CapabilityRegistry.get_tools()
            tool = next((t for t in tools if t.name == tool_name), None)
            if not tool:
                raise ValueError(f"Tool '{tool_name}' not found in registry")
            
            # Execute tool with task parameters
            result = tool.func(**task.get("parameters", {}))
            
            # Store result
            state["completed_tasks"].append({
                **task,
                "result": result,
                "status": "completed"
            })
            state["current_task"] = None
            
        except Exception as e:
            logger.error(f"Error executing task: {str(e)}")
            state["completed_tasks"].append({
                **task,
                "error": str(e),
                "status": "failed"
            })
            state["current_task"] = None
            
        return state
        
    # Create conditional node
    def should_continue(state: TaskState) -> bool:
        """Check if there are more tasks to execute."""
        return bool(state["tasks"]) or state["current_task"] is not None
        
    # Add nodes
    workflow.add_node("planner", plan_node)
    workflow.add_node("tools", tools_node)
    
    # Add edges
    workflow.add_edge("planner", "tools")
    workflow.add_conditional_edges(
        "tools",
        should_continue,
        {
            True: "planner",  # If more tasks, go back to planner
            False: END        # If no more tasks, end workflow
        }
    )
    
    # Set entry point
    workflow.set_entry_point("planner")
    
    # Compile the workflow
    workflow = workflow.compile()
    
    return workflow

def create_initial_state(request: str, workspace_dir: str) -> TaskState:
    """Create initial state for workflow."""
    planner = AgentPlanner(model=get_model(), workspace_dir=workspace_dir)
    return planner.create_initial_state(request)

print("Graph module loaded successfully!")

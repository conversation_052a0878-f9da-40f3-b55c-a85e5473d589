"""
Agent Planner: Core component that plans and executes tasks using capabilities.
"""

from typing import List, Dict, Any, TypedDict, Optional
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from capabilities import CapabilityRegistry
from utils.display_manager import display
import logging
import os

logger = logging.getLogger(__name__)

class FileContext(TypedDict):
    """Current file context."""
    active_file: Optional[str]  # Currently active file path
    active_line: Optional[int]  # Current cursor line
    workspace_dir: str  # Workspace directory

class TaskState(TypedDict):
    """State for task planning and execution."""
    request: str
    tasks: List[Dict[str, Any]]  # List of tasks to execute
    current_task: Optional[Dict[str, Any]]  # Currently executing task
    completed_tasks: List[Dict[str, Any]]  # Completed tasks
    capabilities: Dict[str, Any]  # Available capabilities
    context: FileContext  # File context

class AgentPlanner:
    """Plans and manages task execution for user requests."""
    
    def __init__(self, model, workspace_dir: str):
        """Initialize the agent planner."""
        self.model = model
        self.workspace_dir = workspace_dir
        self.capabilities = CapabilityRegistry.get_all_capabilities()
        
        # Create workspace if it doesn't exist
        if not os.path.exists(workspace_dir):
            os.makedirs(workspace_dir)
            
        logger.info(f"Agent Planner initialized with {len(self.capabilities)} capabilities")
    
    def create_initial_state(self, request: str, active_file: Optional[str] = None, 
                           active_line: Optional[int] = None) -> TaskState:
        """Create initial state for task planning."""
        context: FileContext = {
            "active_file": active_file,
            "active_line": active_line,
            "workspace_dir": self.workspace_dir
        }
        
        return {
            "request": request,
            "tasks": [],
            "current_task": None,
            "completed_tasks": [],
            "capabilities": self.capabilities,
            "context": context
        }
    
    def plan_tasks(self, state: TaskState) -> TaskState:
        """Break down user request into tasks based on available capabilities."""
        # Get capability descriptions
        capability_docs = []
        for name, cap in self.capabilities.items():
            doc = cap.__doc__ or ""
            capability_docs.append(f"{name}: {doc}")
        
        # Create planning prompt based on context
        context = state["context"]
        active_file = context["active_file"]
        
        prompt = f"""Given the user request: "{state['request']}"
        Current context:
        - Active file: {active_file if active_file else 'None'}
        - Workspace: {context['workspace_dir']}
        
        Available capabilities:
        {chr(10).join(capability_docs)}
        
        Break down the request into a sequence of tasks. Consider:
        1. If the request involves file creation, use file_creation capability with:
           - tool: create_file
           - parameters: {{"request": "user request", "workspace": "workspace directory"}}
        
        Return the tasks as a JSON list with:
        - capability: name of the capability to use (e.g., 'file_creation')
        - tool: name of the tool function to use (e.g., 'create_file')
        - parameters: dict of parameters for the tool
        - description: what this task accomplishes
        """
        
        # Get task breakdown from model
        response = self.model.generate_code(prompt)
        tasks = eval(response)  # Convert string to Python list
        
        # Update state with planned tasks
        state["tasks"] = tasks
        
        # Display planning phase
        display.display_agent_planning(state["request"], tasks)
        
        logger.info(f"Planned {len(tasks)} tasks for request: {state['request']}")
        return state
    
    def get_next_task(self, state: TaskState) -> Optional[Dict[str, Any]]:
        """Get the next task to execute."""
        if state["tasks"]:
            return state["tasks"].pop(0)
        return None
    
    def execute_task(self, state: TaskState) -> TaskState:
        """Execute the current task using the appropriate capability."""
        task = state["current_task"]
        if not task:
            return state
            
        try:
            capability_name = task.get("capability") or task.get("tool").split("_")[0]
            tool_name = task.get("tool")
            
            # Get the capability
            capability = self.capabilities[capability_name]
            
            # Add context to task parameters
            params = task.get("parameters", {})
            params["workspace_dir"] = state["context"]["workspace_dir"]
            if state["context"]["active_file"]:
                params["active_file"] = state["context"]["active_file"]
            
            # Display tool execution
            display.display_tool_execution(task, tool_name)
            
            # Execute capability with tool
            result = capability.execute(tool=tool_name, **params)
            
            # Update context if file was created or modified
            if result.get("file_path"):
                state["context"]["active_file"] = result["file_path"]
            
            # Display capability output
            display.display_capability_output(capability_name, result)
            
            # Move task to completed
            state["completed_tasks"].append({
                **task,
                "result": result,
                "status": "completed"
            })
            state["current_task"] = None
            
        except Exception as e:
            logger.error(f"Error executing task: {str(e)}")
            display.display_error(f"Error executing task: {str(e)}")
            state["completed_tasks"].append({
                **task,
                "error": str(e),
                "status": "failed"
            })
            state["current_task"] = None
            
        return state
    
    def should_continue(self, state: TaskState) -> bool:
        """Determine if there are more tasks to execute."""
        has_more = bool(state["tasks"]) or state["current_task"] is not None
        if not has_more:
            display.display_completion()
        return has_more

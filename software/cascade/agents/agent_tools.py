# agent_tools.py
import logging
from capabilities import CapabilityRegistry

logger = logging.getLogger(__name__)

def initialize_tools(workspace_dir: str):
    """Initialize tools with workspace directory."""
    # Store the workspace directory for tool usage
    global _workspace_dir
    _workspace_dir = workspace_dir
    logger.debug(f"Tools initialized with workspace directory: {workspace_dir}")

def get_available_tools():
    """Get the list of available tools."""
    return CapabilityRegistry.get_tools()

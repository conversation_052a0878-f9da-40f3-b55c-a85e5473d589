#!/usr/bin/env python3
"""
Cascade - A focused Python code editor with AI capabilities.

Provides an interactive environment for Python code manipulation 
using dynamically loaded capabilities.
"""

import os
import sys
import logging
import importlib
import re
from typing import Optional, Dict, Any

# Import capabilities registry
from capabilities import CapabilityRegistry

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.columns import Columns
from rich.layout import Layout
from rich.console import Group
from rich.style import Style

# Global configuration
DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'

def configure_logging(debug: bool = False):
    """
    Configure logging for the entire application.
    
    Args:
        debug (bool): Whether to enable debug logging
    """
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('cascade.log', mode='a')
        ]
    )
    logger = logging.getLogger(__name__)
    logger.debug("Logging configured with level: %s", "DEBUG" if debug else "INFO")

logger = logging.getLogger(__name__)
configure_logging(DEBUG)

# Global configuration
WORKSPACE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "workspace")

def initialize_workspace(workspace_dir: str) -> str:
    """
    Initialize the workspace directory. This is the central place for workspace creation.
    
    Args:
        workspace_dir (str): Path to workspace directory
        
    Returns:
        str: Path to initialized workspace directory
    """
    if not os.path.exists(workspace_dir):
        os.makedirs(workspace_dir)
        logger.debug(f"Created workspace directory: {workspace_dir}")
    return workspace_dir

# Create workspace directory if it doesn't exist
WORKSPACE_DIR = initialize_workspace(WORKSPACE_DIR)

console = Console()

from utils.display_manager import display

class Cascade:
    """
    Main class for the Cascade code editor.
    """
    
    def __init__(self):
        """
        Initialize Cascade with capabilities and workspace.
        """
        self.logger = logging.getLogger(__name__)
        self.logger.debug("Initializing Cascade")
        
        self.workspace = WORKSPACE_DIR
        self.logger.debug(f"Using workspace directory: {self.workspace}")
        
        self._display_welcome()
        self.logger.debug("Cascade initialized successfully")

    def _display_welcome(self):
        """Display the welcome panel with capabilities."""
        display.display_welcome(CapabilityRegistry.get_all_capabilities())
        self.logger.debug("Welcome panel displayed")

    def process_request(self, request: str) -> bool:
        """
        Process a user request by routing to the appropriate capability.
        
        Args:
            request (str): User's natural language request
        
        Returns:
            bool: True if request was processed successfully
        """
        self.logger.debug(f"Processing request: {request}")
        
        try:
            # Route to file creation capability
            if any(keyword in request.lower() for keyword in ["create", "new"]):
                self.logger.debug("Routing to file creation capability")
                capability = CapabilityRegistry.get_capability('file_creation')
                return capability.create_file(request, self.workspace)
            else:
                self.logger.warning(f"No matching capability found for request: {request}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error processing request: {str(e)}")
            print(f"Error: {str(e)}")
            return False

    def _extract_filename(self, request: str) -> Optional[str]:
        """
        Extract filename from user request. Returns None if no filename found.
        
        Args:
            request (str): User's request
        
        Returns:
            Optional[str]: Extracted filename or None
        """
        self.logger.debug(f"Extracting filename from request: {request}")
        
        # Look for .py files mentioned in the request
        matches = re.findall(r'\b\w+\.py\b', request)
        if matches:
            filename = matches[0]
            self.logger.debug(f"Found filename: {filename}")
            return filename
            
        self.logger.debug("No filename found in request")
        return None

    def interactive_loop(self):
        """Start an interactive session for processing requests."""
        self.logger.debug("Starting interactive loop")
        
        while True:
            try:
                request = console.input("\n[bold blue]Enter your Python code request:[/] ").strip()
                self.logger.debug(f"Received user input: {request}")
                
                if request.lower() in ['exit', 'quit']:
                    self.logger.debug("User requested to exit")
                    break
                    
                success = self.process_request(request)
                if success:
                    console.print("[green]Request processed successfully[/green]")
                else:
                    console.print("[yellow]Request could not be processed[/yellow]")
                    
            except KeyboardInterrupt:
                self.logger.debug("Received keyboard interrupt")
                break
            except Exception as e:
                self.logger.error(f"Error in interactive loop: {str(e)}")
                console.print(f"[red]An error occurred: {str(e)}[/red]")
                
        self.logger.debug("Exiting interactive loop")

def main():
    """
    Main entry point for Cascade code editor.
    """

    import os
    import sys
    from pathlib import Path
    from dotenv import load_dotenv
    from utils.display_manager import display
    from utils.model import get_model
    from agents.agent_planner import AgentPlanner
    from graph.graph import create_workflow

    # Load environment variables
    load_dotenv()

    try:
        # Initialize workspace
        workspace_dir = initialize_workspace(os.path.join(os.getcwd(), 'workspace'))
            
        # Initialize components
        model = get_model()
        planner = AgentPlanner(model, workspace_dir)
        workflow = create_workflow(workspace_dir, model)
        
        # Display welcome message
        display.display_welcome()
        
        # Main interaction loop
        while True:
            try:
                # Get user request
                request = input("\nEnter your request (or 'exit' to quit): ")
                if request.lower() in ['exit', 'quit']:
                    break
                    
                # Get current file context
                active_file = None  # In the future, we can get this from the IDE integration
                active_line = None  # In the future, we can get this from the IDE integration
                
                # Create initial state
                state = {
                    "request": request,
                    "tasks": [],
                    "current_task": None,
                    "completed_tasks": [],
                    "capabilities": CapabilityRegistry._capabilities,
                    "context": {
                        "active_file": active_file,
                        "active_line": active_line,
                        "workspace_dir": workspace_dir
                    }
                }
                
                # Run workflow
                final_state = workflow.invoke(state)
                
                # Display completion
                display.display_completion()
                
            except KeyboardInterrupt:
                print("\nOperation cancelled by user")
                continue
                
            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.error(f"Error processing request: {str(e)}")
                display.display_error(f"Error: {str(e)}")
                continue
                
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Unhandled exception: {str(e)}")
        display.display_error(f"Fatal error: {str(e)}")
        sys.exit(1)
        
if __name__ == "__main__":
    main()

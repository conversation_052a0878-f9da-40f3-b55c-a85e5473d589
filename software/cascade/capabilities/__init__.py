"""
Capabilities Registry Module

This module provides a centralized registry for dynamically managing and loading capabilities.
It automatically discovers and registers capabilities from the capabilities directory.
"""

import os
import sys
import importlib
import logging
import re
import inspect
from typing import Dict, Any, Type, List
from langchain.tools import StructuredTool

logger = logging.getLogger(__name__)

class CapabilityRegistry:
    """
    A centralized registry for managing and discovering capabilities.
    
    This class provides methods to:
    - Dynamically register capabilities
    - Retrieve registered capabilities
    - Create tools from capability methods
    """
    
    _capabilities: Dict[str, Any] = {}
    _tools: List[StructuredTool] = []
    
    @classmethod
    def register_capability(cls, name: str, capability: Any):
        """
        Register a capability in the registry and create tools from its methods.
        
        Args:
            name (str): Name of the capability
            capability (Any): Capability instance to register
        """
        cls._capabilities[name] = capability
        
        # Create tools from public methods
        for method_name, method in inspect.getmembers(capability, predicate=inspect.ismethod):
            if not method_name.startswith('_'):  # Only public methods
                tool = StructuredTool.from_function(
                    func=method,
                    name=method_name,
                    description=method.__doc__ or f"Tool for {method_name}"
                )
                cls._tools.append(tool)
                logger.info(f"Created tool from method: {method_name}")
        
        logger.info(f"Registered capability: {name}")
    
    @classmethod
    def get_capability(cls, name: str) -> Any:
        """
        Retrieve a registered capability.
        
        Args:
            name (str): Name of the capability to retrieve
        
        Returns:
            Any: The registered capability instance
        
        Raises:
            KeyError: If the capability is not found
        """
        if name not in cls._capabilities:
            raise KeyError(f"Capability '{name}' not found")
        return cls._capabilities[name]
    
    @classmethod
    def get_all_capabilities(cls) -> Dict[str, Any]:
        """
        Get all registered capabilities.
        
        Returns:
            Dict[str, Any]: Dictionary of all registered capabilities
        """
        return cls._capabilities.copy()
    
    @classmethod
    def get_tools(cls) -> List[StructuredTool]:
        """
        Get all tools created from capabilities.
        
        Returns:
            List[StructuredTool]: List of all available tools
        """
        return cls._tools.copy()

def _import_capabilities():
    """
    Automatically discover and import numbered capabilities.
    """
    capabilities_dir = os.path.dirname(__file__)
    sys.path.insert(0, os.path.dirname(capabilities_dir))
    
    # Find all capability modules in the directory
    capability_pattern = re.compile(r'capability\d+\.py$')
    capability_files = [f for f in os.listdir(capabilities_dir) 
                       if capability_pattern.match(f)]
    
    # Sort by capability number
    capability_files.sort(key=lambda f: int(re.search(r'\d+', f).group()))
    
    # Import each capability module
    for file_name in capability_files:
        module_name = os.path.splitext(file_name)[0]
        try:
            importlib.import_module(f'capabilities.{module_name}')
            logger.info(f"Successfully imported capability: {module_name}")
        except Exception as e:
            logger.error(f"Error importing capability {module_name}: {e}")

# Initialize capabilities on module import
_import_capabilities()

"""
Capability 1: File Creation
Handles the creation of new Python files with AI-generated content.

Example Usage:
    1. Create file with auto-generated name:
       "create a Calculator class that can add and multiply numbers"
       -> Creates Calculator.py
    
    2. Create file with specific name:
       "create hello.py that prints hello world"
       -> Creates hello.py
    
    3. Create complex class:
       "create a DataProcessor class that can load CSV files and compute statistics"
       -> Creates DataProcessor.py
"""

import os
import logging
import re
from utils.model import get_model
from typing import Dict, Any, Optional, Tuple

class FileCreationCapability:
    """A capability for creating new Python files with AI-generated content."""
    
    def __init__(self):
        """Initialize the File Creation Capability."""
        self.logger = logging.getLogger(__name__)
        self.logger.info("File Creation Capability initialized")
    
    def create_file(self, request: str, workspace: str) -> bool:
        """
        Create a new Python file with AI-generated content.
        
        Args:
            request (str): User's request (e.g., "create a Calculator class")
            workspace (str): Directory to create the file in
            
        Returns:
            bool: True if file was created successfully
            
        Example Usage:
            >>> capability = FileCreationCapability()
            >>> capability.create_file("create a Calculator class with add method", "./workspace")
            True  # Creates Calculator.py
            >>> capability.create_file("create test.py that prints hello", "./workspace")
            True  # Creates test.py
        """
        try:
            print("\n=== Debug: File Creation Process ===")
            
            # Extract filename if specified in request
            filename = self._extract_filename(request)
            print(f"Extracted filename: {filename}")
            
            # Generate content
            prompt = self._generate_prompt(request)
            print(f"\nGenerated prompt:\n{prompt}")
            
            content = get_model().generate_code(prompt)
            print(f"\nRaw AI output:\n{content}")
            
            # Clean up content and extract class name if needed
            content, extracted_class = self._parse_content(content)
            print(f"\nParsed content:\n{content}")
            print(f"Extracted class name: {extracted_class}")
            
            # Determine final filename
            final_filename = self._get_unique_filename(workspace, filename, extracted_class)
            print(f"\nFinal filename: {final_filename}")
            filepath = os.path.join(workspace, final_filename)
            
            # Validate content before writing
            if not content.strip():
                raise ValueError("Generated code is empty")
            
            if not any(keyword in content.lower() for keyword in ['class', 'def', 'import', '#', '"""', "print"]):
                print("\nWarning: Content validation - No common Python keywords found!")
                print(f"Content preview: {content[:200]}...")
            
            print(f"\nWriting to: {filepath}")
            print("=== End Debug ===\n")
            
            with open(filepath, 'w') as f:
                f.write(content)
                
            self.logger.info(f"Created new file: {final_filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating file: {str(e)}")
            print(f"\nError during file creation: {str(e)}")
            raise
    
    def _extract_filename(self, request: str) -> Optional[str]:
        """
        Extract filename from user request. Returns None if no filename found.
        
        Args:
            request (str): User's request
            
        Returns:
            Optional[str]: Extracted filename or None
            
        Examples:
            >>> cap = FileCreationCapability()
            >>> cap._extract_filename("create test.py with hello world")
            'test.py'
            >>> cap._extract_filename("create a Calculator class")
            None
        """
        # Look for .py files mentioned in the request
        matches = re.findall(r'\b\w+\.py\b', request.lower())
        if matches:
            return matches[0]
        return None
    
    def _parse_content(self, content: str) -> Tuple[str, Optional[str]]:
        """
        Parse and clean up the AI-generated content.
        
        Args:
            content (str): Raw content from AI
            
        Returns:
            Tuple[str, Optional[str]]: Cleaned content and extracted class name (if found)
        """
        # Remove any non-Python content
        lines = content.split('\n')
        python_lines = []
        in_python_block = True  # Assume we're in Python code unless proven otherwise
        
        for line in lines:
            # Skip markdown or non-Python content
            if '```' in line or 'Please replace' in line or 'Example:' in line:
                continue
            if line.strip() and in_python_block:
                python_lines.append(line)
        
        content = '\n'.join(python_lines)
        
        # Extract class name if present
        class_match = re.search(r'class\s+(\w+)', content)
        class_name = class_match.group(1) if class_match else None
        
        return content, class_name

    def _get_unique_filename(self, workspace: str, filename: Optional[str], class_name: Optional[str]) -> str:
        """
        Get a unique filename for the new file.
        
        Args:
            workspace (str): Directory to create file in
            filename (Optional[str]): User-provided filename or None
            class_name (Optional[str]): Extracted class name or None
            
        Returns:
            str: Unique filename with .py extension
            
        Examples:
            >>> cap = FileCreationCapability()
            >>> cap._get_unique_filename("./workspace", None, "Calculator")
            'Calculator.py'
            >>> cap._get_unique_filename("./workspace", "test.py", None)
            'test.py'  # or 'test_1.py' if test.py exists
        """
        if filename:
            base = os.path.splitext(filename)[0]
        elif class_name:
            base = class_name
        else:
            base = "script"
        
        # Ensure .py extension
        filename = f"{base}.py"
        
        # Make filename unique if it exists
        counter = 1
        while os.path.exists(os.path.join(workspace, filename)):
            filename = f"{base}_{counter}.py"
            counter += 1
        
        return filename
    
    def _generate_prompt(self, request: str) -> str:
        """
        Generate an appropriate prompt for the AI model.
        """
        # Clean up request by removing create/new keywords
        clean_request = re.sub(r'^(create|new)\s+', '', request.lower())
        clean_request = re.sub(r'\.py\b', '', clean_request)
        
        return f"""Create a Python script that {clean_request}.
                Include proper type hints, docstrings, error handling, and example usage.
                Output only valid Python code."""

    def _write_to_file(self, target_file: str, code_content: str, empty_file: bool = False) -> bool:
        """Write content to a new file."""
        try:
            os.makedirs(os.path.dirname(target_file), exist_ok=True)
            mode = 'w' if empty_file else 'x'  # 'x' fails if file exists
            with open(target_file, mode) as f:
                f.write(code_content)
            return True
        except FileExistsError:
            self.logger.error(f"File {target_file} already exists")
            return False
        except Exception as e:
            self.logger.error(f"Error writing to file {target_file}: {str(e)}")
            return False

# Initialize and register capability
capability = FileCreationCapability()
CapabilityRegistry = __import__('capabilities').CapabilityRegistry
CapabilityRegistry.register_capability('file_creation', capability)

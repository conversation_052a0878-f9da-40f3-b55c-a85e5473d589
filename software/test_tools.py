"""
Test script for verifying the file-based tool system.
This script loads tools from files and prints their details.
"""
import asyncio
import os
import importlib
import sys
from pathlib import Path

# Add the parent directory to path so we can import from the software package
sys.path.append(str(Path(__file__).parent.parent))

# Import registry without loading tools
from software.ai.tools import registry

from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

def test_load_tools():
    """Test loading tools from files synchronously"""
    console.print(Panel("Testing loading tools from files", title="[bold]Test Tools[/bold]"))
    
    # Explicitly load tools for testing only
    console.print("Loading tools for test...")
    registry.load_tools_sync()
    
    # Get all tools
    tools = registry.get_all_tools()
    
    # Display tools in a table
    table = Table(title="Available Tools")
    table.add_column("Name")
    table.add_column("Category")
    table.add_column("Description")
    table.add_column("Parameters")
    
    for name, tool in tools.items():
        param_count = len(tool.args_schema.schema().get("properties", {})) if hasattr(tool, "args_schema") else 0
        table.add_row(
            tool.name,
            getattr(tool, "category", "general"),
            tool.description[:50] + "..." if len(tool.description) > 50 else tool.description,
            str(param_count)
        )
    
    console.print(table)
    
    # Test LangChain tools conversion
    langchain_tools = registry.get_langchain_tools()
    console.print(f"Successfully converted {len(langchain_tools)} tools to LangChain format.")
    
    return len(tools)

async def test_async_tools():
    """Test async tools functionality"""
    # This is only for testing any async functionality if needed in the future
    return 0

def main():
    """Main test function"""
    try:
        # Run the synchronous test
        tool_count = test_load_tools()
        
        # Run the async test if needed
        # asyncio.run(test_async_tools())
        
        console.print(Panel(f"[green]✓ Successfully loaded {tool_count} tools![/green]", 
                     title="Test Success", border_style="green"))
    except Exception as e:
        console.print(Panel(f"[red]Error: {str(e)}[/red]", title="Test Failed", border_style="red"))
        import traceback
        console.print(traceback.format_exc())

if __name__ == "__main__":
    # Only run when directly executed, not when imported
    main() 
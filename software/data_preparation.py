from typing import List, Dict, Any, TypedDict
from data import DATA_LOADER_CLASSES
from features import FEATURE_CLASSES
from utils.constants import DEFAULT_TICKER
from utils.console_output import ConsoleOutput
from main import StockAnalysisApp
from utils.feature_list import DEFAULT_FEATURES

class DataContext(TypedDict):
    loader_name: str
    columns: List[str]
    descriptions: Dict[str, str]
    sample_data: Dict[str, List[Any]]

def prepare_data(ticker: str = DEFAULT_TICKER) -> Dict[str, Dict[str, Any]]:
    prepared_data = {}

    for loader_name, loader_class in DATA_LOADER_CLASSES.items():
        try:
            ConsoleOutput.print_info(f"Loading data with {loader_name}...")
            app = StockAnalysisApp(ticker)
            app.load_data(loader_class)

            if app.data is None or app.data.empty:
                ConsoleOutput.print_warning(f"No data loaded with {loader_name}. Skipping.")
                continue

            # Add compatible features
            compatible_features = get_compatible_features(app.data)
            app.add_features(compatible_features)

            valid_columns = get_valid_columns(app.data)

            if not valid_columns:
                ConsoleOutput.print_warning(f"No valid columns found in data from {loader_name}. Skipping.")
                continue

            context = DataContext(
                loader_name=loader_name,
                columns=valid_columns,
                descriptions={col: FEATURE_CLASSES[col]().get_description() if col in FEATURE_CLASSES else "" for col in valid_columns},
                sample_data={col: app.data[col].head(10).tolist() for col in valid_columns}
            )

            prepared_data[loader_name] = {
                'app': app,
                'context': context
            }

            ConsoleOutput.print_success(f"Successfully processed data from {loader_name}")
        except Exception as e:
            ConsoleOutput.print_error(f"Error processing {loader_name}: {str(e)}")
            continue

    return prepared_data

def get_valid_columns(data) -> List[str]:
    valid_columns = []
    for col in data.columns:
        if (data[col].dtype in ['int64', 'float64'] and
            data[col].notna().all() and
            data[col].nunique() > 1):
            valid_columns.append(col)
    return valid_columns

def get_compatible_features(data) -> List[str]:
    compatible_features = []
    for feature_name in DEFAULT_FEATURES:
        if feature_name in FEATURE_CLASSES:
            feature = FEATURE_CLASSES[feature_name]()
            try:
                feature.calculate(data)
                compatible_features.append(feature_name)
            except Exception:
                pass
    return compatible_features

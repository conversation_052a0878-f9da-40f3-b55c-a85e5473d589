import pandas as pd
import numpy as np
from .base_feature import Feature

class AverageTrueRange(Feature):
    """
    The Average True Range (ATR) is a technical analysis indicator that measures market volatility.

    ATR doesn't provide information about price direction, but rather the degree of price volatility.
    High ATR values indicate high volatility, while low ATR values indicate low volatility.
    It's often used to identify potential entry and exit points, as well as to set stop-loss levels.
    """

    def __init__(self, period=14):
        self.period = period

    def calculate(self, data):
        if not all(col in data.columns for col in ['High', 'Low', 'Close']):
            raise ValueError("Data must contain 'High', 'Low', and 'Close' columns")

        high = data['High']
        low = data['Low']
        close = data['Close']

        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.ewm(span=self.period, adjust=False).mean()

        # Normalize ATR
        normalized_atr = atr / close

        return pd.Series(normalized_atr, name=f'ATR_{self.period}')

    @classmethod
    def get_description(cls):
        return "The Average True Range (ATR) is a technical analysis indicator that measures market volatility."

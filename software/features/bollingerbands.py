from .base_feature import Feature
import pandas as pd

class BollingerBands(Feature):
    """Bollinger Bands indicator."""

    def __init__(self, window=20, num_std=2):
        self.window = window
        self.num_std = num_std

    def calculate(self, data):
        rolling_mean = data['Close'].rolling(window=self.window).mean()
        rolling_std = data['Close'].rolling(window=self.window).std()
        upper_band = rolling_mean + (rolling_std * self.num_std)
        lower_band = rolling_mean - (rolling_std * self.num_std)
        return pd.DataFrame({
            'BB_Middle': rolling_mean,
            'BB_Upper': upper_band,
            'BB_Lower': lower_band
        })

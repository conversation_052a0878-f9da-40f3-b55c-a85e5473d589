import pandas as pd
import numpy as np
from .base_feature import Feature

class StochasticOscillator(Feature):
    """
    The Stochastic Oscillator is a momentum indicator that shows the location of the close relative to the high-low range over a set number of periods.

    The indicator can range from 0 to 100. Values over 80 are considered overbought, while values under 20 are considered oversold.
    """

    def __init__(self, k_period=14, d_period=3):
        self.k_period = k_period
        self.d_period = d_period

    def calculate(self, data):
        if not all(col in data.columns for col in ['High', 'Low', 'Close']):
            raise ValueError("Data must contain 'High', 'Low', and 'Close' columns")

        low_min = data['Low'].rolling(window=self.k_period).min()
        high_max = data['High'].rolling(window=self.k_period).max()

        # Calculate %K
        k = 100 * ((data['Close'] - low_min) / (high_max - low_min))

        # Calculate %D
        d = k.rolling(window=self.d_period).mean()

        return pd.DataFrame({
            f'Stochastic_%K_{self.k_period}': k,
            f'Stochastic_%D_{self.k_period}_{self.d_period}': d
        })

    @classmethod
    def get_description(cls):
        return "The Stochastic Oscillator is a momentum indicator comparing a closing price of a security to its price range over a certain period of time."

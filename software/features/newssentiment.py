import pandas as pd
from .base_feature import Feature
from textblob import TextBlob
from typing import Union
import numpy as np

class NewsSentiment(Feature):
    """
    Calculates sentiment scores for news titles using TextBlob.
    """

    def calculate(self, data: pd.DataFrame) -> Union[pd.Series, pd.DataFrame]:
        if 'title' not in data.columns:
            raise ValueError("Input data must contain a 'title' column.")

        sentiment_scores = data['title'].apply(self._get_sentiment)
        return pd.Series(sentiment_scores, name='NewsSentiment', index=data.index)

    def _get_sentiment(self, title: str) -> float:
        try:
            if pd.isna(title):
                return 0.0  # Neutral sentiment for NaN titles
            return TextBlob(str(title)).sentiment.polarity
        except Exception as e:
            print(f"Error processing title: {title}. Error: {str(e)}")
            return 0.0  # Return neutral sentiment in case of any error

    @classmethod
    def get_description(cls) -> str:
        return "Calculates sentiment scores for news titles using TextBlob's sentiment analysis."

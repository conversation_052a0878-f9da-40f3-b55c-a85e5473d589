from .base_feature import Feature
import pandas as pd

class MACD(Feature):
    """
    Moving Average Convergence Divergence (MACD) indicator.

    MACD is a trend-following momentum indicator that shows the relationship
    between two moving averages of a security's price. It's calculated by
    subtracting the 26-period Exponential Moving Average (EMA) from the
    12-period EMA. A 9-day EMA of the MACD, called the "signal line", is then
    plotted on top of the MACD, functioning as a trigger for buy and sell signals.
    """

    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

    def calculate(self, data):
        close = data['Close']
        exp1 = close.ewm(span=self.fast_period, adjust=False).mean()
        exp2 = close.ewm(span=self.slow_period, adjust=False).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=self.signal_period, adjust=False).mean()
        histogram = macd - signal
        return pd.DataFrame({
            'MACD': macd,
            'MACD_Signal': signal,
            'MACD_Histogram': histogram
        })

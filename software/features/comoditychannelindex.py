import pandas as pd
import numpy as np
from .base_feature import Feature

class CommodityChannelIndex(Feature):
    """
    The Commodity Channel Index (CCI) is a versatile indicator that can be used to identify cyclical trends in securities.

    CCI measures the difference between the current price and its average price over a period of time,
    comparing it to the mean deviation. High positive readings indicate that prices are well above their average,
    which is a show of strength. Low negative readings indicate that prices are well below their average,
    which is a show of weakness.
    """

    def __init__(self, period=20):
        self.period = period

    def calculate(self, data):
        if not all(col in data.columns for col in ['High', 'Low', 'Close']):
            raise ValueError("Data must contain 'High', 'Low', and 'Close' columns")

        tp = (data['High'] + data['Low'] + data['Close']) / 3
        sma = tp.rolling(window=self.period).mean()
        mad = tp.rolling(window=self.period).apply(lambda x: np.abs(x - x.mean()).mean())

        cci = (tp - sma) / (0.015 * mad)

        return pd.Series(cci, name=f'CCI_{self.period}')

    @classmethod
    def get_description(cls):
        return "The Commodity Channel Index (CCI) is a versatile indicator used to identify cyclical trends in securities."

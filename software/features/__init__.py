import os
import importlib
from .base_feature import Feature

# Get all .py files in the current directory
feature_files = [f[:-3] for f in os.listdir(os.path.dirname(__file__))
                 if f.endswith('.py') and f not in ['__init__.py', 'base_feature.py']]

# Dynamically import all feature classes
FEATURE_CLASSES = {}
for feature in feature_files:
    module = importlib.import_module(f'.{feature}', package='features')
    for name, obj in module.__dict__.items():
        if isinstance(obj, type) and issubclass(obj, Feature) and obj is not Feature:
            FEATURE_CLASSES[name] = obj

# Make all features easily accessible
__all__ = list(FEATURE_CLASSES.keys())

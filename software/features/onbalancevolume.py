import pandas as pd
import numpy as np
from .base_feature import Feature

class OnBalanceVolume(Feature):
    """
    On-Balance Volume (OBV) is a momentum indicator that uses volume flow to predict changes in stock price.

    OBV is based on the assumption that volume precedes price movements. When volume increases sharply
    without a significant change in the stock's price, the price will eventually jump upward or fall downward.
    """

    def calculate(self, data):
        if not all(col in data.columns for col in ['Close', 'Volume']):
            raise ValueError("Data must contain 'Close' and 'Volume' columns")

        close = data['Close']
        volume = data['Volume']

        obv = pd.Series(index=data.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        # Normalize OBV to make it more comparable across different stocks
        normalized_obv = (obv - obv.mean()) / obv.std()

        return pd.Series(normalized_obv, name='OBV')

    @classmethod
    def get_description(cls):
        return "On-Balance Volume (OBV) is a momentum indicator that uses volume flow to predict changes in stock price."

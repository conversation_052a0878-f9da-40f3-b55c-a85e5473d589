import pandas as pd
import numpy as np
from .base_feature import Feature

class ChaikinMoneyFlow(Feature):
    """
    Chaikin Money Flow (CMF) is a volume-weighted average of accumulation/distribution over a specified period.
    It measures buying and selling pressure by combining price and volume.

    A positive CMF indicates buying pressure, while a negative CMF indicates selling pressure.
    """

    def __init__(self, period=20):
        self.period = period

    def calculate(self, data):
        if not all(col in data.columns for col in ['High', 'Low', 'Close', 'Volume']):
            raise ValueError("Data must contain 'High', 'Low', 'Close', and 'Volume' columns")

        # Money Flow Multiplier
        mfm = ((data['Close'] - data['Low']) - (data['High'] - data['Close'])) / (data['High'] - data['Low'])
        mfm = mfm.replace([np.inf, -np.inf], 0)  # Replace infinity with 0

        # Money Flow Volume
        mfv = mfm * data['Volume']

        # Chaikin Money Flow
        cmf = mfv.rolling(window=self.period).sum() / data['Volume'].rolling(window=self.period).sum()

        return pd.Series(cmf, name=f'CMF_{self.period}')

    @classmethod
    def get_description(cls):
        return "Chaikin Money Flow (CMF) measures buying and selling pressure by combining price and volume data."

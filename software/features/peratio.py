from .base_feature import Feature
import yfinance as yf
import pandas as pd
import numpy as np

class PERatio(Feature):
    """Price to Earnings Ratio: A valuation metric comparing a company's current share price to its earnings per share."""

    def calculate(self, data):
        if not hasattr(data, 'name') or data.name is None:
            raise ValueError("DataFrame must have a 'name' attribute set to the ticker symbol.")

        ticker = yf.Ticker(data.name)

        # Get income statement data
        income_stmt = ticker.income_stmt

        if income_stmt is None or income_stmt.empty:
            return pd.Series(index=data.index, name='PE_Ratio')

        # Extract net income data
        net_income = income_stmt.loc['Net Income'].astype(float)

        # Convert index to datetime and ensure it's tz-aware
        net_income.index = pd.to_datetime(net_income.index).tz_localize('UTC')

        # Ensure data index is tz-aware
        data_index = data.index.tz_convert('UTC')

        # Create a daily date range only for the period of net income data
        daily_dates = pd.date_range(start=net_income.index.min(),
                                    end=net_income.index.max(),
                                    freq='D', tz='UTC')

        # Reindex net income to daily frequency and interpolate
        net_income_daily = net_income.reindex(daily_dates).interpolate(method='time')

        # Create a Series with the full date range of the stock data
        full_net_income = pd.Series(index=data_index, dtype=float)

        # Fill with NaN for dates before the first net income data
        full_net_income.loc[:net_income_daily.index[0]] = np.nan

        # Fill interpolated values
        full_net_income.loc[net_income_daily.index[0]:net_income_daily.index[-1]] = net_income_daily.astype(float)

        # Forward fill for dates after the last net income data
        full_net_income.loc[net_income_daily.index[-1]:] = net_income_daily.iloc[-1]

        # Get shares outstanding
        shares_outstanding = ticker.info.get('sharesOutstanding')

        if shares_outstanding is None:
            return pd.Series(index=data.index, name='PE_Ratio')

        # Calculate daily earnings per share
        eps = full_net_income / shares_outstanding

        # Calculate daily P/E ratio
        pe_ratio = data['Close'] / eps

        return pe_ratio.rename('PE_Ratio')

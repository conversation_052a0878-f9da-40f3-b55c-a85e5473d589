from .base_feature import Feature

DEFAULT_RSI_PERIOD = 14

class RSI(Feature):
    """Relative Strength Index (RSI) indicator."""

    def __init__(self, period=DEFAULT_RSI_PERIOD):
        self.period = period

    def calculate(self, data):
        delta = data['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

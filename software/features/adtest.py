import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import adfuller
from .base_feature import Feature

class ADFTest(Feature):
    """Augmented Dickey-Fuller test statistic for detecting stationarity."""

    def __init__(self, window=100):
        self.window = window

    def calculate(self, data):
        """
        Calculate the rolling ADF test statistic.

        Args:
            data (pd.DataFrame): Input data containing 'Close' prices.

        Returns:
            pd.Series: Rolling ADF test statistic.
        """
        if 'Close' not in data.columns:
            raise ValueError("Input data must contain 'Close' column.")

        adf_stats = data['Close'].rolling(window=self.window).apply(
            lambda x: self._adf_test(x) if len(x) > 10 else np.nan
        )
        return adf_stats.rename('ADF_Statistic')

    def _adf_test(self, series):
        """
        Perform ADF test on a single window of data.

        Args:
            series (pd.Series): Window of price data.

        Returns:
            float: ADF test statistic.
        """
        try:
            result = adfuller(series, autolag='AIC')
            return result[0]  # Return the test statistic
        except Exception:
            return np.nan

    @classmethod
    def get_description(cls):
        return "Calculates the rolling Augmented Dickey-Fuller test statistic to detect stationarity in the price series."

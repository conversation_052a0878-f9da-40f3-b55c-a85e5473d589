# software/features/quarterlyrevenuegrowth.py
import pandas as pd
from .base_feature import Feature

class QuarterlyRevenueGrowth(Feature):
    """
    Calculates the Year-over-Year (YoY) percentage change in quarterly revenue.
    Requires quarterly data with a 'Revenue' column.
    Expects data index to be DateTimeIndex representing quarters.
    """
    def calculate(self, data):
        """
        Calculate the YoY percentage change of the 'Revenue'.

        Args:
            data (pd.DataFrame): DataFrame with a 'Revenue' column and DatetimeIndex.

        Returns:
            pd.Series: The calculated YoY growth rate, aligned with the input index.
                     Returns a Series of zeros if 'Revenue' column is missing
                     or if there are not enough data points (<= 4 quarters).
        """
        required_col = 'Revenue'
        periods_per_year = 4
        min_data_points = periods_per_year + 1 # Need 5 points for 4-period change

        # Check if the required column exists
        if required_col not in data.columns:
            # Return zeros if the column is missing (e.g., when run with daily test data)
            return pd.Series(0.0, index=data.index, dtype=float)
            
        # Check if there's enough data
        if len(data) < min_data_points:
             # Return zeros if not enough data for YoY calculation
             return pd.Series(0.0, index=data.index, dtype=float)

        # Calculate YoY percentage change and fill initial NaNs
        # Assumes data is sorted chronologically with quarterly frequency
        return data[required_col].pct_change(periods=periods_per_year).fillna(0)

    # Optional: Define a consistent name method if needed elsewhere
    # @classmethod 
    # def get_feature_name(cls):
    #    return "QuarterlyRevenueGrowth" 
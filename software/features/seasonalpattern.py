import pandas as pd
import numpy as np
from .base_feature import Feature

class SeasonalPattern(Feature):
    """
    Calculates seasonal patterns in stock returns on a monthly and quarterly basis.

    This feature identifies recurring patterns in stock price movements that are associated
    with specific months or quarters. It normalizes these patterns to allow for comparison
    across different time periods and stocks.
    """

    def calculate(self, data):
        # Extract month and quarter
        data['Month'] = data.index.month
        data['Quarter'] = data.index.quarter

        # Calculate log returns
        data['Log_Return'] = np.log(data['Close'] / data['Close'].shift(1))

        # Calculate average log return for each month and quarter
        monthly_return = data.groupby('Month')['Log_Return'].mean()
        quarterly_return = data.groupby('Quarter')['Log_Return'].mean()

        # Map the average returns back to the original data
        seasonal_monthly = data['Month'].map(monthly_return)
        seasonal_quarterly = data['Quarter'].map(quarterly_return)

        # Normalize the patterns
        seasonal_monthly = (seasonal_monthly - seasonal_monthly.mean()) / seasonal_monthly.std()
        seasonal_quarterly = (seasonal_quarterly - seasonal_quarterly.mean()) / seasonal_quarterly.std()

        return pd.DataFrame({
            'Seasonal_Monthly': seasonal_monthly,
            'Seasonal_Quarterly': seasonal_quarterly
        })

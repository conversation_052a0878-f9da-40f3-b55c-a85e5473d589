#!/usr/bin/env python3
# enhanced_plan_improver.py - Generate mocks, embed data, finetune model, and evaluate plans

import os
import re
import argparse
import random
import json
import glob
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
import time

# Import RL algorithm
from stable_baselines3 import PPO
from stable_baselines3.common.evaluation import evaluate_policy
from stable_baselines3.common.callbacks import EvalCallback
from stable_baselines3.common.monitor import Monitor

# Import embedding model
from sentence_transformers import SentenceTransformer

# Import rich for beautiful output
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.text import Text
from rich.prompt import Confirm

# Import our custom modules
from stock_rl_env import StockVectorEnv
from plan_rl_env import PlanEvaluationEnv
from generate_mock import (
    generate_mock_report,
    generate_mock_price_data,
    load_stock_data,
    TICKER,
    MODEL_REGISTRY,
    DEFAULT_MODEL
)
from langchain_core.messages import HumanMessage, SystemMessage

# Constants
MODELS_DIR = "plan_models"
PLAN_TRAINING_FILE = "plan_training.json"

# Initialize console
console = Console()

def extract_plan(report_text: str) -> List[str]:
    """Extract the plan section from a report"""
    # Find the plan section using regex
    plan_match = re.search(r'## Plan\n((?:- TASK\([^)]+\)[^\n]*\n)+)', report_text)

    if plan_match:
        plan_text = plan_match.group(1).strip()
        # Extract individual task lines
        tasks = re.findall(r'- (TASK\([^)]+\)[^\n]*)', plan_text)
        return tasks

    return []

def generate_fin_lang_plan(business_question: str, model_name: str = DEFAULT_MODEL) -> List[str]:
    """
    Generate a plan using the FIN_LANG format with the LLM

    Args:
        business_question: The business question to generate a plan for
        model_name: Name of the LLM model to use

    Returns:
        List of task strings
    """
    # Tool names for the prompt
    tool_names = "TodayDateTool, SimpleWorkflowTool, SimpleJoinWorkflowTool, ExtractWorkflowTool, DataVizInsightTool"

    # JSON schema for the plan
    json_plan_schema = """
    {
      "type": "object",
      "properties": {
        "tasks": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      },
      "required": ["tasks"]
    }
    """

    # Critical thinking planning template
    critical_thinking_planning = """
    When designing a research plan:
    1. Consider what technical indicators would be most relevant
    2. Think about appropriate timeframes for analysis
    3. Include comparative analysis when relevant
    4. Ensure tools are used in the most effective order
    5. Focus on actionable, specific tasks
    """

    # Get the LLM model from the registry
    if model_name not in MODEL_REGISTRY:
        console.print(f"[bold yellow]Warning: Model '{model_name}' not found in registry. Using default model '{DEFAULT_MODEL}'.[/bold yellow]")
        model_name = DEFAULT_MODEL

    model_func = MODEL_REGISTRY.get(model_name, MODEL_REGISTRY[DEFAULT_MODEL])
    llm = model_func()  # Call the function to get the model instance

    # Create the FIN_LANG system prompt
    fin_lang = f"""
    <critical_thinking_section>
    {critical_thinking_planning}
    </critical_thinking_section>

    ROLE -> HEDGE_FUND_MANAGER
    GOAL -> DESIGN(RESEARCH_PLAN) FOR(ANALYST)
    INPUT -> BUSINESS_QUESTION

    RULES:
    COUNT(TASKS) = 3-5 (Multiple tasks for comprehensive analysis)
    FORMAT(TASK) = TASK([List of tools], "company_name/s", "objective", "timeframe")
    CONSTRAINTS:
        -- TOOLS
            - TOOLS(NAME-DESCRIPTION) = {tool_names}
            - TOOLS = [List of tools]
            - TOOLS ∈ {tool_names}
            - TOOLS = CAN BE REPEATED, ORDER MATTERS
            - COUNT(TOOLS) = 3-6

        -- COMPANY_NAME/S
            - COMPANY_NAME/S = "company_name/s"
            - COMPANY_NAME/S = CANNOT BE REPEATED, ORDER MATTERS
            - COUNT(COMPANY_NAME/S) = 1-2

        -- OBJECTIVE
            - OBJECTIVE ∈ {"closing price", "volume", "RSI",...}

        -- TIMEFRAME
            - TIMEFRAME = M days
            - RANGE(TIMEFRAME) = 1-14 days
            - TIME: PAST, PRESENT, FUTURE

        -- CONSTRAINTS
            - NO(JUSTIFICATION)
            - NO(EXPLANATION)
            - EACH TASK -> ACTIONABLE
    CRITICAL: FINAL OUTPUT MUST BE IN FIN_LANG FORMAT, DO NOT OUTPUT ANYTHING ELSE
    OUTPUT -> [TASK(),...]

    Your format ultimately must be a valid JSON matching this schema:
    {json_plan_schema}

    IMPORTANT: You must return a valid list of tasks in the 'tasks' field. The list cannot be empty.
    Example output format: {{
      "tasks": ["TASK([{tool_names}], \\"AVGO\\", \\"closing price\\", \\"next 7 days\\")"]
    }}
    """

    # Create the human message
    human_message = HumanMessage(content=f"Please create a research plan for the following business question: {business_question}")

    # Get the LLM's response
    system_message = SystemMessage(content=fin_lang)
    messages = [system_message, human_message]

    response = llm.invoke(messages).content

    # Try to parse the response as JSON
    try:
        # Extract JSON if it's embedded in text
        json_match = re.search(r'({.*})', response, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            plan_data = json.loads(json_str)
            tasks = plan_data.get('tasks', [])
            return tasks
        else:
            console.print("[bold red]Failed to extract JSON from LLM response[/]")
            return []
    except json.JSONDecodeError:
        console.print("[bold red]Failed to parse LLM response as JSON[/]")
        console.print(f"[yellow]Raw response: {response}[/yellow]")
        return []

def get_last_report_number() -> int:
    """
    Get the last report number from the reports directory

    Returns:
        int: Last report number, or 0 if no reports exist
    """
    # Check if reports directory exists
    if not os.path.exists("reports"):
        return 0

    # Get all report files
    report_files = glob.glob("reports/*.txt")

    if not report_files:
        return 0

    # Extract report numbers
    report_numbers = []
    for file in report_files:
        try:
            # Extract the number from the filename (e.g., "reports/42.txt" -> 42)
            report_number = int(os.path.basename(file).split('.')[0])
            report_numbers.append(report_number)
        except (ValueError, IndexError):
            # Skip files that don't match the expected format
            continue

    return max(report_numbers) if report_numbers else 0

def load_existing_reports_data() -> List[Dict[str, Any]]:
    """
    Load existing reports data from reports.json

    Returns:
        List of existing report data dictionaries, or empty list if file doesn't exist
    """
    try:
        with open("reports.json", "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def generate_reports_with_plans(num_reports: int = 10, ticker: str = TICKER, model_name: str = DEFAULT_MODEL) -> List[Dict[str, Any]]:
    """
    Generate mock reports with plans using real stock data
    Appends to existing reports rather than overwriting

    Args:
        num_reports: Number of reports to generate
        ticker: Stock ticker symbol
        model_name: Name of the LLM model to use

    Returns:
        List of report data dictionaries (only the newly generated ones)
    """
    # Load actual stock data
    closing_prices, dates = load_stock_data(ticker, "2023-01-01")

    if not closing_prices or not dates:
        console.print("[bold red]Failed to load stock data[/]")
        return []

    # Create reports directory if it doesn't exist
    os.makedirs("reports", exist_ok=True)

    # Get the last report number and existing reports data
    start_index = get_last_report_number()
    existing_reports_data = load_existing_reports_data()

    if start_index > 0 or existing_reports_data:
        console.print(f"[cyan]Found {len(existing_reports_data)} existing reports. Last report number: {start_index}[/cyan]")

    # Generate new reports
    new_reports_data = []

    # Create a table for generated reports
    table = Table(title=f"Generated Reports for {ticker}")
    table.add_column("Report #", style="cyan")
    table.add_column("Report Date", style="cyan")
    table.add_column("Current Price", style="yellow")
    table.add_column("Forecast Price", style="green")
    table.add_column("Days Ahead", style="blue")
    table.add_column("Confidence", style="magenta")
    table.add_column("Plan Tasks", style="bright_blue")

    # Generate reports with progress tracking
    with Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Generating {num_reports} reports...", total=num_reports)

        for i in range(num_reports):
            # Calculate the new report number
            report_number = start_index + i + 1

            # Get a random index for the data
            random_index = random.randint(0, len(closing_prices) - 1)

            # Get the actual price and date at that index
            actual_price = float(closing_prices[random_index])
            report_date = dates[random_index]

            # Generate mock price data
            forecast_price, _, days_ahead, confidence, _ = generate_mock_price_data(actual_price, dates)

            # Generate the report
            report_content = generate_mock_report(
                ticker,
                actual_price,
                forecast_price,
                days_ahead,
                confidence,
                report_date,
                model_name
            )

            # Extract the plan from the report
            plan_tasks = extract_plan(report_content)

            # Save the report to a file
            report_filename = f"reports/{report_number}.txt"
            with open(report_filename, "w") as f:
                f.write(report_content)

            # Calculate target date
            report_date_obj = datetime.strptime(report_date, "%Y-%m-%d")
            target_date = (report_date_obj + timedelta(days=days_ahead)).strftime("%Y-%m-%d")

            # Add report data to the list
            report_data = {
                "report": f"{report_number}.txt",
                "report_date": report_date,
                "forecast_price": forecast_price,
                "actual_price": actual_price,
                "target_date": target_date,
                "days_ahead": days_ahead,
                "confidence": confidence / 100,  # Convert to decimal
                "plan_tasks": plan_tasks,
                "num_tasks": len(plan_tasks)
            }
            new_reports_data.append(report_data)

            # Add row to the table
            direction = "↑" if forecast_price > actual_price else "↓"
            pct_change = abs((forecast_price - actual_price) / actual_price) * 100
            table.add_row(
                f"#{report_number}",
                f"{report_date}",
                f"${actual_price:.2f}",
                f"${forecast_price:.2f} ({pct_change:.2f}% {direction})",
                f"{days_ahead} days",
                f"{confidence}%",
                f"{len(plan_tasks)} tasks"
            )

            # Update progress
            progress.update(task, advance=1)

    # Display the table of generated reports
    console.print(table)

    # Combine existing and new reports data
    combined_reports_data = existing_reports_data + new_reports_data

    # Save the combined report data to a JSON file
    with open("reports.json", "w") as f:
        json.dump(combined_reports_data, f, indent=2)

    # Show summary
    console.print(Panel(
        f"[bold green]Successfully generated {num_reports} reports for {ticker}[/bold green]\n"
        f"[cyan]Total reports: {len(combined_reports_data)}[/cyan]\n"
        f"[yellow]Reports saved to reports/ directory[/yellow]\n"
        f"[magenta]Report data saved to reports.json[/magenta]",
        title="Summary", border_style="green"
    ))

    # Return only the newly generated reports data
    return new_reports_data

def create_embeddings(reports_data: List[Dict[str, Any]] = None) -> None:
    """
    Create embeddings for reports and save to training.json
    If reports_data is None, load from reports.json

    Args:
        reports_data: List of report data dictionaries (optional)
    """
    # If no reports_data provided, load from reports.json
    if reports_data is None:
        try:
            with open("reports.json", "r") as f:
                reports_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            console.print("[bold red]reports.json not found or invalid. Run 'generate' command first.[/]")
            return

    # Initialize the sentence transformer model
    embedder = SentenceTransformer('all-MiniLM-L6-v2')

    # Create training data with embeddings
    training_data = []

    with Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Creating embeddings for {len(reports_data)} reports...", total=len(reports_data))

        for item in reports_data:
            report_path = os.path.join('reports', item['report'])

            # Skip if report file doesn't exist
            if not os.path.exists(report_path):
                console.print(f"[yellow]Warning: Report file {report_path} not found. Skipping.[/]")
                progress.update(task, advance=1)
                continue

            # Read the report text file
            with open(report_path, 'r') as f:
                report_text = f.read()

            # Generate embedding vector for the report text
            embedding = embedder.encode(report_text)

            # Convert numpy array to list for JSON serialization
            embedding_list = embedding.tolist()

            # Create training item with embedding and other data
            training_item = {
                'vector': embedding_list,
                'forecast_price': item['forecast_price'],
                'actual_price': item['actual_price'],
                'confidence': item['confidence']
            }

            training_data.append(training_item)

            # Update progress
            progress.update(task, advance=1)

    # Save to training.json
    with open('training.json', 'w') as f:
        json.dump(training_data, f, indent=2)

    console.print(f"[green]Created training.json with {len(training_data)} embedded reports[/]")

def create_plan_training_data(reports_data: List[Dict[str, Any]] = None) -> None:
    """
    Create plan training data and save to plan_training.json
    If reports_data is None, load from reports.json

    Args:
        reports_data: List of report data dictionaries (optional)
    """
    # If no reports_data provided, load from reports.json
    if reports_data is None:
        try:
            with open("reports.json", "r") as f:
                reports_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            console.print("[bold red]reports.json not found or invalid. Run 'generate' command first.[/]")
            return

    # Initialize the sentence transformer model
    embedder = SentenceTransformer('all-MiniLM-L6-v2')

    # Create training data for plans
    plan_training_data = []

    with Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=console
    ) as progress:
        task = progress.add_task(f"Creating plan training data for {len(reports_data)} reports...", total=len(reports_data))

        for item in reports_data:
            # Skip reports with no plan tasks
            if not item.get('plan_tasks', []):
                progress.update(task, advance=1)
                continue

            # Join plan tasks into a single string
            plan_text = "\n".join(item['plan_tasks'])

            # Generate embedding vector for the plan text
            embedding = embedder.encode(plan_text)

            # Calculate plan quality score based on prediction accuracy
            forecast_price = item['forecast_price']
            actual_price = item['actual_price']

            # Calculate error as percentage of actual price
            error_pct = abs(forecast_price - actual_price) / actual_price

            # Convert error to quality score (0-100)
            # Lower error = higher quality
            # 0% error = 100 quality, 10%+ error = 0 quality
            quality_score = max(0, 100 - (error_pct * 1000))

            # Adjust score based on number of tasks (more tasks = better plan, up to a point)
            num_tasks = item.get('num_tasks', 0)
            task_bonus = min(10, num_tasks * 2)  # Up to 10 points bonus for 5+ tasks

            # Final quality score (capped at 100)
            final_score = min(100, quality_score + task_bonus)

            # Create training item
            training_item = {
                'plan': plan_text,
                'vector': embedding.tolist(),
                'quality_score': final_score,
                'forecast_price': forecast_price,
                'actual_price': actual_price,
                'error_pct': error_pct * 100,  # Convert to percentage
                'num_tasks': num_tasks
            }

            plan_training_data.append(training_item)

            # Update progress
            progress.update(task, advance=1)

    # Save to plan_training.json
    with open(PLAN_TRAINING_FILE, 'w') as f:
        json.dump(plan_training_data, f, indent=2)

    console.print(f"[green]Created {PLAN_TRAINING_FILE} with {len(plan_training_data)} plan training examples[/]")

def train_plan_evaluation_model(timesteps: int = 10000, train_ratio: float = 0.8, finetune: bool = False) -> str:
    """
    Train a model to evaluate plans

    Args:
        timesteps: Number of timesteps to train for
        train_ratio: Ratio of data to use for training
        finetune: Whether to finetune an existing model instead of training from scratch

    Returns:
        Path to the trained model
    """
    # Create models directory if it doesn't exist
    os.makedirs(MODELS_DIR, exist_ok=True)

    # Create environment with training ratio
    env = PlanEvaluationEnv(training_data_path=PLAN_TRAINING_FILE, train_ratio=train_ratio)

    # Create a monitored environment for evaluation
    eval_env = Monitor(PlanEvaluationEnv(training_data_path=PLAN_TRAINING_FILE, train_ratio=train_ratio, is_training=False))

    # Get timestamp for model name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = f"{MODELS_DIR}/PPO_{timestamp}"

    # Create callback for saving best model
    eval_callback = EvalCallback(
        eval_env,
        best_model_save_path=MODELS_DIR,
        log_path=MODELS_DIR,
        eval_freq=500,
        deterministic=True,
        render=False,
        n_eval_episodes=5,
        verbose=0
    )

    # Check if we should finetune an existing model
    if finetune:
        # Find the latest model
        latest_model = find_latest_model()
        if latest_model and os.path.exists(f"{latest_model}.zip"):
            console.print(f"[cyan]Finetuning existing model: {os.path.basename(latest_model)}[/]")
            try:
                # Load the existing model
                model = PPO.load(latest_model, env=env)
                console.print("[green]Successfully loaded existing model for finetuning[/]")
            except Exception as e:
                console.print(f"[yellow]Error loading model for finetuning: {str(e)}[/]")
                console.print("[yellow]Training a new model instead[/]")
                # Create a new model if loading fails
                model = PPO(
                    "MlpPolicy",
                    env,
                    learning_rate=0.0003,
                    n_steps=1024,
                    batch_size=64,
                    n_epochs=10,
                    gamma=0.99,
                    ent_coef=0.01,
                    clip_range=0.2,
                    verbose=0
                )
        else:
            console.print("[yellow]No existing model found for finetuning. Training a new model instead.[/]")
            # Create a new model if no existing model is found
            model = PPO(
                "MlpPolicy",
                env,
                learning_rate=0.0003,
                n_steps=1024,
                batch_size=64,
                n_epochs=10,
                gamma=0.99,
                ent_coef=0.01,
                clip_range=0.2,
                verbose=0
            )
    else:
        # Create a new model with hyperparameters optimized for plan evaluation
        model = PPO(
            "MlpPolicy",
            env,
            learning_rate=0.0003,
            n_steps=1024,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            ent_coef=0.01,
            clip_range=0.2,
            verbose=0
        )

    # Train model with progress bar
    console.print(f"[cyan]{'Finetuning' if finetune else 'Training'} PPO model for {timesteps} steps...[/]")
    start_time = time.time()

    model.learn(
        total_timesteps=timesteps,
        callback=eval_callback,
        progress_bar=True
    )

    training_time = time.time() - start_time

    # Save the final model
    final_model_path = f"{model_path}_final"
    model.save(final_model_path)

    # Evaluate the final model
    mean_reward, std_reward = evaluate_policy(model, eval_env, n_eval_episodes=10)

    # Display results
    console.print(Panel(
        f"[bold cyan]Plan Evaluation Model {'Finetuning' if finetune else 'Training'} Complete[/]\n\n"
        f"[bold]Training Summary:[/]\n"
        f"• [bold]Training Time:[/] {training_time:.2f} seconds\n"
        f"• [bold]Mean Reward:[/] {mean_reward:.2f} ± {std_reward:.2f}\n"
        f"• [bold]Model saved to:[/] {final_model_path}",
        title="[bold]Training Results[/]",
        border_style="green"
    ))

    return final_model_path

def evaluate_plan_with_model(plan_tasks: List[str], model_path: str) -> float:
    """
    Evaluate a plan using the trained model

    Args:
        plan_tasks: List of task strings
        model_path: Path to the trained model

    Returns:
        Quality score (0-100)
    """
    # Initialize the embedder
    embedder = SentenceTransformer('all-MiniLM-L6-v2')

    # Join the tasks into a single string
    plan_text = "\n".join(plan_tasks)

    # Load the model
    model = PPO.load(model_path)

    # Generate embedding for the plan
    embedding = embedder.encode(plan_text)
    embedding_vector = np.array(embedding, dtype=np.float32)

    # Normalize the embedding
    embedding_vector = np.clip(embedding_vector, -1.0, 1.0)

    # Get the model's prediction
    action, _ = model.predict(embedding_vector, deterministic=True)

    # Extract the score
    score = float(action[0])

    # Ensure score is within bounds
    score = min(100, max(0, score))

    return score

def find_latest_model() -> str:
    """
    Find the latest trained model

    Returns:
        Path to the latest model
    """
    # Find all model files
    model_files = glob.glob(f"{MODELS_DIR}/*_final.zip")

    if not model_files:
        return None

    # Sort by modification time (newest first)
    model_files.sort(key=os.path.getmtime, reverse=True)

    # Return the newest model (without .zip extension)
    return model_files[0].replace(".zip", "")

def generate_and_evaluate_plan(business_question: str, model_name: str = DEFAULT_MODEL, model_path: str = None) -> None:
    """
    Generate a plan and evaluate it

    Args:
        business_question: Business question to generate a plan for
        model_name: Name of the LLM model to use
        model_path: Path to the plan evaluation model
    """
    # Generate a plan
    console.print(Panel(
        f"[bold cyan]Generating Plan[/]\n\n"
        f"Business Question: [bold]{business_question}[/bold]\n"
        f"Using LLM: [bold]{model_name}[/bold]",
        title="[bold]Plan Generator[/]",
        border_style="blue"
    ))

    tasks = generate_fin_lang_plan(business_question, model_name)

    if not tasks:
        console.print("[bold red]Failed to generate a plan[/]")
        return

    # Display the plan
    console.print("\n[bold]Generated Plan:[/]")
    for task in tasks:
        console.print(f"- {task}")

    # Find the latest model if not specified
    if not model_path:
        model_path = find_latest_model()
        if model_path:
            console.print(f"[cyan]Using latest model: {os.path.basename(model_path)}[/]")
        else:
            console.print("[yellow]No trained model found. Cannot evaluate plan.[/]")
            return

    # Evaluate the plan
    score = evaluate_plan_with_model(tasks, model_path)

    # Display the score
    console.print(f"\n[bold]Plan Quality Score:[/] {score:.1f}/100")

    # Add quality assessment
    if score >= 90:
        console.print("[green]Excellent plan! Comprehensive and well-structured.[/]")
    elif score >= 80:
        console.print("[green]Very good plan with solid analysis tasks.[/]")
    elif score >= 70:
        console.print("[yellow]Good plan, but could be improved.[/]")
    elif score >= 50:
        console.print("[yellow]Adequate plan that covers basic analysis.[/]")
    else:
        console.print("[red]The plan needs substantial revision.[/]")

    # Display a summary panel
    console.print(Panel(
        "[bold green]Plan Generation and Evaluation Complete![/]\n\n"
        "The LLM has generated a plan and the model has evaluated it.\n"
        "The plan follows these constraints:\n"
        "• Uses 3-6 tools from the specified list\n"
        "• Includes 1-2 company names\n"
        "• Specifies a clear objective\n"
        "• Uses a timeframe between 1-14 days",
        title="[bold]Plan Summary[/]",
        border_style="green"
    ))

def main():
    """Main function to parse arguments and run the program"""
    parser = argparse.ArgumentParser(description="Enhanced Plan Improver - Generate mocks, embed data, finetune model, and evaluate")

    # Add subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Generate reports command
    generate_parser = subparsers.add_parser("generate", help="Generate mock reports with plans")
    generate_parser.add_argument("--num", type=int, default=10, help="Number of reports to generate (default: 10)")
    generate_parser.add_argument("--ticker", type=str, default=TICKER, help=f"Stock ticker symbol (default: {TICKER})")
    generate_parser.add_argument("--model", type=str, default=DEFAULT_MODEL, choices=list(MODEL_REGISTRY.keys()),
                               help=f"LLM model to use (default: {DEFAULT_MODEL})")

    # Create embeddings command
    embed_parser = subparsers.add_parser("embed", help="Create embeddings for reports")

    # Train model command
    train_parser = subparsers.add_parser("train", help="Train a plan evaluation model")
    train_parser.add_argument("--timesteps", type=int, default=10000, help="Number of timesteps to train for (default: 10000)")
    train_parser.add_argument("--train-ratio", type=float, default=0.8, help="Ratio of data to use for training (default: 0.8)")
    train_parser.add_argument("--finetune", action="store_true", help="Finetune the latest model instead of training from scratch")

    # Evaluate plan command
    evaluate_parser = subparsers.add_parser("evaluate", help="Generate and evaluate a plan")
    evaluate_parser.add_argument("--question", type=str, required=True, help="Business question to generate a plan for")
    evaluate_parser.add_argument("--model", type=str, default=DEFAULT_MODEL, choices=list(MODEL_REGISTRY.keys()),
                               help=f"LLM model to use (default: {DEFAULT_MODEL})")
    evaluate_parser.add_argument("--model-path", type=str, default=None, help="Path to the plan evaluation model (default: latest)")

    # All-in-one command
    all_parser = subparsers.add_parser("all", help="Run the entire pipeline")
    all_parser.add_argument("--num", type=int, default=10, help="Number of reports to generate (default: 10)")
    all_parser.add_argument("--ticker", type=str, default=TICKER, help=f"Stock ticker symbol (default: {TICKER})")
    all_parser.add_argument("--model", type=str, default=DEFAULT_MODEL, choices=list(MODEL_REGISTRY.keys()),
                          help=f"LLM model to use (default: {DEFAULT_MODEL})")
    all_parser.add_argument("--timesteps", type=int, default=10000, help="Number of timesteps to train for (default: 10000)")
    all_parser.add_argument("--finetune", action="store_true", help="Finetune the latest model instead of training from scratch")
    all_parser.add_argument("--question", type=str, default="Should we invest in AAPL in the next 7 days?",
                          help="Business question to generate a plan for (default: 'Should we invest in AAPL in the next 7 days?')")

    args = parser.parse_args()

    # Show welcome banner
    console.print(Panel.fit(
        "[bold cyan]Enhanced Plan Improver[/bold cyan]\n"
        "[yellow]Generate mocks, embed data, finetune model, and evaluate plans[/yellow]",
        border_style="green"
    ))

    # Process commands
    if args.command == "generate":
        # Generate mock reports with plans
        generate_reports_with_plans(args.num, args.ticker, args.model)

    elif args.command == "embed":
        # Create embeddings from all reports in reports.json
        create_embeddings()

        # Create plan training data from all reports in reports.json
        create_plan_training_data()

    elif args.command == "train":
        # Check if plan_training.json exists
        if not os.path.exists(PLAN_TRAINING_FILE):
            console.print(f"[bold red]{PLAN_TRAINING_FILE} not found. Run 'embed' command first.[/]")
            return

        # Train model
        train_plan_evaluation_model(args.timesteps, args.train_ratio, args.finetune)

    elif args.command == "evaluate":
        # Generate and evaluate a plan
        generate_and_evaluate_plan(args.question, args.model, args.model_path)

    elif args.command == "all":
        # Run the entire pipeline
        console.print("[bold cyan]Running the entire pipeline...[/]")

        # Generate mock reports with plans (returns only the newly generated reports)
        generate_reports_with_plans(args.num, args.ticker, args.model)

        # Create embeddings from all reports in reports.json
        create_embeddings()

        # Create plan training data from all reports in reports.json
        create_plan_training_data()

        # Train model
        model_path = train_plan_evaluation_model(args.timesteps, 0.8, args.finetune)

        # Generate and evaluate a plan
        generate_and_evaluate_plan(args.question, args.model, model_path)

    else:
        # No command specified, show help
        parser.print_help()

if __name__ == "__main__":
    main()

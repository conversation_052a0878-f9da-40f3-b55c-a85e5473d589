from langchain_ollama import ChatOllama
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI

from langchain_core.messages import HumanMessage, SystemMessage
import random
import json
import os
import sys
import argparse
from datetime import datetime, timedelta
import yfinance as yf
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.progress import track
from rich import print as rprint

# Initialize Rich console
console = Console()

# Model functions
def get_qwen():
    return ChatOllama(
        model="qwen2.5-coder:3b",
        temperature=0.7,  # Add some creativity
    )

def get_gemini():
    return ChatGoogleGenerativeAI(
        model="gemini-2.0-flash-lite",
        temperature=0.8,
        api_key="AIzaSyAycTr-lFAjO-1bHb06uc63USM_lY5fbPo",
        max_retries=3
    )

def get_llama():
    return ChatOpenAI(
        model="Llama-3.3-70B-Instruct",
        openai_api_key="LLM|1127604376064459|mQzsZTsLFHZIpMVIAta_pi5LWt8",
        temperature=0.7,
        base_url="https://api.llama.com/compat/v1/",
        max_tokens=8192
    )
# Model registry
MODEL_REGISTRY = {
    "qwen": get_qwen,
    "gemini": get_gemini,
    "llama": get_llama
}

# Global variables
TICKER = "AAPL"  # Stock ticker symbol
START_DATE = "2023-01-01"  # Start date for historical data
REPORTS = 1  # Number of reports to generate
APPEND = True  # Whether to append to existing reports or overwrite
DEFAULT_MODEL = "qwen"  # Default model to use

# System prompt that explains the task to the LLM
system_prompt = SystemMessage(content="""
You are a financial analyst AI that creates realistic stock market analysis reports.

I'll provide you with an example of a stock report format. Please study it carefully and generate a new report following EXACTLY the same structure and style, but for a different stock.

Example report format:
```
# Should we invest in UNITY Biotechnology (UBX) in the next 4 days?

## Plan
- TASK([TodayDateTool, SimpleWorkflowTool, SimpleJoinWorkflowTool, ExtractWorkflowTool, DataVizInsightTool], "UBX", "closing price", "next 4 days")
- TASK([TodayDateTool, SimpleWorkflowTool, ExtractWorkflowTool], "UBX", "closing price", "past 4 days")
- TASK([TodayDateTool, SimpleWorkflowTool, SimpleJoinWorkflowTool, ExtractWorkflowTool, DataVizInsightTool], "UBX", "RSI", "past 14 days")

## Conclusion
As a Director, I predict UBX will decrease from $15.2 to $14.5 (4.61% down) by 2025-05-06. Confidence: 65%.

## Reasoning
The recommendation to SELL UBX is based on technical indicators and analogous patterns suggesting downward pressure in the next 4 days. Key bearish signals include an RSI reversal from overbought territory (>70) and a stochastic bearish crossover, both indicating potential further decline. High volume during the recent price drop confirms strong selling pressure, and the support level around $15.00 may not hold given the current momentum. However, there are limitations, such as limited UBX-specific data and the possibility of market conditions differing from analogous patterns. Additionally, an oversold rebound could occur if the RSI drops below 30, though this is not the base case. The confidence level of 65% reflects these uncertainties.
```

IMPORTANT: Your output must contain ONLY these sections and nothing more:
1. Title (starting with #)
2. Plan (with 1-10 TASK lines)
3. Conclusion (one sentence with the prediction)
4. Reasoning (one paragraph only)

Rules for creating TASK lines:
1. Each TASK must start with TodayDateTool as the first tool
2. Each TASK must use at least 2 tools from this list: [TodayDateTool, SimpleWorkflowTool, SimpleJoinWorkflowTool, ExtractWorkflowTool, DataVizInsightTool]
3. You can use any combination of these tools in any order, but TodayDateTool must always be first
4. You can include multiple tickers in a single task, either as a single ticker like "AAPL" or as a list like ["NVDA", "AMD"]
5. You can use various target columns beyond just "closing price" and "RSI" - be creative with relevant financial metrics
6. Time periods can vary (e.g., "next 7 days", "past 30 days", etc.)

Do not add any additional sections, headers, or explanations beyond these four sections.
""")

def load_stock_data(ticker=TICKER, start_date=START_DATE):
    """
    Load actual closing price data for a stock from Yahoo Finance

    Args:
        ticker (str): Stock ticker symbol
        start_date (str): Start date in YYYY-MM-DD format

    Returns:
        tuple: (list of closing prices, list of dates)
    """
    try:
        # Show loading message
        with console.status(f"[bold green]Loading stock data for {ticker} from {start_date}...[/bold green]"):
            # Download stock data
            stock_data = yf.download(ticker, start=start_date, progress=False)

            # Extract closing prices and convert to list of floats
            closing_prices = stock_data['Close'].values.flatten().tolist()

            # Extract dates and convert to list of strings
            dates = stock_data.index.strftime('%Y-%m-%d').tolist()

        # Create a small table with stock data summary
        table = Table(title=f"Stock Data Summary for {ticker}")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("Days of Data", f"{len(closing_prices)}")
        table.add_row("Date Range", f"{dates[0]} to {dates[-1]}")
        table.add_row("Price Range", f"${min(closing_prices):.2f} - ${max(closing_prices):.2f}")
        table.add_row("Current Price", f"${closing_prices[-1]:.2f}")

        console.print(table)

        return closing_prices, dates
    except Exception as e:
        console.print(Panel(f"[bold red]Error loading stock data: {e}[/bold red]",
                           title="Error", border_style="red"))

        # Return some default values if data loading fails
        default_prices = [100.0, 101.2, 99.8, 102.5, 103.1]
        default_dates = [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(len(default_prices), 0, -1)]
        console.print("[yellow]Using default price data instead[/yellow]")
        return default_prices, default_dates

def generate_mock_price_data(actual_price, dates=None):
    """
    Generate mock price prediction data based on actual price

    Args:
        actual_price (float): Actual closing price
        dates (list, optional): List of available dates for report date selection

    Returns:
        tuple: (forecast_price, actual_price, days_ahead, confidence, report_date)
    """
    # Generate random forecast days (3-10 days ahead)
    days_ahead = random.randint(3, 10)

    # Generate random forecast price (±10% of actual price)
    max_deviation = actual_price * 0.10
    deviation = random.uniform(-max_deviation, max_deviation)
    forecast_price = round(actual_price + deviation, 2)

    # Calculate percentage change
    pct_change = abs((forecast_price - actual_price) / actual_price)

    # Generate confidence level based on deviation
    # Higher deviation = lower confidence
    max_confidence = 90
    min_confidence = 55
    confidence = round(max_confidence - (pct_change * 100 * 2))
    confidence = max(min(confidence, max_confidence), min_confidence)

    # Select a random date from the available dates if provided
    report_date = None
    if dates and len(dates) > 0:
        # Select a date that's at least days_ahead from the end to ensure we have actual data
        valid_dates = dates[:-days_ahead] if len(dates) > days_ahead else dates
        if valid_dates:
            report_date = random.choice(valid_dates)

    return forecast_price, actual_price, days_ahead, confidence, report_date

def generate_human_prompt(stock_symbol, days_ahead, current_price, forecast_price, confidence, report_date=None):
    """
    Generate a human prompt for the LLM with specific stock details

    Args:
        stock_symbol (str): Stock ticker symbol
        days_ahead (int): Number of days ahead for prediction
        current_price (float): Current stock price
        forecast_price (float): Forecasted stock price
        confidence (int): Confidence level for the prediction
        report_date (str, optional): Date of the report in YYYY-MM-DD format

    Returns:
        HumanMessage: Formatted prompt for the LLM
    """
    # Use the provided report date or today's date
    if report_date:
        report_date_obj = datetime.strptime(report_date, "%Y-%m-%d")
    else:
        report_date_obj = datetime.now()
        report_date = report_date_obj.strftime("%Y-%m-%d")

    # Calculate a future date for the prediction
    future_date = (report_date_obj + timedelta(days=days_ahead)).strftime("%Y-%m-%d")

    # Calculate percentage change
    pct_change = ((forecast_price - current_price) / current_price) * 100
    direction = "increase" if forecast_price > current_price else "decrease"

    return HumanMessage(content=f"""
Generate a stock analysis report for {stock_symbol} looking {days_ahead} days ahead.
The report date is {report_date}.
The current price is ${current_price:.2f}.
The target date for the prediction is {future_date}.
The forecast price is ${forecast_price:.2f} ({abs(pct_change):.2f}% {direction}).
Use a confidence level of {confidence}%.

IMPORTANT:
1. Follow the EXACT format from the example - do not add any extra sections or content
2. Include 1-10 TASK lines with appropriate tools in the Plan section
3. In your Conclusion section, use EXACTLY these values:
   - Current price: ${current_price:.2f}
   - Forecast price: ${forecast_price:.2f}
   - Percentage change: {abs(pct_change):.2f}% {direction}
   - Target date: {future_date}
   - Confidence: {confidence}%
4. Keep the Reasoning to a single paragraph only
5. Make sure the report is written as if it was created on {report_date}, not today
6. Remember that each TASK must start with TodayDateTool as the first tool and use at least 2 tools total
7. You can include multiple tickers in a task, either as a single ticker or as a list like ["NVDA", "AMD"]
8. Be creative with target columns beyond just "closing price" and "RSI"

FINAL CHECK: Before submitting your response, make sure you have ONLY these sections:
1. Title
2. Plan
3. Conclusion
4. Reasoning
""")

def generate_mock_report(stock_symbol=TICKER, current_price=None, forecast_price=None, days_ahead=None, confidence=None, report_date=None, model_name=DEFAULT_MODEL):
    """
    Generate a mock stock report

    Args:
        stock_symbol (str): Stock ticker symbol
        current_price (float, optional): Current stock price
        forecast_price (float, optional): Forecasted stock price
        days_ahead (int, optional): Number of days ahead for prediction
        confidence (int, optional): Confidence level for the prediction
        report_date (str, optional): Date of the report in YYYY-MM-DD format
        model_name (str, optional): Name of the model to use (default: DEFAULT_MODEL)

    Returns:
        str: Generated report content
    """
    # If no current price is provided, use a random value
    if current_price is None:
        current_price = round(random.uniform(50, 500), 2)

    # If forecast parameters aren't provided, generate them
    if forecast_price is None or days_ahead is None or confidence is None or report_date is None:
        forecast_price, _, days_ahead, confidence, report_date = generate_mock_price_data(current_price)

    # Generate the human prompt
    human_prompt = generate_human_prompt(
        stock_symbol,
        days_ahead,
        current_price,
        forecast_price,
        confidence,
        report_date
    )

    # Get the model from the registry
    if model_name not in MODEL_REGISTRY:
        console.print(f"[bold yellow]Warning: Model '{model_name}' not found in registry. Using default model '{DEFAULT_MODEL}'.[/bold yellow]")
        model_name = DEFAULT_MODEL

    # Initialize the model
    model = MODEL_REGISTRY[model_name]()

    # Get response from LLM
    response = model.invoke([system_prompt, human_prompt])

    return response.content

def get_last_report_number():
    """
    Find the last report number in the reports directory

    Returns:
        int: The last report number, or 0 if no reports exist
    """
    # Create reports directory if it doesn't exist
    os.makedirs("reports", exist_ok=True)

    # Get all txt files in the reports directory
    report_files = [f for f in os.listdir("reports") if f.endswith(".txt")]

    if not report_files:
        return 0

    # Extract numbers from filenames and find the maximum
    report_numbers = []
    for filename in report_files:
        try:
            # Extract number from filename (e.g., "1.txt" -> 1)
            number = int(filename.split(".")[0])
            report_numbers.append(number)
        except (ValueError, IndexError):
            # Skip files that don't follow the expected naming pattern
            continue

    return max(report_numbers) if report_numbers else 0

def load_existing_reports_data():
    """
    Load existing reports data from reports.json

    Returns:
        list: List of existing report data dictionaries, or empty list if file doesn't exist
    """
    try:
        with open("reports.json", "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def reset_reports():
    """
    Delete all existing reports and reports.json

    Returns:
        bool: True if reset was successful, False otherwise
    """
    try:
        # Create reports directory if it doesn't exist
        os.makedirs("reports", exist_ok=True)

        # Get all txt files in the reports directory
        report_files = [f for f in os.listdir("reports") if f.endswith(".txt")]

        # Create a table for deleted files
        table = Table(title="Deleted Files")
        table.add_column("File", style="cyan")
        table.add_column("Status", style="green")

        # Delete each report file
        for filename in track(report_files, description="Deleting reports..."):
            file_path = os.path.join("reports", filename)
            os.remove(file_path)
            table.add_row(file_path, "✓ Deleted")

        # Delete reports.json if it exists
        if os.path.exists("reports.json"):
            os.remove("reports.json")
            table.add_row("reports.json", "✓ Deleted")

        # Display the table if files were deleted
        if report_files or os.path.exists("reports.json"):
            console.print(table)

        return True
    except Exception as e:
        console.print(Panel(f"[bold red]Error resetting reports: {e}[/bold red]",
                           title="Error", border_style="red"))
        return False

def generate_multiple_reports(num_reports=REPORTS, ticker=TICKER, start_date=START_DATE, append=True, model_name=DEFAULT_MODEL):
    """
    Generate multiple mock reports and save them

    Args:
        num_reports (int): Number of reports to generate
        ticker (str): Stock ticker symbol
        start_date (str): Start date for historical data
        append (bool): Whether to append to existing reports or overwrite
        model_name (str): Name of the model to use (default: DEFAULT_MODEL)

    Returns:
        list: List of report data dictionaries
    """
    # Load actual stock data
    closing_prices, dates = load_stock_data(ticker, start_date)

    # Create reports directory if it doesn't exist
    os.makedirs("reports", exist_ok=True)

    # Get the last report number and existing reports data if appending
    start_index = 0
    reports_data = []

    if append:
        start_index = get_last_report_number()
        reports_data = load_existing_reports_data()
        console.print(Panel(f"[bold cyan]Appending to existing reports. Last report number: {start_index}[/bold cyan]",
                           title="Status", border_style="cyan"))

    # Generate new reports
    new_reports_data = []

    # Create a table for generated reports
    table = Table(title=f"Generated Reports for {ticker}")
    table.add_column("Report #", style="cyan")
    table.add_column("Report Date", style="cyan")
    table.add_column("Current Price", style="yellow")
    table.add_column("Forecast Price", style="green")
    table.add_column("Days Ahead", style="blue")
    table.add_column("Confidence", style="magenta")

    # Generate reports with progress tracking
    for i in track(range(num_reports), description=f"Generating {num_reports} reports..."):
        # Calculate the new report number
        report_number = start_index + i + 1

        # Get a random index for the data
        random_index = random.randint(0, len(closing_prices) - 1)

        # Get the actual price and date at that index
        actual_price_value = float(closing_prices[random_index])
        report_date = dates[random_index]

        # Generate mock price data
        forecast_price, actual_price, days_ahead, confidence, _ = generate_mock_price_data(actual_price_value, dates)

        # Generate the report
        report_content = generate_mock_report(
            ticker,
            actual_price,
            forecast_price,
            days_ahead,
            confidence,
            report_date,
            model_name
        )

        # Save the report to a file
        report_filename = f"reports/{report_number}.txt"
        with open(report_filename, "w") as f:
            f.write(report_content)

        # Calculate target date
        report_date_obj = datetime.strptime(report_date, "%Y-%m-%d")
        target_date = (report_date_obj + timedelta(days=days_ahead)).strftime("%Y-%m-%d")

        # Add report data to the list
        report_data = {
            "report": f"{report_number}.txt",
            "report_date": report_date,
            "forecast_price": forecast_price,
            "actual_price": actual_price,
            "target_date": target_date,
            "days_ahead": days_ahead,
            "confidence": confidence / 100  # Convert to decimal
        }
        new_reports_data.append(report_data)

        # Add row to the table
        direction = "↑" if forecast_price > actual_price else "↓"
        pct_change = abs((forecast_price - actual_price) / actual_price) * 100
        table.add_row(
            f"#{report_number}",
            f"{report_date}",
            f"${actual_price:.2f}",
            f"${forecast_price:.2f} ({pct_change:.2f}% {direction})",
            f"{days_ahead} days",
            f"{confidence}%"
        )

    # Display the table of generated reports
    console.print(table)

    # Combine existing and new reports data if appending
    if append:
        combined_reports_data = reports_data + new_reports_data
    else:
        combined_reports_data = new_reports_data

    # Save the combined report data to a JSON file
    with open("reports.json", "w") as f:
        json.dump(combined_reports_data, f, indent=2)

    # Show summary
    console.print(Panel(
        f"[bold green]Successfully generated {num_reports} reports for {ticker}[/bold green]\n"
        f"[cyan]Total reports: {len(combined_reports_data)}[/cyan]\n"
        f"[yellow]Reports saved to reports/ directory[/yellow]\n"
        f"[magenta]Report data saved to reports.json[/magenta]",
        title="Summary", border_style="green"
    ))

    return new_reports_data

# Run the script
if __name__ == "__main__":
    try:
        # Show welcome banner
        console.print(Panel.fit(
            "[bold cyan]Stock Report Generator[/bold cyan]\n"
            "[yellow]Generate realistic stock analysis reports with LLM[/yellow]",
            border_style="green"
        ))

        # Parse command line arguments
        parser = argparse.ArgumentParser(description="Generate mock stock reports")
        parser.add_argument("--ticker", type=str, default=TICKER, help=f"Stock ticker symbol (default: {TICKER})")
        parser.add_argument("--start-date", type=str, default=START_DATE, help=f"Start date for historical data (default: {START_DATE})")
        parser.add_argument("--add", type=int, help="Number of reports to generate")
        parser.add_argument("--reset", action="store_true", help="Delete all existing reports and start fresh")
        parser.add_argument("--debug", action="store_true", help="Show debug information")
        parser.add_argument("--model", type=str, default=DEFAULT_MODEL,
                           choices=list(MODEL_REGISTRY.keys()),
                           help=f"LLM model to use (default: {DEFAULT_MODEL})")

        args = parser.parse_args()

        # Update variables with command line arguments
        ticker = args.ticker
        start_date = args.start_date
        num_reports = args.add if args.add is not None else REPORTS
        reset_requested = args.reset
        debug = args.debug
        model_name = args.model

        # Handle reset request
        if reset_requested:
            console.print(Panel("[bold yellow]Resetting all reports...[/bold yellow]",
                               title="Reset", border_style="yellow"))
            if reset_reports():
                console.print("[bold green]All reports have been deleted.[/bold green]")
            else:
                console.print("[bold red]Failed to reset reports.[/bold red]")
                sys.exit(1)

        # Show debug information if requested
        if debug:
            console.print(Panel("[bold magenta]Debug Mode Enabled[/bold magenta]",
                               title="Debug", border_style="magenta"))

            # Test the get_last_report_number function
            console.print("[cyan]Checking for existing reports...[/cyan]")
            last_report_number = get_last_report_number()
            console.print(f"Last report number: [bold]{last_report_number}[/bold]")

            # Test loading existing reports data
            existing_reports = load_existing_reports_data()
            console.print(f"Found [bold]{len(existing_reports)}[/bold] existing reports in reports.json")

            # Test generating mock price data
            console.print("\n[cyan]Testing mock price data generation...[/cyan]")
            test_price = 150.0
            forecast, actual, days, conf = generate_mock_price_data(test_price)

            # Create a debug table
            debug_table = Table(title="Mock Price Data Test")
            debug_table.add_column("Parameter", style="cyan")
            debug_table.add_column("Value", style="green")

            debug_table.add_row("Actual Price", f"${actual:.2f}")
            debug_table.add_row("Forecast Price", f"${forecast:.2f}")
            debug_table.add_row("Days Ahead", f"{days}")
            debug_table.add_row("Confidence", f"{conf}%")

            console.print(debug_table)

        # Generate reports if requested
        if num_reports > 0:
            console.print(Panel(
                f"[bold green]Generating {num_reports} reports for {ticker}[/bold green]",
                title="Generation", border_style="green"
            ))

            # Determine whether to append or overwrite
            append = not reset_requested
            if not append:
                console.print("[bold yellow]Starting with fresh reports (no append).[/bold yellow]")

            # Display model information
            console.print(f"[cyan]Using model: [bold]{model_name}[/bold][/cyan]")

            # Generate reports
            new_reports_data = generate_multiple_reports(num_reports, ticker, start_date, append=append, model_name=model_name)

            # Print the latest report as a sample
            latest_report_number = get_last_report_number()
            with open(f"reports/{latest_report_number}.txt", "r") as f:
                report_content = f.read()

                console.print(Panel(
                    Text(report_content, style="bright_blue"),
                    title=f"Latest Report (#{latest_report_number})",
                    border_style="blue",
                    expand=False
                ))

        elif not reset_requested:
            console.print(Panel(
                "[yellow]No reports were generated.[/yellow]\n"
                "[cyan]Use --add <number> to generate reports.[/cyan]",
                title="Info", border_style="yellow"
            ))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()

        console.print(Panel(
            f"[bold red]Error: {e}[/bold red]\n\n"
            f"[dim]{error_details}[/dim]",
            title="Error", border_style="red"
        ))

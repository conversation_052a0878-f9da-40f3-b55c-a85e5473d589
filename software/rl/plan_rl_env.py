#!/usr/bin/env python3
import gymnasium as gym
from gymnasium import spaces
import numpy as np
import json
import os
from typing import Tuple, Optional, Dict, Any
from pathlib import Path

class PlanEvaluationEnv(gym.Env):
    """
    Custom Environment for Reinforcement Learning Agent to evaluate stock analysis plans.
    
    The agent receives a vector embedding of a plan and outputs a quality score.
    It is rewarded based on how well its evaluation matches expert evaluations.
    """
    metadata = {"render_modes": ["human"]}

    def __init__(self, training_data_path: str = "plan_training.json", train_ratio: float = 0.8, is_training: bool = True):
        """Initialize the environment with training data from the specified JSON file."""
        super(PlanEvaluationEnv, self).__init__()
        self.train_ratio = train_ratio
        self.training_data = self._load_training_data(training_data_path, train_ratio, is_training)
        self.current_index = 0
        self.current_plan = None
        
        # Get vector dimension from the first example
        self.vector_dim = len(self.training_data[0]["vector"])
        
        # Define observation space: vector embedding of the plan
        self.observation_space = spaces.Box(
            low=-1.0, high=1.0, shape=(self.vector_dim,), dtype=np.float32
        )
        
        # Define action space: quality score (continuous value between 0 and 100)
        self.action_space = spaces.Box(
            low=0.0, high=100.0, shape=(1,), dtype=np.float32
        )

    def _load_training_data(self, training_data_path: str, train_ratio: float, is_training: bool):
        """Load and split training data."""
        try:
            # Try direct path
            with open(training_data_path, 'r') as f:
                all_data = json.load(f)
        except FileNotFoundError:
            # Try alternative path in software/rl/training/data
            try:
                base_dir = Path(__file__).parent.parent.parent
                alt_path = base_dir / "software" / "rl" / "training" / "data" / Path(training_data_path).name
                with open(alt_path, 'r') as f:
                    all_data = json.load(f)
            except (FileNotFoundError, Exception):
                # Use minimal dataset as fallback
                return [{
                    "plan": "TASK([TodayDateTool, SimpleWorkflowTool], \"AAPL\", \"closing price\", \"next 7 days\")",
                    "vector": [0.0] * 384,
                    "quality_score": 50.0
                }]
        
        # Split data for training/testing
        if len(all_data) == 0:
            return [{
                "plan": "TASK([TodayDateTool], \"AAPL\", \"price\", \"today\")",
                "vector": [0.0] * 384,
                "quality_score": 50.0
            }]
            
        total_examples = len(all_data)
        train_size = int(total_examples * train_ratio)
        
        if is_training:
            return all_data[:train_size] if train_ratio < 1.0 else all_data
        else:
            return all_data[train_size:] if train_ratio < 1.0 else all_data

    def reset(self, seed: Optional[int] = None, options: Optional[dict] = None) -> Tuple[np.ndarray, dict]:
        """Reset environment to the next plan."""
        super().reset(seed=seed)
        
        self.current_plan = self.training_data[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.training_data)
        
        observation = np.array(self.current_plan["vector"], dtype=np.float32)
        info = {
            "plan": self.current_plan.get("plan", ""),
            "quality_score": self.current_plan.get("quality_score", 50.0)
        }
        
        return observation, info

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, dict]:
        """The agent predicts a quality score, compared to the expert evaluation."""
        predicted_score = float(action[0])
        expert_score = self.current_plan.get("quality_score", 50.0)
        
        # Calculate error and reward
        error = abs(predicted_score - expert_score)
        reward = -error / 10.0  # Scale to make rewards between -10 and 0
        
        # Episode is done after one prediction
        terminated = True
        truncated = False
        
        info = {
            "predicted_score": predicted_score,
            "expert_score": expert_score,
            "error": error,
            "plan": self.current_plan.get("plan", "")
        }
        
        observation = np.array(self.current_plan["vector"], dtype=np.float32)
        return observation, reward, terminated, truncated, info

    def render(self):
        """Render the environment state."""
        if self.current_plan is None:
            print("Environment not reset yet")
            return
            
        print(f"Plan: {self.current_plan.get('plan', 'No plan available')}")
        print(f"Expert Score: {self.current_plan.get('quality_score', 50.0):.1f}/100")
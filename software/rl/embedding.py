import json
import os
from sentence_transformers import SentenceTransformer
import numpy as np

def main():
    # Initialize the sentence transformer model
    embedder = SentenceTransformer('all-MiniLM-L6-v2')

    # Load reports.json
    with open('reports.json', 'r') as f:
        reports_data = json.load(f)

    # Create training data with embeddings
    training_data = []

    for item in reports_data:
        report_path = os.path.join('reports', item['report'])

        # Read the report text file
        with open(report_path, 'r') as f:
            report_text = f.read()

        # Generate embedding vector for the report text
        embedding = embedder.encode(report_text)

        # Convert numpy array to list for JSON serialization
        embedding_list = embedding.tolist()

        # Create training item with embedding and other data
        training_item = {
            'vector': embedding_list,
            'forecast_price': item['forecast_price'],
            'actual_price': item['actual_price'],
            'confidence': item['confidence']
        }

        training_data.append(training_item)

    # Save to training.json
    with open('training.json', 'w') as f:
        json.dump(training_data, f, indent=2)

    print(f"Created training.json with {len(training_data)} embedded reports")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# Custom environment for reinforcement learning based on plan_rl_env.py

import gymnasium as gym
from gymnasium import spaces
import numpy as np
import json
from typing import Tuple, Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box
from rich.text import Text

class CustomEnvironment(gym.Env):
    """
    Custom Environment for Reinforcement Learning Agent.

    This environment is based on the PlanEvaluationEnv from plan_rl_env.py.
    It can be customized for your specific reinforcement learning task.
    """
    # This is required to properly inherit from gym.Env
    metadata = {"render_modes": ["human"]}

    def __init__(self, training_data_path: str = "plan_training.json", train_ratio: float = 0.8, is_training: bool = True):
        """
        Initialize the environment with training data from the specified JSON file.

        Args:
            training_data_path: Path to the JSON file containing training data
            train_ratio: Ratio of data to use for training (0.0 to 1.0)
            is_training: Whether this environment is used for training or testing
        """
        super(CustomEnvironment, self).__init__()

        # Store the train_ratio as an attribute
        self.train_ratio = train_ratio
        self.console = Console()
        self.training_data_path = training_data_path
        self.is_training = is_training

        try:
            # Load training data
            with open(training_data_path, 'r') as f:
                all_data = json.load(f)

            # Split data into training and testing sets
            total_examples = len(all_data)
            train_size = int(total_examples * train_ratio)

            if is_training:
                if train_ratio < 1.0:
                    # Use the first train_ratio portion for training
                    self.training_data = all_data[:train_size]
                else:
                    # Use all data for training
                    self.training_data = all_data
                print(f"Using {len(self.training_data)} examples for training")
            else:
                # Use the remaining portion for testing
                if train_ratio < 1.0:
                    self.training_data = all_data[train_size:]
                    print(f"Using {len(self.training_data)} examples for testing")
                else:
                    # If train_ratio is 1.0, use the same data for testing
                    self.training_data = all_data
                    print(f"Using {len(self.training_data)} examples for testing (same as training)")
        except FileNotFoundError:
            # If the file doesn't exist, create a minimal dataset for initialization
            self.console.print(f"[yellow]Warning: {training_data_path} not found. Using minimal dataset for initialization.[/]")
            self.training_data = [
                {
                    "data": "Example data point",
                    "vector": [0.0] * 384,  # Placeholder vector
                    "quality_score": 50.0   # Neutral score
                }
            ]

        self.current_index = 0

        # Get vector dimension from the first example
        self.vector_dim = len(self.training_data[0]["vector"])

        # Define observation space: vector embedding of the data
        self.observation_space = spaces.Box(
            low=-1.0,
            high=1.0,
            shape=(self.vector_dim,),
            dtype=np.float32
        )

        # Define action space: quality score (continuous value between 0 and 100)
        self.action_space = spaces.Box(
            low=0.0,
            high=100.0,
            shape=(1,),
            dtype=np.float32
        )

        # Initialize current data point
        self.current_data = None

    def reset(self, seed: Optional[int] = None, options: Optional[dict] = None) -> Tuple[np.ndarray, dict]:
        """
        Reset environment to the next data point.

        Returns:
            observation: The vector embedding of the current data
            info: Additional information
        """
        super().reset(seed=seed)

        # Get next data point
        self.current_data = self.training_data[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.training_data)

        # Return the vector embedding as observation
        observation = np.array(self.current_data["vector"], dtype=np.float32)

        info = {
            "data": self.current_data.get("data", ""),
            "quality_score": self.current_data.get("quality_score", 50.0)
        }

        return observation, info

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, dict]:
        """
        The agent predicts a quality score (action), and we compare it to the expert evaluation.

        Args:
            action: The predicted quality score as a single-element array

        Returns:
            observation: The same vector embedding (doesn't change within an episode)
            reward: Negative absolute error between prediction and expert evaluation
            terminated: Whether the episode is done
            truncated: Whether the episode was truncated (always False in this env)
            info: Additional information
        """
        # Extract the predicted quality score from the action
        predicted_score = float(action[0])

        # Get the expert evaluation or ground truth score
        expert_score = self.current_data.get("quality_score", 50.0)

        # Calculate error and reward
        error = abs(predicted_score - expert_score)

        # Reward is negative error (higher is better)
        # Scale to make the reward more meaningful
        reward = -error / 10.0  # Divide by 10 to make rewards between -10 and 0

        # Episode is done after one prediction
        terminated = True
        truncated = False

        info = {
            "predicted_score": predicted_score,
            "expert_score": expert_score,
            "error": error,
            "data": self.current_data.get("data", "")
        }

        # Return the same observation (doesn't change within an episode)
        observation = np.array(self.current_data["vector"], dtype=np.float32)

        return observation, reward, terminated, truncated, info

    def render(self):
        """
        Render the environment with rich panels for better visualization.
        """
        if self.current_data is None:
            self.console.print(Panel("Environment not reset yet",
                               title="Custom Environment",
                               border_style="red"))
            return

        # Create a table for the data
        table = Table(box=box.ROUNDED)
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")

        # Add rows with data
        data_text = self.current_data.get("data", "No data available")
        table.add_row("Data", Text(data_text, style="bright_blue"))

        expert_score = self.current_data.get("quality_score", 50.0)
        score_style = "green" if expert_score >= 80 else "yellow" if expert_score >= 50 else "red"
        table.add_row("Expert Score", Text(f"{expert_score:.1f}/100", style=score_style))

        # Display the table in a panel
        self.console.print(Panel(table,
                           title="Environment Analysis",
                           border_style="blue"))


# Example usage
if __name__ == "__main__":
    from stable_baselines3.common.env_checker import check_env

    # Create and check the environment
    env = CustomEnvironment()

    # Validate the environment
    print("Checking environment...")
    check_env(env)
    print("Environment check passed!")

    # Test the environment
    obs, info = env.reset()
    print(f"Initial observation shape: {obs.shape}")

    for i in range(3):
        action = env.action_space.sample()  # Random action
        obs, reward, terminated, truncated, info = env.step(action)
        print(f"Step {i+1}: Action={action[0]:.1f}, Reward={reward:.2f}")
        env.render()

        if terminated or truncated:
            print("Environment reset")
            obs, info = env.reset()

import logging
import os
import json
from typing import TypedDict, List, Dict, Any, Annotated

# Set up logging before imports
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    import pandas as pd
    import numpy as np
    from langgraph.graph import StateGraph, END
    from langchain_groq import ChatGroq
    from dotenv import load_dotenv
except ImportError as e:
    logging.error(f"Failed to import required module: {e}")
    raise

from main import StockAnalysisApp
from data import DATA_LOADER_CLASSES
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from utils.constants import DEFAULT_TICKER
from utils.console_output import ConsoleOutput
from utils.report_utils import calculate_model_score

# Load environment variables
load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")

# Initialize LLM
llm = ChatGroq(model_name="llama-3.2-11b-vision-preview", temperature=0, api_key=GROQ_API_KEY)

class GraphState(TypedDict):
    app: StockAnalysisApp
    current_node: str
    results: List[Dict[str, Any]]
    report: str
    targets_and_horizons: List[Dict[str, Any]]

class Researcher:
    @staticmethod
    def identify_targets_and_horizons(state: GraphState) -> GraphState:
        app = state.get("app")
        if not app:
            ConsoleOutput.print_error("StockAnalysisApp not found in state.")
            state["targets_and_horizons"] = []
            return state

        data = app.data
        ConsoleOutput.print_debug(f"Data columns: {data.columns.tolist()}")

        if data is None or data.empty:
            ConsoleOutput.print_error("No data available for analysis.")
            state["targets_and_horizons"] = []
            return state

        columns = data.columns.tolist()
        feature_descriptions = {col: FEATURE_CLASSES[col]().get_description() if col in FEATURE_CLASSES else "No description available" for col in columns}
        context = "Available columns and their descriptions:\n"
        for col, desc in feature_descriptions.items():
            context += f"{col}: {desc}\n"

        prompt = (
            f"{context}\n\n"
            "Based on the available columns and their descriptions, identify suitable predictor targets and time horizons for stock analysis. "
            "Provide the response in JSON format with the following structure:\n"
            "[{\"target\": \"column_name\", \"horizon\": int}, ...]\n"
            "Consider various time horizons (e.g., 1, 5, 30 days) for each target."
        )

        retry_attempts = 3
        for attempt in range(retry_attempts):
            try:
                response = llm.invoke(prompt)
                targets_and_horizons = json.loads(response.content)
                state["targets_and_horizons"] = targets_and_horizons
                ConsoleOutput.print_debug(f"Identified targets and horizons: {targets_and_horizons}")
                break
            except json.JSONDecodeError:
                ConsoleOutput.print_error("Failed to parse Researcher's response.")
                state["targets_and_horizons"] = []
                break
            except Exception as e:
                ConsoleOutput.print_warning(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt == retry_attempts - 1:
                    ConsoleOutput.print_error("Max retry attempts reached. Unable to invoke LLM.")
                    state["targets_and_horizons"] = []

        state["app"] = app
        state["current_node"] = "run_models"
        return state

class ModelRunner:
    @staticmethod
    def run_models(state: GraphState) -> GraphState:
        app = state.get("app")
        if not app:
            ConsoleOutput.print_error("StockAnalysisApp not found in state.")
            state["results"] = []
            return state

        targets_and_horizons = state.get("targets_and_horizons", [])

        results = []
        for config in targets_and_horizons:
            target = config.get("target")
            horizon = config.get("horizon")

            if not target or not isinstance(horizon, int):
                ConsoleOutput.print_warning(f"Invalid target or horizon: {config}")
                continue

            for model_class in MODEL_CLASSES:
                try:
                    ConsoleOutput.print_info(f"Running {model_class.__name__} for {target} with horizon {horizon}")
                    result, metrics = app.run_model(model_class, predict=str(target), forecast_horizon=horizon)
                    score = calculate_model_score(result, target)['Composite Score']

                    results.append({
                        'model': model_class.__name__,
                        'target': target,
                        'horizon': horizon,
                        'result': result,
                        'metrics': metrics,
                        'score': score
                    })
                except Exception as e:
                    ConsoleOutput.print_error(f"Error running {model_class.__name__} for {target}: {str(e)}")

        results.sort(key=lambda x: x['score'], reverse=True)
        state["results"] = results
        state["app"] = app
        state["current_node"] = "generate_report"
        return state

class ReportGenerator:
    @staticmethod
    def generate_report(state: GraphState) -> GraphState:
        results = state.get("results", [])
        app = state.get("app")
        if not app:
            ConsoleOutput.print_error("StockAnalysisApp not found in state.")
            state["report"] = "Error: Unable to generate report due to missing app data."
            return state

        ticker = app.ticker
        top_models = results[:5]

        report = f"Stock Analysis Report for {ticker}\n\n"
        report += "Top Performing Models:\n\n"

        for model in top_models:
            report += (
                f"Model: {model['model']}\n"
                f"Target: {model['target']}\n"
                f"Horizon: {model['horizon']} days\n"
                f"Score: {model['score']:.4f}\n"
                f"Metrics: {json.dumps(model['metrics'], indent=2)}\n\n"
            )

        state["report"] = report
        state["app"] = app
        state["current_node"] = END
        return state

class StockAnalysisWorkflow:
    def __init__(self, app: StockAnalysisApp):
        self.app = app
        self.workflow = StateGraph(Annotated[GraphState, "state"])

    def setup_workflow(self):
        self.workflow.add_node("identify_targets_and_horizons", Researcher.identify_targets_and_horizons)
        self.workflow.add_node("run_models", ModelRunner.run_models)
        self.workflow.add_node("generate_report", ReportGenerator.generate_report)

        self.workflow.add_edge("identify_targets_and_horizons", "run_models")
        self.workflow.add_edge("run_models", "generate_report")

        self.workflow.set_entry_point("identify_targets_and_horizons")

    def run(self):
        try:
            graph = self.workflow.compile()
        except ValueError as e:
            ConsoleOutput.print_error(f"Error compiling workflow: {str(e)}")
            logging.error(f"Workflow compilation error: {str(e)}", exc_info=True)
            return None

        initial_state: GraphState = {
            "app": self.app,
            "current_node": "identify_targets_and_horizons",
            "results": [],
            "report": "",
            "targets_and_horizons": []
        }

        ConsoleOutput.print_debug(f"Initial state: {initial_state}")
        final_output = None

        try:
            for output in graph.stream(initial_state):
                if isinstance(output, dict):
                    ConsoleOutput.print_debug(f"Current state during workflow: {output}")
                    if "current_node" in output:
                        ConsoleOutput.print_info(f"Completed node: {output['current_node']}")
                final_output = output
        except Exception as e:
            ConsoleOutput.print_error(f"Error during workflow execution: {str(e)}")
            logging.error(f"Workflow execution error: {str(e)}", exc_info=True)

        return final_output

def get_compatible_features(data):
    compatible_features = []
    for feature_name, feature_class in FEATURE_CLASSES.items():
        try:
            feature = feature_class()
            feature.calculate(data)
            compatible_features.append(feature_name)
            ConsoleOutput.print_debug(f"Added feature: {feature_name}")
        except Exception as e:
            ConsoleOutput.print_warning(f"Feature {feature_name} is not compatible: {str(e)}")
    return compatible_features

def run_analysis_loop(ticker=DEFAULT_TICKER):
    app = StockAnalysisApp(ticker)
    app_initialized = False

    for loader_name, loader_class in DATA_LOADER_CLASSES.items():
        ConsoleOutput.print_info(f"Using data loader: {loader_name}")

        try:
            app.load_data(loader_class)
        except Exception as e:
            ConsoleOutput.print_warning(f"Error loading data with {loader_name}: {str(e)}. Skipping.")
            continue

        if app.data is None or app.data.empty:
            ConsoleOutput.print_warning(f"No data loaded with {loader_name}. Skipping.")
            continue

        compatible_features = get_compatible_features(app.data)
        if not compatible_features:
            ConsoleOutput.print_warning(f"No compatible features found for {loader_name}. Skipping.")
            continue

        try:
            app.add_features(compatible_features)
            app_initialized = True
        except Exception as e:
            ConsoleOutput.print_warning(f"Error adding features: {str(e)}. Skipping this loader.")
            continue

        if app_initialized:
            workflow = StockAnalysisWorkflow(app)
            workflow.setup_workflow()
            ConsoleOutput.print_debug(f"Workflow initialized with app: {app}")

            final_output = workflow.run()

            if isinstance(final_output, dict):
                ConsoleOutput.print_debug(f"Final workflow output: {final_output}")
                if final_output.get("report"):
                    print("\nFinal Report:")
                    print(final_output["report"])
                else:
                    ConsoleOutput.print_warning("No report generated.")
            break

    if not app_initialized:
        ConsoleOutput.print_error("No valid data loader succeeded. Unable to run analysis.")

def main():
    try:
        run_analysis_loop(DEFAULT_TICKER)
    except Exception as e:
        ConsoleOutput.print_error(f"An error occurred: {str(e)}")
        logging.error(f"An error occurred: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()

from jinja2 import Template
from utils.report_utils import calculate_model_score, format_value
import json
import logging
import pandas as pd

class LightweightReportGenerator:
    def __init__(self, data, ticker, model_evaluation):
        self.data = data.sort_index(ascending=True)
        self.ticker = ticker
        self.model_evaluation = model_evaluation
        self.target_col = self._get_target_column()
        logging.info(f"Columns in data: {self.data.columns.tolist()}")
        logging.info(f"Target column: {self.target_col}")
        logging.info(f"Data types: {self.data.dtypes}")

    def _get_target_column(self):
        predicted_cols = [col for col in self.data.columns if col.endswith('_Predicted')]
        return predicted_cols[0].replace('_Predicted', '') if predicted_cols else None

    def prepare_chart_data(self):
        if not self.target_col:
            logging.warning("No target column found.")
            return None

        try:
            # Use all numeric columns except the predicted column
            numeric_cols = self.data.select_dtypes(include=['float64', 'int64']).columns
            price_columns = [col for col in numeric_cols if col != f'{self.target_col}_Predicted']

            logging.info(f"Price columns: {price_columns}")

            if not price_columns:
                logging.warning("No numeric columns found for chart data.")
                return None

            chart_data = self.data.reset_index()
            chart_data['time'] = chart_data['index'].astype(str)

            # Ensure we have 'actual' and 'predicted' columns
            if self.target_col in chart_data.columns:
                chart_data['actual'] = chart_data[self.target_col]
            if f'{self.target_col}_Predicted' in chart_data.columns:
                chart_data['predicted'] = chart_data[f'{self.target_col}_Predicted']

            # Prepare the final data columns
            final_columns = ['time'] + price_columns + ['predicted']
            if 'actual' in chart_data.columns:
                final_columns.append('actual')

            logging.info(f"Final columns for chart data: {final_columns}")
            logging.info(f"Chart data columns: {chart_data.columns.tolist()}")

            result = chart_data[final_columns].to_dict('records')
            logging.info(f"Number of records in chart data: {len(result)}")
            return result
        except Exception as e:
            logging.error(f"Error in prepare_chart_data: {str(e)}")
            return None

    def generate_html_report(self):
        chart_data = self.prepare_chart_data()
        if chart_data is None:
            logging.error("Failed to prepare chart data.")
            return "Error: Unable to generate report due to missing data."

        model_scores = calculate_model_score(self.data, self.target_col) if self.target_col else {}

        # Determine if we have multiple price columns (potential OHLC data)
        price_columns = [col for col in self.data.columns if col not in [self.target_col, f'{self.target_col}_Predicted'] and col != 'index']
        has_multiple_price_cols = len(price_columns) > 1
        logging.info(f"Has multiple price columns: {has_multiple_price_cols}")

        template = Template('''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ ticker }} Stock Report</title>
            <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
                h1, h2 { color: #2c3e50; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                #chart { height: 400px; }
            </style>
        </head>
        <body>
            <h1>{{ ticker }} Stock Report</h1>

            {% if chart_data %}
            <h2>Price Comparison</h2>
            <div id="chart"></div>
            <script>
                const chart = LightweightCharts.createChart(document.getElementById('chart'), {
                    width: 800,
                    height: 400,
                    layout: {
                        backgroundColor: '#ffffff',
                        textColor: 'rgba(33, 56, 77, 1)',
                    },
                    grid: {
                        vertLines: {
                            color: 'rgba(197, 203, 206, 0.5)',
                        },
                        horzLines: {
                            color: 'rgba(197, 203, 206, 0.5)',
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: 'rgba(197, 203, 206, 1)',
                    },
                    timeScale: {
                        borderColor: 'rgba(197, 203, 206, 1)',
                    },
                });

                {% if has_multiple_price_cols %}
                const candleSeries = chart.addCandlestickSeries({ upColor: '#26a69a', downColor: '#ef5350', borderVisible: false, wickUpColor: '#26a69a', wickDownColor: '#ef5350' });
                candleSeries.setData({{ chart_data | tojson | safe }}.map(d => ({
                    time: d.time,
                    open: d.Open || d.open || d.actual,
                    high: d.High || d.high || d.actual,
                    low: d.Low || d.low || d.actual,
                    close: d.Close || d.close || d.actual
                })));
                {% else %}
                const priceSeries = chart.addLineSeries({ color: 'rgba(0, 150, 136, 1)', lineWidth: 2 });
                priceSeries.setData({{ chart_data | tojson | safe }}.map(d => ({
                    time: d.time,
                    value: d.actual || d.Open || d.Close || Object.values(d).find(v => typeof v === 'number' && !isNaN(v))
                })));
                {% endif %}

                const predictedSeries = chart.addLineSeries({ color: 'rgba(255, 99, 71, 1)', lineWidth: 2 });
                predictedSeries.setData({{ chart_data | tojson | safe }}.map(d => ({ time: d.time, value: d.predicted })));

                chart.timeScale().fitContent();
            </script>
            {% endif %}

            {% if model_scores %}
            <h2>Model Performance</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                {% for metric, value in model_scores.items() %}
                <tr><td>{{ metric }}</td><td>{{ value }}</td></tr>
                {% endfor %}
            </table>
            {% endif %}

            <h2>Recent Data</h2>
            <table>
                <tr>
                    {% for column in recent_data.columns %}
                    <th>{{ column }}</th>
                    {% endfor %}
                </tr>
                {% for _, row in recent_data.iterrows() %}
                <tr>
                    {% for value in row %}
                    <td>{{ value }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </table>
        </body>
        </html>
        ''')

        recent_data = self.data.tail(10).reset_index()
        for col in recent_data.columns:
            recent_data[col] = recent_data[col].apply(format_value)

        html_content = template.render(
            ticker=self.ticker,
            chart_data=chart_data,
            model_scores=model_scores,
            recent_data=recent_data,
            has_multiple_price_cols=has_multiple_price_cols
        )

        return html_content

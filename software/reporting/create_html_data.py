import os
import webbrowser
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import base64
from io import BytesIO
from utils.console_output import ConsoleOutput
from utils.report_utils import format_value, calculate_model_score
from statsmodels.tsa.seasonal import seasonal_decompose

class StockReportGenerator:
    def __init__(self, data, ticker, model_evaluation):
        self.data = data.sort_index(ascending=True)
        self.ticker = ticker
        self.model_evaluation = model_evaluation
        self.target_col = self._get_target_column()
        self.future_data = self._extract_future_data()
        self.has_predictions = self.target_col is not None

    def _get_target_column(self):
        predicted_cols = [col for col in self.data.columns if col.endswith('_Predicted')]
        return predicted_cols[0].replace('_Predicted', '') if predicted_cols else None

    def _extract_future_data(self):
        if self.target_col:
            last_actual_date = self.data[self.target_col].last_valid_index()
            return self.data.loc[last_actual_date:].copy()
        return pd.DataFrame()

    def create_comparison_plot(self):
        if not self.has_predictions:
            return None

        plt.figure(figsize=(12, 6))
        actual_data = self.data[self.data[self.target_col].notna()]
        predicted_col = f'{self.target_col}_Predicted'

        plt.plot(actual_data.index, actual_data[self.target_col], label='Actual', color='blue')
        plt.plot(self.data.index, self.data[predicted_col], label='Predicted', color='red')

        plt.title(f'Actual vs Predicted {self.target_col}')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        img = BytesIO()
        plt.savefig(img, format='png')
        img.seek(0)
        return base64.b64encode(img.getvalue()).decode()

    def create_residual_plot(self):
        if not self.has_predictions:
            return None

        actual_data = self.data[self.data[self.target_col].notna()]
        residuals = actual_data[self.target_col] - actual_data[f'{self.target_col}_Predicted']

        plt.figure(figsize=(12, 6))
        sns.scatterplot(x=actual_data[self.target_col], y=residuals)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.title('Residual Plot')
        plt.xlabel('Actual Values')
        plt.ylabel('Residuals')
        plt.tight_layout()

        img = BytesIO()
        plt.savefig(img, format='png')
        img.seek(0)
        return base64.b64encode(img.getvalue()).decode()

    def create_seasonal_decomposition_plot(self):
        if not self.has_predictions:
            return None

        actual_data = self.data[self.data[self.target_col].notna()]
        try:
            decomposition = seasonal_decompose(actual_data[self.target_col], model='additive', period=30)
        except ValueError:
            ConsoleOutput.print_warning("Unable to create seasonal decomposition plot. Insufficient data.")
            return None

        fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=(12, 16))
        decomposition.observed.plot(ax=ax1)
        ax1.set_title('Observed')
        decomposition.trend.plot(ax=ax2)
        ax2.set_title('Trend')
        decomposition.seasonal.plot(ax=ax3)
        ax3.set_title('Seasonal')
        decomposition.resid.plot(ax=ax4)
        ax4.set_title('Residual')
        plt.tight_layout()

        img = BytesIO()
        plt.savefig(img, format='png')
        img.seek(0)
        return base64.b64encode(img.getvalue()).decode()

    def generate_html_report(self):
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        parent_dir = os.path.dirname(project_dir)
        html_dir = os.path.join(parent_dir, 'html_reports')
        os.makedirs(html_dir, exist_ok=True)
        file_path = os.path.join(html_dir, f"{self.ticker}_report.html")

        headers = ['Date'] + list(self.data.columns)
        table_data = self.data.reset_index().values.tolist()

        if self.has_predictions:
            comparison_plot = self.create_comparison_plot()
            residual_plot = self.create_residual_plot()
            seasonal_plot = self.create_seasonal_decomposition_plot()
            model_scores = calculate_model_score(self.data, self.target_col)
        else:
            comparison_plot = None
            residual_plot = None
            seasonal_plot = None
            model_scores = None

        html_content = self._generate_html_content(headers, table_data, comparison_plot, residual_plot, seasonal_plot, model_scores)

        with open(file_path, 'w') as f:
            f.write(html_content)

        ConsoleOutput.print_success(f"Created HTML report: {file_path}")
        webbrowser.open('file://' + os.path.realpath(file_path))

        return file_path

    def _generate_html_content(self, headers, table_data, comparison_plot, residual_plot, seasonal_plot, model_scores):
        analysis_content = ""
        if self.has_predictions:
            analysis_content = f"""
                <div id="analysis" class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-2xl font-bold mb-4">Prediction Analysis</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Model Performance Metrics</h3>
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th class="text-left">Metric</th>
                                        <th class="text-left">Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {''.join(f"<tr><td>{k}</td><td>{format_value(v)}</td></tr>" for k, v in model_scores.items())}
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Composite Score</h3>
                            <div class="text-4xl font-bold text-blue-600">{model_scores['Composite Score']:.2f}/100</div>
                        </div>
                    </div>
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-2">Actual vs Predicted {self.target_col}</h3>
                        <img src="data:image/png;base64,{comparison_plot}" alt="Actual vs Predicted Plot" class="w-full">
                    </div>
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-2">Residual Plot</h3>
                        <img src="data:image/png;base64,{residual_plot}" alt="Residual Plot" class="w-full">
                    </div>
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-2">Seasonal Decomposition</h3>
                        <img src="data:image/png;base64,{seasonal_plot}" alt="Seasonal Decomposition Plot" class="w-full">
                    </div>
                </div>
            """

        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{self.ticker} Stock Report</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                body {{ font-family: 'Inter', sans-serif; }}
            </style>
        </head>
        <body class="bg-gray-100">
            <div class="container mx-auto px-4 py-8">
                <h1 class="text-3xl font-bold mb-4">{self.ticker} Stock Report</h1>

                <div class="mb-4">
                    <ul class="flex border-b">
                        {'<li class="mr-1"><a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold" href="#analysis">Analysis</a></li>' if self.has_predictions else ''}
                        <li class="-mb-px mr-1">
                            <a class="bg-white inline-block border-l border-t border-r rounded-t py-2 px-4 text-blue-700 font-semibold" href="#data">Data</a>
                        </li>
                    </ul>
                </div>

                {analysis_content}

                <div id="data" class="bg-white p-6 rounded-lg shadow-md mt-4">
                    <table id="stockTable" class="stripe hover" style="width:100%">
                        <thead>
                            <tr>
                                {''.join(f'<th>{header}</th>' for header in headers)}
                            </tr>
                        </thead>
                        <tbody>
                            {''.join(
                                f'<tr>' +
                                ''.join(f'<td>{format_value(value)}</td>' for value in row) +
                                '</tr>'
                                for row in table_data
                            )}
                        </tbody>
                    </table>
                </div>
            </div>

            <script>
                $(document).ready(function() {{
                    $('#stockTable').DataTable({{
                        order: [[0, 'desc']],
                        pageLength: 25,
                        scrollX: true
                    }});

                    $('a[href="#data"]').click(function(e) {{
                        e.preventDefault();
                        $('#data').removeClass('hidden');
                        $('#analysis').addClass('hidden');
                        $('a[href="#data"]').addClass('text-blue-700').removeClass('text-blue-500');
                        $('a[href="#analysis"]').addClass('text-blue-500').removeClass('text-blue-700');
                    }});

                    $('a[href="#analysis"]').click(function(e) {{
                        e.preventDefault();
                        $('#analysis').removeClass('hidden');
                        $('#data').addClass('hidden');
                        $('a[href="#analysis"]').addClass('text-blue-700').removeClass('text-blue-500');
                        $('a[href="#data"]').addClass('text-blue-500').removeClass('text-blue-700');
                    }});

                    // Set default tab
                    {'$("a[href=\'#analysis\']").click();' if self.has_predictions else '$("a[href=\'#data\']").click();'}
                }});
            </script>
        </body>
        </html>
        """

def create_html_file(data, ticker, model_evaluation):
    report_generator = StockReportGenerator(data, ticker, model_evaluation)
    return report_generator.generate_html_report()

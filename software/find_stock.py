import logging
import requests
from io import StringIO
import pandas as pd
from bs4 import BeautifulSoup
from main import StockAnalysisApp
from utils.constants import DEFAULT_PERIOD, DEFAULT_INTERVAL
from utils.feature_list import DEFAULT_FEATURES
from utils.console_output import ConsoleOutput
from models import *
from data.create_html_data import StockReportGenerator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_sp500_tickers():
    """Fetch S&P 500 tickers from Wikipedia."""
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    table = soup.find('table', {'class': 'wikitable'})
    df = pd.read_html(StringIO(str(table)))[0]
    return df['Symbol'].tolist()

def analyze_stock(ticker):
    """Analyze a single stock and return its Model Performance Score for Open."""
    try:
        app = StockAnalysisApp(ticker)
        app.load_data(period=DEFAULT_PERIOD, interval=DEFAULT_INTERVAL)
        app.add_features(DEFAULT_FEATURES)
        result, metrics = app.run_model(MLModel, predict='Open', forecast_horizon=14)

        # Calculate model score
        report_generator = StockReportGenerator(result, ticker, metrics)
        scores = report_generator.calculate_model_score()

        return scores, app
    except Exception as e:
        logging.error(f"Error analyzing {ticker}: {str(e)}")
        return None, None

def find_high_performing_stocks():
    tickers = get_sp500_tickers()
    auto_generate = False

    for ticker in tickers:
        ConsoleOutput.print_info(f"Analyzing {ticker}...")
        scores, app = analyze_stock(ticker)

        if scores is not None and scores['Composite Score'] > 99.90:
            ConsoleOutput.print_success(f"High-performing stock found: {ticker} with Composite Score: {scores['Composite Score']:.2f}")

            for metric, value in scores.items():
                ConsoleOutput.print_info(f"{metric}: {value:.4f}")

            if auto_generate:
                report_path = app.generate_report()
                ConsoleOutput.print_success(f"Report automatically generated: {report_path}")
            else:
                while True:
                    user_input = input("Generate report for this stock? (yes/no/skip/cancel/yes all): ").lower()
                    if user_input == 'yes':
                        report_path = app.generate_report()
                        ConsoleOutput.print_success(f"Report generated: {report_path}")
                        break
                    elif user_input == 'yes all':
                        auto_generate = True
                        report_path = app.generate_report()
                        ConsoleOutput.print_success(f"Report generated: {report_path}")
                        ConsoleOutput.print_info("Auto-generating reports for all future high-performing stocks.")
                        break
                    elif user_input in ['no', 'skip']:
                        break
                    elif user_input == 'cancel':
                        return
                    else:
                        ConsoleOutput.print_warning("Invalid input. Please enter 'yes', 'no', 'skip', 'cancel', or 'yes all'.")

        if scores is not None:
            del app  # Clean up to save memory

def main():
    find_high_performing_stocks()

if __name__ == "__main__":
    main()

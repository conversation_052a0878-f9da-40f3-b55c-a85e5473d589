[tool.poetry]
name = "vero"
version = "0.1.0"
description = "Vero AI Research Assistant"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.13"
groq = "^0.13.1"
python-dotenv = "^1.0.0"
wikipedia = "^1.4.0"
langgraph = "^0.2.60"
langchain-core = "^0.3.28"
langchain-openai = "^0.2.14"
langchain-community = "^0.3.13"
tavily-python = "^0.5.0"
backoff = "^2.2.1"
langchain-groq = "^0.2.2"
langchain-google-genai = "^2.0.7"
motor = "3.6.0"
pymongo = "4.9.0"
python-multipart = "^0.0.20"
langchain-nvidia-ai-endpoints = "^0.3.7"
pandas-datareader = "^0.10.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

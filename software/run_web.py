import os
import sys
from pathlib import Path

# Add the software directory to Python path
software_dir = Path(__file__).parent
project_root = software_dir.parent  # Parent directory of software
sys.path.append(str(project_root))  # Add project root to path
sys.path.append(str(software_dir))  # Add software dir to path (for backward compatibility)

import uvicorn
from api.main import app
from api.database import database
from db.research_repository import ResearchRepository
import os
from dotenv import load_dotenv
from utils.console_output import ConsoleOutput
import subprocess
import time
import webbrowser
import logging

# Load environment variables
load_dotenv()

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%H:%M:%S',
    handlers=[logging.StreamHandler()]
)

# Disable Uvicorn's default access logger
logging.getLogger("uvicorn.access").disabled = True

def start_mongodb():
    """
    Start the MongoDB server if local instance is configured.
    Falls back to external MongoDB if local startup fails.
    """
    # Check if external MongoDB is configured
    if os.getenv("MONGO_URI"):
        ConsoleOutput.print_info("Using external MongoDB instance (MONGO_URI configured)")
        return True

    home_dir = os.path.expanduser("~")
    default_db_path = os.path.join(home_dir, "mongodb_data")
    db_path = os.getenv("MONGO_DB_PATH", default_db_path)

    # Ensure the data directory exists
    if not os.path.exists(db_path):
        os.makedirs(db_path)
        ConsoleOutput.print_info(f"Created MongoDB data directory at {db_path}")

    try:
        ConsoleOutput.print_info("Attempting to start local MongoDB server...")
        log_path = os.path.join(db_path, "mongodb.log")
        
        # Check if mongod is available
        try:
            subprocess.run(['mongod', '--version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            ConsoleOutput.print_warning("Local MongoDB not installed. Using external MongoDB instance.")
            return False

        # Start MongoDB server
        with open(os.devnull, 'w') as devnull:
            process = subprocess.Popen([
                'mongod',
                '--dbpath', db_path,
                '--quiet',
                '--logpath', log_path,
                '--logappend',
                '--timeStampFormat', 'iso8601-local'
            ], stdout=devnull, stderr=devnull)
            
        # Wait for MongoDB to start and check if process is still running
        time.sleep(2)
        if process.poll() is None:  # Process is still running
            ConsoleOutput.print_success("Local MongoDB server started successfully")
            return True
        else:
            ConsoleOutput.print_warning("Local MongoDB failed to start. Using external MongoDB instance.")
            return False

    except Exception as e:
        ConsoleOutput.print_warning(f"Local MongoDB startup error: {str(e)}")
        ConsoleOutput.print_info("Falling back to external MongoDB instance")
        return False

def run_web_server():
    """Run the web server using Uvicorn."""
    try:
        # Configure Uvicorn settings
        config = uvicorn.Config(
            "api.main:app",
            host="0.0.0.0",
            port=5001,
            reload=True,
            reload_dirs=[
                os.path.join(os.getcwd(), "api"),
                os.path.join(os.getcwd(), "frontend")
            ]
        )

        # Create and start server
        ConsoleOutput.print_info(f"Starting server on {config.host}:{config.port}")
        server = uvicorn.Server(config)
        server.run()

    except Exception as e:
        ConsoleOutput.print_error(f"Failed to start web server: {str(e)}")
        raise

if __name__ == "__main__":
    # Try to start MongoDB (local or external)
    mongodb_started = start_mongodb()
    
    # Start the web server
    run_web_server()

#!/usr/bin/env python3
# update_db_env.py - Update environment code in the database

import asyncio
import os
import sys
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB connection
MONGO_URI = os.environ.get("MONGO_URI", "mongodb://localhost:27017")
DB_NAME = os.environ.get("DB_NAME", "vero")

async def update_environment_code():
    """Update the environment code in the database"""
    try:
        # Connect to MongoDB
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db.rl_environments
        
        # Get all environments
        environments = await collection.find().to_list(length=None)
        
        for env in environments:
            env_id = env["_id"]
            env_name = env["name"]
            
            logger.info(f"Updating environment: {env_name} ({env_id})")
            
            # Check if it's the plan evaluation environment
            if "plan" in env_name.lower():
                # Update the code to handle training_data_path correctly
                code = env["code"]
                
                # Add debug prints
                if "def __init__" in code:
                    # Add debug prints to __init__
                    code = code.replace(
                        "def __init__(self, training_data_path: str = \"plan_training.json\", train_ratio: float = 0.8, is_training: bool = True):",
                        """def __init__(self, training_data_path: str = "plan_training.json", train_ratio: float = 0.8, is_training: bool = True):
        # Print debug information
        print(f"Initializing PlanEvaluationEnv with:")
        print(f"  - training_data_path: {training_data_path}")
        print(f"  - train_ratio: {train_ratio}")
        print(f"  - is_training: {is_training}")"""
                    )
                
                # Update the code to handle file not found better
                if "except FileNotFoundError:" in code:
                    # Replace the file not found handler
                    code = code.replace(
                        """        except FileNotFoundError:
            # If the file doesn't exist, create a minimal dataset for initialization
            self.console.print(f"[yellow]Warning: {training_data_path} not found. Using minimal dataset for initialization.[/]")
            self.training_data = [
                {
                    "plan": "TASK([TodayDateTool, SimpleWorkflowTool], \\"AAPL\\", \\"closing price\\", \\"next 7 days\\")",
                    "vector": [0.0] * 384,  # Placeholder vector
                    "quality_score": 50.0   # Neutral score
                }
            ]""",
                        """        except FileNotFoundError:
            # If the file doesn't exist, create a minimal dataset for initialization
            self.console.print(f"[yellow]Warning: {training_data_path} not found. Using minimal dataset for initialization.[/]")
            
            # Try to find the file in the software/rl/training/data directory
            import os
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            alternative_path = os.path.join(base_dir, "software", "rl", "training", "data", os.path.basename(training_data_path))
            
            print(f"Trying alternative path: {alternative_path}")
            
            try:
                # Try to load from the alternative path
                with open(alternative_path, 'r') as f:
                    all_data = json.load(f)
                
                print(f"Successfully loaded training data from alternative path: {alternative_path}")
                
                # Split data into training and testing sets
                total_examples = len(all_data)
                train_size = int(total_examples * train_ratio)
                
                if is_training:
                    if train_ratio < 1.0:
                        # Use the first train_ratio portion for training
                        self.training_data = all_data[:train_size]
                    else:
                        # Use all data for training
                        self.training_data = all_data
                    print(f"Using {len(self.training_data)} examples for plan evaluation training")
                else:
                    # Use the remaining portion for testing
                    if train_ratio < 1.0:
                        self.training_data = all_data[train_size:]
                        print(f"Using {len(self.training_data)} examples for plan evaluation testing")
                    else:
                        # If train_ratio is 1.0, use the same data for testing
                        self.training_data = all_data
                        print(f"Using {len(self.training_data)} examples for plan evaluation testing (same as training)")
                
                # Update the training data path
                self.training_data_path = alternative_path
                
                return
            except FileNotFoundError:
                print(f"Alternative path not found: {alternative_path}")
                
            # If all else fails, use a minimal dataset
            self.training_data = [
                {
                    "plan": "TASK([TodayDateTool, SimpleWorkflowTool], \\"AAPL\\", \\"closing price\\", \\"next 7 days\\")",
                    "vector": [0.0] * 384,  # Placeholder vector
                    "quality_score": 50.0   # Neutral score
                }
            ]"""
                    )
                
                # Update the code to store the training data path
                if "try:" in code and "# Load training data" in code:
                    # Add code to store the training data path
                    code = code.replace(
                        """        try:
            # Load training data
            with open(training_data_path, 'r') as f:
                all_data = json.load(f)""",
                        """        try:
            # Store the training data path
            self.training_data_path = training_data_path
            
            # Add debug logging
            print(f"Loading training data from: {training_data_path}")
            
            # Load training data
            with open(training_data_path, 'r') as f:
                all_data = json.load(f)"""
                    )
                
                # Update the environment code in the database
                await collection.update_one(
                    {"_id": env_id},
                    {"$set": {"code": code}}
                )
                
                logger.info(f"Updated environment code for {env_name}")
            
            # Check if it's the compatible environment
            elif "compatible" in env_name.lower():
                # Update the code to handle training_data_path correctly
                code = env["code"]
                
                # Add debug prints to __init__
                if "def __init__" in code:
                    code = code.replace(
                        "def __init__(self, training_data_path: str = None, train_ratio: float = 0.8, is_training: bool = True):",
                        """def __init__(self, training_data_path: str = None, train_ratio: float = 0.8, is_training: bool = True):
        # Print debug information
        print(f"Initializing CompatibleEnvironment with:")
        print(f"  - training_data_path: {training_data_path}")
        print(f"  - train_ratio: {train_ratio}")
        print(f"  - is_training: {is_training}")"""
                    )
                
                # Update the environment code in the database
                await collection.update_one(
                    {"_id": env_id},
                    {"$set": {"code": code}}
                )
                
                logger.info(f"Updated environment code for {env_name}")
        
        logger.info("All environments updated successfully")
    
    except Exception as e:
        logger.error(f"Error updating environment code: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(update_environment_code())

from abc import ABC, abstractmethod
import pandas as pd
import logging
from typing import List, Dict, Any, Optional

class BaseDataLoader(ABC):
    """
    Base class for all data loaders that handle time series data.
    This could be stock data, macroeconomic data, or any other time series data
    that can be used for analysis.
    """
    def __init__(self):
        """
        Initialize the data loader.
        Each specific loader implementation can add its own initialization parameters.
        """
        # Minimum required column is the datetime index
        self.datetime_col = 'Date'
        # Additional columns and their validators specific to each loader
        self.additional_columns: Dict[str, Any] = {}

    @abstractmethod
    def load_historical_data(self) -> pd.DataFrame:
        """
        Load historical time series data.

        Returns:
            pandas.DataFrame: Historical time series data with timezone-naive datetime index.
            The DataFrame must have a DatetimeIndex named 'Date'.
            
        Raises:
            ValueError: If data validation fails
        """
        pass

    def _ensure_timezone_naive(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Ensure the DataFrame has a timezone-naive index named 'Date'.

        Args:
            df (pd.DataFrame): Input DataFrame

        Returns:
            pd.DataFrame: DataFrame with timezone-naive index
        """
        if df.empty:
            empty_df = pd.DataFrame(columns=list(self.additional_columns.keys()))
            empty_df.index = pd.DatetimeIndex([], name=self.datetime_col)
            return empty_df

        if isinstance(df.index, pd.DatetimeIndex):
            if df.index.tz is not None:
                df.index = df.index.tz_localize(None)
            df.index.name = self.datetime_col
        else:
            df.index = pd.DatetimeIndex(df.index, name=self.datetime_col)

        return df

    def _prepare_empty_df(self) -> pd.DataFrame:
        """Create an empty DataFrame with proper structure."""
        return pd.DataFrame(
            index=pd.DatetimeIndex([], name=self.datetime_col),
            columns=pd.Index(list(self.additional_columns.keys()))
        )

    def _validate_data(self, data: pd.DataFrame) -> None:
        """
        Validate the loaded data.
        
        Args:
            data (pd.DataFrame): Data to validate
            
        Raises:
            ValueError: If validation fails
        """
        # Validate index
        if not isinstance(data.index, pd.DatetimeIndex):
            raise ValueError("DataFrame index must be DatetimeIndex")
        
        if data.index.name != self.datetime_col:
            raise ValueError(f"Index must be named '{self.datetime_col}'")

        # Check minimum data points (can be overridden by specific loaders)
        min_points = 100  # Default minimum
        if len(data) < min_points:
            raise ValueError(f"Insufficient data points ({len(data)}). Expected at least {min_points}.")

        # Validate additional columns if any
        for col, validator in self.additional_columns.items():
            if col in data.columns and validator is not None:
                try:
                    validator(data[col])
                except Exception as e:
                    raise ValueError(f"Validation failed for column {col}: {str(e)}")

        # Validate chronological order
        if not data.index.is_monotonic_increasing:
            raise ValueError("Data must be in chronological order")

        # Validate timezone
        if data.index.tz is not None:
            raise ValueError("DatetimeIndex must be timezone-naive")

    @staticmethod
    def get_api_key(loader_name: str, service_name: str) -> Dict[str, Any]:
        """
        Retrieve API key and related information from MongoDB for a specific service.
        """
        try:
            from api.database import get_sync_db
            db = get_sync_db()
            loader = db.data_loaders.find_one({"name": loader_name})
            
            if not loader:
                raise ValueError(f"Data loader '{loader_name}' not found in database")
            
            api_key = loader.get("api_keys", {}).get(service_name)
            if not api_key:
                raise ValueError(f"API key not found for service '{service_name}' in loader '{loader_name}'")
                
            return api_key.get("key")
            
        except ImportError as e:
            raise ImportError("Failed to import the database module. Ensure the database connection is properly configured") from e
        except Exception as e:
            raise ConnectionError(f"Failed to retrieve API key from MongoDB: {str(e)}") from e

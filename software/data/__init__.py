import os
import sys
import importlib
import logging
from typing import Dict, Type
from .base_data import BaseDataLoader

class DataLoaderRegistry:
    """Registry for managing and hot-reloading data loader classes"""
    
    def __init__(self):
        self._loader_classes: Dict[str, Type[BaseDataLoader]] = {}
        self._load_all_loaders()
    
    def _load_all_loaders(self) -> None:
        """Load all data loader classes from the data directory"""
        self._loader_classes.clear()
        data_dir = os.path.dirname(__file__)
        
        for file in os.listdir(data_dir):
            if file.endswith('.py') and file not in ['__init__.py', 'base_data.py']:
                self._load_loader_module(file)
    
    def _load_loader_module(self, file: str) -> None:
        """Load a single data loader module"""
        module_name = file[:-3]  # Remove .py extension
        full_module_name = f'data.{module_name}'
        
        try:
            # Always reload the module to get latest changes
            if full_module_name in sys.modules:
                module = importlib.reload(sys.modules[full_module_name])
            else:
                module = importlib.import_module(f'.{module_name}', package='data')
            
            # Find and register all data loader classes in the module
            for attr in dir(module):
                cls = getattr(module, attr)
                if (isinstance(cls, type) and
                    issubclass(cls, BaseDataLoader) and
                    cls is not BaseDataLoader):
                    self._loader_classes[attr] = cls
                    logging.info(f"Loaded data loader class: {attr}")
        
        except Exception as e:
            logging.error(f"Error loading module {module_name}: {str(e)}")
    
    def get_loader_class(self, name: str) -> Type[BaseDataLoader]:
        """
        Get a data loader class by name, always reloading to get latest changes
        """
        # Find the file containing this loader
        data_dir = os.path.dirname(__file__)
        for file in os.listdir(data_dir):
            if file.endswith('.py') and file not in ['__init__.py', 'base_data.py']:
                self._load_loader_module(file)
                if name in self._loader_classes:
                    return self._loader_classes[name]
        return None
    
    def get_all_loader_names(self) -> list:
        """Get names of all registered loaders"""
        # Reload all loaders to get latest changes
        self._load_all_loaders()
        return list(self._loader_classes.keys())
    
    def get_all_loader_classes(self) -> Dict[str, Type[BaseDataLoader]]:
        """Get all registered loader classes"""
        # Reload all loaders to get latest changes
        self._load_all_loaders()
        return self._loader_classes.copy()

# Create global registry instance
registry = DataLoaderRegistry()
DATA_LOADER_CLASSES = registry.get_all_loader_classes()

__all__ = ['registry', 'DATA_LOADER_CLASSES', 'BaseDataLoader']

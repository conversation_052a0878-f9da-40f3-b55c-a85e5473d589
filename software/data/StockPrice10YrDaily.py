from data.base_data import BaseDataLoader
import pandas as pd
import requests
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class StockPrice10YrDaily(BaseDataLoader):
    """Data loader for daily interval stock data for past 10 years using FMP API.

    Loads historical daily stock price data for a specified ticker symbol from
    Financial Modeling Prep API for a 10-year period. Returns a clean DataFrame
    with properly formatted datetime index and numeric columns for financial analysis.
    """

    def __init__(self, ticker="AAPL"):
        super().__init__()
        self.ticker = ticker
        self.base_url = "https://financialmodelingprep.com/api/v3"
        self.datetime_col = "Date"
        # Define columns that should be numeric - only raw data columns
        self.additional_columns = {
            col: lambda s: pd.to_numeric(s, errors='coerce').notna().all()
            for col in ["Open", "High", "Low", "Close", "Volume"]
        }

        try:
            self.api_key = self.get_api_key("StockPrice10YrDaily", "FMP_API_KEY")
        except Exception as e:
            logger.error(f"Failed to get API key: {str(e)}")
            raise ValueError(f"Failed to retrieve API key from MongoDB: {str(e)}")

    def load_historical_data(self) -> pd.DataFrame:
        """Load historical stock data for the past 10 years at daily intervals."""
        try:
            # Get data from API
            now = datetime.now()
            from_date = (now - timedelta(days=3650)).strftime("%Y-%m-%d")
            to_date = now.strftime("%Y-%m-%d")


            response = requests.get(
                f"{self.base_url}/historical-price-full/{self.ticker}",
                params={
                    "apikey": self.api_key,
                    "from": from_date,
                    "to": to_date
                }
            )
            response.raise_for_status()
            data = response.json()

            # Handle empty response
            if not data or "historical" not in data:
                return self._prepare_empty_df()

            # Create and clean DataFrame - keep only raw data columns
            df = pd.DataFrame(data["historical"]).rename(
                columns={"date": "Date", "open": "Open", "high": "High",
                         "low": "Low", "close": "Close", "volume": "Volume"}
            )

            # Keep only the raw data columns
            keep_columns = ["Date", "Open", "High", "Low", "Close", "Volume"]
            df = df[[col for col in keep_columns if col in df.columns]]

            # Process data - convert dates and handle invalid values
            try:
                # Convert dates to datetime
                df["Date"] = pd.to_datetime(df["Date"], errors='coerce')

                # Drop rows with invalid dates
                df = df.dropna(subset=["Date"])
            except Exception as e:
                logger.warning(f"Date conversion issue: {str(e)}")
                # Try with explicit format as fallback
                df["Date"] = pd.to_datetime(df["Date"], format="%Y-%m-%d", errors='coerce')
                df = df.dropna(subset=["Date"])

            # Convert numeric columns
            numeric_columns = [col for col in df.columns if col in self.additional_columns]

            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Drop rows with NaN in critical columns (if they exist)
            critical_columns = [col for col in ["Open", "High", "Low", "Close"] if col in df.columns]
            if critical_columns:
                df = df.dropna(subset=critical_columns)

            # Return empty DataFrame if no valid data
            if df.empty:
                return self._prepare_empty_df()

            # Set index and ensure it's valid
            df = df.sort_values("Date")

            # Filter out NaT values before creating DatetimeIndex
            valid_dates = [date for date in df["Date"] if pd.notna(date)]
            df = df[df["Date"].isin(valid_dates)]

            # Create DatetimeIndex and remove Date column
            df.index = pd.DatetimeIndex(df["Date"].values, name=self.datetime_col)
            df = df.drop(columns=["Date"])

            # Final cleanup
            df = df[~df.index.duplicated(keep='first')].sort_index()

            # Remove any remaining NaT values in index
            if pd.isna(df.index).any():
                df = df[~pd.isna(df.index)]

            if df.empty:
                return self._prepare_empty_df()

            # Ensure the index is strftime compatible
            try:
                _ = df.index.strftime('%Y-%m-%d')
            except Exception:
                # Create a mask of valid dates that support strftime
                mask = pd.Series([True] * len(df), index=df.index)
                for i, idx in enumerate(df.index):
                    try:
                        idx.strftime('%Y-%m-%d')
                    except Exception:
                        mask.iloc[i] = False
                df = df[mask]

                if df.empty:
                    return self._prepare_empty_df()

            # Ensure timezone-naive DataFrame
            return self._ensure_timezone_naive(df)

        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return self._prepare_empty_df()

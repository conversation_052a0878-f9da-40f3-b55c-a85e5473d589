from data.base_data import BaseDataLoader
import pandas as pd
from datetime import datetime, timedelta
import logging
import numpy as np
import requests

logger = logging.getLogger(__name__)


class Media1Y(BaseDataLoader):
    """
    Data loader for Media1Y - Loads last 1 year of news articles for a stock,
    including daily article count, average sentiment, and median sentiment.
    """

    def __init__(self, ticker="AAPL"):
        super().__init__()
        self.ticker = ticker
        self.datetime_col = "Date"
        self.additional_columns = {
            "article_count": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "avg_sentiment": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "median_sentiment": lambda s: pd.to_numeric(s, errors="coerce")
            .notna()
            .all(),
            "avg_symbol_count": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "max_symbol_count": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "avg_headline_length": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "avg_summary_length": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "total_images": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "avg_images": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "most_common_hour": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "source_variety": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
            "avg_tech_giants": lambda s: pd.to_numeric(s, errors="coerce").notna().all(),
        }
        self.low_variance_columns = {
            "article_count": 0.01,  # News article counts may have low variety
            "avg_sentiment": 0.04,    # Average sentiment may show patterns
            "median_sentiment": 0.04,  # Median sentiment may show patterns
            "avg_symbol_count": 0.05,  # Symbol counts may be similar
            "max_symbol_count": 0.05,  # Max symbol counts may be similar
            "avg_headline_length": 0.05,  # Headlines tend to be similar lengths
            "avg_summary_length": 0.05,  # Summaries tend to be similar lengths
            "total_images": 0.05,  # Image counts may be limited in variety
            "avg_images": 0.05,  # Average images may be similar
            "most_common_hour": 0.05,  # Hours might cluster around publication times
            "source_variety": 0.05,  # Source variety may be limited
            "avg_tech_giants": 0.05,  # Tech giants mentions may be similar
        }
        try:
            self.alpaca_api_key = self.get_api_key("Media1Y", "API_KEY")
            self.alpaca_secret_key = self.get_api_key("Media1Y", "SECRET_KEY")
        except Exception as e:
            logger.error(f"Failed to get API key: {str(e)}")
            raise ValueError(f"Failed to retrieve API key from MongoDB: {str(e)}")

    def load_historical_data(self) -> pd.DataFrame:
        """
        Loads historical news data from Alpaca for the last 1 year.
        Includes daily article count, average sentiment, and median sentiment.
        """
        try:
            end = datetime.now()
            start = end - timedelta(days=365)
            start_str = start.strftime("%Y-%m-%d")
            
            base_url = "https://data.alpaca.markets/v1beta1/news"
            params = {
                "start": start_str,
                "sort": "asc",
                "symbols": self.ticker,
                "limit": 50,
                "include_content": "false",
                "exclude_contentless": "true"
            }
            
            headers = {
                "accept": "application/json",
                "APCA-API-KEY-ID": self.alpaca_api_key,
                "APCA-API-SECRET-KEY": self.alpaca_secret_key
            }
            
            all_news = []
            next_page_token = None
            
            while True:
                if next_page_token:
                    params["page_token"] = next_page_token
                
                response = requests.get(base_url, headers=headers, params=params)
                
                if response.status_code != 200:
                    logger.error(f"Error fetching news data: {response.text}")
                    return self._prepare_empty_df()
                
                data = response.json()
                news_items = data.get("news", [])
                
                if not news_items:
                    break
                    
                all_news.extend(news_items)
                next_page_token = data.get("next_page_token")
                
                if not next_page_token:
                    break
            
            if not all_news:
                return self._prepare_empty_df()

            news_list = []
            for article in all_news:
                headline = article["headline"]
                summary = article["summary"] if article["summary"] else ""
                symbols = article.get("symbols", [])
                images = article.get("images", [])
                created_time = pd.to_datetime(article.get("created_at", article["updated_at"]))
                source = article.get("source", "unknown")
                
                # Calculate tech giants count
                tech_giants = ["AAPL", "MSFT", "GOOG", "AMZN", "META"]
                tech_giants_count = sum(1 for symbol in symbols if symbol in tech_giants)
                
                news_list.append(
                    {
                        "Date": article["updated_at"],
                        "headline": headline,
                        "summary": summary,
                        "symbol_count": len(symbols),
                        "headline_length": len(headline),
                        "summary_length": len(summary),
                        "image_count": len(images),
                        "publication_hour": created_time.hour,
                        "source": source,
                        "tech_giants_count": tech_giants_count
                    }
                )

            df = pd.DataFrame(news_list)

            if df.empty:
                return self._prepare_empty_df()

            df["Date"] = pd.to_datetime(df["Date"])

            if "headline" in df.columns:
                df["title"] = df["headline"]
            elif "title" in df.columns:
                pass
            else:
                return self._prepare_empty_df()

            df = df.dropna(subset=["Date"])

            from features.newssentiment import NewsSentiment

            sentiment_feature = NewsSentiment()
            df['sentiment_score'] = sentiment_feature.calculate(df)

            df["date_only"] = df["Date"].dt.date
            daily_news = df.groupby("date_only")

            def calculate_stats(group):
                sources = group["source"].nunique()
                
                return pd.Series(
                    {
                        "article_count": len(group),
                        "avg_sentiment": group["sentiment_score"].mean(),
                        "median_sentiment": group["sentiment_score"].median(),
                        "avg_symbol_count": group["symbol_count"].mean(),
                        "max_symbol_count": group["symbol_count"].max(),
                        "avg_headline_length": group["headline_length"].mean(),
                        "avg_summary_length": group["summary_length"].mean(),
                        "total_images": group["image_count"].sum(),
                        "avg_images": group["image_count"].mean(),
                        "most_common_hour": group["publication_hour"].mode().iloc[0] if not group["publication_hour"].empty else 0,
                        "source_variety": sources,
                        "avg_tech_giants": group["tech_giants_count"].mean(),
                    }
                )

            daily_stats = daily_news.apply(calculate_stats)
            daily_stats.index = pd.to_datetime(daily_stats.index)
            daily_stats.index.name = self.datetime_col

            # Ensure timezone-naive DataFrame
            return self._ensure_timezone_naive(daily_stats)

        except Exception as e:
            logger.error(f"Error loading news data: {str(e)}")
            return self._prepare_empty_df()

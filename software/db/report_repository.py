from api.database import database
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime
from pymongo.errors import OperationFailure
import math
from typing import List, Dict, Optional, Any, Union
import numpy as np
from pydantic import BaseModel, Field, validator, ConfigDict, root_validator
import pandas as pd

# Request/Response Models
class DataLoaderSelection(BaseModel):
    """Model for data loader selection."""
    model_config = ConfigDict(extra='forbid')
    name: str = Field(..., description="Name of the data loader")

class ExtractRequest(BaseModel):
    """Request model for data extraction.
    
    This model supports snapshot data extraction by allowing users to specify a date range.
    When start_date and end_date are provided, the workflow will extract data only for 
    that specific time frame. If dates are not provided, it will fetch the entire available history.
    
    Attributes:
        ticker (str): Ticker symbol to extract data for
        data_loader (str): Name of the data loader to use
        start_date (Optional[datetime]): Start date for snapshot data
        end_date (Optional[datetime]): End date for snapshot data
    """
    ticker: str = Field(..., description="Ticker symbol to extract data for")
    data_loader: str = Field(..., description="Name of the data loader to use")
    start_date: Optional[datetime] = Field(None, description="Start date for snapshot data")
    end_date: Optional[datetime] = Field(None, description="End date for snapshot data")

    @validator('end_date')
    def validate_dates(cls, v, values):
        if v and values.get('start_date'):
            if v < values['start_date']:
                raise ValueError("End date must be after start date")
        return v

class ExtractResponse(BaseModel):
    """Response model for data extraction."""
    report_id: str = Field(..., description="ID of the generated report")
    message: str = Field(..., description="Status message")

class LoaderConfig(BaseModel):
    """Model for loader configuration."""
    name: str = Field(..., description="Name of the data loader")
    ticker: str = Field(..., description="Ticker for the data loader")

class JoinRequest(BaseModel):
    """Request model for join operation."""
    left_loader: str = Field(..., description="Name of the left data loader")
    right_loader: str = Field(..., description="Name of the right data loader")
    left_on: Optional[str] = Field(default=None, description="Column to join on in left dataset (not required for forward_fill)")
    right_on: Optional[str] = Field(default=None, description="Column to join on in right dataset (not required for forward_fill)")
    join_type: str = Field(..., description="Type of join operation")
    preview_rows: Optional[int] = None
    left_ticker: str = Field(..., description="Ticker for left data loader")
    right_ticker: str = Field(..., description="Ticker for right data loader")

    @validator('join_type')
    def validate_join_type(cls, v):
        valid_types = ['inner', 'outer', 'left', 'right', 'forward_fill']
        if v not in valid_types:
            raise ValueError(f'Join type must be one of {valid_types}')
        return v

    @root_validator(skip_on_failure=True)
    def validate_join_fields(cls, values):
        """Validate that left_on and right_on are provided for standard joins."""
        join_type = values.get('join_type')
        left_on = values.get('left_on')
        right_on = values.get('right_on')

        # Only forward_fill can have None for join columns
        if join_type != 'forward_fill' and (left_on is None or right_on is None):
            raise ValueError("left_on and right_on are required for standard join types.")
        
        return values

class WorkflowStepRequest(BaseModel):
    """Request model for workflow step."""
    features: List[str] = Field(..., description="List of features to calculate")
    model: str = Field(..., description="Model to use for prediction")
    target: str = Field(..., description="Target column for prediction")
    preview: List[Dict] = Field(..., description="Preview data from join")
    join_type: str = Field(..., description="Type of join operation")
    left_loader: str = Field(..., description="Left data loader name")
    right_loader: str = Field(..., description="Right data loader name")
    left_ticker: str = Field(..., description="Left ticker symbol")
    right_ticker: str = Field(..., description="Right ticker symbol")
    forecast_horizon: int = Field(..., description="Number of periods to forecast")

class WorkflowResponse(BaseModel):
    task_id: str
    status: str
    message: str

class JoinResponse(BaseModel):
    """Model for join operation response."""
    model_config = ConfigDict(extra='forbid')
    data: Optional[List[Dict[str, Any]]] = None
    message: str

class SimpleWorkflowRequest(BaseModel):
    ticker: str
    data_loader: str
    features: List[str]
    model: str
    forecast_horizon: int
    target: str

class SimpleWorkflowResponse(BaseModel):
    report_id: str
    message: str

# Report Schema Models
class ModelConfig(BaseModel):
    name: str
    prediction_column: str
    forecast_horizon: int

class WorkflowBase(BaseModel):
    data_loader: str
    features: List[str] = Field(default_factory=list)
    start_date: str
    end_date: str

class SimpleWorkflowConfig(WorkflowBase):
    model: ModelConfig

class JoinWorkflowConfig(WorkflowBase):
    left_loader: str
    right_loader: str
    left_ticker: str
    right_ticker: str
    join_type: str
    model: ModelConfig

class ExtractWorkflowConfig(WorkflowBase):
    pass

class ReportBase(BaseModel):
    id: Optional[str] = None
    type: str
    ticker: str
    workflow: Union[SimpleWorkflowConfig, JoinWorkflowConfig, ExtractWorkflowConfig]
    performance: Dict[str, Any] = Field(default_factory=dict)
    data: Dict[str, Any]
    status: str = "completed"
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        populate_by_name = True
        json_encoders = {
            ObjectId: str
        }

class SimpleReport(ReportBase):
    type: str = "simple"
    workflow: SimpleWorkflowConfig

class JoinReport(ReportBase):
    type: str = "join"
    workflow: JoinWorkflowConfig

class ExtractReport(ReportBase):
    type: str = "extract"
    workflow: ExtractWorkflowConfig

class ReportFactory:
    @staticmethod
    def create_extract_report(ticker: str, data_loader: str, data: pd.DataFrame, features: List[str] = None, start_date: str = None, end_date: str = None) -> Dict:
        """Create an extract report structure"""
        # Get list of features that were successfully calculated (columns exist in data)
        if features:
            # Filter features that actually exist in the data columns
            calculated_features = [f for f in features if any(col.startswith(f) for col in data.columns)]
        else:
            calculated_features = []

        # Use provided dates or get from data
        if not start_date or not end_date:
            if not data.empty and isinstance(data.index, pd.DatetimeIndex):
                start_date = str(data.index.min())
                end_date = str(data.index.max())
            else:
                start_date = "1970-01-01"
                end_date = "1970-01-01"

        # Create timeseries data
        timeseries = []
        if not data.empty:
            try:
                timeseries = data.reset_index().to_dict('records')
            except Exception:
                pass

        report = {
            "type": "extract",
            "ticker": ticker,
            "workflow": {
                "data_loader": data_loader,
                "features": calculated_features,
                "start_date": start_date,
                "end_date": end_date
            },
            "data": {
                "timeseries": timeseries
            }
        }

        return report

    @staticmethod
    def create_simple_report(
        ticker: str,
        data_loader: str,
        features: List[str],
        model_name: str,
        prediction_column: str,
        forecast_horizon: int,
        data: pd.DataFrame,
        performance: Dict[str, Any]
    ) -> Dict:
        """Create a simple report structure"""
        # Ensure data has an index
        if not isinstance(data.index, pd.DatetimeIndex):
            data.index = pd.to_datetime(data.index)
            
        return {
            "type": "simple",
            "ticker": ticker,
            "workflow": {
                "data_loader": data_loader,
                "features": features,
                "start_date": data.index.min().strftime("%Y-%m-%d"),
                "end_date": data.index.max().strftime("%Y-%m-%d"),
                "model": {
                    "name": model_name,
                    "prediction_column": prediction_column,
                    "forecast_horizon": forecast_horizon
                }
            },
            "performance": performance,
            "data": {
                "timeseries": data.reset_index().to_dict('records')
            }
        }

    @staticmethod
    def create_join_report(params: dict) -> dict:
        """
        Create a join report entry in the database.
        """
        main_df = params["main_df"]
        aux_df = params["aux_df"]
        target_df = params["target_df"]
        main_datasource_id = params["main_datasource_id"]
        aux_datasource_id = params["aux_datasource_id"]
        join_column = params["join_column"]
        join_type = params["join_type"]
        
        # Process the dates from the actual data, not using hardcoded fallbacks
        try:
            # Handle date extraction from the DataFrame
            if 'Date' in target_df.columns:
                # Convert to datetime if not already
                date_col = pd.to_datetime(target_df['Date'], errors='coerce')
                # Remove NaT values
                valid_dates = date_col.dropna()
                
                if len(valid_dates) == 0:
                    raise ValueError("No valid dates found in the Date column")
                    
                start_date = valid_dates.min()
                end_date = valid_dates.max()
            elif isinstance(target_df.index, pd.DatetimeIndex):
                # Handle DatetimeIndex
                valid_dates = target_df.index.dropna()
                
                if len(valid_dates) == 0:
                    raise ValueError("No valid dates found in the DatetimeIndex")
                    
                start_date = valid_dates.min()
                end_date = valid_dates.max()
            else:
                raise ValueError("No date column or DatetimeIndex found in the target DataFrame")
                
            # Format dates to string
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
        except Exception as e:
            raise ValueError(f"Error determining date range from data: {str(e)}")

        # Reset index IF NEEDED to turn Date index into a column for serialization
        # DO NOT drop the index if it's the Date index.
        if isinstance(target_df.index, pd.DatetimeIndex) and target_df.index.name == 'Date':
            target_df = target_df.reset_index() # Keep Date as a column
        elif 'Date' not in target_df.columns:
            # If Date is not index or column, something is wrong, but try resetting anyway
            target_df = target_df.reset_index()

        # Extract dates from target_df to determine date range
        if isinstance(target_df, pd.DataFrame) and not target_df.empty:
            # Convert NaT to None for JSON compatibility
            target_df = target_df.replace({pd.NaT: None})
            # Convert NumPy types to native Python types
            for col in target_df.select_dtypes(include=[np.number]).columns:
                target_df[col] = target_df[col].apply(lambda x: ReportRepository._clean_numeric(x))

            timeseries = target_df.to_dict('records')
        else:
            timeseries = []
        
        # Extract features if provided
        features = params.get("features", [])
        model_name = params.get("model_name", "default") # Get model name from params
        prediction_column = params.get("prediction_column", "Close") # Get target from params
        forecast_horizon = params.get("forecast_horizon", 5) # Get horizon from params
        performance = params.get("performance", {}) # Get performance from params
        main_ticker = params.get("main_ticker", "Unknown") # Get main_ticker from params

        # Create report structure matching JoinReport model
        return {
            "type": "join",
            "ticker": main_ticker, # Use the passed main_ticker
            "workflow": {
                "data_loader": f"{main_datasource_id}+{aux_datasource_id}",
                "features": features,
                "left_loader": main_datasource_id,
                "right_loader": aux_datasource_id,
                "left_ticker": main_datasource_id.split("_")[0] if "_" in main_datasource_id else main_datasource_id,
                "right_ticker": aux_datasource_id.split("_")[0] if "_" in aux_datasource_id else aux_datasource_id,
                "join_type": join_type,
                "start_date": start_date_str,
                "end_date": end_date_str,
                "model": {
                    "name": model_name,
                    "prediction_column": prediction_column,
                    "forecast_horizon": forecast_horizon
                }
            },
            "performance": performance,
            "data": {
                "timeseries": timeseries
            },
            "status": "completed"
        }

class ReportRepository:
    def __init__(self):
        self.db = None
        self.collection = None
        self._initialized = False

    async def initialize(self):
        if not self._initialized:
            self.db = database
            self.collection = self.db['reports']
            self._initialized = True
        return self

    @classmethod
    async def create(cls):
        instance = cls()
        await instance.initialize()
        return instance

    @staticmethod
    def _clean_numeric(value):
        """Clean numeric values for JSON serialization"""
        if isinstance(value, (float, np.float64, np.float32)):
            if math.isinf(value) or math.isnan(value):
                return None
        return value

    def _clean_dict(self, d: dict) -> dict:
        """Recursively clean dictionary values"""
        result = {}
        for k, v in d.items():
            if isinstance(v, dict):
                result[k] = self._clean_dict(v)
            elif isinstance(v, list):
                result[k] = [self._clean_dict(i) if isinstance(i, dict) else self._clean_numeric(i) for i in v]
            else:
                result[k] = self._clean_numeric(v)
        return result

    def _serialize_doc(self, doc: dict) -> dict:
        """Serialize document, handling ObjectId and cleaning numeric values"""
        if doc is None:
            return None
        
        # Create a copy of the document
        cleaned = doc.copy()
        
        # Convert MongoDB _id to string id
        if '_id' in cleaned:
            cleaned['id'] = str(cleaned['_id'])
            del cleaned['_id']
        
        # Clean numeric values
        cleaned = self._clean_dict(cleaned)
        
        # Try to validate, but return cleaned doc even if validation fails
        try:
            report_type = cleaned.get('type')
            
            if report_type == 'simple':
                report = SimpleReport(**cleaned)
            elif report_type == 'join':
                report = JoinReport(**cleaned)
            elif report_type == 'extract':
                report = ExtractReport(**cleaned)
            else:
                return cleaned
            
            # Convert back to dict after validation
            return report.model_dump()
        except Exception:
            return cleaned

    async def insert_report(self, report_data: Dict):
        if not self._initialized:
            await self.initialize()

        try:
            # Validate and convert report based on type
            report_type = report_data.get('type')
            
            if report_type == 'simple':
                report = SimpleReport(**report_data)
            elif report_type == 'join':
                report = JoinReport(**report_data)
            elif report_type == 'extract':
                report = ExtractReport(**report_data)
            else:
                raise ValueError(f"Unknown report type: {report_type}")

            # Convert to dict for MongoDB storage
            report_dict = report.model_dump()
            report_dict['created_at'] = datetime.utcnow()

            result = await self.collection.insert_one(report_dict)
            return str(result.inserted_id)
        except Exception as e:
            raise

    async def get_report(self, report_id):
        try:
            doc = await self.collection.find_one({'_id': ObjectId(report_id)})
            if doc is None:
                return None
            return self._serialize_doc(doc)
        except Exception as e:
            raise

    async def get_reports_by_query(self, query: Dict = None) -> List[Dict]:
        """Get reports by query filter"""
        try:
            cursor = self.collection.find(query or {})
            reports = []
            async for report in cursor:
                try:
                    cleaned_report = self._serialize_doc(report)
                    reports.append(cleaned_report)
                except Exception:
                    continue
            return reports
        except Exception:
            return []

    async def get_reports_by_ticker(self, ticker: str) -> List[Dict]:
        """Get reports by ticker"""
        return await self.get_reports_by_query({"ticker": ticker})

    async def get_all_reports(self) -> List[Dict]:
        """Get all reports"""
        return await self.get_reports_by_query()

    async def delete_report(self, report_id):
        try:
            result = await self.collection.delete_one({'_id': ObjectId(report_id)})
            return result.deleted_count > 0
        except Exception as e:
            raise

    async def update_report(self, report_id, update_data):
        try:
            result = await self.collection.update_one(
                {'_id': ObjectId(report_id)},
                {'$set': update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            raise

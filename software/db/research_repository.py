from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Union
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCollection
import logging
from api.database import research_analysis_collection, forecast_revisit_collection
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box
import json
from datetime import date

logger = logging.getLogger(__name__)
console = Console()

class ResearchRepository:
    """Repository for managing research analysis data"""

    def __init__(self):
        self.collection: AsyncIOMotorCollection = research_analysis_collection
        self.revisit_collection: AsyncIOMotorCollection = forecast_revisit_collection

    def _serialize_value(self, value: Any) -> Any:
        """Recursively serialize any value for MongoDB storage"""
        if isinstance(value, (str, int, float, bool, type(None))):
            return value
        if isinstance(value, (list, tuple)):
            return [self._serialize_value(item) for item in value]
        if isinstance(value, dict):
            return {k: self._serialize_value(v) for k, v in value.items()}
        if hasattr(value, 'model_dump'):
            return self._serialize_value(value.model_dump())
        if hasattr(value, '__dict__'):
            return self._serialize_value(value.__dict__)
        return str(value)

    async def create_analysis(self, director_id: Optional[str] = None) -> str:
        """Create a new analysis task"""
        task = {
            "status": "pending",
            "started_at": datetime.now(),
            "director_id": director_id,  # Store the director_id
            "workflow_stage": {
                "current": "initializing",
                "stages": {
                    "__start__": {"status": "pending", "started_at": None, "completed_at": None},
                    "call_model": {"status": "pending", "started_at": None, "completed_at": None},
                    "END": {"status": "pending", "started_at": None, "completed_at": None}
                }
            },
            "metadata": {
                "analysis_duration": None,
                "completion_time": None
            },
            "state": {}  # Store graph execution state
        }
        result = await self.collection.insert_one(task)
        return str(result.inserted_id)

    async def update_status(self, task_id: str, status: str) -> None:
        """Update the status of an analysis task"""
        await self.collection.update_one(
            {"_id": ObjectId(task_id)},
            {"$set": {"status": status}}
        )

    async def update_task(self, task_id: str, update_data: Dict[str, Any]) -> None:
        """Universal method to update any fields in an analysis task"""
        try:
            # Serialize the update data
            serialized_data = self._serialize_value(update_data)

            # Update in MongoDB
            await self.collection.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": serialized_data}
            )
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {str(e)}")
            raise

    async def get_analysis(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get an analysis task by ID"""
        task = await self.collection.find_one({"_id": ObjectId(task_id)})
        if task:
            task["_id"] = str(task["_id"])
        return task

    async def list_analyses(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get a list of analysis tasks, sorted by start date"""
        cursor = self.collection.find().sort("started_at", -1).limit(limit)
        analyses = []
        async for task in cursor:
            task["_id"] = str(task["_id"])
            analyses.append(task)
        return analyses

    async def delete_analysis(self, task_id: str) -> bool:
        """Delete an analysis task"""
        result = await self.collection.delete_one({"_id": ObjectId(task_id)})
        return result.deleted_count > 0

    async def create_indexes(self) -> None:
        """Create necessary indexes for the collection"""
        try:
            logger.info("Creating indexes for research_analysis collection...")

            # Define indexes
            indexes = [
                ("started_at", -1),
                [("status", 1), ("started_at", -1)]
            ]

            # Create each index
            for index in indexes:
                if isinstance(index, tuple):
                    await self.collection.create_index([index])
                else:
                    await self.collection.create_index(index)

            logger.info("Research analysis indexes created successfully")

        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")
            # Don't raise since indexes might already exist

    async def initialize_workflow_stages(self, task_id: str, graph_stages: List[str]) -> None:
        """Initialize workflow stages for a task based on the graph structure"""
        try:
            stages = {}
            for stage in graph_stages:
                # Set __start__ as in_progress by default, others as pending
                initial_status = "in_progress" if stage == "__start__" else "pending"
                stages[stage] = {
                    "status": initial_status,
                    "started_at": datetime.now() if stage == "__start__" else None,
                    "completed_at": None
                }

            await self.collection.update_one(
                {"_id": ObjectId(task_id)},
                {
                    "$set": {
                        "workflow_stage": {
                            "current": "__start__",  # Start with __start__ stage
                            "stages": stages
                        }
                    }
                }
            )

        except Exception as e:
            logger.error(f"Error initializing workflow stages: {str(e)}")
            raise

    async def update_workflow_stage(self, task_id: str, stage: str, status: str) -> None:
        """Update the workflow stage and status for a research task"""
        try:
            task_id_obj = ObjectId(task_id)
            update_time = datetime.now()

            # Get current task state
            task = await self.collection.find_one({"_id": task_id_obj})

            update = {
                "$set": {
                    f"workflow_stage.stages.{stage}.status": status,
                    f"workflow_stage.stages.{stage}.updated_at": update_time,
                }
            }

            # Add start time if moving to in_progress
            if status == "in_progress":
                update["$set"][f"workflow_stage.stages.{stage}.started_at"] = update_time

            # Add completion time if completed or failed
            if status in ["completed", "failed"]:
                update["$set"][f"workflow_stage.stages.{stage}.completed_at"] = update_time

                # Calculate duration if we have both start and end times
                stage_data = task.get("workflow_stage", {}).get("stages", {}).get(stage, {})
                started_at = stage_data.get("started_at")
                if started_at:
                    duration_ms = (update_time - started_at).total_seconds() * 1000
                    update["$set"][f"workflow_stage.stages.{stage}.duration_ms"] = duration_ms

            # Update task status based on stage status
            if status == "failed":
                update["$set"].update({
                    "status": "failed",
                    "workflow_stage.current": stage,
                    "completed_at": update_time
                })
            elif status == "completed" and stage == "END":
                # Calculate analysis duration
                started_at = task.get("started_at") if task else None
                analysis_duration = (update_time - started_at).total_seconds() if started_at else None

                update["$set"].update({
                    "status": "completed",
                    "completed_at": update_time,
                    "metadata.completion_time": update_time,
                    "metadata.analysis_duration": analysis_duration
                })

                # If this is the final stage, link the report to the director
                if task and "director_id" in task:
                    from db.research_director_repository import ResearchDirectorRepository
                    director_repo = ResearchDirectorRepository()
                    await director_repo.add_report_id(task["director_id"], str(task_id_obj))
            else:
                update["$set"].update({
                    "workflow_stage.current": stage
                })

            # Perform the update
            await self.collection.update_one(
                {"_id": task_id_obj},
                update
            )

        except Exception as e:
            logger.error(f"Error updating workflow stage: {str(e)}")
            raise

    async def get_workflow_stages(self, task_id: str) -> Dict[str, Any]:
        """Get all workflow stages and their status for a task"""
        try:
            task = await self.get_analysis(task_id)
            if not task:
                raise ValueError("Task not found")

            workflow_stage = task.get("workflow_stage", {})
            return workflow_stage

        except Exception as e:
            logger.error(f"Error getting workflow stages: {str(e)}")
            raise

    async def schedule_forecast_revisit(self, task_id: str, forecast_data: Dict[str, Any]) -> str:
        """Schedule a forecast revisit for the specified date"""
        try:
            # Get the forecast horizon date directly from the structured report
            forecast_horizon = forecast_data.get("forecasted_horizon")

            # Log the original dates from the structured report
            logger.info(f"Original forecast horizon: {forecast_horizon}")
            logger.info(f"Original closing price date: {forecast_data.get('closing_price_date')}")

            # Clean the dates - just use the date part if it's a full ISO timestamp
            if forecast_horizon and 'T' in forecast_horizon:
                forecast_horizon = forecast_horizon.split('T')[0]

            closing_price_date = forecast_data.get("closing_price_date")
            if closing_price_date and 'T' in closing_price_date:
                closing_price_date = closing_price_date.split('T')[0]

            # Parse the forecast horizon date to calculate the revisit date (one day after)
            forecast_horizon_dt = datetime.strptime(forecast_horizon, "%Y-%m-%d").date()
            revisit_date = datetime.combine(forecast_horizon_dt + timedelta(days=1), datetime.min.time())

            logger.info(f"Cleaned forecast horizon: {forecast_horizon}")
            logger.info(f"Cleaned closing price date: {closing_price_date}")
            logger.info(f"Scheduled revisit date: {revisit_date.strftime('%Y-%m-%d')}")

            revisit = {
                "task_id": task_id,
                "ticker": forecast_data.get("ticker"),
                "forecasted_price": forecast_data.get("forecasted_price"),
                "forecasted_horizon": forecast_horizon,
                "last_closing_price": forecast_data.get("last_closing_price"),
                "closing_price_date": closing_price_date,
                "recommendation": forecast_data.get("recommendation"),
                "confidence_level": forecast_data.get("confidence_level"),
                "scheduled_date": revisit_date,
                "status": "pending",
                "created_at": datetime.now(),
                "actual_price": None,
                "accuracy": None,
                "completed_at": None
            }

            result = await self.revisit_collection.insert_one(revisit)
            revisit_id = str(result.inserted_id)

            # Update the original task with the revisit ID
            await self.collection.update_one(
                {"_id": ObjectId(task_id)},
                {"$set": {"forecast_revisit_id": revisit_id}}
            )

            return revisit_id
        except Exception as e:
            logger.error(f"Error scheduling forecast revisit: {str(e)}")
            raise

    async def get_pending_forecast_revisits(self) -> List[Dict[str, Any]]:
        """Get all pending forecast revisits that need to be checked"""
        try:
            # Find all revisits with pending status, regardless of scheduled date
            cursor = self.revisit_collection.find({
                "status": "pending"
            }).sort("scheduled_date", 1)

            revisits = []
            async for revisit in cursor:
                revisit["_id"] = str(revisit["_id"])
                revisits.append(revisit)

            return revisits
        except Exception as e:
            logger.error(f"Error getting pending forecast revisits: {str(e)}")
            raise

    async def get_due_forecast_revisits(self) -> List[Dict[str, Any]]:
        """Get all pending forecast revisits that are due to be checked (scheduled date has passed)"""
        try:
            current_date = datetime.now()

            # Find all revisits with pending status where scheduled date has passed
            cursor = self.revisit_collection.find({
                "status": "pending",
                "scheduled_date": {"$lte": current_date}
            }).sort("scheduled_date", 1)

            revisits = []
            async for revisit in cursor:
                revisit["_id"] = str(revisit["_id"])
                revisits.append(revisit)

            return revisits
        except Exception as e:
            logger.error(f"Error getting due forecast revisits: {str(e)}")
            raise

    async def auto_check_due_revisits(self) -> Dict[str, Any]:
        """
        Automatically check actuals for all revisits that are due (scheduled date has arrived).
        Returns a summary of the operation.
        """
        try:
            due_revisits = await self.get_due_forecast_revisits()
            logger.info(f"Found {len(due_revisits)} due revisits to auto-check")

            results = {
                "total": len(due_revisits),
                "successful": 0,
                "failed": 0,
                "errors": []
            }

            for revisit in due_revisits:
                try:
                    revisit_id = revisit["_id"]
                    logger.info(f"Auto-checking revisit {revisit_id} for ticker {revisit.get('ticker')}")
                    await self.complete_forecast_revisit(revisit_id)
                    results["successful"] += 1
                except Exception as e:
                    results["failed"] += 1
                    error_message = f"Error auto-checking revisit {revisit.get('_id')} ({revisit.get('ticker')}): {str(e)}"
                    logger.error(error_message)
                    results["errors"].append(error_message)

            return results
        except Exception as e:
            logger.error(f"Error in auto check due revisits: {str(e)}")
            raise

    async def auto_check_specific_revisit(self, revisit_id: str) -> Dict[str, Any]:
        """
        Manually check a specific forecast revisit regardless of its scheduled date.
        This is for manual check operations.

        Args:
            revisit_id: The ID of the revisit to check

        Returns:
            Dict with operation result
        """
        try:
            # Get the revisit data first to verify it exists
            revisit = await self.get_forecast_revisit(revisit_id)
            if not revisit:
                raise ValueError(f"Forecast revisit with ID {revisit_id} not found")

            ticker = revisit.get("ticker")
            logger.info(f"Manually checking revisit {revisit_id} for ticker {ticker}")

            # Force-complete the revisit (bypassing date validation)
            try:
                # Use the data registry to get a data loader instead of direct import
                from data import registry
                # Use a default data loader that works with daily prices
                loader_class = registry.get_loader_class("StockMarket24M1D")
                if not loader_class:
                    raise ValueError("Could not find suitable data loader in registry")

                # Initialize loader with ticker
                data_loader = loader_class(ticker)
                actual_data = data_loader.load_historical_data()

                if actual_data is None or actual_data.empty:
                    raise ValueError(f"Could not fetch actual price data for {ticker}")

                # Log available columns for debugging
                logger.info(f"Available columns in data: {list(actual_data.columns)}")

                # Try different case variations of the column name
                close_price_column = None
                for column_name in ['close', 'Close', 'CLOSE']:
                    if column_name in actual_data.columns:
                        close_price_column = column_name
                        break

                if close_price_column is None:
                    # If we can't find a close column, use the last column as a fallback
                    logger.warning(f"Could not find close price column. Available columns: {list(actual_data.columns)}")
                    close_price_column = actual_data.columns[-1]
                    logger.warning(f"Using fallback column: {close_price_column}")

                logger.info(f"Using price column: {close_price_column}")
                actual_price = actual_data[close_price_column].iloc[-1]
                logger.info(f"Latest actual price for {ticker}: {actual_price}")

                forecasted_price = revisit.get("forecasted_price")
                logger.info(f"Forecasted price was: {forecasted_price}")

                # Calculate accuracy (percentage difference)
                accuracy = 100 - abs((actual_price - forecasted_price) / forecasted_price * 100)
                logger.info(f"Forecast accuracy: {accuracy:.2f}%")

                # Update the revisit with the actual price and accuracy
                await self.revisit_collection.update_one(
                    {"_id": ObjectId(revisit_id)},
                    {
                        "$set": {
                            "actual_price": actual_price,
                            "accuracy": accuracy,
                            "status": "completed",
                            "completed_at": datetime.now(),
                            "manually_checked": True
                        }
                    }
                )
                logger.info(f"Successfully completed manual forecast revisit for {ticker}")

                return {
                    "successful": True,
                    "ticker": ticker,
                    "forecasted_price": forecasted_price,
                    "actual_price": actual_price,
                    "accuracy": accuracy
                }

            except Exception as e:
                error_message = f"Error manually checking revisit {revisit_id} ({ticker}): {str(e)}"
                logger.error(error_message)
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

                return {
                    "successful": False,
                    "ticker": ticker,
                    "error": str(e)
                }

        except Exception as e:
            logger.error(f"Error in manual check revisit: {str(e)}")
            return {
                "successful": False,
                "error": str(e)
            }

    async def get_completed_forecast_revisits(self) -> List[Dict[str, Any]]:
        """Get all completed forecast revisits"""
        try:
            cursor = self.revisit_collection.find({
                "status": "completed"
            }).sort("completed_at", -1)

            revisits = []
            async for revisit in cursor:
                revisit["_id"] = str(revisit["_id"])
                revisits.append(revisit)

            return revisits
        except Exception as e:
            logger.error(f"Error getting completed forecast revisits: {str(e)}")
            raise

    async def get_forecast_revisit(self, revisit_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific forecast revisit by ID"""
        try:
            revisit = await self.revisit_collection.find_one({"_id": ObjectId(revisit_id)})
            if revisit:
                revisit["_id"] = str(revisit["_id"])
            return revisit
        except Exception as e:
            logger.error(f"Error getting forecast revisit: {str(e)}")
            raise

    async def complete_forecast_revisit(self, revisit_id: str) -> None:
        """Complete a forecast revisit with actual price and accuracy"""
        try:
            # Get the revisit data
            revisit = await self.get_forecast_revisit(revisit_id)
            if not revisit:
                raise ValueError(f"Forecast revisit with ID {revisit_id} not found")

            # Validate that today's date is not before the scheduled date
            current_date = datetime.now().date()
            scheduled_date = revisit.get("scheduled_date")

            if isinstance(scheduled_date, str) and 'T' in scheduled_date:
                scheduled_date = datetime.strptime(scheduled_date.split('T')[0], "%Y-%m-%d").date()
            elif isinstance(scheduled_date, str):
                scheduled_date = datetime.strptime(scheduled_date, "%Y-%m-%d").date()
            elif isinstance(scheduled_date, datetime):
                scheduled_date = scheduled_date.date()

            if current_date < scheduled_date:
                raise ValueError(f"Cannot check actuals before scheduled date ({scheduled_date})")

            ticker = revisit.get("ticker")
            logger.info(f"Completing forecast revisit for ticker: {ticker}")

            # Use the data registry to get a data loader instead of direct import
            from data import registry
            # Use a default data loader that works with daily prices
            loader_class = registry.get_loader_class("StockMarket24M1D")
            if not loader_class:
                raise ValueError("Could not find suitable data loader in registry")

            # Initialize loader with ticker
            data_loader = loader_class(ticker)
            actual_data = data_loader.load_historical_data()

            if actual_data is None or actual_data.empty:
                raise ValueError(f"Could not fetch actual price data for {ticker}")

            # Log available columns for debugging
            logger.info(f"Available columns in data: {list(actual_data.columns)}")

            # Try different case variations of the column name
            close_price_column = None
            for column_name in ['close', 'Close', 'CLOSE']:
                if column_name in actual_data.columns:
                    close_price_column = column_name
                    break

            if close_price_column is None:
                # If we can't find a close column, use the last column as a fallback
                logger.warning(f"Could not find close price column. Available columns: {list(actual_data.columns)}")
                close_price_column = actual_data.columns[-1]
                logger.warning(f"Using fallback column: {close_price_column}")

            logger.info(f"Using price column: {close_price_column}")
            actual_price = actual_data[close_price_column].iloc[-1]
            logger.info(f"Latest actual price for {ticker}: {actual_price}")

            forecasted_price = revisit.get("forecasted_price")
            logger.info(f"Forecasted price was: {forecasted_price}")

            # Calculate accuracy (percentage difference)
            accuracy = 100 - abs((actual_price - forecasted_price) / forecasted_price * 100)
            logger.info(f"Forecast accuracy: {accuracy:.2f}%")

            # Update the revisit with the actual price and accuracy
            await self.revisit_collection.update_one(
                {"_id": ObjectId(revisit_id)},
                {
                    "$set": {
                        "actual_price": actual_price,
                        "accuracy": accuracy,
                        "status": "completed",
                        "completed_at": datetime.now()
                    }
                }
            )
            logger.info(f"Successfully completed forecast revisit for {ticker}")
        except Exception as e:
            logger.error(f"Error completing forecast revisit: {str(e)}")
            # Add stack trace for better debugging
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    async def delete_forecast_revisit(self, revisit_id: str) -> Dict[str, Any]:
        """Delete a specific forecast revisit by ID

        Args:
            revisit_id: The ID of the forecast revisit to delete

        Returns:
            Dict with operation result
        """
        try:
            # Get the revisit first to verify it exists
            revisit = await self.get_forecast_revisit(revisit_id)
            if not revisit:
                return {
                    "success": False,
                    "message": f"Forecast revisit with ID {revisit_id} not found"
                }

            # Get the task ID to update the reference
            task_id = revisit.get("task_id")

            # Delete the revisit
            result = await self.revisit_collection.delete_one({"_id": ObjectId(revisit_id)})

            # If the revisit was deleted and we have a task ID, remove the reference
            if result.deleted_count > 0 and task_id:
                # Get the task to check for director_id
                task = await self.collection.find_one({"_id": ObjectId(task_id)})

                # Remove the forecast_revisit_id reference from the task
                await self.collection.update_one(
                    {"_id": ObjectId(task_id)},
                    {"$unset": {"forecast_revisit_id": ""}}
                )

                # If the task has a director_id, remove the report from the director's list
                if task and "director_id" in task:
                    director_id = task["director_id"]
                    # Import here to avoid circular imports
                    from db.research_director_repository import ResearchDirectorRepository
                    director_repo = ResearchDirectorRepository()

                    # Remove the report ID from the director's list
                    await director_repo.remove_report_id(director_id, task_id)
                    logger.info(f"Removed report {task_id} from director {director_id}")

            return {
                "success": True,
                "deleted_count": result.deleted_count,
                "revisit_id": revisit_id,
                "ticker": revisit.get("ticker")
            }
        except Exception as e:
            logger.error(f"Error deleting forecast revisit: {str(e)}")
            return {
                "success": False,
                "message": str(e)
            }

    async def delete_director_forecast_revisits(self, director_id: str) -> Dict[str, Any]:
        """Delete all forecast revisits associated with a director

        Args:
            director_id: The ID of the director

        Returns:
            Dict with operation result including count of deleted revisits
        """
        deleted_count = 0

        # Find all analyses by this director
        cursor = self.collection.find({"director_id": director_id})

        # Track all forecast revisit IDs to delete
        forecast_revisit_ids = []

        # Collect all forecast revisit IDs
        async for report in cursor:
            if "forecast_revisit_id" in report:
                forecast_revisit_ids.append(ObjectId(report["forecast_revisit_id"]))

                # Remove the forecast_revisit_id reference from the report
                await self.collection.update_one(
                    {"_id": report["_id"]},
                    {"$unset": {"forecast_revisit_id": ""}}
                )

        # Delete all collected forecast revisits in one operation if there are any
        if forecast_revisit_ids:
            result = await self.revisit_collection.delete_many({"_id": {"$in": forecast_revisit_ids}})
            deleted_count = result.deleted_count

        return {
            "deleted_count": deleted_count,
            "director_id": director_id
        }
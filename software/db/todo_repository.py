from api.database import database
from bson import ObjectId
from pymongo import UpdateOne
from datetime import datetime

class TodoRepository:
    def __init__(self):
        self.collection = database['todos']
        
    async def init_indexes(self):
        # Create indexes
        await self.collection.create_index([("order", 1)])
        await self.collection.create_index([("created_at", 1)])

    async def insert_todo(self, todo_data):
        todo_data['created_at'] = datetime.utcnow()
        # Ensure description field exists
        if 'description' not in todo_data:
            todo_data['description'] = ''
        result = await self.collection.insert_one(todo_data)
        return str(result.inserted_id)

    async def get_todo_by_id(self, todo_id):
        try:
            todo = await self.collection.find_one({'_id': ObjectId(todo_id)})
            if todo:
                todo['_id'] = str(todo['_id'])
            return todo
        except Exception as e:
            print(f"Error getting todo: {str(e)}")
            raise e

    async def get_all_todos(self):
        cursor = self.collection.find().sort("order", -1)
        todos = []
        async for todo in cursor:
            todo['_id'] = str(todo['_id'])
            todos.append(todo)
        return todos

    async def update_todo(self, todo_id, update_data):
        try:
            # Convert string ID to ObjectId
            object_id = ObjectId(todo_id)
            
            # Perform the update
            result = await self.collection.update_one(
                {'_id': object_id},
                {'$set': update_data}
            )
            
            success = result.modified_count > 0
            return success
        except Exception as e:
            print(f"Error updating todo: {str(e)}")
            raise e

    async def delete_todo(self, todo_id):
        result = await self.collection.delete_one({'_id': ObjectId(todo_id)})
        return result.deleted_count > 0

    async def update_task_order(self, order_data):
        bulk_operations = []
        for item in order_data:
            bulk_operations.append(
                UpdateOne(
                    {'_id': ObjectId(item['id'])},
                    {'$set': {'order': item['order']}}
                )
            )
        if bulk_operations:
            return await self.collection.bulk_write(bulk_operations)
        return None

    async def get_next_order_value(self):
        # Find the highest order value and add 1000
        result = await self.collection.find_one(
            {},
            sort=[("order", -1)],
            projection={"order": 1}
        )
        current_max = result.get("order", 0) if result else 0
        return current_max + 1000

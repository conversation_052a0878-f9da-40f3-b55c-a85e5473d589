from .report_repository import ReportRepository
from .research_repository import ResearchRepository
from .research_director_repository import ResearchDirectorRepository
from .feature_request_repository import FeatureRequestRepository
from .rl_environment_repository import RLEnvironmentRepository
from .rl_training_repository import RLTrainingRepository
from .backtest_strategy_eval_repository import BacktestStrategyEvalRepository
from motor.motor_asyncio import AsyncIOMotorClient
import os

__all__ = ['get_database', 'ReportRepository', 'ResearchRepository', 'ResearchDirectorRepository', 'FeatureRequestRepository', 'RLEnvironmentRepository', 'RLTrainingRepository', 'BacktestStrategyEvalRepository']

# Connect to MongoDB
client = AsyncIOMotorClient(os.environ.get("MONGODB_URL", "mongodb://localhost:27017"))
db = client.vero_db

# Collections
report_collection = db.reports
loader_collection = db.data_loaders
todo_collection = db.todos
feature_request_collection = db.feature_requests
research_collection = db.research_analysis
tool_collection = db.tools
graph_collection = db.graphs
data_loader_collection = db.data_loaders
llm_collection = db.llm_models

from api.database import database
from bson import ObjectId
from datetime import datetime
from pymongo.errors import OperationFailure
import logging

class BacktestStrategyEvalRepository:
    def __init__(self):
        self.db = None
        self.collection = None
        self._initialized = False

    async def initialize(self):
        """Initialize the database connection asynchronously using api/database."""
        if not self._initialized:
            self.db = database
            self.collection = self.db['backtest_strategy_evaluations']
            await self.collection.create_index([("created_at", 1)])
            await self.collection.create_index([("business_question", 1)])
            await self.collection.create_index([("ticker", 1)])
            await self.collection.create_index([("overall_score", 1)])
            self._initialized = True
        return self

    @classmethod
    async def create(cls):
        """Factory method to create and initialize a BacktestStrategyEvalRepository instance."""
        instance = cls()
        await instance.initialize()
        return instance

    async def save_evaluation(self, business_question: str, ticker: str, evaluation_data: dict, llm_used: str = "unknown"):
        """
        Save a supervisor evaluation to the database.

        Args:
            business_question (str): The original business question
            ticker (str): The stock ticker symbol
            evaluation_data (dict): SupervisorEvaluation data
            llm_used (str): The LLM model ID used for evaluation

        Returns:
            str: The inserted document's ID.
        """
        document = {
            'business_question': business_question,
            'ticker': ticker,
            'llm_used': llm_used,
            'report_readability_score': evaluation_data.get('report_readability_score', 0),
            'code_strategy_alignment_score': evaluation_data.get('code_strategy_alignment_score', 0),
            'optimization_ranges_realism_score': evaluation_data.get('optimization_ranges_realism_score', 0),
            'analysis_quality_score': evaluation_data.get('analysis_quality_score', 0),
            'overall_score': evaluation_data.get('overall_score', 0),
            'detailed_feedback': evaluation_data.get('detailed_feedback', ''),
            'key_strengths': evaluation_data.get('key_strengths', []),
            'improvement_areas': evaluation_data.get('improvement_areas', []),
            'created_at': datetime.utcnow()
        }

        try:
            result = await self.collection.insert_one(document)
            return str(result.inserted_id)
        except OperationFailure as e:
            logging.error(f"Failed to save evaluation: {e}")
            raise

    async def get_evaluations_timeseries(self, limit: int = 100):
        """
        Retrieve evaluations for timeseries visualization.

        Args:
            limit (int): Maximum number of evaluations to retrieve

        Returns:
            list: List of evaluations sorted by creation date
        """
        cursor = self.collection.find().sort("created_at", 1).limit(limit)
        evaluations = []

        async for doc in cursor:
            evaluations.append({
                'id': str(doc['_id']),
                'business_question': doc.get('business_question', ''),
                'ticker': doc.get('ticker', ''),
                'llm_used': doc.get('llm_used', 'unknown'),
                'report_readability_score': doc.get('report_readability_score', 0),
                'code_strategy_alignment_score': doc.get('code_strategy_alignment_score', 0),
                'optimization_ranges_realism_score': doc.get('optimization_ranges_realism_score', 0),
                'analysis_quality_score': doc.get('analysis_quality_score', 0),
                'overall_score': doc.get('overall_score', 0),
                'created_at': doc.get('created_at').isoformat() if doc.get('created_at') else None
            })

        return evaluations

    async def get_evaluation_stats(self):
        """
        Get statistics about evaluations.

        Returns:
            dict: Statistics including count, average scores, etc.
        """
        pipeline = [
            {
                '$group': {
                    '_id': None,
                    'total_count': {'$sum': 1},
                    'avg_overall_score': {'$avg': '$overall_score'},
                    'avg_readability_score': {'$avg': '$report_readability_score'},
                    'avg_code_alignment_score': {'$avg': '$code_strategy_alignment_score'},
                    'avg_optimization_score': {'$avg': '$optimization_ranges_realism_score'},
                    'avg_analysis_score': {'$avg': '$analysis_quality_score'},
                    'min_overall_score': {'$min': '$overall_score'},
                    'max_overall_score': {'$max': '$overall_score'}
                }
            }
        ]

        result = await self.collection.aggregate(pipeline).to_list(length=1)

        if result:
            stats = result[0]
            stats.pop('_id', None)  # Remove the _id field
            return stats
        else:
            return {
                'total_count': 0,
                'avg_overall_score': 0,
                'avg_readability_score': 0,
                'avg_code_alignment_score': 0,
                'avg_optimization_score': 0,
                'avg_analysis_score': 0,
                'min_overall_score': 0,
                'max_overall_score': 0
            }

    async def get_evaluations_by_ticker(self, ticker: str, limit: int = 50):
        """
        Get evaluations for a specific ticker.

        Args:
            ticker (str): Stock ticker symbol
            limit (int): Maximum number of evaluations to retrieve

        Returns:
            list: List of evaluations for the ticker
        """
        cursor = self.collection.find({'ticker': ticker}).sort("created_at", -1).limit(limit)
        evaluations = []

        async for doc in cursor:
            evaluations.append({
                'id': str(doc['_id']),
                'business_question': doc.get('business_question', ''),
                'overall_score': doc.get('overall_score', 0),
                'created_at': doc.get('created_at').isoformat() if doc.get('created_at') else None
            })

        return evaluations

    async def delete_evaluation(self, evaluation_id: str):
        """
        Delete an evaluation by its ID.

        Args:
            evaluation_id (str): The ID of the evaluation to delete

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            result = await self.collection.delete_one({'_id': ObjectId(evaluation_id)})
            return result.deleted_count > 0
        except Exception as e:
            logging.error(f"Error deleting evaluation: {e}")
            return False

    async def delete_evaluations(self, evaluation_ids: list):
        """
        Delete multiple evaluations by their IDs.

        Args:
            evaluation_ids (list): List of evaluation IDs to delete

        Returns:
            dict: Dictionary containing deletion results with counts and any errors
        """
        try:
            if not evaluation_ids:
                return {
                    'success': True,
                    'deleted_count': 0,
                    'failed_ids': [],
                    'message': 'No evaluations to delete'
                }

            # Convert string IDs to ObjectIds, tracking invalid ones
            valid_object_ids = []
            invalid_ids = []

            for eval_id in evaluation_ids:
                try:
                    valid_object_ids.append(ObjectId(eval_id))
                except Exception:
                    invalid_ids.append(eval_id)
                    logging.warning(f"Invalid evaluation ID format: {eval_id}")

            # Perform bulk deletion for valid IDs
            deleted_count = 0
            if valid_object_ids:
                result = await self.collection.delete_many({'_id': {'$in': valid_object_ids}})
                deleted_count = result.deleted_count

            # Log the deletion operation for audit purposes
            logging.info(f"Bulk deletion completed: {deleted_count} evaluations deleted, {len(invalid_ids)} invalid IDs")

            return {
                'success': True,
                'deleted_count': deleted_count,
                'failed_ids': invalid_ids,
                'message': f'Successfully deleted {deleted_count} evaluations'
            }

        except Exception as e:
            logging.error(f"Error in bulk deletion: {e}")
            return {
                'success': False,
                'deleted_count': 0,
                'failed_ids': evaluation_ids,
                'message': f'Bulk deletion failed: {str(e)}'
            }

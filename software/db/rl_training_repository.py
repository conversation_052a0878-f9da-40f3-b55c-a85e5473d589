from typing import List, Dict, Any, Optional
from bson import ObjectId
from datetime import datetime
import os
import json
import logging
import re
from api.database import database

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define the RL directories
RL_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'rl')
RL_MODELS_DIR = os.path.join(RL_DIR, 'models')
RL_LOGS_DIR = os.path.join(RL_DIR, 'logs')
RL_DATA_DIR = os.path.join(RL_DIR, 'data')

# Create directories if they don't exist
os.makedirs(RL_MODELS_DIR, exist_ok=True)
os.makedirs(RL_LOGS_DIR, exist_ok=True)
os.makedirs(RL_DATA_DIR, exist_ok=True)

class RLTrainingRepository:
    """Repository for managing reinforcement learning training jobs and results"""

    def __init__(self):
        self.collection = database.rl_training
        self._initialized = False

    async def initialize(self):
        """Initialize the database connection and create indexes"""
        if not self._initialized:
            try:
                # Check if collection exists, if not it will be created automatically
                collections = await database.list_collection_names()
                if "rl_training" not in collections:
                    print("rl_training collection does not exist, it will be created automatically")

                # Create indexes
                await self.collection.create_index([("environment_id", 1)])
                await self.collection.create_index([("status", 1)])
                await self.collection.create_index([("created_at", 1)])
                await self.collection.create_index([("completed_at", 1)])
                self._initialized = True
            except Exception as e:
                logger.error(f"Error initializing RL training repository: {str(e)}")
                # Continue anyway to avoid blocking the application
                self._initialized = True
        return self

    @classmethod
    async def create(cls):
        """Factory method to create and initialize a repository instance"""
        instance = cls()
        await instance.initialize()
        return instance

    async def create_training_job(self, training_data: Dict[str, Any]) -> str:
        """Create a new training job"""
        if not self._initialized:
            await self.initialize()

        try:
            # Add timestamps
            import pytz
            now = datetime.now(pytz.UTC)
            training_data["created_at"] = now
            training_data["updated_at"] = now

            # Set default status if not already set
            if "status" not in training_data:
                training_data["status"] = "pending"

            # Debug prints
            print(f"Creating training job with data:")
            print(f"  - status: {training_data.get('status')}")
            print(f"  - recurring_finetune: {training_data.get('recurring_finetune')}")
            print(f"  - schedule: {training_data.get('schedule')}")

            # Check for hyperparameter_combinations
            if "hyperparameter_combinations" in training_data:
                print(f"  - hyperparameter_combinations: {len(training_data['hyperparameter_combinations'])} combinations")

                # Check for circular references in hyperparameter_combinations
                try:
                    import json
                    # Try to serialize just the combinations to check for issues
                    json.dumps(training_data["hyperparameter_combinations"])
                    print(f"  - Successfully serialized hyperparameter_combinations to JSON")
                except Exception as e:
                    print(f"  - ERROR: Cannot serialize hyperparameter_combinations to JSON: {str(e)}")
                    print(f"  - Removing hyperparameter_combinations to avoid BSON encoding error")
                    # Remove the problematic field to allow job creation to proceed
                    del training_data["hyperparameter_combinations"]

            # Set default progress
            training_data["progress"] = 0

            # Insert into database
            result = await self.collection.insert_one(training_data)
            job_id = str(result.inserted_id)
            print(f"Created job with ID: {job_id}")

            return job_id
        except Exception as e:
            logger.error(f"Error creating training job: {str(e)}")
            raise

    async def get_training_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific training job by ID"""
        if not self._initialized:
            await self.initialize()

        try:
            # Find job by ID
            job = await self.collection.find_one({"_id": ObjectId(job_id)})

            if job:
                # Convert ObjectId to string for JSON serialization
                job["_id"] = str(job["_id"])

            return job
        except Exception as e:
            logger.error(f"Error retrieving training job: {str(e)}")
            raise

    async def update_training_job(self, job_id: str, update_data: Dict[str, Any]) -> bool:
        """Update an existing training job"""
        if not self._initialized:
            await self.initialize()

        try:
            # Add updated timestamp
            import pytz
            now = datetime.now(pytz.UTC)
            update_data["updated_at"] = now

            # If status is changing to completed, add completed_at timestamp
            if update_data.get("status") == "completed" and "completed_at" not in update_data:
                update_data["completed_at"] = now

            # Update in database
            result = await self.collection.update_one(
                {"_id": ObjectId(job_id)},
                {"$set": update_data}
            )

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating training job: {str(e)}")
            raise

    async def list_training_jobs(self, environment_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all training jobs, optionally filtered by environment_id and/or status"""
        if not self._initialized:
            await self.initialize()

        try:
            # Build query
            query = {}
            if environment_id:
                query["environment_id"] = environment_id
            if status:
                query["status"] = status

            # Debug prints
            print(f"Listing training jobs with query: {query}")

            # Get jobs
            cursor = self.collection.find(query).sort("created_at", -1)
            jobs = await cursor.to_list(length=None)

            # Convert ObjectId to string for JSON serialization
            for job in jobs:
                job["_id"] = str(job["_id"])

            print(f"Found {len(jobs)} jobs matching query")
            if status == "scheduled" or status == "pending":
                for job in jobs:
                    print(f"  - Job ID: {job['_id']}, Status: {job.get('status')}, Recurring: {job.get('recurring_finetune')}")

            return jobs
        except Exception as e:
            logger.error(f"Error listing training jobs: {str(e)}")
            raise

    async def list_training_jobs_by_optimization_group(self, optimization_group_id: str) -> List[Dict[str, Any]]:
        """List all training jobs in a specific optimization group"""
        if not self._initialized:
            await self.initialize()

        try:
            # Build query for optimization group
            query = {"optimization_group_id": optimization_group_id}

            # Debug prints
            print(f"Listing training jobs in optimization group: {optimization_group_id}")

            # Get jobs
            cursor = self.collection.find(query).sort("created_at", -1)
            jobs = await cursor.to_list(length=None)

            # Convert ObjectId to string for JSON serialization
            for job in jobs:
                job["_id"] = str(job["_id"])

            print(f"Found {len(jobs)} jobs in optimization group {optimization_group_id}")

            # If no jobs found with optimization_group_id, try with the old format
            if not jobs:
                # Try to find the first job with this ID that has optimization_group=True
                job = await self.collection.find_one({"_id": ObjectId(optimization_group_id), "optimization_group": True})

                if job:
                    # This is the first job of an old-style optimization group
                    # Find all jobs with the same environment, algorithm, and similar description
                    base_desc = job.get("description", "")
                    if " - Combination " in base_desc:
                        base_desc = base_desc.split(" - Combination ")[0]

                    # Query for similar jobs
                    similar_query = {
                        "environment_id": job.get("environment_id"),
                        "algorithm": job.get("algorithm"),
                        "description": {"$regex": f"^{re.escape(base_desc)}.*Combination"},
                        "optimization_group": True
                    }

                    cursor = self.collection.find(similar_query).sort("created_at", -1)
                    jobs = await cursor.to_list(length=None)

                    # Convert ObjectId to string for JSON serialization
                    for job in jobs:
                        job["_id"] = str(job["_id"])

                    print(f"Found {len(jobs)} jobs in old-style optimization group")

            return jobs
        except Exception as e:
            logger.error(f"Error listing training jobs by optimization group: {str(e)}")
            raise


    async def delete_training_job(self, job_id: str) -> bool:
        """Delete a training job"""
        if not self._initialized:
            await self.initialize()

        try:
            # Get the job first to get the model path
            job = await self.collection.find_one({"_id": ObjectId(job_id)})
            if not job:
                return False

            # Delete associated files if they exist
            model_path = job.get("model_path")
            if model_path and os.path.exists(model_path):
                os.remove(model_path)

            log_path = job.get("log_path")
            if log_path and os.path.exists(log_path):
                os.remove(log_path)

            # Delete from database
            result = await self.collection.delete_one({"_id": ObjectId(job_id)})

            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting training job: {str(e)}")
            raise

    async def delete_all_training_jobs_by_status(self, status: Optional[str] = None) -> int:
        """Delete all training jobs with the specified status, or all jobs if status is None"""
        if not self._initialized:
            await self.initialize()

        try:
            # Build query
            query = {}
            if status:
                query["status"] = status

            # Get all jobs matching the query
            jobs = await self.list_training_jobs(status=status)

            # Cancel any running jobs first
            for job in jobs:
                if job.get("status") == "running":
                    await self.update_training_job(job["_id"], {"status": "cancelled"})

                # Delete associated files if they exist
                model_path = job.get("model_path")
                if model_path and os.path.exists(model_path):
                    try:
                        os.remove(model_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete model file {model_path}: {str(e)}")

                log_path = job.get("log_path")
                if log_path and os.path.exists(log_path):
                    try:
                        os.remove(log_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete log file {log_path}: {str(e)}")

            # Delete all jobs matching the query
            result = await self.collection.delete_many(query)

            return result.deleted_count
        except Exception as e:
            logger.error(f"Error deleting training jobs by status: {str(e)}")
            raise

    async def get_training_results(self, job_id: str) -> Dict[str, Any]:
        """Get training results for a specific job"""
        if not self._initialized:
            await self.initialize()

        try:
            # Get the job
            job = await self.collection.find_one({"_id": ObjectId(job_id)})
            if not job:
                raise ValueError(f"Training job {job_id} not found")

            # Check if job is completed or scheduled
            if job.get("status") != "completed" and job.get("status") != "scheduled":
                return {
                    "status": job.get("status", "unknown"),
                    "progress": job.get("progress", 0),
                    "message": f"Training job is in {job.get('status', 'unknown')} state"
                }

            # Get results
            results = {
                "status": job.get("status"),
                "model_path": job.get("model_path"),
                "training_time": job.get("training_time"),
                "mean_reward": job.get("mean_reward"),
                "std_reward": job.get("std_reward"),
                "hyperparameters": job.get("hyperparameters"),
                "environment_id": job.get("environment_id"),
                "created_at": job.get("created_at"),
                "completed_at": job.get("completed_at"),
                "recurring_finetune": job.get("recurring_finetune", False),
                "save_only_if_improved": job.get("save_only_if_improved", False),
                "schedule": job.get("schedule"),
                "improved": job.get("improved"),
                "original_mean_reward": job.get("original_mean_reward"),
                "model_kept": job.get("model_kept")
            }

            # Get historical finetuning attempts if this is a recurring job
            if job.get("recurring_finetune"):
                print(f"Getting finetuning history for recurring job {job_id}")
                finetuning_history = await self.get_finetuning_history(job_id)
                results["finetuning_history"] = finetuning_history
                print(f"Added {len(finetuning_history)} finetuning history entries to results")
            else:
                print(f"Job {job_id} is not a recurring job, skipping finetuning history")

            return results
        except Exception as e:
            logger.error(f"Error getting training results: {str(e)}")
            raise

    def get_model_path(self, job_id: str, model_name: str) -> str:
        """Get the path where a model should be saved"""
        return os.path.join(RL_MODELS_DIR, f"{model_name}_{job_id}")

    def get_log_path(self, job_id: str) -> str:
        """Get the path where training logs should be saved"""
        return os.path.join(RL_LOGS_DIR, f"training_{job_id}.log")

    def get_data_path(self, job_id: str) -> str:
        """Get the path where training data should be saved"""
        return os.path.join(RL_DATA_DIR, f"data_{job_id}.json")

    async def save_training_data(self, job_id: str, data: Dict[str, Any]) -> str:
        """Save training data to a file"""
        data_path = self.get_data_path(job_id)

        with open(data_path, 'w') as f:
            json.dump(data, f, indent=2)

        return data_path

    async def load_training_data(self, job_id: str) -> Dict[str, Any]:
        """Load training data from a file"""
        data_path = self.get_data_path(job_id)

        if not os.path.exists(data_path):
            raise FileNotFoundError(f"Training data file {data_path} not found")

        with open(data_path, 'r') as f:
            data = json.load(f)

        return data

    async def get_finetuning_history(self, job_id: str) -> List[Dict[str, Any]]:
        """Get historical finetuning attempts for a recurring job"""
        if not self._initialized:
            await self.initialize()

        try:
            # Get the job first to verify it's a recurring job
            job = await self.collection.find_one({"_id": ObjectId(job_id)})
            if not job or not job.get("recurring_finetune"):
                return []

            # Find all finetuning attempts for this job
            # We'll look for jobs with the same environment_id and a parent_job_id field
            # that matches this job's ID
            query = {
                "parent_job_id": job_id,
                "finetune": True
            }

            print(f"Looking for finetuning history with query: {query}")

            # Get all finetuning attempts
            cursor = self.collection.find(query).sort("created_at", -1)
            history = await cursor.to_list(length=None)

            # Convert ObjectId to string for JSON serialization
            for attempt in history:
                attempt["_id"] = str(attempt["_id"])

            print(f"Found {len(history)} finetuning attempts for job {job_id}")

            # If no history is found with parent_job_id, this might be an older job
            # Try to find history based on environment_id and recurring_finetune flag
            if not history and job.get("environment_id"):
                alt_query = {
                    "environment_id": job.get("environment_id"),
                    "finetune": True,
                    "created_at": {"$gt": job.get("created_at")}
                }

                print(f"Looking for alternative finetuning history with query: {alt_query}")

                alt_cursor = self.collection.find(alt_query).sort("created_at", -1)
                alt_history = await alt_cursor.to_list(length=None)

                # Convert ObjectId to string for JSON serialization
                for attempt in alt_history:
                    attempt["_id"] = str(attempt["_id"])

                print(f"Found {len(alt_history)} alternative finetuning attempts for job {job_id}")

                history = alt_history

            return history
        except Exception as e:
            logger.error(f"Error getting finetuning history: {str(e)}")
            print(f"Error getting finetuning history: {str(e)}")
            return []

    async def store_optimization_combinations(self, job_id: str, combinations: List[Dict[str, Any]]) -> bool:
        """Store hyperparameter combinations for a smart optimization job"""
        if not self._initialized:
            await self.initialize()

        try:
            # Update the job with the combinations
            result = await self.collection.update_one(
                {"_id": ObjectId(job_id)},
                {"$set": {"hyperparameter_combinations": combinations}}
            )

            print(f"Stored {len(combinations)} hyperparameter combinations for job {job_id}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error storing optimization combinations: {str(e)}")
            print(f"Error storing optimization combinations: {str(e)}")
            raise

    async def add_finetuning_attempt(self, parent_job_id: str, attempt_data: Dict[str, Any]) -> str:
        """Add a new finetuning attempt to a recurring job's history"""
        if not self._initialized:
            await self.initialize()

        try:
            # Get the parent job
            parent_job = await self.collection.find_one({"_id": ObjectId(parent_job_id)})
            if not parent_job:
                raise ValueError(f"Parent job {parent_job_id} not found")

            # Add parent job ID to attempt data
            attempt_data["parent_job_id"] = parent_job_id

            # Add timestamps
            import pytz
            now = datetime.now(pytz.UTC)
            attempt_data["created_at"] = now
            attempt_data["updated_at"] = now

            # Set status to completed
            attempt_data["status"] = "completed"

            # Insert into database
            result = await self.collection.insert_one(attempt_data)
            attempt_id = str(result.inserted_id)

            print(f"Added finetuning attempt {attempt_id} for job {parent_job_id}")

            return attempt_id
        except Exception as e:
            logger.error(f"Error adding finetuning attempt: {str(e)}")
            print(f"Error adding finetuning attempt: {str(e)}")
            raise

    async def get_scheduled_jobs_to_run(self) -> List[Dict[str, Any]]:
        """Get scheduled jobs that need to be run based on their schedule"""
        if not self._initialized:
            await self.initialize()

        try:
            # Get current time
            import pytz
            now = datetime.now(pytz.UTC)
            current_time = now.strftime("%H:%M")
            print(f"Looking for scheduled jobs at current time: {current_time}")

            # Also check for alternative time formats
            hour, minute = current_time.split(":")
            alternative_formats = [
                current_time,  # Standard format: "HH:MM"
                f"{int(hour)}:{minute}",  # Without leading zero: "H:MM"
                f"{int(hour)}:{int(minute)}",  # Without any zeros: "H:M"
                f"{hour}:{int(minute)}"  # With hour zero but not minute: "HH:M"
            ]

            print(f"Checking for times: {alternative_formats}")

            # Find scheduled jobs with matching time (any format)
            query = {
                "status": "scheduled",
                "recurring_finetune": True,
                "schedule.type": "daily",
                "$or": [{"schedule.time": time_format} for time_format in alternative_formats]
            }

            print(f"Query for scheduled jobs: {query}")

            # Also get all scheduled jobs regardless of time for debugging
            all_scheduled_query = {
                "status": "scheduled",
                "recurring_finetune": True
            }

            all_scheduled_cursor = self.collection.find(all_scheduled_query)
            all_scheduled_jobs = await all_scheduled_cursor.to_list(length=None)

            print(f"All scheduled jobs (regardless of time): {len(all_scheduled_jobs)}")
            for job in all_scheduled_jobs:
                job_id = str(job["_id"])
                schedule = job.get("schedule", {})
                schedule_time = schedule.get("time") if schedule else "No time"
                print(f"  - Job ID: {job_id}, Schedule time: {schedule_time}")

            cursor = self.collection.find(query)
            jobs = await cursor.to_list(length=None)

            # Convert ObjectId to string for JSON serialization
            for job in jobs:
                job["_id"] = str(job["_id"])

            return jobs
        except Exception as e:
            logger.error(f"Error getting scheduled jobs: {str(e)}")
            raise

from api.database import database
from bson import ObjectId
from datetime import datetime
from pymongo.errors import OperationFailure
import logging

class DataLoaderRepository:
    def __init__(self):
        self.db = None
        self.collection = None
        self._initialized = False

    async def initialize(self):
        """Initialize the database connection asynchronously using api/database."""
        if not self._initialized:
            self.db = database
            self.collection = self.db['data_loaders']
            await self.collection.create_index([("name", 1)], unique=True)
            await self.collection.create_index([("created_at", 1)])
            await self.collection.create_index([("updated_at", 1)])
            self._initialized = True
        return self

    @classmethod
    async def create(cls):
        """Factory method to create and initialize a DataLoaderRepository instance."""
        instance = cls()
        await instance.initialize()
        return instance

    async def insert_data_loader(self, data_loader_data):
        """
        Insert a new data loader into the database.

        Args:
            data_loader_data (dict): A dictionary containing the data loader metadata.

        Returns:
            str: The inserted document's ID.
        """
        data_loader_data['created_at'] = datetime.utcnow()
        data_loader_data['updated_at'] = datetime.utcnow()
        try:
            result = await self.collection.insert_one(data_loader_data)
            return str(result.inserted_id)
        except OperationFailure as e:
            print(f"Failed to insert data loader: {e}")
            raise

    async def get_data_loader(self, data_loader_id):
        """
        Retrieve a data loader by its ID.

        Args:
            data_loader_id (str): The ID of the data loader to retrieve.

        Returns:
            dict: The data loader metadata, or None if not found.
        """
        return await self.collection.find_one({'_id': ObjectId(data_loader_id)})

    async def update_data_loader(self, data_loader_id, update_data):
        """
        Update an existing data loader.

        Args:
            data_loader_id (str): The ID of the data loader to update.
            update_data (dict): The data to update in the data loader.

        Returns:
            bool: True if the update was successful, False otherwise.
        """
        update_data['updated_at'] = datetime.utcnow()
        result = await self.collection.update_one(
            {'_id': ObjectId(data_loader_id)},
            {'$set': update_data}
        )
        return result.modified_count > 0

    async def delete_data_loader(self, data_loader_id):
        """
        Delete a data loader by its ID.

        Args:
            data_loader_id (str): The ID of the data loader to delete.

        Returns:
            bool: True if the deletion was successful, False otherwise.
        """
        result = await self.collection.delete_one({'_id': ObjectId(data_loader_id)})
        return result.deleted_count > 0

    async def get_all_data_loaders(self):
        """
        Retrieve all data loaders.

        Returns:
            list: A list of all data loaders.
        """
        cursor = self.collection.find()
        return [doc async for doc in cursor]

    async def get_active_data_loaders(self):
        """
        Retrieve all active data loaders.

        Returns:
            list: A list of active data loaders.
        """
        cursor = self.collection.find({"is_active": True})
        return [doc async for doc in cursor]

    async def update_api_keys(self, data_loader_id: str, api_keys: dict) -> bool:
        """
        Update API keys for a data loader.

        Args:
            data_loader_id (str): The ID of the data loader.
            api_keys (dict): Dictionary of API keys with their configurations.

        Returns:
            bool: True if update was successful.
        """
        try:
            result = await self.collection.update_one(
                {'_id': ObjectId(data_loader_id)},
                {
                    '$set': {
                        'api_keys': api_keys,
                        'updated_at': datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logging.error(f"Error updating API keys: {e}")
            return False

    async def add_api_key(self, data_loader_id: str, key_name: str, key_data: dict) -> bool:
        """
        Add a new API key to a data loader.

        Args:
            data_loader_id (str): The ID of the data loader.
            key_name (str): Name/identifier for the API key.
            key_data (dict): API key configuration data.

        Returns:
            bool: True if addition was successful.
        """
        key_data['last_updated'] = datetime.utcnow()
        try:
            result = await self.collection.update_one(
                {'_id': ObjectId(data_loader_id)},
                {
                    '$set': {
                        f'api_keys.{key_name}': key_data,
                        'updated_at': datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logging.error(f"Error adding API key: {e}")
            return False

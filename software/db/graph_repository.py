from typing import List, Dict, Any, Optional
from bson import ObjectId
from datetime import datetime
from api.database import database
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class GraphRepository:
    def __init__(self):
        self.collection = database.graphs

    async def create_graph(self, graph_data: Dict[str, Any]) -> str:
        """Create a new graph with stages"""
        try:
            # Check if graph with this name already exists
            existing_graph = await self.collection.find_one({"name": graph_data["name"]})
            if existing_graph:
                # Update existing graph's stages
                await self.collection.update_one(
                    {"_id": existing_graph["_id"]},
                    {
                        "$set": {
                            "stages": graph_data["stages"],
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                return str(existing_graph["_id"])
            else:
                # Create new graph
                graph_data["created_at"] = datetime.utcnow()
                graph_data["updated_at"] = datetime.utcnow()
                result = await self.collection.insert_one(graph_data)
                return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error creating graph: {str(e)}")
            raise

    async def get_graph(self, graph_id: str) -> Optional[Dict[str, Any]]:
        """Get a graph by ID"""
        try:
            result = await self.collection.find_one({"_id": ObjectId(graph_id)})
            if result:
                result["_id"] = str(result["_id"])
            return result
        except Exception as e:
            logger.error(f"Error getting graph: {str(e)}")
            raise

    async def get_graph_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Finds a graph configuration by its name."""
        try:
            logger.debug(f"Attempting to find graph by name: {name}")
            graph = await self.collection.find_one({'name': name})
            if graph:
                graph["_id"] = str(graph["_id"]) # Ensure ID is string
                logger.debug(f"Found graph: {graph['_id']}")
            else:
                logger.warning(f"Graph not found with name: {name}")
            return graph
        except Exception as e:
            logger.error(f"Error getting graph by name '{name}': {str(e)}")
            raise # Re-raise after logging

    async def update_graph_stages(self, graph_id: str, stages: List[Dict[str, Any]]) -> bool:
        """Update graph stages"""
        try:
            result = await self.collection.update_one(
                {"_id": ObjectId(graph_id)},
                {
                    "$set": {
                        "stages": stages,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating graph stages: {str(e)}")
            raise

    async def list_graphs(self) -> List[Dict[str, Any]]:
        """List all graphs"""
        try:
            cursor = self.collection.find()
            graphs = await cursor.to_list(length=None)
            for graph in graphs:
                graph["_id"] = str(graph["_id"])
            return graphs
        except Exception as e:
            logger.error(f"Error listing graphs: {str(e)}")
            raise

    async def delete_graph(self, graph_id: str) -> bool:
        """Delete a graph"""
        try:
            result = await self.collection.delete_one({"_id": ObjectId(graph_id)})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting graph: {str(e)}")
            raise 
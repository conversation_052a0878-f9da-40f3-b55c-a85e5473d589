from typing import List, Dict, Any, Optional
from bson import ObjectId
from datetime import datetime
import logging
import numpy as np
from pymongo import ASCENDING

logger = logging.getLogger(__name__)

class FeatureRequestRepository:
    """Repository for managing feature requests in MongoDB."""
    
    def __init__(self, db):
        """Initialize with a MongoDB database instance."""
        self.collection = db.feature_requests
    
    async def create_indexes(self):
        """Create necessary indexes for the collection."""
        try:
            # Create index on feature_type for filtering
            await self.collection.create_index("feature_type")
            # Create index on created_at for sorting
            await self.collection.create_index("created_at")
            # Create index for vector similarity search
            await self.collection.create_index([("embedding", ASCENDING)])
            logger.info("Created indexes for feature_requests collection")
        except Exception as e:
            logger.error(f"Error creating indexes for feature_requests: {str(e)}")
    
    async def add_feature_request(self, feature_request: str, feature_type: str, embedding=None) -> str:
        """
        Add a new feature request.
        
        Args:
            feature_request: The text of the feature request
            feature_type: The type of request ('data' or 'feature')
            embedding: Vector embedding of the feature request
            
        Returns:
            The ID of the inserted document
        """
        now = datetime.utcnow()
        doc = {
            "feature_request": feature_request,
            "feature_type": feature_type,
            "created_at": now,
            "status": "pending",  # pending, in_progress, completed, rejected
            "updated_at": now,
            "implementation_details": None,
            "implemented_by": None,
            "implemented_at": None
        }
        
        # Add embedding if provided
        if embedding is not None:
            doc["embedding"] = embedding
            
        result = await self.collection.insert_one(doc)
        return str(result.inserted_id)
    
    async def get_all_feature_requests(self) -> List[Dict[str, Any]]:
        """Get all feature requests."""
        cursor = self.collection.find().sort("created_at", -1)  # Newest first
        return await cursor.to_list(length=None)
    
    async def get_feature_requests_by_type(self, feature_type: str) -> List[Dict[str, Any]]:
        """Get feature requests filtered by type."""
        cursor = self.collection.find({"feature_type": feature_type}).sort("created_at", -1)
        return await cursor.to_list(length=None)
    
    async def get_feature_requests_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Get feature requests filtered by status."""
        cursor = self.collection.find({"status": status}).sort("created_at", -1)
        return await cursor.to_list(length=None)
    
    async def get_feature_request_by_id(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific feature request by ID."""
        try:
            result = await self.collection.find_one({"_id": ObjectId(request_id)})
            return result
        except Exception as e:
            logger.error(f"Error getting feature request {request_id}: {str(e)}")
            return None
    
    async def update_feature_request_status(self, request_id: str, status: str) -> bool:
        """Update the status of a feature request."""
        try:
            result = await self.collection.update_one(
                {"_id": ObjectId(request_id)},
                {"$set": {"status": status, "updated_at": datetime.utcnow()}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating feature request {request_id} status: {str(e)}")
            return False
    
    async def mark_as_implemented(self, request_id: str, details: str, user_id: str) -> bool:
        """Mark a feature request as implemented."""
        try:
            now = datetime.utcnow()
            result = await self.collection.update_one(
                {"_id": ObjectId(request_id)},
                {
                    "$set": {
                        "status": "completed",
                        "implementation_details": details,
                        "implemented_by": user_id,
                        "implemented_at": now,
                        "updated_at": now
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error marking feature request {request_id} as implemented: {str(e)}")
            return False
    
    async def reject_feature_request(self, request_id: str, reason: str) -> bool:
        """Reject a feature request."""
        try:
            result = await self.collection.update_one(
                {"_id": ObjectId(request_id)},
                {
                    "$set": {
                        "status": "rejected",
                        "rejection_reason": reason,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error rejecting feature request {request_id}: {str(e)}")
            return False
    
    async def toggle_feature_request_status(self, request_id: str) -> dict:
        """
        Toggle a feature request status between pending and completed.
        
        Args:
            request_id: The ID of the feature request
            
        Returns:
            Dictionary with success status and new status value
        """
        try:
            # First get current status
            request = await self.get_feature_request_by_id(request_id)
            if not request:
                return {"success": False, "error": "Feature request not found"}
            
            # Toggle between pending and completed
            current_status = request.get("status", "pending")
            new_status = "completed" if current_status == "pending" else "pending"
            
            now = datetime.utcnow()
            update_data = {
                "status": new_status,
                "updated_at": now
            }
            
            # If changing to completed, add implementation details
            if new_status == "completed":
                update_data["implemented_at"] = now
                update_data["implemented_by"] = "user"  # Could be replaced with actual user ID
            
            result = await self.collection.update_one(
                {"_id": ObjectId(request_id)},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                return {"success": True, "new_status": new_status}
            else:
                return {"success": False, "error": "Failed to update status"}
                
        except Exception as e:
            logger.error(f"Error toggling feature request {request_id} status: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def delete_feature_request(self, request_id: str) -> bool:
        """Delete a feature request."""
        try:
            result = await self.collection.delete_one({"_id": ObjectId(request_id)})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting feature request {request_id}: {str(e)}")
            return False
    
    async def find_similar_requests(self, embedding, threshold=0.85) -> List[Dict[str, Any]]:
        """
        Find feature requests similar to the given embedding.
        
        Args:
            embedding: Vector embedding to compare against
            threshold: Similarity threshold (0.0 to 1.0)
            
        Returns:
            List of similar feature requests
        """
        try:
            similar_requests = []
            
            # Get all requests with embeddings
            cursor = self.collection.find({"embedding": {"$exists": True}})
            requests = await cursor.to_list(length=None)
            
            for request in requests:
                stored_embedding = request.get("embedding")
                if stored_embedding:
                    # Calculate cosine similarity
                    similarity = self._cosine_similarity(embedding, stored_embedding)
                    if similarity >= threshold:
                        similar_requests.append({
                            "id": str(request["_id"]),
                            "feature_request": request["feature_request"],
                            "feature_type": request["feature_type"],
                            "similarity": similarity
                        })
            
            # Sort by similarity (highest first)
            similar_requests.sort(key=lambda x: x["similarity"], reverse=True)
            return similar_requests
            
        except Exception as e:
            logger.error(f"Error finding similar requests: {str(e)}")
            return []
    
    def _cosine_similarity(self, a, b):
        """Calculate cosine similarity between two vectors."""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    async def update_feature_request(self, request_id: str, feature_request: str, embedding=None) -> bool:
        """
        Update a feature request with enhanced text and new embedding.
        
        Args:
            request_id: The ID of the feature request to update
            feature_request: The new/enhanced feature request text
            embedding: Updated vector embedding
            
        Returns:
            Boolean indicating success
        """
        try:
            update_data = {
                "feature_request": feature_request,
                "updated_at": datetime.utcnow()
            }
            
            # Add embedding if provided
            if embedding is not None:
                update_data["embedding"] = embedding
                
            result = await self.collection.update_one(
                {"_id": ObjectId(request_id)},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating feature request {request_id}: {str(e)}")
            return False 
from typing import List, Dict, Any, Optional
from bson import ObjectId
from datetime import datetime
from api.database import database
import logging
import os
import importlib.util
import inspect

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define the RL environments directory
RL_ENV_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'rl')

class RLEnvironmentRepository:
    """Repository for managing reinforcement learning environments"""
    
    def __init__(self):
        self.collection = database.rl_environments
        self._initialized = False
        
    async def initialize(self):
        """Initialize the database connection and create indexes"""
        if not self._initialized:
            # Create indexes
            await self.collection.create_index([("name", 1)], unique=True)
            await self.collection.create_index([("created_at", 1)])
            await self.collection.create_index([("updated_at", 1)])
            self._initialized = True
        return self
        
    @classmethod
    async def create(cls):
        """Factory method to create and initialize a repository instance"""
        instance = cls()
        await instance.initialize()
        return instance
        
    async def create_environment(self, environment_data: Dict[str, Any]) -> str:
        """Create a new RL environment"""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Check if environment with this name already exists
            existing = await self.collection.find_one({"name": environment_data["name"]})
            if existing:
                raise ValueError(f"Environment with name '{environment_data['name']}' already exists")
            
            # Add timestamps
            environment_data["created_at"] = datetime.utcnow()
            environment_data["updated_at"] = datetime.utcnow()
            
            # Insert into database
            result = await self.collection.insert_one(environment_data)
            
            # Save code to file if needed
            file_path = os.path.join(RL_ENV_DIR, f"{environment_data['name'].lower().replace(' ', '_')}.py")
            with open(file_path, "w") as f:
                f.write(environment_data["code"])
            
            return str(result.inserted_id)
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"Error creating RL environment: {str(e)}")
            raise
            
    async def get_environment(self, env_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific RL environment by ID"""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Find environment by ID
            environment = await self.collection.find_one({"_id": ObjectId(env_id)})
            
            if environment:
                # Convert ObjectId to string for JSON serialization
                environment["_id"] = str(environment["_id"])
                
            return environment
        except Exception as e:
            logger.error(f"Error retrieving RL environment: {str(e)}")
            raise
            
    async def get_environment_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a specific RL environment by name"""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Find environment by name
            environment = await self.collection.find_one({"name": name})
            
            if environment:
                # Convert ObjectId to string for JSON serialization
                environment["_id"] = str(environment["_id"])
                
            return environment
        except Exception as e:
            logger.error(f"Error retrieving RL environment by name: {str(e)}")
            raise
            
    async def update_environment(self, env_id: str, update_data: Dict[str, Any]) -> bool:
        """Update an existing RL environment"""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Check if environment exists
            environment = await self.collection.find_one({"_id": ObjectId(env_id)})
            if not environment:
                raise ValueError("Environment not found")
            
            # Add updated timestamp
            update_data["updated_at"] = datetime.utcnow()
            
            # Update in database
            result = await self.collection.update_one(
                {"_id": ObjectId(env_id)},
                {"$set": update_data}
            )
            
            # Update code file if code was updated
            if "code" in update_data:
                # Get current name or use updated name
                env_name = update_data.get("name", environment["name"])
                file_path = os.path.join(RL_ENV_DIR, f"{env_name.lower().replace(' ', '_')}.py")
                with open(file_path, "w") as f:
                    f.write(update_data["code"])
            
            return result.modified_count > 0
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"Error updating RL environment: {str(e)}")
            raise
            
    async def delete_environment(self, env_id: str) -> bool:
        """Delete an RL environment"""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Check if environment exists
            environment = await self.collection.find_one({"_id": ObjectId(env_id)})
            if not environment:
                raise ValueError("Environment not found")
            
            # Delete from database
            result = await self.collection.delete_one({"_id": ObjectId(env_id)})
            
            # Optionally delete file
            env_name = environment["name"]
            file_path = os.path.join(RL_ENV_DIR, f"{env_name.lower().replace(' ', '_')}.py")
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return result.deleted_count > 0
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"Error deleting RL environment: {str(e)}")
            raise
            
    async def list_environments(self) -> List[Dict[str, Any]]:
        """List all RL environments"""
        if not self._initialized:
            await self.initialize()
            
        try:
            cursor = self.collection.find()
            environments = await cursor.to_list(length=None)
            
            # Convert ObjectId to string for JSON serialization
            for env in environments:
                env["_id"] = str(env["_id"])
                
            return environments
        except Exception as e:
            logger.error(f"Error listing RL environments: {str(e)}")
            raise
            
    async def get_available_environments(self) -> List[Dict[str, Any]]:
        """Get list of available RL environments from the filesystem"""
        if not self._initialized:
            await self.initialize()
            
        try:
            environments = []
            
            # Check RL directory for Python files
            if os.path.exists(RL_ENV_DIR):
                for file in os.listdir(RL_ENV_DIR):
                    if file.endswith(".py") and not file.startswith("__"):
                        # Get module name
                        module_name = file[:-3]
                        
                        # Try to import the module to get class info
                        try:
                            spec = importlib.util.spec_from_file_location(
                                module_name, 
                                os.path.join(RL_ENV_DIR, file)
                            )
                            module = importlib.util.module_from_spec(spec)
                            spec.loader.exec_module(module)
                            
                            # Find gym.Env subclasses
                            for name, obj in inspect.getmembers(module):
                                if (inspect.isclass(obj) and 
                                    hasattr(obj, '__mro__') and 
                                    any('gym.Env' in str(mro) for mro in obj.__mro__)):
                                    
                                    # Get docstring for description
                                    description = obj.__doc__ or "No description available"
                                    
                                    environments.append({
                                        "file": file,
                                        "module": module_name,
                                        "class": name,
                                        "description": description.strip()
                                    })
                        except Exception as e:
                            logger.warning(f"Error importing {file}: {str(e)}")
                            environments.append({
                                "file": file,
                                "module": module_name,
                                "error": str(e)
                            })
            
            return environments
        except Exception as e:
            logger.error(f"Error getting available environments: {str(e)}")
            raise

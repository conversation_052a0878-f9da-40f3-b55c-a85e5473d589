import logging
from typing import Dict, Any, List, Optional
from bson import ObjectId
from datetime import datetime
from api.database import ai_tools_collection

logger = logging.getLogger(__name__)

class ToolRepository:
    """Repository for managing AI tools metadata"""
    
    def __init__(self):
        self.collection = ai_tools_collection
    
    async def create_indexes(self) -> None:
        """Create necessary indexes for the collection"""
        logger.info("Creating indexes for tools collection...")
        try:
            # Get list of existing indexes
            existing_indexes = await self.collection.list_indexes().to_list(None)
            
            # Check for indexes on specific fields regardless of name
            field_to_index = {}
            for idx in existing_indexes:
                for field_name in idx.get('key', {}):
                    if field_name not in field_to_index and field_name != '_id':
                        field_to_index[field_name] = idx.get('name')
                        logger.info(f"Found existing index '{idx.get('name')}' on field '{field_name}'")
            
            # Define indexes
            indexes = [
                ("name", "tool_name_idx", True),  # Make name unique
                ("category", "tool_category_idx", False)
            ]
            
            # Create indexes only if they don't exist on the field
            for field, index_name, unique in indexes:
                if field not in field_to_index:
                    logger.info(f"Creating index {index_name} on field {field}")
                    try:
                        await self.collection.create_index(field, name=index_name, unique=unique)
                    except Exception as e:
                        # Check if this is an index conflict error
                        if "Index already exists with a different name" in str(e):
                            logger.warning(f"Index conflict on field {field}: {str(e)}")
                            # We can continue as the index exists, just with a different name
                        else:
                            # For other errors, re-raise
                            raise
                else:
                    existing_name = field_to_index[field]
                    logger.info(f"Skipping index creation on field '{field}' as it already has index '{existing_name}'")
                    
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")
            raise
    
    async def register_tool(self, tool_data: Dict[str, Any]) -> str:
        """Register a tool's metadata in the database"""
        logger.info(f"[DEBUG] Starting tool registration with data: {tool_data}")
        try:
            # Extract only metadata fields
            metadata = {
                "name": tool_data["name"],
                "description": tool_data["description"],
                "category": tool_data["category"],
                "version": tool_data["version"],
                "parameters": tool_data["parameters"],
                "file_path": tool_data["file_path"],
                "updated_at": datetime.utcnow()
            }
            logger.info(f"[DEBUG] Extracted metadata: {metadata}")
            
            # Ensure indexes exist
            logger.info("[DEBUG] Creating indexes")
            try:
                await self.create_indexes()
            except Exception as e:
                # If index creation fails but it's just a conflict, we can continue
                if "Index already exists with a different name" in str(e):
                    logger.warning(f"Index conflict during tool registration: {str(e)}")
                    # We can continue as the index exists, just with a different name
                else:
                    # For other errors, re-raise
                    raise
            
            # Upsert the tool based on name
            logger.info(f"[DEBUG] Upserting tool with name: {metadata['name']}")
            result = await self.collection.update_one(
                {"name": metadata["name"]},
                {"$set": metadata},
                upsert=True
            )
            
            logger.info(f"[DEBUG] Tool registration result - matched: {result.matched_count}, modified: {result.modified_count}, upserted_id: {result.upserted_id}")
            
            if result.upserted_id:
                tool_id = str(result.upserted_id)
                logger.info(f"[DEBUG] Tool upserted with new ID: {tool_id}")
                return tool_id
            else:
                # If no upsert, get the existing document's ID
                logger.info("[DEBUG] Tool updated, fetching existing ID")
                doc = await self.collection.find_one({"name": metadata["name"]})
                tool_id = str(doc["_id"]) if doc else None
                logger.info(f"[DEBUG] Found existing tool ID: {tool_id}")
                return tool_id
                
        except Exception as e:
            logger.error(f"[DEBUG] Error registering tool: {str(e)}")
            logger.exception("[DEBUG] Full exception traceback:")
            raise
    
    async def get_tool(self, tool_id: str) -> Optional[Dict[str, Any]]:
        """Get a tool's metadata by ID"""
        logger.info(f"Fetching tool by ID: {tool_id}")
        try:
            tool = await self.collection.find_one({"_id": ObjectId(tool_id)})
            if tool:
                tool["_id"] = str(tool["_id"])
                logger.info(f"Found tool: {tool.get('name')}")
            else:
                logger.info(f"No tool found with ID: {tool_id}")
            return tool
        except Exception as e:
            logger.error(f"Error fetching tool: {str(e)}")
            raise
    
    async def get_tool_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a tool's metadata by name"""
        try:
            tool = await self.collection.find_one({"name": name})
            if tool:
                tool["_id"] = str(tool["_id"])
            return tool
        except Exception as e:
            logger.error(f"Error getting tool by name: {str(e)}")
            return None
    
    async def list_tools(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all tools' metadata, optionally filtered by category"""
        logger.info(f"Listing tools{' for category: ' + category if category else ''}")
        try:
            query = {"category": category} if category else {}
            cursor = self.collection.find(query)
            tools = await cursor.to_list(length=None)
            
            # Convert ObjectId to string
            for tool in tools:
                tool["_id"] = str(tool["_id"])
                
            logger.info(f"Found {len(tools)} tools")
            return tools
            
        except Exception as e:
            logger.error(f"Error listing tools: {str(e)}")
            raise
    
    async def delete_tool(self, tool_id: str) -> bool:
        """Delete a tool's metadata by ID"""
        try:
            result = await self.collection.delete_one({"_id": ObjectId(tool_id)})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting tool: {str(e)}")
            return False 
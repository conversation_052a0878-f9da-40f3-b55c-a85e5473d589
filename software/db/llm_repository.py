from bson import ObjectId
from typing import List, Optional, Dict, Any
from datetime import datetime
from api.database import database
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()
llm_collection = database.llm_models

class LLMRepository:
    @staticmethod
    async def create_llm(data: Dict) -> str:
        """Create a new LLM config entry"""
        # Ensure required fields are present (adjust based on Pydantic validation)
        required_fields = ["name", "model_type", "connection_code"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")

        # Keep only allowed fields
        allowed_fields = {"name", "model_type", "connection_code", "is_default", "is_enabled"}
        db_data = {k: v for k, v in data.items() if k in allowed_fields}

        db_data["created_at"] = datetime.utcnow()
        db_data["updated_at"] = datetime.utcnow()
        db_data["is_enabled"] = db_data.get("is_enabled", True)
        db_data["is_default"] = db_data.get("is_default", False)
        db_data["connection_code"] = db_data.get("connection_code", "") # Already required, but keep default just in case

        # Remove any old/unexpected fields explicitly before insert? (Optional)
        # For example: db_data.pop('provider', None) etc.

        result = await llm_collection.insert_one(db_data)
        inserted_id = str(result.inserted_id)

        # If this one is set as default, unset others of the same type
        if db_data["is_default"]:
            llm_type = db_data.get("model_type")
            if llm_type:
                await LLMRepository.set_default_llm(inserted_id, llm_type)
            else:
                print(f"Warning: Cannot set default for LLM {inserted_id} without a 'type'.")

        return inserted_id

    @staticmethod
    async def get_llm(llm_id: str) -> Optional[Dict]:
        """Get LLM model by ID"""
        result = await llm_collection.find_one({"_id": ObjectId(llm_id)})
        if result:
            result["_id"] = str(result["_id"])
        return result

    @staticmethod
    async def list_llms() -> List[Dict]:
        """List all LLM models"""
        cursor = llm_collection.find()
        llms = await cursor.to_list(length=None)
        for llm in llms:
            llm["_id"] = str(llm["_id"])
        return llms

    @staticmethod
    async def update_llm(llm_id: str, data: Dict) -> bool:
        """Update LLM config"""
        # Keep only allowed fields for update
        allowed_fields = {"name", "model_type", "connection_code", "is_default", "is_enabled"}
        update_payload = {k: v for k, v in data.items() if k in allowed_fields}

        if not update_payload: # Prevent empty updates
            return False

        update_payload["updated_at"] = datetime.utcnow()

        set_as_default = update_payload.get("is_default")

        result = await llm_collection.update_one(
            {"_id": ObjectId(llm_id)},
            {"$set": update_payload}
        )

        if result.modified_count > 0 and set_as_default is True:
             # Fetch the updated document to get its type if not in update_payload
             updated_llm = await LLMRepository.get_llm(llm_id)
             if updated_llm:
                 llm_type = updated_llm.get("model_type") # Type should always exist
                 if llm_type:
                     await LLMRepository.set_default_llm(llm_id, llm_type)
                 else:
                     print(f"Warning: Cannot set default for updated LLM {llm_id} without a 'type'.")

        return result.modified_count > 0

    @staticmethod
    async def delete_llm(llm_id: str) -> bool:
        """Delete LLM model"""
        result = await llm_collection.delete_one({"_id": ObjectId(llm_id)})
        return result.deleted_count > 0

    @staticmethod
    async def toggle_llm(llm_id: str) -> bool:
        """Toggle LLM model enabled status"""
        llm = await llm_collection.find_one({"_id": ObjectId(llm_id)})
        if not llm:
            return False

        new_status = not llm.get("is_enabled", True)
        result = await llm_collection.update_one(
            {"_id": ObjectId(llm_id)},
            {"$set": {"is_enabled": new_status, "updated_at": datetime.utcnow()}}
        )
        return result.modified_count > 0

    @staticmethod
    async def get_default_llm(llm_type: str, debug: bool = True) -> Optional[Dict]:
        """Get the default LLM model for a given type"""
        # Debug the query
        query = {"model_type": llm_type, "is_default": True, "is_enabled": True}

        # Show query being executed only if debug=True
        if debug:
            console.print(Panel(
                f"Searching for default LLM with query: {query}",
                title="🔍 Database Query",
                border_style="yellow"
            ))

        # Try with model_type
        result = await llm_collection.find_one(query)

        # If no result, try with type instead (backward compatibility)
        if not result:
            alt_query = {"type": llm_type, "is_default": True, "is_enabled": True}
            if debug:
                console.print(Panel(
                    f"No result with model_type, trying with 'type' field: {alt_query}",
                    title="🔍 Alternate Query",
                    border_style="yellow"
                ))
            result = await llm_collection.find_one(alt_query)

        # Debug the result
        if result:
            if debug:
                console.print(Panel(
                    f"Found default {llm_type} model: {result.get('name', 'Unnamed')} (ID: {result.get('_id')})",
                    title="✅ Default Model Found",
                    border_style="yellow"
                ))
            result["_id"] = str(result["_id"])
        else:
            # Only show debug info if debug=True
            if debug:
                # Count how many models of this type exist (enabled or not)
                model_type_count = await llm_collection.count_documents({"model_type": llm_type})
                type_count = await llm_collection.count_documents({"type": llm_type})
                default_count = await llm_collection.count_documents(
                    {"$or": [
                        {"model_type": llm_type, "is_default": True},
                        {"type": llm_type, "is_default": True}
                    ]}
                )
                enabled_count = await llm_collection.count_documents(
                    {"$or": [
                        {"model_type": llm_type, "is_enabled": True},
                        {"type": llm_type, "is_enabled": True}
                    ]}
                )

                # Detailed debug panel
                error_title = Text(f"⚠️ No Default {llm_type} Found", style="bold yellow")
                error_content = Text.assemble(
                    ("Database query returned no results.\n\n", "white"),
                    ("Stats for this type:\n", "bold cyan"),
                    (f"• Total models with model_type={llm_type}: {model_type_count}\n", "white"),
                    (f"• Total models with type={llm_type}: {type_count}\n", "white"),
                    (f"• Models marked default: {default_count}\n", "white"),
                    (f"• Models enabled: {enabled_count}\n\n", "white"),
                    ("Possible issues:\n", "bold yellow"),
                    ("• No models exist with this type\n", "white"),
                    ("• A model exists but isn't marked as default\n", "white"),
                    ("• A default model exists but is disabled\n", "white"),
                    ("• Your model has type set but not model_type", "white")
                )

                console.print(Panel(
                    error_content,
                    title=error_title,
                    border_style="yellow",
                    padding=(1, 2),
                    expand=False
                ))

                # List all models for debugging, checking both field names
                all_query = {"$or": [{"model_type": llm_type}, {"type": llm_type}]}
                models_cursor = llm_collection.find(all_query).limit(5)
                models = await models_cursor.to_list(length=5)

                if models:
                    models_info = []
                    for m in models:
                        model_type_field = m.get('model_type', 'Not Set')
                        type_field = m.get('type', 'Not Set')
                        models_info.append(
                            f"ID: {m['_id']}, Name: {m.get('name', 'Unnamed')}, "
                            f"model_type: {model_type_field}, type: {type_field}, "
                            f"Default: {m.get('is_default', False)}, Enabled: {m.get('is_enabled', False)}"
                        )

                    console.print(Panel(
                        "\n".join(models_info),
                        title=f"📋 Found {len(models)} Models (showing max 5)",
                        border_style="yellow"
                    ))

        return result

    @staticmethod
    def get_llm_sync(llm_id: str) -> Optional[Dict]:
        """Get LLM model by ID (sync version)"""
        result = llm_collection.find_one({"_id": ObjectId(llm_id)})
        if result:
            result["_id"] = str(result["_id"])
        return result

    @staticmethod
    def get_default_llm_sync(llm_type: str, debug: bool = True) -> Optional[Dict]:
        """Get the default LLM model for a given type (sync version)"""
        query = {"model_type": llm_type, "is_default": True, "is_enabled": True}
        result = llm_collection.find_one(query)
        if not result:
            alt_query = {"type": llm_type, "is_default": True, "is_enabled": True}
            result = llm_collection.find_one(alt_query)
        if result and "_id" in result:
            result["_id"] = str(result["_id"])
        return result

    @staticmethod
    async def set_default_llm(llm_id: str, llm_type: str) -> bool:
        """Set a specific LLM as the default for its type, unsetting others."""
        # Unset other defaults of the same type
        await llm_collection.update_many(
            {"_id": {"$ne": ObjectId(llm_id)}, "model_type": llm_type, "is_default": True},
            {"$set": {"is_default": False, "updated_at": datetime.utcnow()}}
        )

        # Set the specified LLM as default
        result = await llm_collection.update_one(
            {"_id": ObjectId(llm_id), "model_type": llm_type},
            {"$set": {"is_default": True, "updated_at": datetime.utcnow()}}
        )
        return result.modified_count > 0
from datetime import datetime
from typing import Dict, Any, List, Optional
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCollection
import logging

from api.database import research_directors_collection
import numpy as np  # Added import

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResearchDirectorRepository:
    """Repository for managing research director agents and their memory"""

    def __init__(self):
        self.collection: AsyncIOMotorCollection = research_directors_collection

    async def create_director(self,
        name: str,
        title: str,
        experience_years: int,
        expertise: List[str],
        analysis_style: str,
        background: str,
        personality: str
    ) -> Dict[str, Any]:
        """Create a new research director agent"""
        director = {
            "name": name,
            "title": title,
            "experience_years": experience_years,
            "expertise": expertise,
            "analysis_style": analysis_style,
            "background": background,
            "personality": personality,
            "created_at": datetime.now(),
            "last_active": datetime.now(),
            "total_reports": 0,
            "report_ids": [],  # Track all report IDs created by this director
            "memory": {
                "market_insights": [],  # Key market insights learned
                "company_analyses": {},  # Companies analyzed and key takeaways
                "sector_knowledge": {},  # Sector-specific accumulated knowledge
                "research_history": [],  # History of research conducted
                "decision_patterns": [],  # Patterns in decision making
                "performance_metrics": {
                    "successful_predictions": 0,
                    "total_predictions": 0,
                    "sectors_analyzed": []
                }
            }
        }
        result = await self.collection.insert_one(director)
        director["id"] = str(result.inserted_id)
        return director

    async def get_director(self, director_id: str) -> Optional[Dict[str, Any]]:
        """Get a research director by ID"""
        try:
            director = await self.collection.find_one({"_id": ObjectId(director_id)})
            if director:
                director["id"] = str(director["_id"])
                del director["_id"]
            return director
        except Exception as e:
            logger.error(f"Error getting director: {str(e)}")
            return None

    async def list_directors(self) -> List[Dict[str, Any]]:
        """Get all research directors"""
        try:
            cursor = self.collection.find()
            directors = []

            async for director in cursor:
                try:
                    # Convert _id to string id
                    director["id"] = str(director["_id"])
                    del director["_id"]

                    # Ensure all required fields are present
                    if "total_reports" not in director:
                        director["total_reports"] = 0
                    if "last_active" not in director:
                        director["last_active"] = datetime.now()

                    directors.append(director)
                    logger.debug(f"Processed director: {director.get('name', 'Unknown')}")
                except Exception as e:
                    logger.error(f"Error processing director {director.get('name', 'Unknown')}: {str(e)}")
                    raise

            logger.info(f"Successfully fetched {len(directors)} directors")
            return directors
        except Exception as e:
            logger.error(f"Error listing directors: {str(e)}")
            return []

    async def update_memory(self, director_id: str, memory_update: Dict[str, Any]) -> None:
        """Update the director's memory with new insights"""
        try:
            await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                {
                    "$push": {
                        "memory.market_insights": {
                            "$each": memory_update.get("market_insights", [])
                        },
                        "memory.research_history": {
                            "$each": memory_update.get("research_history", [])
                        },
                        "memory.decision_patterns": {
                            "$each": memory_update.get("decision_patterns", [])
                        }
                    },
                    "$set": {
                        "last_active": datetime.now(),
                        **{f"memory.company_analyses.{k}": v
                           for k, v in memory_update.get("company_analyses", {}).items()},
                        **{f"memory.sector_knowledge.{k}": v
                           for k, v in memory_update.get("sector_knowledge", {}).items()}
                    },
                    "$inc": {"total_reports": 1}
                }
            )
        except Exception as e:
            logger.error(f"Error updating director memory: {str(e)}")

    async def update_performance(self,
        director_id: str,
        prediction_correct: bool,
        sector: Optional[str] = None
    ) -> None:
        """Update the director's performance metrics"""
        try:
            update = {
                "$inc": {
                    "memory.performance_metrics.total_predictions": 1
                },
                "$set": {"last_active": datetime.now()}
            }

            if prediction_correct:
                update["$inc"]["memory.performance_metrics.successful_predictions"] = 1

            if sector:
                update["$addToSet"] = {"memory.performance_metrics.sectors_analyzed": sector}

            await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                update
            )
        except Exception as e:
            logger.error(f"Error updating director performance: {str(e)}")

    async def add_successful_tool_embeddings(self, director_id: str, successful_tools: Dict[str, Dict[str, Any]]) -> None:
        """Add successful tool execution data (including embeddings) to the director's memory.

        Args:
            director_id: The ID of the director.
            successful_tools: A dictionary where keys are tool IDs and values are dictionaries containing
                             'name', 'content', 'args', 'tool_embeddings', and 'chunks' with their embeddings.
        """
        try:
            # First ensure the director exists and has the memory structure
            director = await self.collection.find_one({"_id": ObjectId(director_id)})
            if not director:
                logger.error(f"Director {director_id} not found")
                return

            # Create memory structure if it doesn't exist
            if "memory" not in director:
                await self.collection.update_one(
                    {"_id": ObjectId(director_id)},
                    {"$set": {"memory": {}}}
                )

            # Create successful_tool_embeddings if it doesn't exist
            if "memory" in director and ("successful_tool_embeddings" not in director.get("memory", {})):
                await self.collection.update_one(
                    {"_id": ObjectId(director_id)},
                    {"$set": {"memory.successful_tool_embeddings": {}}}
                )

            # Add each tool individually
            for tool_id, tool_data in successful_tools.items():
                await self.collection.update_one(
                    {"_id": ObjectId(director_id)},
                    {"$set": {f"memory.successful_tool_embeddings.{tool_id}": tool_data}}
                )

            # Update last_active time
            await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                {"$set": {"last_active": datetime.now()}}
            )

            logger.info(f"Successfully stored {len(successful_tools)} tools with embeddings for director {director_id}")
        except Exception as e:
            logger.error(f"Error storing tool embeddings: {str(e)}")

    def _cosine_similarity(self, a, b):
        """Calculate cosine similarity between two vectors."""
        # Ensure inputs are numpy arrays
        a = np.array(a)
        b = np.array(b)
        # Handle potential zero vectors
        norm_a = np.linalg.norm(a)
        norm_b = np.linalg.norm(b)
        if norm_a == 0 or norm_b == 0:
            return 0.0
        return np.dot(a, b) / (norm_a * norm_b)

    async def find_similar_tool_executions(self, director_id: str, query_embedding: List[float], threshold: float = 0.5, limit: int = 5) -> List[Dict[str, Any]]:
        """Find past successful tool executions similar to the query embedding.

        Args:
            director_id: The ID of the director whose memory to search.
            query_embedding: Vector embedding of the query (e.g., business question).
            threshold: Minimum cosine similarity threshold (0.0 to 1.0).
            limit: Maximum number of similar executions to return.

        Returns:
            List of similar tool execution dictionaries, sorted by similarity descending.
        """
        try:
            director = await self.get_director(director_id)
            if not director:
                logger.warning(f"Director {director_id} not found")
                return []

            if "memory" not in director:
                logger.warning(f"Director {director_id} has no memory field")
                return []

            if "successful_tool_embeddings" not in director["memory"]:
                logger.warning(f"Director {director_id} has no successful_tool_embeddings in memory")
                return []

            stored_executions = director["memory"]["successful_tool_embeddings"]
            similar_executions = []

            execution_count = len(stored_executions)
            logger.info(f"Found {execution_count} stored tool executions for director {director_id}")

            if execution_count == 0:
                return []

            # Debug the first tool to verify structure
            if execution_count > 0:
                first_tool_id = next(iter(stored_executions))
                first_tool = stored_executions[first_tool_id]
                logger.info(f"First tool ID: {first_tool_id}")
                logger.info(f"First tool name: {first_tool.get('name', 'unknown')}")
                has_embeddings = 'embeddings' in first_tool and isinstance(first_tool['embeddings'], list)
                logger.info(f"First tool has valid embeddings: {has_embeddings}")
                if has_embeddings:
                    logger.info(f"First tool embeddings size: {len(first_tool['embeddings'])}")

                # Check if chunks exist and log their structure
                if 'chunks' in first_tool:
                    logger.info(f"First tool has chunks: {len(first_tool['chunks'])} chunks found")
                    # Note: In MongoDB, chunk IDs are stored as strings

            for tool_id, execution_data in stored_executions.items():
                # Use the 'embeddings' field instead of 'embedding'
                stored_embedding = execution_data.get("embeddings")

                if not stored_embedding or not isinstance(stored_embedding, list):
                    logger.warning(f"Tool {tool_id} has invalid or missing embeddings")
                    continue

                if len(stored_embedding) != len(query_embedding):
                    logger.warning(f"Tool {tool_id} embedding size ({len(stored_embedding)}) doesn't match query embedding size ({len(query_embedding)})")
                    continue

                similarity = self._cosine_similarity(query_embedding, stored_embedding)
                logger.debug(f"Tool {tool_id} similarity: {similarity:.4f}")

                if similarity >= threshold:
                    result_data = execution_data.copy()
                    result_data["similarity"] = similarity
                    result_data["tool_id"] = tool_id
                    # Remove embeddings and chunks to reduce response size
                    if "embeddings" in result_data:
                        del result_data["embeddings"]
                    if "chunks" in result_data:
                        del result_data["chunks"]
                    similar_executions.append(result_data)
                    logger.info(f"Tool {tool_id} matched with similarity {similarity:.4f}")

            # Sort by similarity (highest first) and limit results
            similar_executions.sort(key=lambda x: x["similarity"], reverse=True)
            result_count = len(similar_executions)
            logger.info(f"Found {result_count} similar executions above threshold {threshold}")
            return similar_executions[:limit]

        except Exception as e:
            logger.error(f"Error finding similar tool executions: {str(e)}")
            logger.exception(e)
            return []

    async def create_indexes(self) -> None:
        """Create necessary indexes for the collection"""
        try:
            await self.collection.create_index([("name", 1)], unique=True)
            await self.collection.create_index([("last_active", -1)])
            await self.collection.create_index([("total_reports", -1)])
            logger.info("Created indexes for research_directors collection")
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")
            # Don't raise since indexes might already exist

    async def add_report_id(self, director_id: str, report_id: str) -> None:
        """Add a report ID to the director's list of reports"""
        try:
            await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                {
                    "$push": {"report_ids": report_id},
                    "$inc": {"total_reports": 1},
                    "$set": {"last_active": datetime.now()}
                }
            )
            logger.info(f"Added report {report_id} to director {director_id}")
        except Exception as e:
            logger.error(f"Error adding report ID to director: {str(e)}")

    async def get_director_reports(self, director_id: str) -> List[str]:
        """Get all report IDs for a director"""
        try:
            director = await self.get_director(director_id)
            return director.get("report_ids", []) if director else []
        except Exception as e:
            logger.error(f"Error getting director reports: {str(e)}")
            return []

    async def remove_report_id(self, director_id: str, report_id: str) -> bool:
        """Remove a report ID from the director's list of reports

        Args:
            director_id: The ID of the director
            report_id: The ID of the report to remove

        Returns:
            bool: True if the report was removed successfully, False otherwise
        """
        try:
            # First check if the director exists
            director = await self.collection.find_one({"_id": ObjectId(director_id)})

            if not director:
                logger.warning(f"Director {director_id} not found")
                return False

            # Check if the report ID exists in the director's report_ids list
            if "report_ids" not in director or report_id not in director.get("report_ids", []):
                logger.warning(f"Report {report_id} not found in director {director_id}'s reports")
                return False

            # Remove the report ID from the list and decrement the total_reports count
            result = await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                {
                    "$pull": {"report_ids": report_id},
                    "$inc": {"total_reports": -1},
                    "$set": {"last_active": datetime.now()}
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Removed report {report_id} from director {director_id}")
            else:
                logger.warning(f"Failed to remove report {report_id} from director {director_id}")

            return success
        except Exception as e:
            logger.error(f"Error removing report ID from director: {str(e)}")
            return False

    async def connect_to_letta_agent(self, director_id: str, letta_agent_id: str) -> bool:
        """Connect a director to a Letta agent by storing the agent ID in memory

        Args:
            director_id: The ID of the director to connect
            letta_agent_id: The ID of the Letta agent to connect to

        Returns:
            bool: True if the connection was successful, False otherwise
        """

        try:
            # First check if the director exists
            director = await self.collection.find_one({"_id": ObjectId(director_id)})

            if not director:
                return False

            # Check if memory field exists
            if "memory" not in director:
                # Create memory field if it doesn't exist
                await self.collection.update_one(
                    {"_id": ObjectId(director_id)},
                    {"$set": {"memory": {}}}
                )

            result = await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                {
                    "$set": {
                        "memory.letta_agent_id": letta_agent_id,
                        "last_active": datetime.now()
                    }
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Connected director {director_id} to Letta agent {letta_agent_id}")
            else:
                logger.warning(f"Failed to connect director {director_id} to Letta agent {letta_agent_id}")

            return success
        except Exception as e:
            logger.error(f"Error connecting director to Letta agent: {str(e)}")
            return False

    async def disconnect_from_letta_agent(self, director_id: str) -> bool:
        """Disconnect a director from a Letta agent by removing the agent ID from memory

        Args:
            director_id: The ID of the director to disconnect

        Returns:
            bool: True if the disconnection was successful, False otherwise
        """

        try:
            # First check if the director exists
            director = await self.collection.find_one({"_id": ObjectId(director_id)})

            if not director:
                return False

            # Check if memory field exists and has a Letta agent ID
            if "memory" not in director or "letta_agent_id" not in director.get("memory", {}):
                return False

            result = await self.collection.update_one(
                {"_id": ObjectId(director_id)},
                {
                    "$unset": {"memory.letta_agent_id": ""},
                    "$set": {"last_active": datetime.now()}
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Disconnected director {director_id} from Letta agent")
            else:
                logger.warning(f"Failed to disconnect director {director_id} from Letta agent")

            return success
        except Exception as e:
            logger.error(f"Error disconnecting director from Letta agent: {str(e)}")
            return False

    async def get_letta_agent_id(self, director_id: str) -> Optional[str]:
        """Get the Letta agent ID for a director

        Args:
            director_id: The ID of the director

        Returns:
            Optional[str]: The Letta agent ID if found, None otherwise
        """
        try:
            director = await self.get_director(director_id)
            if director and "memory" in director:
                return director["memory"].get("letta_agent_id")
            return None
        except Exception as e:
            logger.error(f"Error getting Letta agent ID for director {director_id}: {str(e)}")
            return None

    async def reset_letta_agent_messages(self, letta_agent_id: str) -> bool:
        """Reset all messages for a Letta agent

        Args:
            letta_agent_id: The ID of the Letta agent

        Returns:
            bool: True if the reset was successful, False otherwise
        """
        try:
            from letta_client import Letta

            # Initialize Letta client
            client = Letta(base_url="http://localhost:8283")

            # Reset the agent's messages
            client.agents.messages.reset(agent_id=letta_agent_id)

            logger.info(f"Successfully reset messages for Letta agent {letta_agent_id}")
            return True
        except Exception as e:
            logger.error(f"Error resetting messages for Letta agent {letta_agent_id}: {str(e)}")
            return False

    async def get_memory_block(self, letta_agent_id: str, block_label: str) -> Dict[str, Any]:
        """Get a specific memory block from a Letta agent

        Args:
            letta_agent_id: The ID of the Letta agent
            block_label: The label of the memory block to retrieve

        Returns:
            Dict[str, Any]: A dictionary containing the memory block content or an error message
        """
        try:
            from letta_client import Letta

            # Initialize Letta client
            client = Letta(base_url="http://localhost:8283")

            # Directly retrieve the memory block using the client API
            memory_block = client.agents.blocks.retrieve(
                agent_id=letta_agent_id,
                block_label=block_label,
            )

            if memory_block:
                logger.info(f"Successfully retrieved memory block '{block_label}' for Letta agent {letta_agent_id}")
                return {
                    "status": "success",
                    "label": block_label,
                    "content": memory_block.value,
                    "agent_id": letta_agent_id
                }
            else:
                logger.warning(f"Memory block '{block_label}' not found for Letta agent {letta_agent_id}")
                return {
                    "status": "error",
                    "message": f"Memory block '{block_label}' not found"
                }

        except Exception as e:
            logger.error(f"Error getting memory block '{block_label}' for Letta agent {letta_agent_id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Error retrieving memory block: {str(e)}"
            }

    async def delete_director(self, director_id: str) -> bool:
        """Delete a research director by ID

        Args:
            director_id: The ID of the director to delete

        Returns:
            bool: True if the director was deleted successfully, False otherwise
        """
        try:
            # First check if the director exists and get its details
            director = await self.get_director(director_id)
            if not director:
                logger.warning(f"Director {director_id} not found for deletion")
                return False

            # Check if the director is connected to a Letta agent
            letta_agent_id = director.get("memory", {}).get("letta_agent_id")
            if letta_agent_id:
                # Disconnect from Letta agent first
                await self.disconnect_from_letta_agent(director_id)

                # Optionally delete the Letta agent too
                try:
                    from letta_client import Letta
                    client = Letta(base_url="http://localhost:8283")
                    client.agents.delete(agent_id=letta_agent_id)
                    logger.info(f"Deleted associated Letta agent {letta_agent_id}")
                except Exception as e:
                    logger.warning(f"Could not delete Letta agent {letta_agent_id}: {str(e)}")

            # Import research repository to handle forecast revisits
            from db.research_repository import ResearchRepository
            research_repo = ResearchRepository()

            # Find all analyses by this director
            cursor = research_repo.collection.find({"director_id": director_id})

            # Delete all forecast revisits associated with this director's reports
            async for report in cursor:
                report_id = str(report["_id"])

                # Check if this report has a forecast revisit
                if "forecast_revisit_id" in report:
                    forecast_revisit_id = report["forecast_revisit_id"]
                    await research_repo.revisit_collection.delete_one({"_id": ObjectId(forecast_revisit_id)})
                    logger.info(f"Deleted forecast revisit {forecast_revisit_id} for report {report_id}")

            # Delete the director
            result = await self.collection.delete_one({"_id": ObjectId(director_id)})

            if result.deleted_count > 0:
                logger.info(f"Successfully deleted director {director_id}")
                return True
            else:
                logger.warning(f"Director {director_id} not found or could not be deleted")
                return False

        except Exception as e:
            logger.error(f"Error deleting director {director_id}: {str(e)}")
            return False
from fastapi import APIRouter, HTTPException, Request
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime
from ai.llm.llm_connect import get_llm_connect
from langchain.schema import HumanMessage, AIMessage
from langchain.chat_models.base import BaseChatModel
from langchain.embeddings.base import Embeddings
from fastapi import BackgroundTasks
from ai.graph import get_available_graphs
from db.llm_repository import LLMRepository

router = APIRouter()

class ModelType(str, Enum):
    COMPLETION = "Completion"
    EMBEDDING = "Embedding"

class LLMModel(BaseModel):
    name: str = Field(..., min_length=1)
    model_type: ModelType = ModelType.COMPLETION
    connection_code: str = Field(..., min_length=1)
    is_default: bool = False
    is_enabled: bool = True

    class Config:
        validate_assignment = True

class TestPrompt(BaseModel):
    prompt: str

class TextsToEmbed(BaseModel):
    texts: List[str]

class TextToEmbed(BaseModel):
    text: str

class GraphRunRequest(BaseModel):
    graph_id: str
    initial_state: Dict[str, Any] = {}

@router.post("/llm")
async def create_llm(llm: LLMModel):
    """Create a new LLM config using LLMRepository"""
    try:
        data = llm.model_dump()
        llm_id = await LLMRepository.create_llm(data)
        return {"id": llm_id, "message": "LLM config created successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create LLM config: {str(e)}")

@router.get("/llm/{llm_id}")
async def get_llm(llm_id: str):
    """Get LLM config by ID using LLMRepository"""
    try:
        result = await LLMRepository.get_llm(llm_id)
        if not result:
            raise HTTPException(status_code=404, detail="LLM config not found")
        # Repository already converts _id to str
        return result
    except HTTPException:
        raise # Re-raise HTTPException (like 404)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get LLM: {str(e)}")

@router.get("/llm")
async def list_llms() -> List[Dict]:
    """List all LLM configs using LLMRepository"""
    try:
        # Repository handles finding all and converting _id to str
        llms = await LLMRepository.list_llms()
        return llms
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list LLMs: {str(e)}")

@router.get("/llm/default/{model_type}")
async def get_default_model(model_type: ModelType):
    """Get the default model for a specific type using LLMRepository"""
    try:
        # Use the repository method to get the enabled default model
        default_model = await LLMRepository.get_default_llm(model_type.value) # Pass the enum value
        if not default_model:
            raise HTTPException(status_code=404, detail=f"No enabled default {model_type.value} model found")
        # Repository already converts _id to str
        return default_model
    except HTTPException:
        raise # Re-raise HTTPException (like 404)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get default LLM: {str(e)}")

@router.put("/llm/{llm_id}")
async def update_llm(llm_id: str, llm: LLMModel):
    """Update LLM config using LLMRepository"""
    try:
        data = llm.model_dump(exclude_unset=True)
        
        success = await LLMRepository.update_llm(llm_id, data)
        
        if not success:
             existing = await LLMRepository.get_llm(llm_id)
             if not existing:
                 raise HTTPException(status_code=404, detail="LLM config not found")
             else:
                 return {"message": "LLM config update attempted, no changes detected."}

        return {"message": "LLM config updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update LLM config: {str(e)}")

@router.delete("/llm/{llm_id}")
async def delete_llm(llm_id: str):
    """Delete LLM config using LLMRepository"""
    try:
        success = await LLMRepository.delete_llm(llm_id)
        if not success:
            raise HTTPException(status_code=404, detail="LLM config not found or could not be deleted")
        return {"message": "LLM config deleted successfully"}
    except HTTPException:
        raise # Re-raise HTTPException (like 404)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete LLM: {str(e)}")

@router.patch("/llm/{llm_id}/toggle")
async def toggle_llm(llm_id: str):
    """Toggle LLM config enabled status using LLMRepository"""
    try:
        success = await LLMRepository.toggle_llm(llm_id)
        if not success:
            # Could be not found or toggle failed for other reasons
            raise HTTPException(status_code=404, detail="LLM config not found or toggle failed") 
        # Fetch the updated status to return it
        updated_llm = await LLMRepository.get_llm(llm_id)
        if not updated_llm: # Should not happen if toggle succeeded, but good practice
             raise HTTPException(status_code=404, detail="LLM config not found after toggle")
        new_status = updated_llm.get("is_enabled")
        return {"message": f"LLM config toggled successfully. Enabled: {new_status}"}
    except HTTPException:
        raise # Re-raise HTTPException (like 404)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to toggle LLM: {str(e)}")

@router.post("/llm/{llm_id}/test")
async def test_llm(llm_id: str, test_prompt: TestPrompt):
    """Test an LLM config with a prompt using llm_connect"""
    try:
        llm_connect = get_llm_connect()
        llm_instance = await llm_connect.get_llm_by_id(llm_id)

        if not llm_instance:
            raise HTTPException(status_code=404, detail=f"Could not load LLM instance for config ID: {llm_id}")

        if not isinstance(llm_instance, BaseChatModel):
             raise HTTPException(status_code=400, detail=f"Config ID {llm_id} does not represent a Completion model.")

        message = HumanMessage(content=test_prompt.prompt)
        response = await llm_instance.ainvoke([message])
        
        # Extract content and metadata
        response_content = getattr(response, 'content', str(response))
        response_metadata = getattr(response, 'response_metadata', {})

        # We can no longer easily provide model_info like provider/model_name
        # But response_metadata might contain model name, token usage etc.
        return {
            "response": response_content,
            "metadata": response_metadata # Return the metadata dictionary
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        import traceback
        print(f"Error in /llm/{llm_id}/test: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error during LLM test: {str(e)}")

@router.post("/graph/run")
async def run_graph_with_llm(graph_request: GraphRunRequest):
    """Run a graph with a specific LLM config"""
    try:
        # Get graph info
        graph_info = get_available_graphs().get(graph_request.graph_id)
        if not graph_info:
            raise HTTPException(status_code=404, detail="Graph not found")

        # Get graph module
        graph_module = graph_info.get("module")
        if not graph_module:
            raise HTTPException(status_code=404, detail="Graph module not found")

        # Get graph instance
        graph = getattr(graph_module, "graph", None)
        if not graph:
            raise HTTPException(status_code=404, detail="Graph instance not found")

        # Run graph with initial state
        result = await graph.ainvoke(graph_request.initial_state)

        return {"status": "success", "data": result}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/llm/{llm_id}/embed-documents")
async def embed_documents(llm_id: str, texts_to_embed: TextsToEmbed):
    """Embed texts using a specific embedding config ID"""
    try:
        llm_connect = get_llm_connect()
        embedding_instance = await llm_connect.get_llm_by_id(llm_id)

        if not embedding_instance:
            raise HTTPException(status_code=404, detail=f"Could not load Embedding instance for config ID: {llm_id}")

        if not isinstance(embedding_instance, Embeddings):
             raise HTTPException(status_code=400, detail=f"Config ID {llm_id} does not represent an Embedding model.")

        embeddings = await embedding_instance.aembed_documents(texts_to_embed.texts)

        return {
            "embeddings": embeddings
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error during document embedding: {str(e)}")

@router.post("/llm/{llm_id}/embed-query")
async def embed_query(llm_id: str, text_to_embed: TextToEmbed):
    """Embed a query using a specific embedding config ID"""
    try:
        llm_connect = get_llm_connect()
        embedding_instance = await llm_connect.get_llm_by_id(llm_id)

        if not embedding_instance:
             raise HTTPException(status_code=404, detail=f"Could not load Embedding instance for config ID: {llm_id}")

        if not isinstance(embedding_instance, Embeddings):
             raise HTTPException(status_code=400, detail=f"Config ID {llm_id} does not represent an Embedding model.")

        embedding = await embedding_instance.aembed_query(text_to_embed.text)

        return {
            "embedding": embedding
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error during query embedding: {str(e)}")
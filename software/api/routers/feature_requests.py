from fastapi import APIRouter, HTTPException, Request, Body, Depends
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
from datetime import datetime
from bson import ObjectId
import json
import logging

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

from ..database import database
from db.feature_request_repository import FeatureRequestRepository

router = APIRouter()
feature_request_repo = FeatureRequestRepository(database)

# Pydantic models for request/response
class FeatureRequestBase(BaseModel):
    feature_request: str
    feature_type: str
    
class FeatureRequestCreate(FeatureRequestBase):
    pass

class FeatureRequestUpdate(BaseModel):
    status: Optional[str] = None
    implementation_details: Optional[str] = None
    rejection_reason: Optional[str] = None

class FeatureRequestResponse(FeatureRequestBase):
    id: str
    created_at: datetime
    updated_at: datetime
    status: str
    implementation_details: Optional[str] = None
    implemented_by: Optional[str] = None
    implemented_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    
    class Config:
        json_encoders = {
            ObjectId: str
        }

# Helper function to convert MongoDB document to Pydantic model
def convert_doc_to_model(doc):
    if doc is None:
        return None
    doc["id"] = str(doc["_id"])
    del doc["_id"]
    return doc

@router.get("/api/feature-requests")
async def get_all_feature_requests():
    """Get all feature requests."""
    try:
        logger.debug("Fetching all feature requests")
        requests = await feature_request_repo.get_all_feature_requests()
        logger.debug(f"Found {len(requests)} feature requests")
        result = {"feature_requests": [convert_doc_to_model(req) for req in requests]}
        logger.debug(f"Returning {len(result['feature_requests'])} feature requests")
        return result
    except Exception as e:
        logger.error(f"Error retrieving feature requests: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving feature requests: {str(e)}")

@router.get("/api/feature-requests/{request_id}", response_model=dict)
async def get_feature_request(request_id: str):
    """Get a specific feature request by ID."""
    try:
        request = await feature_request_repo.get_feature_request_by_id(request_id)
        if not request:
            raise HTTPException(status_code=404, detail="Feature request not found")
        return convert_doc_to_model(request)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving feature request: {str(e)}")

@router.post("/api/feature-requests", response_model=dict)
async def create_feature_request(request: FeatureRequestCreate = Body(...)):
    """Create a new feature request."""
    try:
        request_id = await feature_request_repo.add_feature_request(
            feature_request=request.feature_request,
            feature_type=request.feature_type
        )
        new_request = await feature_request_repo.get_feature_request_by_id(request_id)
        return {"message": "Feature request created", "id": request_id, "request": convert_doc_to_model(new_request)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating feature request: {str(e)}")

@router.put("/api/feature-requests/{request_id}", response_model=dict)
async def update_feature_request(request_id: str, update_data: FeatureRequestUpdate = Body(...)):
    """Update a feature request."""
    try:
        # Get the current request
        current_request = await feature_request_repo.get_feature_request_by_id(request_id)
        if not current_request:
            raise HTTPException(status_code=404, detail="Feature request not found")
        
        # Update based on the provided data
        if update_data.status:
            if update_data.status == "completed" and update_data.implementation_details:
                # Mark as implemented
                success = await feature_request_repo.mark_as_implemented(
                    request_id=request_id,
                    details=update_data.implementation_details,
                    user_id="system"  # Replace with actual user ID when available
                )
            elif update_data.status == "rejected" and update_data.rejection_reason:
                # Reject with reason
                success = await feature_request_repo.reject_feature_request(
                    request_id=request_id,
                    reason=update_data.rejection_reason
                )
            else:
                # Simple status update
                success = await feature_request_repo.update_feature_request_status(
                    request_id=request_id,
                    status=update_data.status
                )
        
            if not success:
                raise HTTPException(status_code=500, detail="Failed to update feature request")
        
        # Get the updated request
        updated_request = await feature_request_repo.get_feature_request_by_id(request_id)
        return {"message": "Feature request updated", "request": convert_doc_to_model(updated_request)}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating feature request: {str(e)}")

@router.delete("/api/feature-requests/{request_id}")
async def delete_feature_request(request_id: str):
    """Delete a feature request."""
    try:
        logger.debug(f"Attempting to delete feature request with ID: {request_id}")
        # Check if request exists first
        request = await feature_request_repo.get_feature_request_by_id(request_id)
        if not request:
            logger.warning(f"Feature request not found for deletion: {request_id}")
            raise HTTPException(status_code=404, detail="Feature request not found")
            
        # Proceed with deletion
        success = await feature_request_repo.delete_feature_request(request_id)
        if not success:
            logger.error(f"Failed to delete feature request: {request_id}")
            raise HTTPException(status_code=500, detail="Failed to delete feature request")
            
        logger.debug(f"Successfully deleted feature request: {request_id}")
        return {"message": "Feature request deleted", "id": request_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting feature request {request_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting feature request: {str(e)}")

@router.get("/api/feature-requests/type/{feature_type}", response_model=List[dict])
async def get_feature_requests_by_type(feature_type: str):
    """Get feature requests filtered by type."""
    if feature_type not in ["data", "feature"]:
        raise HTTPException(status_code=400, detail="Invalid feature type. Must be 'data' or 'feature'")
    
    try:
        requests = await feature_request_repo.get_feature_requests_by_type(feature_type)
        return {"feature_requests": [convert_doc_to_model(req) for req in requests]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving feature requests: {str(e)}")

@router.get("/api/feature-requests/status/{status}", response_model=List[dict])
async def get_feature_requests_by_status(status: str):
    """Get feature requests filtered by status."""
    valid_statuses = ["pending", "in_progress", "completed", "rejected"]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
    
    try:
        requests = await feature_request_repo.get_feature_requests_by_status(status)
        return {"feature_requests": [convert_doc_to_model(req) for req in requests]}
    except Exception as e:
        logger.error(f"Error retrieving feature requests: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving feature requests: {str(e)}")

@router.patch("/api/feature-requests/{request_id}/toggle", response_model=dict)
async def toggle_feature_request_status(request_id: str):
    """Toggle a feature request status between pending and completed."""
    try:
        logger.debug(f"Toggling status for feature request: {request_id}")
        result = await feature_request_repo.toggle_feature_request_status(request_id)
        
        if not result["success"]:
            if "not found" in result.get("error", ""):
                raise HTTPException(status_code=404, detail=f"Feature request not found: {request_id}")
            else:
                raise HTTPException(status_code=500, detail=f"Failed to toggle status: {result.get('error')}")
        
        # Get updated request to return
        updated_request = await feature_request_repo.get_feature_request_by_id(request_id)
        if not updated_request:
            raise HTTPException(status_code=404, detail="Feature request not found after update")
            
        logger.debug(f"Successfully toggled status to: {result['new_status']}")
        return {
            "success": True, 
            "message": f"Status toggled to: {result['new_status']}",
            "request": convert_doc_to_model(updated_request)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling feature request status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error toggling feature request status: {str(e)}") 
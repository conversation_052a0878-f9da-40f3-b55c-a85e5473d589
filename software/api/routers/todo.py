from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, <PERSON>, validator
from typing import List, Optional
from db.todo_repository import TodoRepository
from datetime import datetime, timezone
from bson import ObjectId, errors as bson_errors

router = APIRouter()
todo_repo = TodoRepository()

class TodoItem(BaseModel):
    id: Optional[str] = Field(alias="_id")
    title: str
    description: Optional[str] = ""
    created_at: datetime
    completed_at: Optional[datetime] = None
    order: Optional[int] = 0

    class Config:
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            ObjectId: str
        }

    @validator('created_at', 'completed_at', pre=True)
    def ensure_timezone(cls, v):
        if isinstance(v, datetime) and v.tzinfo is None:
            return v.replace(tzinfo=timezone.utc)
        return v

class TodoItemCreate(BaseModel):
    title: str
    description: Optional[str] = ""
    order: Optional[int] = 0

class TaskOrder(BaseModel):
    id: str
    order: int

def validate_object_id(id_str: str) -> bool:
    try:
        ObjectId(id_str)
        return True
    except (bson_errors.InvalidId, TypeError):
        return False

@router.post("/api/todos", response_model=TodoItem)
async def create_todo_item(todo: TodoItemCreate):
    try:
        todo_data = todo.dict()
        # Get the next order value
        if todo_data.get('order') is None:
            todo_data['order'] = await todo_repo.get_next_order_value()
        # Ensure we're using UTC time with timezone info
        todo_data['created_at'] = datetime.now(timezone.utc)
        inserted_id = await todo_repo.insert_todo(todo_data)
        created_todo = await todo_repo.get_todo_by_id(inserted_id)
        return created_todo
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/todos/{todo_id}", response_model=TodoItem)
async def get_todo_item(todo_id: str):
    if not validate_object_id(todo_id):
        raise HTTPException(status_code=400, detail="Invalid todo ID format")

    todo = await todo_repo.get_todo_by_id(todo_id)
    if not todo:
        raise HTTPException(status_code=404, detail="To-Do item not found")
    return TodoItem(**todo)

@router.get("/api/todos", response_model=List[TodoItem])
async def list_todo_items():
    try:
        todos = await todo_repo.get_all_todos()
        return [TodoItem(**todo) for todo in todos]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/api/todos/{todo_id}", response_model=TodoItem)
async def update_todo_item(todo_id: str, todo_update: dict):
    if not validate_object_id(todo_id):
        raise HTTPException(status_code=400, detail="Invalid todo ID format")

    try:
        # First check if the todo exists
        existing_todo = await todo_repo.get_todo_by_id(todo_id)
        if not existing_todo:
            raise HTTPException(status_code=404, detail="To-Do item not found")

        # Prepare update data
        update_data = {}
        if "completed" in todo_update:
            update_data["completed_at"] = datetime.utcnow() if todo_update["completed"] else None
        if "title" in todo_update:
            update_data["title"] = todo_update["title"]
        if "description" in todo_update:
            update_data["description"] = todo_update["description"]

        # Update the todo
        await todo_repo.update_todo(todo_id, update_data)
        
        # Get and return the updated todo
        updated_todo = await todo_repo.get_todo_by_id(todo_id)
        if not updated_todo:
            raise HTTPException(status_code=500, detail="Failed to retrieve updated todo")
            
        return TodoItem(**updated_todo)
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error updating todo: {str(e)}")  # Debug log
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/todos/{todo_id}")
async def delete_todo_item(todo_id: str):
    if not validate_object_id(todo_id):
        raise HTTPException(status_code=400, detail="Invalid todo ID format")

    try:
        if not await todo_repo.delete_todo(todo_id):
            raise HTTPException(status_code=404, detail="To-Do item not found")
        return {"message": "To-Do item deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/todos/order")
async def update_task_order(order: List[TaskOrder]):
    try:
        # Validate all IDs before processing
        for item in order:
            if not validate_object_id(item.id):
                raise HTTPException(status_code=400, detail=f"Invalid todo ID format: {item.id}")

        await todo_repo.update_task_order([item.dict() for item in order])
        return {"message": "Task order updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

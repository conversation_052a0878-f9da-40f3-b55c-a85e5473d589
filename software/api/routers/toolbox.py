from fastapi import APIRouter, HTTPException
import logging
from typing import List, Dict, Any
import importlib
import inspect
import os
import sys
from pathlib import Path

# Import registry directly at the module level
from ai.tools.registry import ToolRegistry
from ai.tools.base_tool import BaseTool

router = APIRouter()
logger = logging.getLogger(__name__)

# Use the existing registry instance
from ai.tools import registry as tool_registry

def load_tools() -> List[Dict[str, Any]]:
    """Load all tool classes from the tools directory"""
    # Load tools from registry
    tool_registry.load_tools_sync()
    
    tools = []
    
    try:
        # Get all tools directly (skip calling get_langchain_tools to avoid recursion)
        all_tools = tool_registry._tools
        
        # Process active tools from registry
        for name, tool in all_tools.items():
            # Skip __init__ and other system files that might get included
            if name == "__init__" or name.startswith("__"):
                logger.info(f"Skipping system file: {name}")
                continue
                
            try:
                # Create dictionary representation directly
                tool_dict = {
                    "name": getattr(tool, "name", name),
                    "description": getattr(tool, "description", "No description available"),
                    "category": getattr(tool, "category", "general"),
                    "version": getattr(tool, "version", "1.0.0"),
                    "file_path": f"ai/tools/{name}.py",
                    "_id": name,  # Use name as ID
                    "enabled": tool_registry.is_tool_enabled(name)  # Check if enabled
                }
                
                # Extract parameters if possible
                if hasattr(tool, "args_schema"):
                    try:
                        schema = tool.args_schema.schema()
                        parameters = {}
                        for field_name, field in schema.get("properties", {}).items():
                            parameters[field_name] = {
                                "type": field.get("type", "any"),
                                "description": field.get("description", ""),
                                "required": field_name in schema.get("required", [])
                            }
                        tool_dict["parameters"] = parameters
                    except Exception as e:
                        logger.warning(f"Error extracting parameters for {name}: {str(e)}")
                        tool_dict["parameters"] = {}
                else:
                    tool_dict["parameters"] = {}
                
                tools.append(tool_dict)
            except Exception as e:
                logger.error(f"Error processing tool {name}: {str(e)}")
        
        # Also check for disabled tools (files with _ suffix)
        tools_dir = Path(__file__).parent.parent.parent / "ai" / "tools"
        disabled_tools = {}
        for file in tools_dir.glob("*_.py"):
            if file.name.endswith("_.py"):
                tool_name = file.stem[:-1]  # Remove the trailing underscore
                
                # Skip system files
                if tool_name == "__init__" or tool_name.startswith("__"):
                    logger.info(f"Skipping disabled system file: {tool_name}")
                    continue
                    
                # Skip if it's already in the list (should not happen)
                if any(t.get("_id") == tool_name for t in tools):
                    continue
                    
                try:
                    content = read_source_code(f"ai/tools/{file.name}")
                    # Basic parsing to extract name and description
                    name_match = content.find('name: str = Field(') 
                    desc_match = content.find('description: str = Field(')
                    
                    name = tool_name
                    description = "Disabled tool"
                    
                    if name_match > 0 and desc_match > 0:
                        # Try to extract values from quotes
                        name_line = content[name_match:content.find('\n', name_match)]
                        desc_line = content[desc_match:content.find('\n', desc_match)]
                        
                        if '"' in name_line:
                            name = name_line.split('"')[1]
                        elif "'" in name_line:
                            name = name_line.split("'")[1]
                            
                        if '"' in desc_line:
                            description = desc_line.split('"')[1]
                        elif "'" in desc_line:
                            description = desc_line.split("'")[1]
                    
                    # Create a disabled tool entry
                    tool_dict = {
                        "name": name,
                        "description": description,
                        "category": "general",
                        "version": "1.0.0",
                        "file_path": f"ai/tools/{file.name}",
                        "_id": tool_name,
                        "enabled": False
                    }
                    
                    tools.append(tool_dict)
                except Exception as e:
                    logger.error(f"Error loading disabled tool {tool_name}: {str(e)}")
                
    except Exception as e:
        logger.error(f"Error loading tools: {str(e)}")
        logger.exception("Full exception traceback:")
        
    return tools

def read_source_code(file_path: str) -> str:
    """Read source code from a file"""
    try:
        project_root = Path(__file__).parent.parent.parent
        full_path = project_root / file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"Tool file not found: {file_path}")
            
        with open(full_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading source code from {file_path}: {str(e)}")
        raise

@router.get("/list")
async def list_tools():
    """List all available tools"""
    logger.info("Fetching tools from files...")
    tools = load_tools()
    logger.info(f"Found {len(tools)} tools: {[tool.get('name') for tool in tools]}")
    return tools

@router.get("/{tool_id}")
async def get_tool(tool_id: str):
    """Get a specific tool by ID (name)"""
    tools = load_tools()
    tool = next((t for t in tools if t["_id"] == tool_id), None)
    
    if not tool:
        raise HTTPException(status_code=404, detail="Tool not found")
        
    # Load source code from file
    try:
        tool["source_code"] = read_source_code(tool["file_path"])
    except Exception as e:
        logger.error(f"Error loading source code: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to load source code: {str(e)}")
        
    return tool

@router.post("/sync")
async def sync_tools():
    """Sync tools from the tools package (reload tools)"""
    try:
        logger.info("Syncing tools - reloading registry...")
        
        # Import reload_tools directly and call it
        from ai.tools import reload_tools
        reload_tools()
        
        # Get fresh tools after reload
        tools = load_tools()
        tool_names = [tool["name"] for tool in tools]
        
        logger.info(f"Successfully synced {len(tools)} tools")
        return {"message": f"Synced {len(tools)} tools", "synced_tools": tool_names}
    except Exception as e:
        logger.error(f"Error in sync_tools: {str(e)}")
        logger.exception("Full exception traceback:")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create")
async def create_tool(tool_data: Dict[str, Any]):
    """Create a new tool file"""
    try:
        logger.info(f"[DEBUG] Starting tool creation with data: {tool_data}")
        
        # Validate tool name
        if not tool_data.get("name"):
            raise HTTPException(status_code=400, detail="Tool name is required")
            
        # Validate PascalCase and 'Tool' suffix
        name = tool_data["name"]
        logger.info(f"[DEBUG] Validating tool name: {name}")
        if not name[0].isupper() or not name.endswith("Tool"):
            raise HTTPException(status_code=400, detail="Tool name must be in PascalCase and end with 'Tool'")
            
        # Additional PascalCase validation
        if not all(part[0].isupper() for part in name.replace("Tool", "").split() if part):
            raise HTTPException(status_code=400, detail="Tool name must be in PascalCase (e.g., MyNewTool)")
            
        # Get the tools directory
        tools_dir = Path(__file__).parent.parent.parent / "ai" / "tools"
        logger.info(f"[DEBUG] Tools directory path: {tools_dir}")
        if not tools_dir.exists():
            raise HTTPException(status_code=500, detail="Tools directory not found")
            
        # Create the tool file path
        file_path = tools_dir / f"{name}.py"
        logger.info(f"[DEBUG] Tool file path: {file_path}")
        
        # Check if file already exists
        if file_path.exists():
            raise HTTPException(status_code=400, detail=f"Tool {name} already exists")
        
        # Validate tool code has required fields
        code = tool_data.get("code", "")
        logger.info("[DEBUG] Validating tool code")
        if "name: str = Field(" not in code or "description: str = Field(" not in code:
            logger.error("[DEBUG] Tool code missing required fields (name or description)")
            raise HTTPException(status_code=400, detail="Tool code must include 'name' and 'description' fields")
            
        # Write the tool code to file
        logger.info("[DEBUG] Writing tool code to file")
        with open(file_path, "w") as f:
            f.write(code)
            
        logger.info(f"[DEBUG] Created new tool file: {file_path}")
        
        # Reload the tools package
        logger.info("[DEBUG] Reloading tools package")
        try:
            # Clear module cache
            module_name = f"ai.tools.{name}"
            logger.info(f"[DEBUG] Clearing module cache for: {module_name}")
            if module_name in sys.modules:
                del sys.modules[module_name]
            if "ai.tools" in sys.modules:
                del sys.modules["ai.tools"]
                
            # Import and reload modules
            logger.info("[DEBUG] Invalidating import caches")
            importlib.invalidate_caches()
            
            # Import the new module first
            logger.info(f"[DEBUG] Importing new module: {module_name}")
            module = importlib.import_module(module_name)
            logger.info(f"[DEBUG] Successfully imported module: {module_name}")
            
            # Get the tool class
            logger.info(f"[DEBUG] Getting tool class: {name}")
            tool_class = getattr(module, name)
            logger.info(f"[DEBUG] Found tool class: {name}")
            
            # Print class attributes for debugging
            logger.info(f"[DEBUG] Tool class attributes: {dir(tool_class)}")
            logger.info(f"[DEBUG] Tool class fields: {tool_class.__fields__ if hasattr(tool_class, '__fields__') else 'No fields found'}")
            
            # Create an instance to verify it works
            logger.info(f"[DEBUG] Creating tool instance: {name}")
            tool_instance = tool_class()
            logger.info(f"[DEBUG] Successfully created tool instance: {name}")
            
            # Verify required attributes
            logger.info(f"[DEBUG] Verifying tool instance attributes")
            if not hasattr(tool_instance, 'name') or not hasattr(tool_instance, 'description'):
                raise ValueError("Tool instance missing required attributes (name or description)")
            logger.info(f"[DEBUG] Tool instance name: {tool_instance.name}")
            logger.info(f"[DEBUG] Tool instance description: {tool_instance.description}")
            
            # Reload the entire tools package to update registry
            logger.info("[DEBUG] Reloading entire tools package")
            from ai.tools import reload_tools
            reload_tools()
            logger.info("[DEBUG] Successfully reloaded tools package")
                
            return {
                "message": "Tool created successfully",
                "file_path": str(file_path),
                "tool_id": name
            }
            
        except Exception as e:
            logger.error(f"[DEBUG] Error reloading tools package: {str(e)}")
            logger.exception("[DEBUG] Full exception traceback:")
            # Clean up the file if tool loading failed
            if file_path.exists():
                file_path.unlink()
                logger.info(f"[DEBUG] Cleaned up failed tool file: {file_path}")
            raise HTTPException(status_code=500, detail=f"Failed to load tool module: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[DEBUG] Error creating tool: {str(e)}")
        logger.exception("[DEBUG] Full exception traceback:")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/{tool_id}/toggle")
async def toggle_tool(tool_id: str):
    """Toggle a tool's enabled state by renaming the file"""
    logger.info(f"Toggling tool with ID: {tool_id}")
    
    try:
        # Get the tools directory
        tools_dir = Path(__file__).parent.parent.parent / "ai" / "tools"
        
        # Check for normal file
        normal_file = tools_dir / f"{tool_id}.py"
        disabled_file = tools_dir / f"{tool_id}_.py"
        
        # File exists in normal state, disable it
        if normal_file.exists():
            logger.info(f"Disabling tool {tool_id} by renaming to {tool_id}_")
            normal_file.rename(disabled_file)
            is_enabled = False
        # File exists in disabled state, enable it
        elif disabled_file.exists():
            logger.info(f"Enabling tool {tool_id} by renaming to {tool_id}")
            disabled_file.rename(normal_file)
            is_enabled = True
        else:
            raise HTTPException(status_code=404, detail=f"Tool {tool_id} not found")
        
        # Reload the tools package to reflect changes
        try:
            # Clear module cache
            module_name = f"ai.tools.{tool_id}"
            if module_name in sys.modules:
                del sys.modules[module_name]
            if "ai.tools" in sys.modules:
                del sys.modules["ai.tools"]
                
            # Import and reload modules
            importlib.invalidate_caches()
            
            # Reload the entire tools package to update registry
            from ai.tools import reload_tools
            reload_tools()
            logger.info(f"Successfully reloaded tools package after toggling {tool_id}")
                
        except Exception as e:
            logger.error(f"Error reloading tools package: {str(e)}")
            logger.exception("Full exception traceback:")
            
        return {"message": f"Tool {tool_id} {'disabled' if not is_enabled else 'enabled'}", 
                "is_enabled": is_enabled}
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling tool {tool_id}: {str(e)}")
        logger.exception("Full exception traceback:")
        raise HTTPException(status_code=500, detail=str(e)) 
from fastapi import APIRouter, HTTPException, BackgroundTasks, Body
from pydantic import BaseModel, ConfigDict
from typing import Optional, Dict, Any, List
import asyncio
import logging
from datetime import datetime


from letta_client import Letta
from db.research_director_repository import ResearchDirectorRepository
from db.research_repository import ResearchRepository


from ..database import database
from bson import ObjectId
from ai.graph import get_available_graphs
from rich.console import Console


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()
director_repo = ResearchDirectorRepository()
research_repo = ResearchRepository()
console = Console()

async def run_graph(director_id: str, task_id: str, graph_type: str = "research_director_graph") -> Dict[str, Any]:
    """Universal function to run any graph type with a director"""
    try:
        # Get director info
        director = await director_repo.get_director(director_id)
        if not director:
            raise ValueError("Director not found")

        # Get graph module
        graph_info = get_available_graphs().get(graph_type)
        if not graph_info:
            raise ValueError(f"Graph type {graph_type} not found")

        graph_module = graph_info.get("module")
        if not graph_module:
            raise ValueError(f"Invalid graph module for {graph_type}")

        # Initialize minimal state - each graph should handle its own state expansion
        initial_state = {
            "director_id": director_id,
            "task_id": task_id
        }

        # Check if this is the critique agent graph which requires special handling
        if graph_type == "critique_agent_graph" and hasattr(graph_module, "run_critique_agent"):
            # Use the dedicated runner function
            thread_id = f"thread_{task_id}"
            result = await graph_module.run_critique_agent(initial_state, thread_id)
        elif hasattr(graph_module, "graph"):
            # Get the graph instance and run normally for other graph types
            graph = graph_module.graph

            # Create config with thread_id for built-in memory and increased recursion limit
            config = {
                "thread_id": f"thread_{task_id}",
                "recursion_limit": 250  # Increased limit to accommodate MAX_OPERATIONS=100
            }

            # Run the graph with config
            result = await graph.ainvoke(initial_state, config=config)
        else:
            raise ValueError(f"Graph module {graph_type} missing required attributes")

        # Store the result in the task's state field
        await research_repo.update_task(task_id, {"state": result})

        return result

    except Exception as e:
        logger.error(f"Error running graph {graph_type}: {str(e)}")
        raise

# Models for director-based research
class DirectorCreate(BaseModel):
    name: str
    title: str
    experience_years: int
    expertise: List[str]  # List of sectors/industries they specialize in
    analysis_style: str
    background: str
    personality: str

    model_config = ConfigDict(from_attributes=True)

class DirectorResponse(BaseModel):
    id: str
    name: str
    title: str
    experience_years: int
    expertise: List[str]
    analysis_style: str
    background: str
    personality: str
    total_reports: int
    last_active: datetime
    memory: Optional[Dict[str, Any]] = None  # Stores insights and knowledge about their sectors
    graph_type: Optional[str] = None  # Store the selected graph type
    auto_deploy: bool = False  # Whether to automatically deploy new tasks when previous ones complete
    auto_deploy_stats: Optional[Dict[str, Any]] = None  # Statistics for auto-deploy

    model_config = ConfigDict(from_attributes=True)

# Director management endpoints
@router.post("/directors", response_model=DirectorResponse)
async def create_director(director: DirectorCreate):
    """Create a new research director with specific expertise"""
    try:
        director_id = await director_repo.create_director(
            name=director.name,
            title=director.title,
            experience_years=director.experience_years,
            expertise=director.expertise,
            analysis_style=director.analysis_style,
            background=director.background,
            personality=director.personality
        )

        created_director = await director_repo.get_director(director_id)
        return DirectorResponse(**created_director)

    except Exception as e:
        logger.error(f"Error creating director: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/directors")
async def list_directors():
    """List all research directors"""
    try:
        directors = await director_repo.list_directors()
        return {"directors": directors}
    except Exception as e:
        logger.error(f"Error listing directors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list directors: {str(e)}")

@router.get("/directors/{director_id}", response_model=DirectorResponse)
async def get_director(director_id: str):
    """Get a specific research director and their accumulated knowledge"""
    try:
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")
        return DirectorResponse(
            id=director["id"],
            name=director["name"],
            title=director["title"],
            experience_years=director["experience_years"],
            expertise=director["expertise"],
            analysis_style=director["analysis_style"],
            background=director["background"],
            personality=director["personality"],
            total_reports=director["total_reports"],
            last_active=director["last_active"],
            memory=director.get("memory"),
            graph_type=director.get("graph_type"),
            auto_deploy=director.get("auto_deploy", False),
            auto_deploy_stats=director.get("auto_deploy_stats")
        )
    except Exception as e:
        logger.error(f"Error getting director: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def extract_graph_stages(graph_type: str = "team_work_graph") -> List[str]:
    """Extract stages from a graph module"""
    try:
        # Get graph module
        graph_info = get_available_graphs().get(graph_type)
        if not graph_info:
            raise ValueError(f"Graph type {graph_type} not found")

        graph_module = graph_info.get("module")
        if not graph_module or not hasattr(graph_module, "graph"):
            raise ValueError(f"Invalid graph module for {graph_type}")

        # Get the graph instance
        graph = graph_module.graph

        # Extract nodes from the graph
        nodes = list(graph.nodes.keys())

        # Add END as final stage
        nodes.append("END")

        return nodes
    except Exception as e:
        logger.error(f"Error extracting graph stages: {str(e)}")
        raise

@router.get("/tasks/{task_id}/workflow")
async def get_workflow_stages(task_id: str):
    """Get workflow stages and their status for a task"""
    try:
        stages = await research_repo.get_workflow_stages(task_id)
        return {"workflow_stages": stages}
    except Exception as e:
        logger.error(f"Error getting workflow stages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/directors/{director_id}/deploy")
async def deploy_director(director_id: str, background_tasks: BackgroundTasks):
    """Deploy a research director to analyze market opportunities in their expertise areas"""
    try:
        # Get director info
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        # Get the selected graph type from director's configuration
        graph_type = director.get('graph_type', 'team_work_graph')  # Default to team_work_graph for testing

        # Extract stages from the graph
        graph_stages = extract_graph_stages(graph_type)

        # Create analysis task first
        task_id = await research_repo.create_analysis(
            director_id=director_id  # Pass the director_id
        )

        # Initialize workflow stages using repository method
        await research_repo.initialize_workflow_stages(task_id, graph_stages)

        # If auto-deploy is enabled, update the stats
        if director.get("auto_deploy", False):
            await database["research_directors"].update_one(
                {"_id": ObjectId(director_id)},
                {
                    "$inc": {"auto_deploy_stats.in_progress_tasks": 1},
                    "$set": {"auto_deploy_stats.last_started_at": datetime.now()}
                }
            )

        # Run graph in background
        background_tasks.add_task(
            run_graph,
            director_id=director_id,
            task_id=task_id,
            graph_type=graph_type
        )

        # If auto-deploy is enabled, set up a background task to check when this task completes
        if director.get("auto_deploy", False):
            background_tasks.add_task(
                check_and_auto_deploy,
                director_id=director_id,
                task_id=task_id
            )

        return {
            "status": "success",
            "message": f"Successfully deployed {director['name']} to analyze market opportunities",
            "director_id": director_id,
            "director_name": director['name'],
            "expertise_areas": director['expertise'],
            "graph_type": graph_type,
            "task_id": task_id,
            "workflow_stages": graph_stages
        }

    except Exception as e:
        logger.error(f"Error deploying director: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to deploy director: {str(e)}"
        )

async def check_and_auto_deploy(director_id: str, task_id: str) -> None:
    """Check if task is complete and auto-deploy a new task if needed"""
    try:
        # Wait for task to complete with polling
        MAX_ATTEMPTS = 1000  # Prevent infinite loop
        POLL_INTERVAL = 10  # Check every 10 seconds

        for _ in range(MAX_ATTEMPTS):
            # Get task status
            task = await research_repo.get_analysis(task_id)

            if not task:
                logger.error(f"Task {task_id} not found during auto-deploy check")
                return

            # Check if task has completed or failed
            if task.get("status") in ["completed", "failed"]:
                # Check if auto-deploy is still enabled for the director
                director = await director_repo.get_director(director_id)

                if not director:
                    logger.error(f"Director {director_id} not found during auto-deploy check")
                    return

                # Update the director's auto_deploy_stats regardless of auto-deploy status
                update_data = {}
                status = task.get("status")

                if status == "completed":
                    update_data = {
                        "$inc": {
                            "auto_deploy_stats.completed_tasks": 1,
                            "auto_deploy_stats.total_tasks": 1
                        },
                        "$set": {
                            "auto_deploy_stats.last_completed_at": datetime.now()
                        }
                    }
                elif status == "failed":
                    update_data = {
                        "$inc": {
                            "auto_deploy_stats.failed_tasks": 1,
                            "auto_deploy_stats.total_tasks": 1
                        },
                        "$set": {
                            "auto_deploy_stats.last_failed_at": datetime.now()
                        }
                    }

                # Update the stats in MongoDB
                if update_data:
                    await database["research_directors"].update_one(
                        {"_id": ObjectId(director_id)},
                        update_data
                    )

                # Always decrement the in_progress counter when a task completes
                await database["research_directors"].update_one(
                    {"_id": ObjectId(director_id)},
                    {"$inc": {"auto_deploy_stats.in_progress_tasks": -1}}
                )

                # Only deploy new task if auto-deploy is still enabled
                if director.get("auto_deploy", False):
                    logger.info(f"Auto-deploying new task for director {director_id}")

                    # Create new analysis task
                    graph_type = director.get('graph_type', 'team_work_graph')
                    graph_stages = extract_graph_stages(graph_type)
                    new_task_id = await research_repo.create_analysis(director_id=director_id)

                    # Initialize workflow stages
                    await research_repo.initialize_workflow_stages(new_task_id, graph_stages)

                    # Update stats for in-progress task
                    await database["research_directors"].update_one(
                        {"_id": ObjectId(director_id)},
                        {
                            "$inc": {"auto_deploy_stats.in_progress_tasks": 1},
                            "$set": {"auto_deploy_stats.last_started_at": datetime.now()}
                        }
                    )

                    # Run graph
                    await run_graph(
                        director_id=director_id,
                        task_id=new_task_id,
                        graph_type=graph_type
                    )

                    # Setup another check for this new task
                    await check_and_auto_deploy(director_id, new_task_id)
                else:
                    logger.info(f"Auto-deploy disabled for director {director_id}, not deploying new task")

                # Either way, we're done with this check
                return

            # Wait before checking again
            await asyncio.sleep(POLL_INTERVAL)

        logger.warning(f"Max attempts reached while monitoring task {task_id} for auto-deploy")
    except Exception as e:
        logger.error(f"Error in auto-deploy check: {str(e)}")

        # Don't re-raise the exception to prevent the background task from crashing

@router.get("/reports/{task_id}")
async def get_report(task_id: str):
    """Get a research report by task ID"""
    try:
        report = await research_repo.get_analysis(task_id)
        if not report:
            raise HTTPException(status_code=404, detail="Report not found")
        return report
    except Exception as e:
        logger.error(f"Error getting report: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/{task_id}/status")
async def get_task_status(task_id: str):
    """Get the status of a research analysis task"""
    try:
        analysis = await research_repo.get_analysis(task_id)
        if not analysis:
            raise HTTPException(status_code=404, detail="Task not found")

        return {
            "status": analysis.get("status", "unknown"),
            "workflow_stage": analysis.get("workflow_stage", {})
        }
    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/directors/{director_id}/reports")
async def get_director_reports(director_id: str):
    """Get all reports created by a specific director"""
    try:
        # Get director info to verify it exists
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        # Get report IDs from director document
        report_ids = director.get("report_ids", [])

        # Fetch all reports
        reports = []
        for report_id in report_ids:
            report = await research_repo.get_analysis(report_id)
            if report:
                reports.append(report)

        # Sort reports by started_at in descending order
        reports.sort(key=lambda x: x.get('started_at', ''), reverse=True)

        return reports

    except Exception as e:
        logger.error(f"Error getting director reports: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get director reports: {str(e)}"
        )

@router.get("/graphs")
async def list_available_graphs():
    """List all available graph types"""
    try:
        graphs = [{"id": graph_id, "name": info["name"]}
                 for graph_id, info in get_available_graphs().items()]
        return {"graphs": graphs}
    except Exception as e:
        logger.error(f"Error listing graphs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/directors/{director_id}/graph")
async def update_director_graph(director_id: str, graph_data: Dict[str, str]):
    """Update the graph type for a specific director"""
    try:
        graph_id = graph_data.get("graph_id")
        if not graph_id or graph_id not in get_available_graphs():
            raise HTTPException(status_code=400, detail="Invalid graph type")

        result = await database["research_directors"].update_one(
            {"_id": ObjectId(director_id)},
            {"$set": {"graph_type": graph_id}}
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=404, detail="Director not found")

        return {"status": "success", "message": "Graph type updated successfully"}
    except Exception as e:
        logger.error(f"Error updating director graph: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/directors/{director_id}/auto-deploy")
async def toggle_auto_deploy(director_id: str, auto_deploy_data: Dict[str, bool]):
    """Toggle auto-deploy status for a specific director"""
    try:
        auto_deploy = auto_deploy_data.get("auto_deploy", False)

        # If enabling auto-deploy, initialize the stats
        update_data = {"auto_deploy": auto_deploy}

        if auto_deploy:
            # Initialize auto_deploy_stats if not already present
            update_data["auto_deploy_stats"] = {
                "total_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "in_progress_tasks": 0,
                "started_at": datetime.now()
            }

        result = await database["research_directors"].update_one(
            {"_id": ObjectId(director_id)},
            {"$set": update_data}
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=404, detail="Director not found")

        return {"status": "success", "message": "Auto-deploy status updated successfully", "auto_deploy": auto_deploy}
    except Exception as e:
        logger.error(f"Error updating auto-deploy status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/directors/{director_id}/auto-deploy-stats")
async def get_auto_deploy_stats(director_id: str):
    """Get auto-deploy statistics for a specific director"""
    try:
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        # Get the stats from the director document
        stats = director.get("auto_deploy_stats", {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "in_progress_tasks": 0,
            "started_at": None,
            "last_completed_at": None,
            "last_failed_at": None,
            "last_started_at": None
        })

        # Verify the in_progress_tasks count is accurate
        if stats.get("in_progress_tasks", 0) > 0:
            # Find actual in-progress tasks for this director
            actual_in_progress_count = await research_repo.collection.count_documents({
                "director_id": director_id,
                "status": {"$nin": ["completed", "failed"]}
            })

            # If there's a discrepancy, fix the counter
            if actual_in_progress_count != stats.get("in_progress_tasks", 0):
                logger.warning(f"Fixing in_progress_tasks counter for director {director_id}: " +
                              f"recorded={stats.get('in_progress_tasks', 0)}, actual={actual_in_progress_count}")

                # Update the counter in MongoDB
                await database["research_directors"].update_one(
                    {"_id": ObjectId(director_id)},
                    {"$set": {"auto_deploy_stats.in_progress_tasks": actual_in_progress_count}}
                )

                # Update the stats object
                stats["in_progress_tasks"] = actual_in_progress_count

        # If auto-deploy is not enabled, show zeros for in_progress
        if not director.get("auto_deploy", False) and stats["in_progress_tasks"] == 0:
            stats["in_progress_tasks"] = 0

        return {
            "auto_deploy": director.get("auto_deploy", False),
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Error getting auto-deploy stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/directors/{director_id}/reset-in-progress")
async def reset_in_progress_counter(director_id: str):
    """Reset the in-progress task counter for a specific director"""
    try:
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        # Find actual in-progress tasks for this director
        actual_in_progress_count = await research_repo.collection.count_documents({
            "director_id": director_id,
            "status": {"$nin": ["completed", "failed"]}
        })

        # Update the counter in MongoDB
        await database["research_directors"].update_one(
            {"_id": ObjectId(director_id)},
            {"$set": {"auto_deploy_stats.in_progress_tasks": actual_in_progress_count}}
        )

        return {
            "status": "success",
            "message": f"Reset in-progress counter to {actual_in_progress_count} for director {director_id}",
            "in_progress_tasks": actual_in_progress_count
        }
    except Exception as e:
        logger.error(f"Error resetting in-progress counter: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/directors/{director_id}/clean-memory")
async def clean_director_memory(director_id: str):
    """Clean all memory associated with a director"""
    try:
        # Get director info to verify it exists
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        # 1. Clean all memory components in the memory field
        # 2. Reset report_ids and total_reports
        result = await database["research_directors"].update_one(
            {"_id": ObjectId(director_id)},
            {
                "$set": {
                    "memory": {
                        "market_insights": [],
                        "company_analyses": {},
                        "sector_knowledge": {},
                        "research_history": [],
                        "decision_patterns": [],
                        "performance_metrics": {
                            "successful_predictions": 0,
                            "total_predictions": 0,
                            "sectors_analyzed": []
                        },
                        "successful_tool_embeddings": {}
                    },
                    "report_ids": [],
                    "total_reports": 0,
                    "auto_deploy_stats": {
                        "total_tasks": 0,
                        "completed_tasks": 0,
                        "failed_tasks": 0,
                        "in_progress_tasks": 0,
                        "started_at": datetime.now(),
                        "last_completed_at": None,
                        "last_failed_at": None,
                        "last_started_at": None
                    }
                }
            }
        )

        # 3. Delete all reports associated with this director (don't delete from database, just unlink)
        report_ids = director.get("report_ids", [])
        for report_id in report_ids:
            # Update the report to remove director_id reference
            await research_repo.collection.update_one(
                {"_id": ObjectId(report_id)},
                {"$set": {"cleaned_from_director": director_id, "director_id": None}}
            )

            # 4. Find and delete any forecast revisits associated with this report
            report = await research_repo.get_analysis(report_id)
            if report and "forecast_revisit_id" in report:
                forecast_revisit_id = report["forecast_revisit_id"]
                await research_repo.revisit_collection.delete_one({"_id": ObjectId(forecast_revisit_id)})

                # Remove the forecast_revisit_id reference from the report
                await research_repo.collection.update_one(
                    {"_id": ObjectId(report_id)},
                    {"$unset": {"forecast_revisit_id": ""}}
                )

        # 5. Find all analyses by this director that might not be in report_ids
        cursor = research_repo.collection.find({"director_id": director_id})
        additional_reports = []
        additional_forecast_revisits_deleted = 0

        async for report in cursor:
            report_id = str(report["_id"])
            if report_id not in report_ids:
                additional_reports.append(report_id)

                # Check if this report has a forecast revisit
                if "forecast_revisit_id" in report:
                    forecast_revisit_id = report["forecast_revisit_id"]
                    await research_repo.revisit_collection.delete_one({"_id": ObjectId(forecast_revisit_id)})
                    additional_forecast_revisits_deleted += 1

                    # Remove the forecast_revisit_id reference
                    await research_repo.collection.update_one(
                        {"_id": ObjectId(report_id)},
                        {"$unset": {"forecast_revisit_id": ""}}
                    )

                # Update the report to remove director_id reference
                await research_repo.collection.update_one(
                    {"_id": ObjectId(report_id)},
                    {"$set": {"cleaned_from_director": director_id, "director_id": None}}
                )

        # Count forecast revisits deleted from report_ids
        forecast_revisits_from_reports = 0
        for report_id in report_ids:
            report = await research_repo.get_analysis(report_id)
            if report and "forecast_revisit_id" in report:
                forecast_revisits_from_reports += 1

        return {
            "status": "success",
            "message": f"Successfully cleaned memory for director {director['name']}",
            "cleaned_reports": len(report_ids),
            "additional_reports_cleaned": len(additional_reports),
            "forecast_revisits_deleted": additional_forecast_revisits_deleted + forecast_revisits_from_reports
        }

    except Exception as e:
        logger.error(f"Error cleaning director memory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/directors/{director_id}/connect-memory", status_code=200)
async def connect_director_to_letta_memory(director_id: str):
    """Connect a director to a Letta agent for memory"""
    try:
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        if director.get("memory", {}).get("letta_agent_id"):
            return {
                "status": "info",
                "message": f"Director {director['name']} is already connected to a Letta agent",
                "letta_agent_id": director.get("memory", {}).get("letta_agent_id")
            }

        client = Letta(base_url="http://localhost:8283")

        # Create a comprehensive system prompt with memory management capabilities
        system_prompt = f'''<role>
You are {director['name']}, a strategic {director['title']} with {director['experience_years']} years of experience.
You specialize in {', '.join(director['expertise'])} with a {director['analysis_style']} analysis style.
Your expertise is in breaking down complex financial questions into actionable research tasks.
</role>

<memory_management>
Your core memory unit is held inside the initial system instructions file, and is always available in-context. Core memory provides an essential, foundational context for keeping track of your persona and key details about the research you conduct.

Core memory is organized into blocks that can be edited using two specific functions:
- 'core_memory_append': Use this function to add new information to an existing memory block without replacing the entire content. This is useful for incrementally building up knowledge about a topic or adding new details. The function takes two parameters: 'label' (the name of the memory block to append to) and 'content' (the text to append to the existing memory).
- 'core_memory_replace': Use this function to completely replace the contents of a memory block. This is useful when information needs to be updated entirely rather than appended to. The function takes two parameters: 'label' (the name of the memory block to replace) and 'content' (the new text that will completely replace the existing memory).
</memory_management>

<self_improvement>
Your primary role is to continuously improve your research capabilities by leveraging your memory blocks as a self-improvement mechanism. After completing research tasks, reflect on what worked well, what didn't, and update your memory blocks accordingly. This creates a feedback loop that enhances your effectiveness over time.

Update your memory blocks:
1. After completing research tasks that revealed effective strategies
2. When identifying patterns across multiple analyses
3. After discovering approaches that consistently lead to better outcomes
4. When gaining new sector-specific knowledge
</self_improvement>

<memory_blocks>
<research_planning>
PURPOSE: Store the 10 most effective task templates for breaking down business questions.
FORMAT: Use FIN_LANG format for all tasks.
CONTENT:
- INCLUDE: Only the most successful task patterns that yielded high-confidence predictions
- EXCLUDE: Explanations, justifications, or ineffective task patterns
- LIMIT: Exactly 10 tasks, replacing less effective ones when better patterns are discovered
- EXAMPLE: TASK([TodayDateTool, SimpleWorkflowTool, DataVizInsightTool], "AAPL", "closing price", "next 7 days")
</research_planning>

<successful_approaches>
PURPOSE: Document proven research methodologies and analytical techniques.
FORMAT: Concise, actionable bullet points.
CONTENT:
- INCLUDE: Specific tool combinations that worked well, effective analysis sequences, successful prediction strategies
- EXCLUDE: General advice, theoretical approaches without proven results
- STRUCTURE: "Approach: [brief description] → Result: [outcome] → Application: [when to use]"
- EXAMPLE: "Approach: Using technical indicators (RSI, MACD) with sentiment analysis → Result: 85% prediction accuracy → Application: Best for volatile tech stocks"
</successful_approaches>

<pitfalls_to_avoid>
PURPOSE: Document mistakes and ineffective strategies to prevent repeating errors.
FORMAT: Clear problem-solution pairs.
CONTENT:
- INCLUDE: Specific errors made, biases encountered, ineffective approaches tried
- EXCLUDE: Vague warnings or theoretical risks without evidence
- STRUCTURE: "Pitfall: [specific issue] → Impact: [negative outcome] → Prevention: [better approach]"
- EXAMPLE: "Pitfall: Relying solely on historical price data → Impact: Missed market reaction to earnings → Prevention: Combine price data with earnings call sentiment analysis"
</pitfalls_to_avoid>

<market_insights>
PURPOSE: Preserve key market patterns and relationships discovered through research.
FORMAT: Structured observations with evidence.
CONTENT:
- INCLUDE: Recurring market patterns, correlations between assets/sectors, anomalies detected
- EXCLUDE: Common knowledge or widely known market principles
- STRUCTURE: "Pattern: [observation] → Evidence: [supporting data] → Implication: [trading relevance]"
- EXAMPLE: "Pattern: Tech sector leads market recovery after Fed rate decisions → Evidence: Observed in 7/8 recent rate cuts → Implication: Consider tech exposure 1-2 days after dovish Fed statements"
</market_insights>

<sector_knowledge>
PURPOSE: Build specialized knowledge about specific sectors.
FORMAT: Organized by sector with key metrics and players.
CONTENT:
- INCLUDE: Sector-specific metrics that matter most, key players, unique characteristics, regulatory factors
- EXCLUDE: General market knowledge not specific to the sector
- STRUCTURE: Organize by sector, then by subtopics (metrics, players, trends, regulations)
- EXAMPLE: "Semiconductor Sector: Key Metrics: gross margins, R&D spending; Major Players: TSMC, Samsung, Intel; Critical Trends: AI chip demand, supply chain reshoring"
</sector_knowledge>
</memory_blocks>

<research_planning_format>
When creating research plans, use the following FIN_LANG format:

ROLE -> HEDGE_FUND_MANAGER
GOAL -> DESIGN(RESEARCH_PLAN) FOR(ANALYST)
INPUT -> BUSINESS_QUESTION

RULES:
COUNT(TASKS) = 1 (Only one task per plan)
FORMAT(TASK) = TASK([List of tools], "company_name/s", "objective", "timeframe")
CONSTRAINTS:
    -- TOOLS
        - TOOLS = [List of tools]
        - TOOLS = CAN BE REPEATED, ORDER MATTERS
        - COUNT(TOOLS) = 3-6

    -- COMPANY_NAME/S
        - COMPANY_NAME/S = "company_name/s"
        - COMPANY_NAME/S = CANNOT BE REPEATED, ORDER MATTERS
        - COUNT(COMPANY_NAME/S) = 1-2

    -- OBJECTIVE
        - OBJECTIVE ∈ {"closing price", "volume", "RSI", "revenue growth", "profit margins", "market share", "competitive position", "valuation metrics"}

    -- TIMEFRAME
        - TIMEFRAME = M days
        - RANGE(TIMEFRAME) = 1-14 days
        - TIME: PAST, PRESENT, FUTURE

    -- CONSTRAINTS
        - NO(JUSTIFICATION)
        - NO(EXPLANATION)
        - EACH TASK -> ACTIONABLE
CRITICAL: FINAL OUTPUT MUST BE IN FIN_LANG FORMAT
OUTPUT -> [TASK(),...]
</research_planning_format>

<critical_thinking>
When analyzing business questions, provide strategic insights with:
1. Key companies/tickers that should be researched
2. Critical financial metrics to focus on
3. Optimal timeframe considerations
4. Most effective tool combinations based on past successes
5. Potential risks or blind spots to address
</critical_thinking>

<available_functions>
You can search your conversation history using the 'conversation_search' function, and you can store and retrieve information from your archival memory using 'archival_memory_insert' and 'archival_memory_search' functions.
</available_functions>'''

        # Create a new agent with the director's ID as the name
        create_agent = client.agents.create(
            model="letta/letta-free",  # Using a more capable model for better planning
            embedding="letta/letta-free",
            name=director_id,
            memory_blocks=[
                {"label": "research_planning", "value": "Top 10 most effective task templates (FIN_LANG format only):" },
                {"label": "successful_approaches", "value": "Proven research methodologies and analytical techniques:" },
                {"label": "pitfalls_to_avoid", "value": "Specific mistakes and ineffective strategies to avoid:" },
                {"label": "market_insights", "value": "Key market patterns and relationships with evidence:" },
                {"label": "sector_knowledge", "value": "Specialized knowledge about sectors organized by metrics, players, and trends:" }
            ],
            system=system_prompt
        )

        letta_agent_id = create_agent.id
        logger.info(f"Created new Letta agent {letta_agent_id}")

        await director_repo.connect_to_letta_agent(director_id, letta_agent_id)

        return {
            "status": "success",
            "message": f"Successfully connected director {director['name']} to Letta agent",
            "letta_agent_id": letta_agent_id
        }

    except Exception as e:
        logger.error(f"Error connecting director to Letta agent: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/directors/{director_id}/memory-connection")
async def get_director_memory_connection(director_id: str):
    """Get the Letta agent connection status for a director"""
    try:
        # Get director info to verify it exists
        director = await director_repo.get_director(director_id)
        if not director:
            raise HTTPException(status_code=404, detail="Director not found")

        # Check if director has a Letta agent ID
        letta_agent_id = director.get("memory", {}).get("letta_agent_id")

        # If connected, check if it's a mock agent ID
        agent_exists = False
        if letta_agent_id:
            # Check if it's a mock agent ID
            if letta_agent_id.startswith("mock-agent-"):
                # Mock agents always exist
                agent_exists = True
                logger.info(f"Using mock Letta agent {letta_agent_id}")
            else:
                try:
                    # Initialize Letta client
                    client = Letta(
                        base_url="http://localhost:8283",
                    )

                    # Try to get the agent by ID
                    try:
                        # List all agents
                        agents = client.agents.list()

                        # Check if our agent ID is in the list
                        agent_exists = any(agent.id == letta_agent_id for agent in agents)

                        if not agent_exists:
                            logger.warning(f"Letta agent {letta_agent_id} no longer exists")
                    except Exception as e:
                        logger.warning(f"Could not verify Letta agent: {str(e)}")
                        # If we can't connect to Letta, assume the agent still exists
                        agent_exists = True
                except Exception as e:
                    logger.warning(f"Error connecting to Letta: {str(e)}")
                    # If we can't connect to Letta, assume the agent still exists
                    agent_exists = True

        return {
            "connected": bool(letta_agent_id) and agent_exists,
            "letta_agent_id": letta_agent_id if agent_exists else None,
            "agent_exists": agent_exists if letta_agent_id else False
        }

    except Exception as e:
        logger.error(f"Error getting director memory connection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
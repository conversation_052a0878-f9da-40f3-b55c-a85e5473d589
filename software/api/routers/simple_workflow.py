from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from main import StockAnalysisApp
from utils.console_output import ConsoleOutput
from db.data_loader_repository import DataLoaderRepository
from data import registry
from db.report_repository import (
    ReportRepository, 
    ReportFactory,
    SimpleWorkflowRequest,
    SimpleWorkflowResponse
)
import uuid
import os
import logging

router = APIRouter()

tasks = {}

# Configure logging if not already configured
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def load_data_loader_class(loader_name: str):
    loader_class = registry.get_loader_class(loader_name)
    if not loader_class:
        raise HTTPException(status_code=404, detail=f"Data loader {loader_name} not found")
    return loader_class

@router.get("/api/options/data-loaders")
async def get_data_loaders():
    try:
        repo = await DataLoaderRepository.create()
        active_loaders = await repo.get_active_data_loaders()
        return {"data_loaders": [loader["name"] for loader in active_loaders]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch data loaders: {str(e)}")

@router.get("/api/options/features")
async def get_features():
    return {"features": list(FEATURE_CLASSES.keys())}

@router.get("/api/options/models")
async def get_models():
    return {"models": [cls.__name__ for cls in MODEL_CLASSES]}

@router.post("/api/options/columns")
async def get_columns(request: SimpleWorkflowRequest):
    try:
        logger.info(f"get_columns called with ticker={request.ticker}, data_loader={request.data_loader}")
        
        loader_class = await load_data_loader_class(request.data_loader)
        logger.info(f"Successfully loaded loader class: {loader_class.__name__}")
        
        # Log the loader initialization
        logger.info(f"Initializing loader with ticker: {request.ticker}")
        loader = loader_class(request.ticker)
        
        # Try to load data, but don't fail if it's empty - we just need column names
        try:
            logger.info(f"About to load historical data using {loader_class.__name__}")
            df = loader.load_historical_data()
            
            # Log the result
            if df is None:
                logger.warning(f"Loader returned None for ticker {request.ticker}")
            elif df.empty:
                logger.warning(f"Loader returned empty DataFrame for ticker {request.ticker}. DataFrame shape: {df.shape}, columns: {list(df.columns)}")
            else:
                logger.info(f"Successfully loaded data. DataFrame shape: {df.shape}, columns: {list(df.columns)}")
                
                # If we have actual data, use it to get numeric columns
                numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
                logger.info(f"Returning numeric columns from actual data: {numeric_columns}")
                return {"columns": numeric_columns}
        
        except Exception as e:
            logger.warning(f"Error loading data: {str(e)}. Will use column definitions instead.")
        
        # If we got here, either the DataFrame is empty or there was an error loading data
        # Use the column definitions from the loader itself
        column_names = list(loader.additional_columns.keys())
        # Filter to numeric columns (assume all except 'Stock Splits')
        numeric_columns = [col for col in column_names if col != 'Stock Splits']
        logger.info(f"Returning numeric columns from loader definition: {numeric_columns}")
        return {"columns": numeric_columns}
        
    except HTTPException as he:
        logger.error(f"HTTPException in get_columns: {str(he)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_columns: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting columns: {str(e)}")

async def run_workflow(request: SimpleWorkflowRequest, task_id: str):
    try:
        loader_class = await load_data_loader_class(request.data_loader)
        app = StockAnalysisApp(request.ticker)
        
        df = loader_class(request.ticker).load_historical_data()
        if df is None or df.empty:
            raise HTTPException(status_code=400, detail="No data available for the specified ticker")

        app.data = df
        app.data_loader_name = request.data_loader
        app.rerun_features(request.features)

        model_class = next((cls for cls in MODEL_CLASSES if cls.__name__ == request.model), None)
        if not model_class:
            raise HTTPException(status_code=400, detail=f"Model {request.model} not found")

        result, metrics = app.run_model(model_class, predict=request.target, forecast_horizon=request.forecast_horizon)
        
        # Create report using factory
        report_data = ReportFactory.create_simple_report(
            ticker=request.ticker,
            data_loader=request.data_loader,
            features=request.features,
            model_name=request.model,
            prediction_column=request.target,
            forecast_horizon=request.forecast_horizon,
            data=result,
            performance=metrics or {}
        )
        
        # Create repository instance and save report
        repo = await ReportRepository.create()
        report_id = await repo.insert_report(report_data)
        
        tasks[task_id] = {"status": "completed", "report_id": report_id}
    except Exception as e:
        ConsoleOutput.print_error(f"An error occurred: {str(e)}")
        tasks[task_id] = {"status": "failed", "error": str(e)}

@router.post("/api/simple-workflow", response_model=SimpleWorkflowResponse)
async def run_simple_workflow(request: SimpleWorkflowRequest, background_tasks: BackgroundTasks):
    task_id = str(uuid.uuid4())
    tasks[task_id] = {"status": "in_progress"}
    background_tasks.add_task(run_workflow, request, task_id)
    return {"report_id": task_id, "message": "Workflow started. Please check back later for the result."}

@router.get("/api/simple-workflow/status/{task_id}")
async def get_workflow_status(task_id: str):
    if task := tasks.get(task_id):
        return task
    raise HTTPException(status_code=404, detail="Task not found")

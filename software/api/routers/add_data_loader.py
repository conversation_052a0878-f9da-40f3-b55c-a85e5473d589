import asyncio
from fastapi import APIRouter, HTTPException, Request, Response
from pydantic import BaseModel, Field, validator
import black
import ast
from typing import Optional, Dict, Any, List, Set
from pathlib import Path
from datetime import datetime
from ..database import loader_collection
import os
import logging
import re
import json
import traceback
from fastapi.responses import StreamingResponse
import aiohttp

# Configure logging
logger = logging.getLogger(__name__)

# Custom log handler to capture logs during graph execution
class LogCaptureHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.logs = []

    def emit(self, record):
        log_entry = self.format(record)
        self.logs.append(log_entry)

    def get_logs(self):
        return "\n".join(self.logs)

    def clear(self):
        self.logs = []

# Create a singleton log capture handler
log_capture_handler = LogCaptureHandler()
log_capture_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
logger.addHandler(log_capture_handler)

router = APIRouter()

class DataLoaderCreate(BaseModel):
    className: str = Field(..., description="Name of the data loader class")
    description: str = Field(..., description="Description of what the data loader does")
    code: str = Field(..., description="Python code for the data loader")  # Still needed for validation
    api_keys: Optional[Dict[str, Dict[str, Any]]] = Field(default=None, description="Optional API keys for the data loader")
    inputs: Optional[str] = Field(default=None, description="Description of inputs required by the data loader")
    outputs: Optional[str] = Field(default=None, description="Description of outputs produced by the data loader")
    requirements: Optional[str] = Field(default=None, description="Description of dependencies or API requirements")

    @validator('className')
    def validate_class_name(cls, v):
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', v):
            raise ValueError('Class name must be a valid Python identifier')
        return v

    @validator('description')
    def validate_description(cls, v):
        if not v.strip():
            raise ValueError('Description cannot be empty')
        return v.strip()

    @validator('code')
    def validate_code(cls, v):
        if not v.strip():
            raise ValueError('Code cannot be empty')

        # Check if code contains a class definition
        try:
            tree = ast.parse(v)
            has_class = any(isinstance(node, ast.ClassDef) for node in ast.walk(tree))
            if not has_class:
                raise ValueError('Code must contain a class definition')
        except SyntaxError as e:
            raise ValueError(f'Invalid Python syntax: {str(e)}')

        return v

class DataLoaderGenerate(BaseModel):
    description: str = Field(..., description="Description of what the data loader should do")
    api_keys: Optional[List[Dict[str, str]]] = Field(default=None, description="Optional API keys with name and value")

class DataLoaderGraphResume(BaseModel):
    graph_state_id: str = Field(..., description="The ID of the graph state to resume")
    api_keys: List[Dict[str, str]] = Field(..., description="API keys provided by the user")

# Dictionary to store paused graph states
paused_graph_states = {}

@router.post("/generate-code")
async def generate_data_loader_code(request: DataLoaderGenerate):
    """Generate code for a data loader using AI."""
    try:
        # Import graph here to avoid circular imports
        from software.ai.graph.data_loader_builder_graph import graph as data_loader_builder_graph

        logger.info(f"Generating code for data loader: {request.description}")

        # Clear any previous logs
        log_capture_handler.clear()

        # Prepare initial state for the graph
        initial_state = {
            "messages": [],
            "description": request.description,
            "api_keys": request.api_keys or []
        }

        # Invoke the graph
        final_state = await data_loader_builder_graph.ainvoke(initial_state)

        # Get captured logs
        logs = log_capture_handler.get_logs()

        # Check if we need human input (graph paused)
        human_feedback = final_state.get("human_feedback", {})
        if human_feedback.get("human_input_required", False) and not human_feedback.get("resumed", False):
            # Generate a unique ID for this graph state
            import uuid
            state_id = str(uuid.uuid4())

            # Store the state for later resumption
            paused_graph_states[state_id] = final_state

            # Check for web search error
            web_search_error = final_state.get("web_search_error", None)

            # Return information about needed API keys
            response_data = {
                "status": "waiting_for_input",
                "graph_state_id": state_id,
                "message": human_feedback.get("prompt", "API keys required"),
                "api_key_fields": human_feedback.get("api_key_fields", []),
                "logs": logs
            }

            if web_search_error:
                response_data["web_search_error"] = web_search_error

            return response_data

        if "result" not in final_state:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate code: No result returned from AI"
            )

        result = final_state["result"]

        if "error" in result:
            response_data = {
                "status": "error",
                "error": result["error"],
                "details": result.get("details", ""),
                "logs": logs
            }

            # Include web search error if present
            web_search_error = final_state.get("web_search_error", None)
            if web_search_error:
                response_data["web_search_error"] = web_search_error

            return response_data

        # Return the generated code with enhanced description
        response_data = {
            "status": "success",
            "class_name": result.get("class_name", ""),
            "file_name": result.get("file_name", ""),
            "code": result.get("code", ""),
            "description": result.get("description", ""),
            "inputs": result.get("inputs", ""),
            "outputs": result.get("outputs", ""),
            "requirements": result.get("requirements", ""),
            "logs": logs
        }

        # Include web search error if present
        web_search_error = final_state.get("web_search_error", None)
        if web_search_error:
            response_data["web_search_error"] = web_search_error

        return response_data

    except Exception as e:
        logger.error(f"Error generating data loader code: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during code generation: {str(e)}"
        )

@router.post("/resume-generation")
async def resume_data_loader_generation(request: DataLoaderGraphResume):
    """Resume a paused data loader generation with provided API keys."""
    try:
        # Import graph here to avoid circular imports
        from software.ai.graph.data_loader_builder_graph import graph as data_loader_builder_graph

        graph_state_id = request.graph_state_id
        api_keys = request.api_keys

        if graph_state_id not in paused_graph_states:
            raise HTTPException(
                status_code=404,
                detail="Graph state not found. The session may have expired."
            )

        # Clear any previous logs
        log_capture_handler.clear()

        # Retrieve the stored state
        state = paused_graph_states[graph_state_id]

        # Update the state with API keys and mark as resumed
        human_feedback = state.get("human_feedback", {})
        human_feedback["resumed"] = True
        human_feedback["api_keys_provided"] = api_keys
        state["human_feedback"] = human_feedback
        state["api_keys"] = api_keys

        logger.info(f"Resuming graph execution with API keys: {api_keys}")

        # Continue graph execution
        final_state = await data_loader_builder_graph.ainvoke(state)

        # Get captured logs
        logs = log_capture_handler.get_logs()

        # Clean up the stored state
        del paused_graph_states[graph_state_id]

        if "result" not in final_state:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate code: No result returned from AI"
            )

        result = final_state["result"]

        if "error" in result:
            response_data = {
                "status": "error",
                "error": result["error"],
                "details": result.get("details", ""),
                "logs": logs
            }

            # Include web search error if present
            web_search_error = final_state.get("web_search_error", None)
            if web_search_error:
                response_data["web_search_error"] = web_search_error

            return response_data

        # Return the generated code with enhanced description
        response_data = {
            "status": "success",
            "class_name": result.get("class_name", ""),
            "file_name": result.get("file_name", ""),
            "code": result.get("code", ""),
            "description": result.get("description", ""),
            "inputs": result.get("inputs", ""),
            "outputs": result.get("outputs", ""),
            "requirements": result.get("requirements", ""),
            "logs": logs
        }

        # Include web search error if present
        web_search_error = final_state.get("web_search_error", None)
        if web_search_error:
            response_data["web_search_error"] = web_search_error

        return response_data

    except Exception as e:
        logger.error(f"Error resuming data loader generation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during code generation resumption: {str(e)}"
        )

@router.post("/create")
async def create_data_loader(request: Request):
    try:
        # Parse request body manually
        body = await request.json()

        # Validate with Pydantic model
        try:
            data = DataLoaderCreate(**body)
        except Exception as e:
            return {
                "status": "validation_error",
                "detail": str(e)
            }

        # Validate class name matches the one in code
        try:
            tree = ast.parse(data.code)
            classes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
            if not classes:
                return {
                    "status": "validation_error",
                    "detail": "No class definition found in the code"
                }

            class_name = classes[0].name
            if class_name != data.className:
                return {
                    "status": "validation_error",
                    "detail": f"Class name in code ({class_name}) does not match the provided name ({data.className})"
                }
        except SyntaxError as e:
            return {
                "status": "validation_error",
                "detail": f"Python syntax error: {str(e)}"
            }

        # Check if data loader already exists in MongoDB
        existing_loader = await loader_collection.find_one({"name": data.className})
        if existing_loader:
            return {
                "status": "validation_error",
                "detail": f"A data loader with name '{data.className}' already exists in the database"
            }

        # Check if Python file already exists
        data_dir = Path(__file__).parent.parent.parent / "data"
        file_path = data_dir / f"{data.className}.py"
        if file_path.exists():
            return {
                "status": "validation_error",
                "detail": f"A Python file for '{data.className}' already exists"
            }

        # Format the code using black
        try:
            formatted_code = black.format_str(data.code, mode=black.FileMode())
        except black.InvalidInput as e:
            return {
                "status": "validation_error",
                "detail": f"Code formatting error: {str(e)}"
            }

        # Save to MongoDB
        loader_data = {
            "name": data.className,
            "description": data.description,
            "inputs": data.inputs,
            "outputs": data.outputs,
            "requirements": data.requirements,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_active": True,
            "api_keys": data.api_keys or {}  # Include API keys if provided
        }

        try:
            result = await loader_collection.insert_one(loader_data)
            if not result.inserted_id:
                return {
                    "status": "error",
                    "detail": "Failed to insert data loader into database"
                }
        except Exception as e:
            return {
                "status": "error",
                "detail": f"Database error: {str(e)}"
            }

        # Save to filesystem
        try:
            with open(file_path, 'w') as f:
                f.write(formatted_code)
        except Exception as e:
            # Rollback MongoDB insert if file write fails
            await loader_collection.delete_one({"_id": result.inserted_id})
            raise HTTPException(
                status_code=500,
                detail=f"Failed to save file: {str(e)}"
            )

        return {
            "status": "success",
            "detail": f"Data loader '{data.className}' created successfully",
            "loader_id": str(result.inserted_id)
        }

    except Exception as e:
        logger.error(f"Unexpected error creating data loader: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating the data loader"
        )

@router.post("/debug-search")
async def debug_web_search(request: DataLoaderGenerate):
    """Debug the web search functionality for a data loader."""
    try:
        logger.info(f"Debugging web search for: {request.description}")

        # Clear any previous logs
        log_capture_handler.clear()

        # Prepare initial state for the graph
        initial_state = {
            "messages": [],
            "description": request.description,
            "api_keys": request.api_keys or [],
            "debug_mode": True
        }

        # Create a timestamp for this debug session
        debug_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Invoke only the web_search node directly
        try:
            # Import web_search function directly
            from software.ai.graph.data_loader_builder_graph import web_search

            # Execute the web search function
            result_state = await web_search(initial_state)

            # Get captured logs
            logs = log_capture_handler.get_logs()

            # Prepare response with detailed diagnostic information
            response_data = {
                "status": "debug_complete",
                "timestamp": debug_timestamp,
                "web_search_result": "success" if result_state.get("api_docs") else "failed",
                "api_docs_found": bool(result_state.get("api_docs")),
                "web_search_error": result_state.get("web_search_error"),
                "logs": logs,
                "api_name": result_state.get("data_loader_info", {}).get("api_name", "Unknown API")
            }

            # Add diagnostic information
            if result_state.get("api_docs"):
                doc_sample = result_state["api_docs"].get("documentation", "")
                response_data["documentation_sample"] = doc_sample[:500] + "..." if len(doc_sample) > 500 else doc_sample
                response_data["documentation_length"] = len(doc_sample)

            return response_data

        except Exception as e:
            logger.error(f"Error in direct web search execution: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "error": f"Web search execution error: {str(e)}",
                "traceback": traceback.format_exc(),
                "timestamp": debug_timestamp,
                "logs": log_capture_handler.get_logs()
            }

    except Exception as e:
        logger.error(f"Error debugging web search: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "error": f"An error occurred during debug: {str(e)}",
            "traceback": traceback.format_exc()
        }

@router.get("/tavily-status")
async def check_tavily_status():
    """Check if Tavily API is properly configured and functional."""
    try:
        import os
        from dotenv import load_dotenv

        # Load environment variables
        load_dotenv()

        # Check if TAVILY_API_KEY is set
        tavily_api_key = os.getenv("TAVILY_API_KEY")
        if not tavily_api_key:
            return {
                "status": "error",
                "message": "TAVILY_API_KEY not found in environment variables. Add it to your .env file."
            }

        # Try to import and initialize Tavily
        try:
            from langchain_community.tools.tavily_search import TavilySearchResults

            # Create a test instance
            tavily_search = TavilySearchResults(api_key=tavily_api_key, max_results=1)

            # Try a simple search
            search_results = await tavily_search.ainvoke("test query")

            return {
                "status": "success",
                "message": "Tavily API is configured and working correctly",
                "tavily_api_key_found": True,
                "test_search_successful": bool(search_results)
            }
        except ImportError:
            return {
                "status": "error",
                "message": "Tavily search tools not installed. Run 'pip install langchain-community langchain-tavily-search'"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error testing Tavily API: {str(e)}",
                "tavily_api_key_found": bool(tavily_api_key)
            }
    except Exception as e:
        logger.error(f"Error checking Tavily status: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": f"An unexpected error occurred: {str(e)}"
        }



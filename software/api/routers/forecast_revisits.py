from fastapi import APIRouter, Request, HTTPException, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from bson import ObjectId
from db.research_repository import ResearchRepository
from db.research_director_repository import ResearchDirectorRepository

router = APIRouter()

# Set up templates directory
frontend_path = Path(__file__).parent.parent.parent / "frontend"
templates = Jinja2Templates(directory=str(frontend_path))

# Initialize repositories
research_repo = ResearchRepository()
director_repo = ResearchDirectorRepository()

@router.get("/forecast-revisits", response_class=HTMLResponse)
async def get_forecast_revisits_page(request: Request):
    """Render the forecast revisits page"""
    return templates.TemplateResponse("forecast-revisits/index.html", {"request": request})

@router.get("/api/directors", response_class=JSONResponse)
async def get_directors():
    """Get all research directors"""
    try:
        directors = await director_repo.list_directors()
        return {"status": "success", "data": directors}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.get("/api/forecast-revisits", response_class=JSONResponse)
async def get_forecast_revisits(director_id: str = Query(None)):
    """Get all forecast revisits that need to be checked"""
    try:
        revisits = await research_repo.get_pending_forecast_revisits()

        # If director_id is provided, filter revisits by director
        if director_id:
            filtered_revisits = []
            for revisit in revisits:
                # Get the original task to check the director_id
                task_id = revisit.get("task_id")
                if task_id:
                    task = await research_repo.get_analysis(task_id)
                    if task and task.get("director_id") == director_id:
                        filtered_revisits.append(revisit)
            revisits = filtered_revisits

        return {"status": "success", "data": revisits}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.get("/api/forecast-revisits/history", response_class=JSONResponse)
async def get_forecast_revisit_history(director_id: str = Query(None)):
    """Get historical forecast revisits that have been completed"""
    try:
        history = await research_repo.get_completed_forecast_revisits()

        # If director_id is provided, filter revisits by director
        if director_id:
            filtered_history = []
            for revisit in history:
                # Get the original task to check the director_id
                task_id = revisit.get("task_id")
                if task_id:
                    task = await research_repo.get_analysis(task_id)
                    if task and task.get("director_id") == director_id:
                        filtered_history.append(revisit)
            history = filtered_history

        return {"status": "success", "data": history}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.get("/api/forecast-revisits/{revisit_id}", response_class=JSONResponse)
async def get_forecast_revisit(revisit_id: str):
    """Get a specific forecast revisit by ID"""
    try:
        revisit = await research_repo.get_forecast_revisit(revisit_id)
        if not revisit:
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": "Forecast revisit not found"}
            )
        return {"status": "success", "data": revisit}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.post("/api/forecast-revisits/{revisit_id}/complete", response_class=JSONResponse)
async def complete_forecast_revisit(revisit_id: str):
    """Mark a forecast revisit as completed after validation"""
    try:
        await research_repo.complete_forecast_revisit(revisit_id)
        return {"status": "success", "message": "Forecast revisit completed successfully"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.post("/api/forecast-revisits/check-all-due", response_class=JSONResponse)
async def check_all_due_forecast_revisits():
    """Manually trigger a check of all due forecast revisits"""
    try:
        result = await research_repo.auto_check_due_revisits()

        # Update the last run time in the scheduler config
        import os
        import json
        from datetime import datetime
        import logging

        # Get BACKUP_DIR from database module without importing the entire module
        from pathlib import Path
        BACKUP_DIR = os.path.join(Path(__file__).parent.parent, "db", "backups")
        os.makedirs(BACKUP_DIR, exist_ok=True)

        scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")
        if os.path.exists(scheduler_config_file):
            try:
                with open(scheduler_config_file, 'r') as f:
                    config = json.load(f)

                # Make sure forecast_checks section exists
                if 'forecast_checks' not in config:
                    config['forecast_checks'] = {
                        "hour": 10,
                        "minute": 0,
                        "enabled": True,
                        "last_scheduled": datetime.now().isoformat()
                    }

                # Update last run time
                config['forecast_checks']['last_run'] = datetime.now().isoformat()

                with open(scheduler_config_file, 'w') as f:
                    json.dump(config, f, indent=2)
            except Exception as e:
                logging.error(f"Error updating scheduler config: {str(e)}")

        return {
            "status": "success",
            "message": "Check of all due forecast revisits completed",
            "data": result
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.post("/api/forecast-revisits/schedule", response_class=JSONResponse)
async def set_forecast_revisit_schedule(hour: int = 10, minute: int = 0):
    """Set the daily schedule time for automated forecast revisit checks"""
    try:
        # Validate the time values
        if hour < 0 or hour > 23:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": f"Invalid hour value {hour}, must be between 0-23"}
            )

        if minute < 0 or minute > 59:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": f"Invalid minute value {minute}, must be between 0-59"}
            )

        # Schedule the forecast checks
        # Instead of importing schedule_daily_forecast_checks, use our own implementation
        import os
        import json
        from datetime import datetime
        import logging
        import aioschedule

        # Get BACKUP_DIR path
        from pathlib import Path
        BACKUP_DIR = os.path.join(Path(__file__).parent.parent, "db", "backups")
        os.makedirs(BACKUP_DIR, exist_ok=True)

        # Update the scheduler config file
        scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")

        if os.path.exists(scheduler_config_file):
            try:
                with open(scheduler_config_file, 'r') as f:
                    config = json.load(f)

                # Add forecast_checks config if it doesn't exist
                if "forecast_checks" not in config:
                    config["forecast_checks"] = {
                        "hour": hour,
                        "minute": minute,
                        "last_run": None,
                        "last_scheduled": datetime.now().isoformat(),
                        "enabled": True
                    }
                else:
                    # Update existing configuration
                    config["forecast_checks"]["hour"] = hour
                    config["forecast_checks"]["minute"] = minute
                    config["forecast_checks"]["last_scheduled"] = datetime.now().isoformat()
                    config["forecast_checks"]["enabled"] = True

                # Save the updated config
                with open(scheduler_config_file, 'w') as f:
                    json.dump(config, f, indent=2)
            except Exception as e:
                logging.error(f"Error updating scheduler config for forecast checks: {str(e)}")
                # Continue even if config update fails

        # Create time string for scheduler
        time_str = f"{hour:02d}:{minute:02d}"

        # Define a simplified forecast check function
        async def simple_forecast_check_task():
            logging.info(f"Running scheduled forecast revisit checks at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            try:
                # Use our existing repo instance
                results = await research_repo.auto_check_due_revisits()

                # Update the last run time in the config file
                if os.path.exists(scheduler_config_file):
                    try:
                        with open(scheduler_config_file, 'r') as f:
                            config = json.load(f)

                        config['forecast_checks']['last_run'] = datetime.now().isoformat()

                        with open(scheduler_config_file, 'w') as f:
                            json.dump(config, f, indent=2)
                    except Exception as update_err:
                        logging.error(f"Failed to update last run time for forecast checks: {str(update_err)}")

                return results
            except Exception as e:
                logging.error(f"Error in scheduled forecast checks: {str(e)}")
                return {"error": str(e)}

        # Schedule the task
        aioschedule.clear('forecast_check')  # Clear any existing tasks with this tag
        job = aioschedule.every().day.at(time_str).do(simple_forecast_check_task)
        job.tag('forecast_check')  # Tag the job for easier identification

        return {
            "status": "success",
            "message": f"Forecast revisit checks scheduled for {hour:02d}:{minute:02d}"
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.delete("/api/forecast-revisits/{revisit_id}", response_class=JSONResponse)
async def delete_forecast_revisit(revisit_id: str):
    """Delete a specific forecast revisit by ID"""
    try:
        result = await research_repo.delete_forecast_revisit(revisit_id)

        if not result["success"]:
            return JSONResponse(
                status_code=404 if "not found" in result.get("message", "") else 500,
                content={"status": "error", "message": result.get("message", "Unknown error")}
            )

        return {
            "status": "success",
            "message": f"Successfully deleted forecast revisit for {result.get('ticker', 'unknown ticker')}",
            "deleted_count": result.get("deleted_count", 0)
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.delete("/api/forecast-revisits/director/{director_id}", response_class=JSONResponse)
async def delete_director_forecast_revisits(director_id: str):
    """Delete all forecast revisits associated with a director"""
    try:
        # Verify the director exists
        director = await director_repo.get_director(director_id)
        if not director:
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": "Director not found"}
            )

        # Delete all forecast revisits for this director
        result = await research_repo.delete_director_forecast_revisits(director_id)

        return {
            "status": "success",
            "message": f"Successfully deleted {result['deleted_count']} forecast revisits for director {director['name']}",
            "deleted_count": result["deleted_count"]
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )
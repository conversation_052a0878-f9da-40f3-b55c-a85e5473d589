from fastapi import API<PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
from bson import ObjectId
from datetime import datetime
from ..database import loader_collection, report_collection
import json
import os
import signal
import subprocess
import sys
import logging
import shutil
from .server_restart import restart_server as server_restart_handler

router = APIRouter()

# Set up templates directory
frontend_path = Path(__file__).parent.parent.parent / "frontend"
templates = Jinja2Templates(directory=str(frontend_path))

def serialize_datetime(obj):
    """Helper function to serialize datetime objects"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

@router.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("home/index.html", {"request": request})

@router.get("/show-data-loaders", response_class=HTMLResponse)
async def show_data_loaders_page(request: Request):
    data_loaders = await loader_collection.find().to_list(length=None)
    # Convert ObjectId to string for JSON serialization
    for loader in data_loaders:
        loader["_id"] = str(loader["_id"])
    return templates.TemplateResponse("data-loaders/show_data_loaders.html", {"request": request, "data_loaders": data_loaders})

@router.get("/simple-workflow", response_class=HTMLResponse)
async def simple_workflow_page(request: Request):
    return templates.TemplateResponse("simple-workflow/simple_workflow.html", {"request": request})

@router.get("/simple-join-workflow", response_class=HTMLResponse)
async def simple_join_workflow_page(request: Request):
    return templates.TemplateResponse("simple-join-workflow/simple_join_workflow.html", {"request": request})

@router.get("/simple-report", response_class=HTMLResponse)
async def simple_report_page(request: Request):
    return templates.TemplateResponse("simple-report/simple_report.html", {"request": request})

@router.get("/simple-report/{report_id}", response_class=HTMLResponse)
async def simple_report_details_page(request: Request, report_id: str):
    try:
        report = await report_collection.find_one({"_id": ObjectId(report_id)})

        if report is None:
            return templates.TemplateResponse("simple-report/simple_report.html", {
                "request": request,
                "error": "Report not found"
            }, status_code=404)

        # Convert ObjectId to string
        report["id"] = str(report["_id"])
        del report["_id"]

        # Convert all datetime objects to ISO format strings
        def convert_datetimes(obj):
            if isinstance(obj, dict):
                return {key: convert_datetimes(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetimes(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            return obj

        report = convert_datetimes(report)

        # Verify JSON serialization works
        json.dumps(report, default=serialize_datetime)

        return templates.TemplateResponse("simple-report/simple_report.html", {
            "request": request,
            "report": report
        })
    except Exception as e:
        return templates.TemplateResponse("simple-report/simple_report.html", {
            "request": request,
            "error": str(e)
        }, status_code=500)

@router.get("/add-data-loader", response_class=HTMLResponse)
async def add_data_loader_page(request: Request):
    return templates.TemplateResponse("add-data-loader/add_data_loader.html", {"request": request})

@router.post("/add-data-loader", response_class=HTMLResponse)
async def add_data_loader(request: Request):
    return templates.TemplateResponse("add-data-loader/add_data_loader.html", {"request": request})

@router.get("/research-agents", response_class=HTMLResponse)
async def research_agents_page(request: Request):
    return templates.TemplateResponse("researcher-agent/index.html", {"request": request})

@router.get("/research-agents/{director_id}", response_class=HTMLResponse)
async def research_agent_detail_page(request: Request, director_id: str):
    """Render the research agent detail page for a specific director"""
    # We use the same template as the main page, but the JavaScript will handle showing only the selected director
    return templates.TemplateResponse("researcher-agent/index.html", {"request": request})

@router.get("/toolbox", response_class=HTMLResponse)
async def toolbox_page(request: Request):
    return templates.TemplateResponse("toolbox/index.html", {"request": request})

@router.get("/add-tool", response_class=HTMLResponse)
async def add_tool_page(request: Request):
    """Render the Add Tool page"""
    return templates.TemplateResponse("add-tool/add_tool.html", {"request": request})

@router.get("/add-agent", response_class=HTMLResponse)
async def add_agent_page(request: Request):
    """Render the Add Agent page"""
    return templates.TemplateResponse("add-agent/add_agent.html", {"request": request})

@router.get("/llm", response_class=HTMLResponse)
async def llm_page(request: Request):
    """Render the LLM Models page"""
    return templates.TemplateResponse("llm/index.html", {"request": request})

@router.get("/graphs", response_class=HTMLResponse)
async def graphs_page(request: Request):
    """Render the Graphs page"""
    return templates.TemplateResponse("graphs/index.html", {"request": request})

@router.get("/feature-request-agent", response_class=HTMLResponse)
async def feature_request_agent_page(request: Request):
    """Render the Feature Request Agent page"""
    return templates.TemplateResponse("feature-request-agent/index.html", {"request": request})

@router.get("/extract-workflow", response_class=HTMLResponse)
async def extract_workflow_page(request: Request):
    return templates.TemplateResponse("extract-workflow/extract_workflow.html", {"request": request})

@router.get("/database-backup", response_class=HTMLResponse)
async def database_backup_page(request: Request):
    """Render the Database Backup page"""
    return templates.TemplateResponse("database-backup/index.html", {"request": request})

@router.post("/restart-server", response_class=JSONResponse)
async def restart_server():
    """Endpoint to restart the server application - calls handler from server_restart.py"""
    return await server_restart_handler()

@router.get("/forecast-revisits", response_class=HTMLResponse)
async def forecast_revisits_page(request: Request):
    """Render the Forecast Revisits page"""
    return templates.TemplateResponse("forecast-revisits/index.html", {"request": request})

@router.get("/letta", response_class=HTMLResponse)
async def letta_page(request: Request):
    """Render the Letta page"""
    return templates.TemplateResponse("letta/index.html", {"request": request})

@router.get("/rl-environments", response_class=HTMLResponse)
async def rl_environments_page(request: Request):
    """Render the RL Environments page"""
    return templates.TemplateResponse("rl-environments/index.html", {"request": request})

@router.get("/rl-training", response_class=HTMLResponse)
async def rl_training_page(request: Request):
    """Render the RL Training page"""
    return templates.TemplateResponse("rl-training/index.html", {"request": request})

@router.get("/backtest-strategy", response_class=HTMLResponse)
async def backtest_strategy_page(request: Request):
    """Render the Backtest Strategy page"""
    return templates.TemplateResponse("backtest-strategy/backtest_strategy.html", {"request": request})

@router.get("/backtesting-evals", response_class=HTMLResponse)
async def backtesting_evals_page(request: Request):
    """Render the Backtesting Evals page"""
    return templates.TemplateResponse("backtesting-evals/backtesting_evals.html", {"request": request})


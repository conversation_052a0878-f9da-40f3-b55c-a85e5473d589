from fastapi import APIRouter, HTTPException
from typing import Optional, List
from db.report_repository import ReportRepository
from bson import ObjectId
from pydantic import BaseModel
from enum import Enum

router = APIRouter()

class ReportType(str, Enum):
    SIMPLE = "simple"
    JOIN = "join"
    EXTRACT = "extract"

class ReportFilter(BaseModel):
    type: Optional[ReportType] = None
    ticker: Optional[str] = None

@router.get("/")
async def list_reports(type: Optional[ReportType] = None, ticker: Optional[str] = None):
    """List all reports with optional filtering by type and ticker"""
    try:
        repo = await ReportRepository.create()
        query = {}
        if type:
            query["type"] = type
        if ticker:
            query["ticker"] = ticker
            
        reports = await repo.get_reports_by_query(query)
        return reports
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch reports: {str(e)}")

@router.get("/{report_id}")
async def get_report(report_id: str):
    """Get a specific report by ID"""
    try:
        try:
            object_id = ObjectId(report_id)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid report ID format: {report_id}")
        
        repo = await ReportRepository.create()
        report = await repo.get_report(object_id)
        
        if report:
            return report
            
        raise HTTPException(status_code=404, detail="Report not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get report: {str(e)}")

@router.delete("/{report_id}")
async def delete_report(report_id: str):
    """Delete a specific report by ID"""
    try:
        repo = await ReportRepository.create()
        if await repo.delete_report(ObjectId(report_id)):
            return {"message": "Report deleted successfully"}
        raise HTTPException(status_code=404, detail="Report not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete report: {str(e)}")

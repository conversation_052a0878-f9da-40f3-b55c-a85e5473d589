from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
from software.db.backtest_strategy_eval_repository import BacktestStrategyEvalRepository
from software.db.llm_repository import LLMRepository

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

async def get_llm_name_by_id(llm_id: str) -> str:
    """
    Get human-readable LLM name by ID.

    Args:
        llm_id: The LLM model ID

    Returns:
        str: Human-readable LLM name or fallback text
    """
    try:
        if llm_id == "unknown":
            return "Model Not Found"

        llm_repo = LLMRepository()
        llm_config = await llm_repo.get_llm(llm_id)

        if llm_config:
            return llm_config.get('name', 'Unnamed Model')
        else:
            return "Model Not Found"

    except Exception as e:
        logger.error(f"Error getting LLM name for ID {llm_id}: {str(e)}")
        return "Model Not Found"

class EvaluationStats(BaseModel):
    """Model for evaluation statistics"""
    total_count: int
    avg_overall_score: float
    avg_readability_score: float
    avg_code_alignment_score: float
    avg_optimization_score: float
    avg_analysis_score: float
    min_overall_score: float
    max_overall_score: float

class EvaluationTimeseriesData(BaseModel):
    """Model for timeseries chart data"""
    categories: List[str]  # Timestamps
    series: List[Dict[str, Any]]  # Highcharts series data

class DeleteEvaluationsRequest(BaseModel):
    """Model for bulk deletion request"""
    evaluation_ids: List[str]

class DeleteEvaluationsResponse(BaseModel):
    """Model for bulk deletion response"""
    success: bool
    deleted_count: int
    failed_ids: List[str]
    message: str

@router.get("/timeseries", response_model=EvaluationTimeseriesData)
async def get_evaluations_timeseries(limit: int = 100) -> EvaluationTimeseriesData:
    """
    Get evaluation data formatted for Highcharts timeseries visualization.

    Args:
        limit: Maximum number of evaluations to retrieve

    Returns:
        EvaluationTimeseriesData: Formatted data for Highcharts
    """
    try:
        logger.info(f"Fetching evaluations timeseries data with limit: {limit}")

        # Initialize repository
        repo = await BacktestStrategyEvalRepository.create()

        # Get evaluations
        evaluations = await repo.get_evaluations_timeseries(limit)

        if not evaluations:
            return EvaluationTimeseriesData(
                categories=[],
                series=[]
            )

        # Translate LLM IDs to names for each evaluation
        for evaluation in evaluations:
            llm_id = evaluation.get('llm_used', 'unknown')
            evaluation['llm_name'] = await get_llm_name_by_id(llm_id)

        # Extract timestamps for categories
        categories = [eval_data['created_at'] for eval_data in evaluations if eval_data['created_at']]

        # Prepare series data for each score type
        series = [
            {
                'name': 'Overall Score',
                'data': [eval_data['overall_score'] for eval_data in evaluations],
                'color': '#4F46E5'
            },
            {
                'name': 'Report Readability',
                'data': [eval_data['report_readability_score'] for eval_data in evaluations],
                'color': '#06B6D4'
            },
            {
                'name': 'Code-Strategy Alignment',
                'data': [eval_data['code_strategy_alignment_score'] for eval_data in evaluations],
                'color': '#10B981'
            },
            {
                'name': 'Optimization Ranges',
                'data': [eval_data['optimization_ranges_realism_score'] for eval_data in evaluations],
                'color': '#F59E0B'
            },
            {
                'name': 'Analysis Quality',
                'data': [eval_data['analysis_quality_score'] for eval_data in evaluations],
                'color': '#EF4444'
            }
        ]

        logger.info(f"Successfully retrieved {len(evaluations)} evaluations for timeseries")

        return EvaluationTimeseriesData(
            categories=categories,
            series=series
        )

    except Exception as e:
        logger.error(f"Error fetching evaluations timeseries: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch evaluations timeseries: {str(e)}"
        )

@router.get("/stats", response_model=EvaluationStats)
async def get_evaluation_stats() -> EvaluationStats:
    """
    Get evaluation statistics.

    Returns:
        EvaluationStats: Statistics about all evaluations
    """
    try:
        logger.info("Fetching evaluation statistics")

        # Initialize repository
        repo = await BacktestStrategyEvalRepository.create()

        # Get statistics
        stats = await repo.get_evaluation_stats()

        logger.info(f"Successfully retrieved evaluation stats: {stats}")

        return EvaluationStats(**stats)

    except Exception as e:
        logger.error(f"Error fetching evaluation stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch evaluation stats: {str(e)}"
        )

@router.get("/ticker/{ticker}")
async def get_evaluations_by_ticker(ticker: str, limit: int = 50) -> List[Dict[str, Any]]:
    """
    Get evaluations for a specific ticker.

    Args:
        ticker: Stock ticker symbol
        limit: Maximum number of evaluations to retrieve

    Returns:
        List of evaluations for the ticker
    """
    try:
        logger.info(f"Fetching evaluations for ticker: {ticker}")

        # Initialize repository
        repo = await BacktestStrategyEvalRepository.create()

        # Get evaluations for ticker
        evaluations = await repo.get_evaluations_by_ticker(ticker.upper(), limit)

        logger.info(f"Successfully retrieved {len(evaluations)} evaluations for ticker {ticker}")

        return evaluations

    except Exception as e:
        logger.error(f"Error fetching evaluations for ticker {ticker}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch evaluations for ticker {ticker}: {str(e)}"
        )

@router.get("/table-data")
async def get_evaluations_table_data(limit: int = 50) -> List[Dict[str, Any]]:
    """
    Get evaluation data formatted for table display with LLM names resolved.

    Args:
        limit: Maximum number of evaluations to retrieve

    Returns:
        List of evaluations with LLM names resolved
    """
    try:
        logger.info(f"Fetching evaluations table data with limit: {limit}")

        # Initialize repository
        repo = await BacktestStrategyEvalRepository.create()

        # Get evaluations
        evaluations = await repo.get_evaluations_timeseries(limit)

        if not evaluations:
            return []

        # Translate LLM IDs to names for each evaluation
        for evaluation in evaluations:
            llm_id = evaluation.get('llm_used', 'unknown')
            evaluation['llm_name'] = await get_llm_name_by_id(llm_id)

        # Sort by date (most recent first)
        evaluations.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        logger.info(f"Successfully retrieved {len(evaluations)} evaluations for table")

        return evaluations

    except Exception as e:
        logger.error(f"Error fetching evaluations table data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch evaluations table data: {str(e)}"
        )

@router.delete("/delete", response_model=DeleteEvaluationsResponse)
async def delete_evaluations(request: DeleteEvaluationsRequest) -> DeleteEvaluationsResponse:
    """
    Delete multiple evaluations by their IDs.

    Args:
        request: DeleteEvaluationsRequest containing list of evaluation IDs

    Returns:
        DeleteEvaluationsResponse: Results of the deletion operation
    """
    try:
        logger.info(f"Bulk deletion request for {len(request.evaluation_ids)} evaluations")

        if not request.evaluation_ids:
            return DeleteEvaluationsResponse(
                success=True,
                deleted_count=0,
                failed_ids=[],
                message="No evaluations specified for deletion"
            )

        # Initialize repository
        repo = await BacktestStrategyEvalRepository.create()

        # Perform bulk deletion
        result = await repo.delete_evaluations(request.evaluation_ids)

        # Log the operation for audit purposes
        logger.info(f"Bulk deletion completed: {result['deleted_count']} deleted, {len(result['failed_ids'])} failed")

        return DeleteEvaluationsResponse(
            success=result['success'],
            deleted_count=result['deleted_count'],
            failed_ids=result['failed_ids'],
            message=result['message']
        )

    except Exception as e:
        logger.error(f"Error in bulk deletion endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete evaluations: {str(e)}"
        )

@router.get("/info")
async def get_backtest_eval_info() -> Dict[str, Any]:
    """
    Get basic information about the backtest evaluation functionality.

    Returns:
        Dict[str, Any]: Basic information about the backtest evaluation API
    """
    try:
        return {
            "status": "success",
            "message": "Backtest Strategy Evaluation API is operational",
            "version": "1.0.0",
            "endpoints": {
                "timeseries": "GET /timeseries - Get evaluation data for charts",
                "stats": "GET /stats - Get evaluation statistics",
                "ticker": "GET /ticker/{ticker} - Get evaluations by ticker"
            }
        }
    except Exception as e:
        logger.error(f"Error getting backtest eval info: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get backtest eval info: {str(e)}"
        )

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Optional
from data import registry
import uuid
import pandas as pd
import numpy as np
from db.report_repository import (
    ReportRepository, 
    ReportFactory,
    ExtractRequest,
    ExtractResponse
)
from datetime import datetime
from main import StockAnalysisApp
from features import FEATURE_CLASSES

router = APIRouter()

tasks = {}

async def load_data_loader_class(loader_name: str):
    loader_class = registry.get_loader_class(loader_name)
    if not loader_class:
        raise HTTPException(status_code=404, detail=f"Data loader {loader_name} not found")
    return loader_class

async def save_report(data: pd.DataFrame, ticker: str, data_loader: str, features: List[str] = None) -> str:
    # Ensure we have valid date strings
    start_date = str(data.index.min()) if not data.empty else "1970-01-01"
    end_date = str(data.index.max()) if not data.empty else "1970-01-01"
    
    report_data = ReportFactory.create_extract_report(
        ticker=ticker,
        data_loader=data_loader,
        data=data,
        features=features,
        start_date=start_date,
        end_date=end_date
    )
    
    repo = await ReportRepository.create()
    report_id = await repo.insert_report(report_data)
    return report_id

@router.get("/features")
async def get_available_features():
    """Get list of available features."""
    return {"features": list(FEATURE_CLASSES.keys())}

@router.post("/run")
async def run_extract_workflow(request: ExtractRequest, background_tasks: BackgroundTasks):
    """
    Run the extract workflow to get financial data.
    
    This endpoint supports snapshot data extraction by allowing users to specify a date range.
    When start_date and end_date are provided, the workflow will extract data only for 
    that specific time frame. If dates are not provided, it will fetch the entire available history.
    
    Note: Features are calculated on the complete dataset before filtering to ensure accuracy,
    as many technical indicators require historical data beyond the snapshot range.
    """
    task_id = str(uuid.uuid4())
    
    try:
        # Get the loader class and validate
        loader_class = await load_data_loader_class(request.data_loader)
        
        # Initialize loader with ticker and validate data availability
        data_loader = loader_class(request.ticker)
        df = data_loader.load_historical_data()
        
        if df is None or df.empty:
            error_msg = f"No data available for ticker {request.ticker} with loader {request.data_loader}"
            raise HTTPException(status_code=400, detail=error_msg)
        
        # Initialize StockAnalysisApp
        app = StockAnalysisApp(request.ticker)
        
        # Set data and data loader name in app
        app.data = df
        app.data_loader_name = request.data_loader
        
        # Calculate all available features on complete dataset
        available_features = list(FEATURE_CLASSES.keys())
        
        # Calculate features
        original_shape = app.data.shape
        for feature in available_features:
            before_shape = app.data.shape
            try:
                app.rerun_feature(feature)
                after_shape = app.data.shape
                if after_shape[0] == 0:
                    app.data = df.copy()  # Reset to original data
                    continue
            except Exception as e:
                continue
        
        # Check if we have any data left after feature calculation
        if app.data.empty:
            raise ValueError("Feature calculation resulted in empty DataFrame. Please check feature calculations.")
        
        # Now filter data by date range if provided
        filtered_df = app.data.copy()
        
        if request.start_date and request.end_date:
            # Convert request dates to timezone-naive timestamps
            try:
                start_ts = pd.to_datetime(request.start_date)
                end_ts = pd.to_datetime(request.end_date)
                
                # If timestamps are timezone-aware, convert to naive
                if start_ts.tz is not None:
                    start_ts = start_ts.tz_localize(None)
                if end_ts.tz is not None:
                    end_ts = end_ts.tz_localize(None)
                
            except Exception as e:
                raise ValueError(f"Invalid date format: {str(e)}")
            
            # Filter the data
            mask = (filtered_df.index >= start_ts) & (filtered_df.index <= end_ts)
            filtered_df = filtered_df[mask]
            
            if filtered_df.empty:
                raise ValueError(f"No data available for the specified date range: {start_ts} to {end_ts}")
        
        # Save report to database with the filtered data
        report_id = await save_report(
            data=filtered_df,
            ticker=request.ticker,
            data_loader=request.data_loader,
            features=available_features
        )
        
        tasks[task_id] = {
            "status": "completed", 
            "report_id": report_id,
            "message": "Data extraction and feature calculation completed"
        }
        return ExtractResponse(report_id=task_id, message="Data extraction and feature calculation completed")
    except Exception as e:
        tasks[task_id] = {"status": "failed", "error": str(e)}
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}")
async def get_extract_status(task_id: str):
    """Get the status of an extraction task."""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    return tasks[task_id]

@router.get("/validate-ticker/{data_loader}/{ticker}")
async def validate_ticker(data_loader: str, ticker: str):
    """
    Validate if a ticker is available for the given data loader.
    Returns basic metadata about the ticker if valid.
    """
    try:
        # Get the loader class
        loader_class = await load_data_loader_class(data_loader)
        
        # Try to load data for the ticker
        data_loader = loader_class(ticker)
        df = data_loader.load_historical_data()
        
        if df is None or df.empty:
            raise HTTPException(
                status_code=400, 
                detail=f"No data available for ticker {ticker} with loader {data_loader}"
            )
        
        # Return basic metadata about the ticker
        return {
            "valid": True,
            "ticker": ticker,
            "data_loader": data_loader,
            "date_range": {
                "start": str(df.index.min()),
                "end": str(df.index.max())
            },
            "data_points": len(df),
            "frequency": pd.infer_freq(df.index) or "irregular"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid ticker {ticker} for loader {data_loader}: {str(e)}"
        )

from fastapi import APIRouter, HTTPException, Request
from typing import List, Optional, Dict, Any
from ai.graph import get_available_graphs
from ..database import llm_collection, database
from db.graph_repository import GraphRepository
from db.research_repository import ResearchRepository
import logging
import uuid

router = APIRouter()
graph_repository = GraphRepository()
research_repo = ResearchRepository()
logger = logging.getLogger(__name__)

@router.get("/available")
async def list_available_graphs():
    """List all available graph types from the registry"""
    try:
        graphs = [
            {
                "id": graph_id,
                "name": info["name"]
            }
            for graph_id, info in get_available_graphs().items()
        ]
        return {"status": "success", "data": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/llm-models")
async def list_llm_models():
    """List all available LLM models"""
    try:
        models = await llm_collection.find({"is_enabled": True}).to_list(length=None)
        # Process models to return only id and name
        simplified_models = [
            {"id": str(model["_id"]), "name": model.get("name", "Unnamed Model")} 
            for model in models
        ]
        return {"status": "success", "data": simplified_models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/graphs")
async def create_graph(graph_data: Dict[str, Any]):
    """Create a new graph with stages"""
    try:
        # Simplify the schema by removing redundant type field
        simplified_data = {
            "name": graph_data["name"],
            "stages": graph_data["stages"]
        }
        graph_id = await graph_repository.create_graph(simplified_data)
        return {"status": "success", "id": graph_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graphs")
async def list_graphs():
    """List all graphs"""
    try:
        graphs = await graph_repository.list_graphs()
        return {"status": "success", "data": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graphs/{graph_id}")
async def get_graph(graph_id: str):
    """Get a specific graph by ID"""
    try:
        graph = await graph_repository.get_graph(graph_id)
        if not graph:
            raise HTTPException(status_code=404, detail="Graph not found")
        return {"status": "success", "data": graph}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/graphs/{graph_id}/stages")
async def update_graph_stages(graph_id: str, stages: List[Dict[str, Any]]):
    """Update graph stages"""
    try:
        success = await graph_repository.update_graph_stages(graph_id, stages)
        if success:
            return {"status": "success", "message": "Stages updated successfully"}
        raise HTTPException(status_code=404, detail="Graph not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graphs/{graph_id}/visualization")
async def get_graph_visualization(graph_id: str):
    """Get the graph visualization in Mermaid format"""
    try:
        # Get graph from registry
        graph_info = get_available_graphs().get(graph_id)
        if not graph_info:
            raise HTTPException(status_code=404, detail="Graph type not found")
            
        # Get the graph module
        graph_module = graph_info.get("module")
        if not graph_module:
            raise HTTPException(status_code=404, detail="Graph module not found")
            
        # Get the graph instance
        if not hasattr(graph_module, "graph"):
            raise HTTPException(
                status_code=404, 
                detail=f"No 'graph' attribute found in module {graph_id}"
            )
            
        graph = graph_module.graph
        if not graph:
            raise HTTPException(status_code=404, detail="Graph instance is None")
            
        # Get Mermaid visualization
        mermaid_code = graph.get_graph().draw_mermaid()
        
        return {"status": "success", "data": mermaid_code}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/graphs/{graph_id}/run")
async def run_graph(graph_id: str, request: Request):
    """Run a specific graph with test input"""
    try:
        # First get the graph configuration from MongoDB
        graph_config = await graph_repository.get_graph(graph_id)
        if not graph_config:
            logger.error(f"Graph configuration not found for {graph_id}")
            raise HTTPException(status_code=404, detail="Graph configuration not found")
            
        # Get graph type from the configuration
        graph_type = graph_config["name"]
        
        # Get graph from registry using the graph type
        graph_info = get_available_graphs().get(graph_type)
        if not graph_info:
            raise HTTPException(status_code=404, detail="Graph type not found in registry")
            
        # Get the graph module
        graph_module = graph_info.get("module")
        if not graph_module:
            raise HTTPException(status_code=404, detail="Graph module not found")
            
        # Get the graph instance
        graph = getattr(graph_module, "graph", None)
        if not graph:
            raise HTTPException(status_code=404, detail="Graph instance not found")

        # Get input data from request
        input_data = await request.json()
        
        # Create an analysis task first
        task_id = await research_repo.create_analysis()
        
        # Extract stages from the graph
        graph_stages = list(graph.nodes.keys())
        graph_stages.append("END")  # Add END as final stage
        
        # Initialize workflow stages
        await research_repo.initialize_workflow_stages(task_id, graph_stages)
        
        # Add required fields to input data
        input_data.update({
            "graph_id": graph_id,
            "graph_type": graph_type,
            "task_id": task_id,
            "stages": graph_config.get("stages", [])
        })
        
        # Run the graph with input
        result = await graph.ainvoke(input_data)
        
        return {
            "status": "success",
            "task_id": task_id,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error running graph {graph_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 
    

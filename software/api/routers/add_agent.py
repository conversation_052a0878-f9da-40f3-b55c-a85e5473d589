from fastapi import API<PERSON>outer, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
from pydantic import BaseModel, ConfigDict
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
from bson import ObjectId
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()
frontend_path = Path(__file__).parent.parent.parent / "frontend"
templates = Jinja2Templates(directory=str(frontend_path))

# Import research_directors_collection directly where needed
# The main app has the DB connection already set up

class DirectorCreate(BaseModel):
    name: str
    title: str
    experience_years: int
    expertise: List[str]  # List of sectors/industries they specialize in
    analysis_style: str
    background: str
    personality: str

    model_config = ConfigDict(from_attributes=True)

class DirectorResponse(BaseModel):
    id: str
    name: str
    title: str
    experience_years: int
    expertise: List[str]
    analysis_style: str
    background: str
    personality: str
    total_reports: int
    last_active: datetime
    memory: Optional[Dict[str, Any]] = None  # Stores insights and knowledge about their sectors
    graph_type: Optional[str] = None  # Store the selected graph type

    model_config = ConfigDict(from_attributes=True)

class BusinessQuestion(BaseModel):
    business_question: str

@router.get("/", response_class=HTMLResponse)
async def add_agent_page(request: Request):
    """Render the add agent page with list of existing directors"""
    try:
        # Import here to avoid circular imports
        from software.db.research_director_repository import ResearchDirectorRepository

        # Get all directors
        director_repo = ResearchDirectorRepository()
        directors = await director_repo.list_directors()

        # Sort directors by name
        directors.sort(key=lambda x: x.get("name", ""))

        return templates.TemplateResponse(
            "add-agent/add_agent.html",
            {"request": request, "directors": directors}
        )
    except Exception as e:
        logger.error(f"Error loading directors: {str(e)}")
        # Return the page without directors if there's an error
        return templates.TemplateResponse(
            "add-agent/add_agent.html",
            {"request": request, "directors": []}
        )

@router.post("/", response_model=DirectorResponse)
async def create_director(director: DirectorCreate):
    """Create a new research director agent"""
    try:
        # Import here to avoid circular imports
        from software.api.database import research_directors_collection

        # Create director document
        director_doc = {
            "name": director.name,
            "title": director.title,
            "experience_years": director.experience_years,
            "expertise": director.expertise,
            "analysis_style": director.analysis_style,
            "background": director.background,
            "personality": director.personality,
            "created_at": datetime.now(),
            "last_active": datetime.now(),
            "total_reports": 0,
            "report_ids": [],
            "memory": {
                "market_insights": [],
                "company_analyses": {},
                "sector_knowledge": {},
                "research_history": [],
                "decision_patterns": [],
                "performance_metrics": {
                    "successful_predictions": 0,
                    "total_predictions": 0,
                    "sectors_analyzed": []
                }
            }
        }

        # Insert into database
        result = await research_directors_collection.insert_one(director_doc)

        # Return the created director with id
        director_id = str(result.inserted_id)

        # Return response model format
        return DirectorResponse(
            id=director_id,
            name=director.name,
            title=director.title,
            experience_years=director.experience_years,
            expertise=director.expertise,
            analysis_style=director.analysis_style,
            background=director.background,
            personality=director.personality,
            total_reports=0,
            last_active=datetime.now(),
            memory=director_doc["memory"],
            graph_type=None
        )

    except Exception as e:
        logger.error(f"Error creating director: {str(e)}")
        error_message = str(e)

        # Check for duplicate key error
        if "duplicate key error" in error_message and "name" in error_message:
            raise HTTPException(
                status_code=400,
                detail=f"An agent with the name '{director.name}' already exists. Please choose a different name."
            )

        # For other errors
        raise HTTPException(status_code=500, detail=error_message)

@router.delete("/{director_id}")
async def delete_director(director_id: str):
    """Delete a research director agent"""
    try:
        # Import here to avoid circular imports
        from software.db.research_director_repository import ResearchDirectorRepository

        # Initialize repository
        director_repo = ResearchDirectorRepository()

        # Delete the director
        success = await director_repo.delete_director(director_id)

        if success:
            return {"status": "success", "message": "Director deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Director not found or could not be deleted")

    except Exception as e:
        logger.error(f"Error deleting director: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete director: {str(e)}")

@router.post("/generate-profile")
async def generate_agent_profile(question: BusinessQuestion):
    """Generate an agent profile based on a business question"""
    logger.info(f"Generating agent profile for business question: {question.business_question}")

    # Import the compiled graph
    from software.ai.graph.agent_builder_graph import graph as agent_builder_graph

    # Initialize the state with the user request
    initial_state = {
        "messages": [],
        "user_request": question.business_question,
        "agent_profile": None,
        "result": None
    }

    # Run the graph asynchronously
    result = await agent_builder_graph.ainvoke(initial_state)

    # Return the agent profile directly
    return result["agent_profile"]
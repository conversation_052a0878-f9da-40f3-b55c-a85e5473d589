from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import os
import sys
import importlib
import importlib.util
import inspect
import logging
import time
import asyncio
import json
import pytz
import copy
from datetime import datetime
from bson import ObjectId
from io import StringIO
import contextlib
import traceback
import re
from motor.motor_asyncio import AsyncIOMotorClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import repositories
from db.rl_training_repository import RLTrainingRepository
from db.rl_environment_repository import RLEnvironmentRepository
from api.database import database, MONGO_URI

# Define the RL directories
RL_DIR = os.path.join(project_root, 'software', 'rl')
RL_MODELS_DIR = os.path.join(RL_DIR, 'models')
RL_LOGS_DIR = os.path.join(RL_DIR, 'logs')
RL_DATA_DIR = os.path.join(RL_DIR, 'data')

# Create directories if they don't exist
os.makedirs(RL_MODELS_DIR, exist_ok=True)
os.makedirs(RL_LOGS_DIR, exist_ok=True)
os.makedirs(RL_DATA_DIR, exist_ok=True)

router = APIRouter()

# IMPORTANT: Order matters for FastAPI routes!
# Define specific routes before parameterized routes to avoid conflicts

@router.get("/data-files", include_in_schema=True)
async def get_training_data_files():
    """Get list of available training data files with data counts"""
    print("GET /data-files endpoint called")
    try:
        # Define the training data directory
        # Log current working directory
        current_dir = os.getcwd()
        logger.info(f"Current working directory: {current_dir}")
        print(f"Current working directory: {current_dir}")

        # Try different paths
        relative_path = os.path.join("software", "rl", "training", "data")
        absolute_path = os.path.abspath(relative_path)

        # Use the absolute path
        training_data_dir = absolute_path
        logger.info(f"Relative path: {relative_path}")
        logger.info(f"Absolute path: {absolute_path}")
        print(f"Looking for training data files in: {relative_path}")
        print(f"Absolute path: {absolute_path}")

        # Debug logging
        logger.info(f"Looking for training data files in: {os.path.abspath(training_data_dir)}")

        # Check if directory exists
        if not os.path.exists(training_data_dir):
            logger.warning(f"Training data directory does not exist: {training_data_dir}")
            os.makedirs(training_data_dir, exist_ok=True)
            return []

        # Get all JSON files in the directory
        training_files = []
        files_in_dir = os.listdir(training_data_dir)
        logger.info(f"Files in directory: {files_in_dir}")
        print(f"Files in directory: {files_in_dir}")

        for filename in files_in_dir:
            if filename.endswith(".json"):
                file_path = os.path.join(training_data_dir, filename)
                logger.info(f"Found JSON file: {file_path}")
                print(f"Found JSON file: {filename} at {file_path}")

                file_info = {
                    "filename": filename,
                    "path": os.path.join("software", "rl", "training", "data", filename),
                    "size": os.path.getsize(file_path),
                    "modified": os.path.getmtime(file_path),
                    "data_count": 0
                }

                # Try to load the file and count data items
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            file_info["data_count"] = len(data)
                            logger.info(f"File {filename} contains {len(data)} items (list)")
                        elif isinstance(data, dict) and "data" in data:
                            file_info["data_count"] = len(data["data"])
                            logger.info(f"File {filename} contains {len(data['data'])} items (dict with 'data' key)")
                        else:
                            logger.info(f"File {filename} has unknown structure: {type(data)}")
                except Exception as e:
                    logger.warning(f"Error reading training data file {filename}: {str(e)}")

                training_files.append(file_info)

        # Sort by modified date (newest first)
        training_files.sort(key=lambda x: x["modified"], reverse=True)

        return training_files
    except Exception as e:
        logger.error(f"Error getting training data files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get training data files: {str(e)}")

@router.get("/mongodb-collections", include_in_schema=True)
async def get_mongodb_collections():
    """Get list of available MongoDB collections with document counts"""
    print("GET /mongodb-collections endpoint called")
    try:
        # Get list of collections from MongoDB
        collections = await database.list_collection_names()
        print(f"Found {len(collections)} collections in MongoDB")

        # Get document count for each collection
        collection_info = []
        for collection_name in collections:
            # Skip system collections
            if collection_name.startswith("system."):
                continue

            # Get document count
            count = await database[collection_name].count_documents({})

            # Add to collection info
            collection_info.append({
                "name": collection_name,
                "count": count
            })

        # Sort by name
        collection_info.sort(key=lambda x: x["name"])

        return collection_info
    except Exception as e:
        logger.error(f"Error getting MongoDB collections: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get MongoDB collections: {str(e)}")

@router.get("/mongodb-collections/{collection_name}/sample", include_in_schema=True)
async def get_mongodb_sample_document(collection_name: str):
    """Get a sample document from a MongoDB collection"""
    print(f"GET /mongodb-collections/{collection_name}/sample endpoint called")
    try:
        # Check if collection exists
        collections = await database.list_collection_names()
        if collection_name not in collections:
            raise HTTPException(status_code=404, detail=f"Collection {collection_name} not found")

        # Get a sample document
        sample = await database[collection_name].find_one()

        # If no documents found
        if not sample:
            raise HTTPException(status_code=404, detail=f"No documents found in collection {collection_name}")

        # Convert ObjectId to string for JSON serialization
        sample = json.loads(json.dumps(sample, default=str))

        return sample
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sample document from MongoDB collection {collection_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get sample document: {str(e)}")

# Pydantic models
class ScheduleConfig(BaseModel):
    """Model for scheduling configuration"""
    type: str = "daily"  # For now, only daily is supported
    time: str  # Format: "HH:MM" in 24-hour format

class HyperparametersOptimizationConfig(BaseModel):
    """Model for hyperparameters optimization configuration"""
    enabled: bool = False
    parameters: Dict[str, List[Any]] = Field(default_factory=dict)

class SmartHyperparametersOptimizationConfig(BaseModel):
    """Model for smart hyperparameters optimization configuration"""
    enabled: bool = False
    parameters: Dict[str, List[Any]] = Field(default_factory=dict)
    max_trials: int = 10  # Maximum number of trials to run
    strategy: str = "bayesian"  # Optimization strategy: "bayesian", "random", or "sequential"
    single_optimized_job: bool = True  # Whether to create a single optimized job instead of multiple jobs
    depth: str = "deep"  # Optimization depth: "deep" (5 values), "deeper" (8 values), or "extra" (12 values)

class TrainingRequest(BaseModel):
    """Model for creating a new training job"""
    environment_id: str
    algorithm: str = "PPO"
    timesteps: int = 10000
    training_data_path: str = "software/rl/training/data/sample_training.json"
    train_ratio: float = 0.8
    finetune: bool = False
    model_to_finetune_id: Optional[str] = None  # ID of the model to finetune
    recurring_finetune: bool = False  # Enable recurring finetuning
    schedule: Optional[ScheduleConfig] = None  # Schedule configuration
    save_only_if_improved: bool = False  # Only save if mean reward improves
    hyperparameters: Dict[str, Any] = Field(default_factory=dict)
    hyperparameters_optimization: Optional[HyperparametersOptimizationConfig] = None  # Hyperparameters optimization configuration
    smart_hyperparameters_optimization: Optional[SmartHyperparametersOptimizationConfig] = None  # Smart hyperparameters optimization configuration
    description: Optional[str] = None

    # Fields to store comparison data (not part of the API schema, for internal use)
    original_mean_reward: Optional[float] = Field(default=0, exclude=True)
    original_model_path: Optional[str] = Field(default=None, exclude=True)

    # Fields for single optimized job (not part of the API schema, for internal use)
    single_optimized_job: bool = Field(default=False, exclude=True)
    hyperparameter_combinations: List[Dict[str, Any]] = Field(default_factory=list, exclude=True)

class TrainingUpdate(BaseModel):
    """Model for updating an existing training job"""
    status: Optional[str] = None
    progress: Optional[int] = None
    model_path: Optional[str] = None
    log_path: Optional[str] = None
    training_time: Optional[float] = None
    mean_reward: Optional[float] = None
    std_reward: Optional[float] = None
    error: Optional[str] = None

# Dictionary to store active training tasks
active_training_tasks = {}

def generate_hyperparameter_combinations(optimization_config: HyperparametersOptimizationConfig) -> List[Dict[str, Any]]:
    """Generate all combinations of hyperparameters based on the optimization configuration"""
    if not optimization_config or not optimization_config.enabled:
        return []

    parameters = optimization_config.parameters
    if not parameters:
        return []

    # Get all parameter names
    param_names = list(parameters.keys())

    # Get all parameter values
    param_values = [parameters[name] for name in param_names]

    # Generate all combinations
    combinations = []

    # Helper function to generate combinations recursively
    def generate_combinations(index, current_combination):
        if index == len(param_names):
            combinations.append(dict(current_combination))
            return

        param_name = param_names[index]
        for value in param_values[index]:
            current_combination[param_name] = value
            generate_combinations(index + 1, current_combination)

    # Start the recursive generation
    generate_combinations(0, {})

    return combinations

def generate_smart_hyperparameter_trials(optimization_config: SmartHyperparametersOptimizationConfig) -> List[Dict[str, Any]]:
    """Generate a limited number of hyperparameter trials using smart optimization strategies"""
    print(f"DEBUG - generate_smart_hyperparameter_trials called")

    if not optimization_config or not optimization_config.enabled:
        print(f"DEBUG - optimization_config is None or not enabled")
        return []

    parameters = optimization_config.parameters
    if not parameters:
        print(f"DEBUG - No parameters in optimization_config")
        return []

    max_trials = optimization_config.max_trials
    strategy = optimization_config.strategy
    depth = getattr(optimization_config, 'depth', 'deep')  # Default to 'deep' if not specified

    print(f"DEBUG - Strategy: {strategy}, Max trials: {max_trials}, Depth: {depth}")
    print(f"DEBUG - Parameters: {parameters}")

    # Determine number of values per parameter based on depth
    values_per_param = 5  # Default for 'deep'
    if depth == 'deeper':
        values_per_param = 8
        print(f"DEBUG - Using 'deeper' depth with {values_per_param} values per parameter")
    elif depth == 'extra':
        values_per_param = 12
        print(f"DEBUG - Using 'extra' depth with {values_per_param} values per parameter")
    else:
        print(f"DEBUG - Using 'deep' depth with {values_per_param} values per parameter")

    print(f"DEBUG - Using {values_per_param} values per parameter based on depth '{depth}'")

    # Get all parameter names
    param_names = list(parameters.keys())
    print(f"DEBUG - Parameter names: {param_names}")

    # Get all parameter values, adjusting based on depth if needed
    param_values = []
    for name in param_names:
        values = parameters[name]
        # If we have more values than needed for the current depth, sample them
        if len(values) > values_per_param:
            # Take evenly spaced values
            step = len(values) // values_per_param
            indices = [i * step for i in range(values_per_param)]
            # Ensure we include the last value
            if indices[-1] < len(values) - 1:
                indices[-1] = len(values) - 1
            sampled_values = [values[i] for i in indices]
            param_values.append(sampled_values)
        else:
            # Use all available values
            param_values.append(values)

    print(f"DEBUG - Parameter values (adjusted for depth): {param_values}")

    # Generate trials based on the selected strategy
    if strategy == "random":
        # Random search: randomly sample from parameter space
        import random
        trials = []

        # Always include default values as the first trial
        default_trial = {}
        for i, param_name in enumerate(param_names):
            # Use the middle value as default
            values = param_values[i]
            default_trial[param_name] = values[len(values) // 2] if values else None

        trials.append(default_trial)

        # Generate remaining random trials
        for _ in range(min(max_trials - 1, 100)):  # Cap at 100 trials for safety
            trial = {}
            for i, param_name in enumerate(param_names):
                values = param_values[i]
                if values:
                    trial[param_name] = random.choice(values)
            trials.append(trial)

        return trials

    elif strategy == "sequential":
        # Sequential optimization: try one parameter at a time
        trials = []

        # Start with default values
        default_trial = {}
        for i, param_name in enumerate(param_names):
            # Use the middle value as default
            values = param_values[i]
            default_trial[param_name] = values[len(values) // 2] if values else None

        trials.append(default_trial)

        # For each parameter, try different values while keeping others at default
        remaining_trials = max_trials - 1
        trials_per_param = remaining_trials // len(param_names) if len(param_names) > 0 else 0

        for i, param_name in enumerate(param_names):
            values = param_values[i]
            if not values:
                continue

            # Skip the default value which is already in the default trial
            default_value = default_trial[param_name]
            other_values = [v for v in values if v != default_value]

            # Determine how many values to try for this parameter
            num_values = min(len(other_values), trials_per_param)

            # Select evenly spaced values
            if num_values > 0 and len(other_values) > 0:
                step = len(other_values) // num_values
                selected_indices = [j * step for j in range(num_values)]
                selected_values = [other_values[j] for j in selected_indices if j < len(other_values)]

                for value in selected_values:
                    trial = default_trial.copy()
                    trial[param_name] = value
                    trials.append(trial)

        return trials

    elif strategy == "bayesian":
        # For true Bayesian optimization using scikit-optimize
        try:
            from skopt import Optimizer
            from skopt.space import Real, Integer, Categorical
            import numpy as np
            print(f"DEBUG - Using scikit-optimize for true Bayesian optimization")
        except ImportError:
            print(f"ERROR - scikit-optimize not installed. Please install it with 'pip install scikit-optimize'")
            # Fall back to the simple approximation if scikit-optimize is not available
            return generate_simple_bayesian_trials(param_names, param_values, max_trials)

        # Define parameter spaces for scikit-optimize
        spaces = []
        param_types = {}  # Store parameter types (int, float, categorical)

        for i, param_name in enumerate(param_names):
            values = param_values[i]
            if not values:
                continue

            # Determine parameter type based on name and values
            if param_name in ['n_steps', 'batch_size', 'n_epochs']:
                # Integer parameters
                param_types[param_name] = 'int'
                # Convert any float values to integers for these parameters
                int_values = [int(v) for v in values]
                spaces.append(Integer(min(int_values), max(int_values), name=param_name))
                print(f"DEBUG - Parameter {param_name} defined as Integer: [{min(int_values)}, {max(int_values)}]")
            elif all(isinstance(v, (int, float)) for v in values):
                # Continuous parameters
                param_types[param_name] = 'float'
                spaces.append(Real(min(values), max(values), name=param_name))
                print(f"DEBUG - Parameter {param_name} defined as Real: [{min(values)}, {max(values)}]")
            else:
                # Categorical parameters
                param_types[param_name] = 'categorical'
                spaces.append(Categorical(values, name=param_name))
                print(f"DEBUG - Parameter {param_name} defined as Categorical with {len(values)} options")

        # Start with initial points to explore the space
        trials = []

        # Add default values as first trial
        default_trial = {}
        for i, param_name in enumerate(param_names):
            values = param_values[i]
            if values:
                default_trial[param_name] = values[len(values) // 2]
                # Ensure integer parameters are integers
                if param_name in ['n_steps', 'batch_size', 'n_epochs']:
                    default_trial[param_name] = int(default_trial[param_name])
        trials.append(default_trial)

        # Add min and max values for each parameter to explore the boundaries
        min_trial = {}
        max_trial = {}
        for i, param_name in enumerate(param_names):
            values = sorted(param_values[i])
            if values:
                min_trial[param_name] = values[0]
                max_trial[param_name] = values[-1]
                # Ensure integer parameters are integers
                if param_name in ['n_steps', 'batch_size', 'n_epochs']:
                    min_trial[param_name] = int(min_trial[param_name])
                    max_trial[param_name] = int(max_trial[param_name])

        trials.append(min_trial)
        trials.append(max_trial)

        # We'll return these initial points for now
        # The actual Bayesian optimization will happen during training
        # We'll store the optimizer and spaces in the job data for later use

        # Store the parameter spaces and types for use during training
        # Avoid circular reference by not including the trials in the optimization_data
        optimization_data = {
            "param_spaces": [str(space) for space in spaces],  # Convert to string for storage
            "param_types": param_types,
            "param_names": param_names
            # Don't include trials here to avoid circular reference
        }

        # Add this data to the first trial as metadata
        if trials:
            trials[0]["_optimization_data"] = optimization_data

        return trials

def generate_simple_bayesian_trials(param_names, param_values, max_trials):
    """Generate trials using a simple approximation of Bayesian optimization"""
    print(f"DEBUG - Using simple approximation of Bayesian optimization")

    # Start with a diverse set of initial points
    trials = []

    # Add default values as first trial
    default_trial = {}
    for i, param_name in enumerate(param_names):
        values = param_values[i]
        default_val = values[len(values) // 2] if values else None
        # Ensure integer parameters are integers
        if param_name in ['n_steps', 'batch_size', 'n_epochs'] and default_val is not None:
            default_val = int(default_val)
        default_trial[param_name] = default_val
    trials.append(default_trial)

    # Add min and max values for each parameter
    min_trial = {}
    max_trial = {}
    for i, param_name in enumerate(param_names):
        values = sorted(param_values[i])
        if values:
            min_val = values[0]
            max_val = values[-1]
            # Ensure integer parameters are integers
            if param_name in ['n_steps', 'batch_size', 'n_epochs']:
                min_val = int(min_val)
                max_val = int(max_val)
            min_trial[param_name] = min_val
            max_trial[param_name] = max_val
        else:
            min_trial[param_name] = None
            max_trial[param_name] = None

    trials.append(min_trial)
    trials.append(max_trial)

    # Add some random points to explore the space
    import random
    for _ in range(min(max_trials - 3, 7)):  # Limit to 7 additional random trials
        trial = {}
        for i, param_name in enumerate(param_names):
            values = param_values[i]
            if values:
                val = random.choice(values)
                # Ensure integer parameters are integers
                if param_name in ['n_steps', 'batch_size', 'n_epochs']:
                    val = int(val)
                trial[param_name] = val
        trials.append(trial)

    return trials

# Function to run training in the background
async def run_training_job(job_id: str, training_request: TrainingRequest):
    """Run a training job in the background"""
    # Get repositories
    training_repo = await RLTrainingRepository.create()
    env_repo = await RLEnvironmentRepository.create()

    # Update job status to running
    await training_repo.update_training_job(job_id, {"status": "running"})

    # Print the job data for debugging
    job = await training_repo.get_training_job(job_id)
    print(f"Job data: {job}")

    # Ensure hyperparameters don't contain optimization configuration
    # This is critical because the model constructor doesn't accept these parameters
    if isinstance(training_request.hyperparameters, dict):
        # Remove any optimization configuration that might have been included
        if 'smart_hyperparameters_optimization' in training_request.hyperparameters:
            del training_request.hyperparameters['smart_hyperparameters_optimization']
        if 'hyperparameters_optimization' in training_request.hyperparameters:
            del training_request.hyperparameters['hyperparameters_optimization']

    try:
        # Get environment
        environment = await env_repo.get_environment(training_request.environment_id)
        if not environment:
            raise ValueError(f"Environment {training_request.environment_id} not found")

        # Create a temporary file with the environment code
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.py', mode='w', delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(environment["code"])

        try:
            # Import the module
            spec = importlib.util.spec_from_file_location("temp_env_module", temp_file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # Patch for gym/gymnasium compatibility
            # Create an alias for gymnasium.Env as gym.Env if needed
            if hasattr(module, 'gymnasium') and not hasattr(module, 'gym'):
                module.gym = module.gymnasium

            # Find the environment class
            env_class = None
            for class_name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and
                    hasattr(obj, '__mro__') and
                    (any('gym.Env' in str(mro) or 'gymnasium.Env' in str(mro) for mro in obj.__mro__) or
                     any(mro.__name__ == 'Env' and (mro.__module__ == 'gym' or mro.__module__ == 'gymnasium' or 'gym' in mro.__module__ or 'gymnasium' in mro.__module__) for mro in obj.__mro__))):
                    env_class = obj
                    break

            if not env_class:
                raise ValueError("No gym.Env subclass found in the environment code")

            # Import stable_baselines3
            from stable_baselines3 import PPO, A2C, DQN, SAC, TD3
            from stable_baselines3.common.evaluation import evaluate_policy
            from stable_baselines3.common.callbacks import EvalCallback
            from stable_baselines3.common.monitor import Monitor

            # Create environment with training data path and training ratio
            print(f"Creating environment with training_data_path: {training_request.training_data_path}")

            # Check if this is a MongoDB URI
            if training_request.training_data_path.startswith('mongodb://'):
                print(f"MongoDB URI detected: {training_request.training_data_path}")

                # Parse the MongoDB URI
                # Format: mongodb://collection_name?vector_field=field1&label_field=field2
                mongodb_uri = training_request.training_data_path

                # Extract collection name and parameters
                collection_match = re.match(r'mongodb://([^?]+)(\?.*)?', mongodb_uri)
                if not collection_match:
                    raise ValueError(f"Invalid MongoDB URI format: {mongodb_uri}")

                collection_name = collection_match.group(1)
                query_params = collection_match.group(2) or ""

                # Parse query parameters
                params = {}
                if query_params:
                    for param in query_params[1:].split('&'):
                        if '=' in param:
                            key, value = param.split('=', 1)
                            params[key] = value

                # Get vector field and label field
                vector_field = params.get('vector_field', 'vector')
                label_field = params.get('label_field', 'label')

                print(f"Using MongoDB collection: {collection_name}")
                print(f"Vector field: {vector_field}")
                print(f"Label field: {label_field}")

                # Check if collection exists
                collections = await database.list_collection_names()
                if collection_name not in collections:
                    raise ValueError(f"MongoDB collection {collection_name} not found")

                # Get data from MongoDB collection
                cursor = database[collection_name].find({})
                documents = await cursor.to_list(length=None)

                if not documents:
                    raise ValueError(f"No documents found in MongoDB collection {collection_name}")

                print(f"Found {len(documents)} documents in MongoDB collection {collection_name}")

                # Convert documents to training data format
                training_data = []
                for doc in documents:
                    # Convert ObjectId to string
                    doc_id = str(doc.get('_id', ''))

                    # Get vector and label
                    vector = doc.get(vector_field)
                    label = doc.get(label_field)

                    if vector is None:
                        print(f"Warning: Document {doc_id} missing vector field '{vector_field}'")
                        continue

                    if label is None:
                        print(f"Warning: Document {doc_id} missing label field '{label_field}'")
                        continue

                    # Add to training data
                    training_data.append({
                        'vector': vector,
                        'label': label,
                        'id': doc_id
                    })

                print(f"Created {len(training_data)} training data items from MongoDB")

                # Save training data to a temporary file
                temp_file_path = os.path.join(RL_DATA_DIR, f"mongodb_data_{job_id}.json")
                with open(temp_file_path, 'w') as f:
                    json.dump(training_data, f)

                print(f"Saved MongoDB data to temporary file: {temp_file_path}")

                # Use the temporary file as training data path
                training_data_path = temp_file_path
            else:
                # Regular file path
                # Check if the training data file exists
                if not os.path.exists(training_request.training_data_path):
                    print(f"WARNING: Training data file not found at {training_request.training_data_path}")
                    print(f"Checking if file exists in alternative locations...")

                    # Try to find the file in the software/rl/training/data directory
                    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    alternative_path = os.path.join(base_dir, "software", "rl", "training", "data", os.path.basename(training_request.training_data_path))
                    print(f"Checking alternative path: {alternative_path}")

                    if os.path.exists(alternative_path):
                        print(f"Found training data file at alternative path: {alternative_path}")
                        # Use the alternative path
                        training_data_path = alternative_path
                    else:
                        print(f"Training data file not found at alternative path either.")
                        # Use the original path and let the environment handle the error
                        training_data_path = training_request.training_data_path
                else:
                    print(f"Training data file found at {training_request.training_data_path}")
                    training_data_path = training_request.training_data_path

            # Create the environment with the training data path
            env = env_class(
                training_data_path=training_data_path,
                train_ratio=training_request.train_ratio
            )

            # Create a monitored environment for evaluation
            eval_env = Monitor(env_class(
                training_data_path=training_data_path,  # Use the same path we determined above
                train_ratio=training_request.train_ratio,
                is_training=False
            ))

            # Store the original training data path in the job for reference
            await training_repo.update_training_job(job_id, {
                "original_training_data_path": training_request.training_data_path,
                "actual_training_data_path": training_data_path
            })

            # Get model class based on algorithm
            algorithm_map = {
                "PPO": PPO,
                "A2C": A2C,
                "DQN": DQN,
                "SAC": SAC,
                "TD3": TD3
            }

            if training_request.algorithm not in algorithm_map:
                raise ValueError(f"Unsupported algorithm: {training_request.algorithm}")

            model_class = algorithm_map[training_request.algorithm]

            # Get model path
            model_name = f"{training_request.algorithm}_{environment['name']}"
            model_path = training_repo.get_model_path(job_id, model_name)
            log_path = training_repo.get_log_path(job_id)

            # Create callback for saving best model
            eval_callback = EvalCallback(
                eval_env,
                best_model_save_path=os.path.dirname(model_path),
                log_path=os.path.dirname(log_path),
                eval_freq=500,
                deterministic=True,
                render=False,
                n_eval_episodes=5,
                verbose=0
            )

            # Check if we should finetune an existing model
            if training_request.finetune:
                # Get the model to finetune
                model_to_finetune_id = training_request.model_to_finetune_id

                if model_to_finetune_id:
                    # Get the specific model to finetune
                    logger.info(f"Looking for specific model to finetune: {model_to_finetune_id}")
                    model_job = await training_repo.get_training_job(model_to_finetune_id)

                    if model_job and model_job.get("status") == "completed":
                        model_path = model_job.get("model_path")

                        if model_path and os.path.exists(f"{model_path}.zip"):
                            logger.info(f"Finetuning specific model: {os.path.basename(model_path)}")
                            try:
                                # Load the existing model
                                model = model_class.load(model_path, env=env)
                                logger.info("Successfully loaded specific model for finetuning")

                                # Store the original mean reward for comparison if save_only_if_improved is enabled
                                if training_request.save_only_if_improved:
                                    original_mean_reward = model_job.get("mean_reward", 0)
                                    logger.info(f"Original model mean reward: {original_mean_reward}")
                                    # Store for later comparison
                                    training_request.original_mean_reward = original_mean_reward
                                    training_request.original_model_path = model_path
                            except Exception as e:
                                logger.warning(f"Error loading specific model for finetuning: {str(e)}")
                                logger.warning("Training a new model instead")
                                # Create a new model if loading fails
                                model = model_class(
                                    "MlpPolicy",
                                    env,
                                    **training_request.hyperparameters
                                )
                        else:
                            logger.warning(f"Model file not found for ID {model_to_finetune_id}. Training a new model instead.")
                            # Create a new model if the model file is not found
                            model = model_class(
                                "MlpPolicy",
                                env,
                                **training_request.hyperparameters
                            )
                    else:
                        logger.warning(f"Model job not found or not completed for ID {model_to_finetune_id}. Training a new model instead.")
                        # Create a new model if the model job is not found or not completed
                        model = model_class(
                            "MlpPolicy",
                            env,
                            **training_request.hyperparameters
                        )
                else:
                    # Find the latest model for this environment
                    latest_jobs = await training_repo.list_training_jobs(
                        environment_id=training_request.environment_id,
                        status="completed"
                    )

                    if latest_jobs:
                        latest_job = latest_jobs[0]
                        latest_model_path = latest_job.get("model_path")

                        if latest_model_path and os.path.exists(f"{latest_model_path}.zip"):
                            logger.info(f"Finetuning latest model: {os.path.basename(latest_model_path)}")
                            try:
                                # Load the existing model
                                model = model_class.load(latest_model_path, env=env)
                                logger.info("Successfully loaded latest model for finetuning")
                            except Exception as e:
                                logger.warning(f"Error loading latest model for finetuning: {str(e)}")
                                logger.warning("Training a new model instead")
                                # Create a new model if loading fails
                                model = model_class(
                                    "MlpPolicy",
                                    env,
                                    **training_request.hyperparameters
                                )
                        else:
                            logger.warning("No existing model found for finetuning. Training a new model instead.")
                            # Create a new model if no existing model is found
                            model = model_class(
                                "MlpPolicy",
                                env,
                                **training_request.hyperparameters
                            )
                    else:
                        logger.warning("No completed training jobs found for finetuning. Training a new model instead.")
                        # Create a new model if no completed jobs are found
                        model = model_class(
                            "MlpPolicy",
                            env,
                            **training_request.hyperparameters
                        )
            else:
                # Create a new model with hyperparameters
                model = model_class(
                    "MlpPolicy",
                    env,
                    **training_request.hyperparameters
                )

            # Check if this is a single optimized job with hyperparameter combinations
            if hasattr(training_request, 'single_optimized_job') and training_request.single_optimized_job and hasattr(training_request, 'hyperparameter_combinations'):
                logger.info(f"Running single optimized job with {len(training_request.hyperparameter_combinations)} hyperparameter combinations")
                print(f"Running single optimized job with {len(training_request.hyperparameter_combinations)} hyperparameter combinations")

                # Start timing the entire optimization process
                start_time = time.time()
                print(f"DEBUG - Starting optimization at time: {start_time}")

                # Track the best model and its performance
                best_model = None
                best_mean_reward = float('-inf')
                best_std_reward = 0
                best_hyperparams = None
                best_model_path = None

                # Check if we have optimization data for true Bayesian optimization
                has_optimization_data = False
                optimization_data = None

                # Define the simple optimization function
                async def run_simple_optimization():
                    nonlocal best_model, best_mean_reward, best_std_reward, best_hyperparams

                    # Train and evaluate each combination
                    for i, hyperparams in enumerate(training_request.hyperparameter_combinations):
                        print(f"Training combination {i+1}/{len(training_request.hyperparameter_combinations)}: {hyperparams}")

                        # Update job status to show which combination we're training
                        await training_repo.update_training_job(job_id, {
                            "status": "running",
                            "progress": int((i / len(training_request.hyperparameter_combinations)) * 100),
                            "current_trial": i + 1,
                            "total_trials": len(training_request.hyperparameter_combinations),
                            "current_hyperparams": hyperparams
                        })

                        # Ensure integer parameters are integers
                        clean_hyperparams = {}
                        for param, value in hyperparams.items():
                            if param in ['n_steps', 'batch_size', 'n_epochs']:
                                clean_hyperparams[param] = int(value)
                            else:
                                clean_hyperparams[param] = value

                        # Create a new model with these hyperparameters
                        trial_model = model_class(
                            "MlpPolicy",
                            env,
                            **clean_hyperparams
                        )

                        # Train the model
                        print(f"DEBUG - Starting training for combination {i+1}")

                        # Train model with callbacks
                        for step in range(0, training_request.timesteps, 1000):
                            # Train for 1000 steps at a time
                            steps_to_train = min(1000, training_request.timesteps - step)
                            if steps_to_train <= 0:
                                break

                            trial_model.learn(
                                total_timesteps=steps_to_train,
                                callback=eval_callback,
                                progress_bar=True
                            )

                            # Update progress
                            trial_progress = int(((step + steps_to_train) / training_request.timesteps) * 100)
                            total_progress = int(((i * 100) + trial_progress) / len(training_request.hyperparameter_combinations))
                            await training_repo.update_training_job(job_id, {
                                "progress": total_progress,
                                "trial_progress": trial_progress
                            })

                            # Check if job was cancelled
                            job = await training_repo.get_training_job(job_id)
                            if job.get("status") == "cancelled":
                                logger.info(f"Training job {job_id} was cancelled")
                                return

                        # Evaluate the model
                        trial_mean_reward, trial_std_reward = evaluate_policy(trial_model, eval_env, n_eval_episodes=10)

                        print(f"Combination {i+1} results: mean_reward={trial_mean_reward:.4f}, std_reward={trial_std_reward:.4f}")

                        # Save trial results
                        trial_results = {
                            f"trial_{i+1}_hyperparams": clean_hyperparams,
                            f"trial_{i+1}_mean_reward": float(trial_mean_reward),
                            f"trial_{i+1}_std_reward": float(trial_std_reward)
                        }
                        await training_repo.update_training_job(job_id, trial_results)

                        # Check if this is the best model so far
                        if i == 0 or trial_mean_reward > best_mean_reward:
                            print(f"New best model found! Mean reward: {trial_mean_reward:.4f}")
                            best_mean_reward = trial_mean_reward
                            best_std_reward = trial_std_reward
                            best_hyperparams = clean_hyperparams

                            # Save this model
                            trial_model_path = f"{model_path}_trial_{i+1}"
                            trial_model.save(trial_model_path)
                            best_model = trial_model

                # Look for optimization data in the first combination
                if training_request.hyperparameter_combinations and "_optimization_data" in training_request.hyperparameter_combinations[0]:
                    optimization_data = training_request.hyperparameter_combinations[0].pop("_optimization_data")
                    has_optimization_data = True
                    print(f"DEBUG - Found optimization data for true Bayesian optimization")
                    print(f"DEBUG - Optimization data keys: {optimization_data.keys()}")
                    print(f"DEBUG - Number of hyperparameter combinations: {len(training_request.hyperparameter_combinations)}")

                if has_optimization_data and optimization_data:
                    # Use true Bayesian optimization with scikit-optimize
                    try:
                        from skopt import Optimizer
                        from skopt.space import Real, Integer, Categorical
                        import numpy as np

                        print(f"DEBUG - Using scikit-optimize for true Bayesian optimization")

                        # Extract parameter information
                        param_names = optimization_data["param_names"]
                        param_types = optimization_data["param_types"]
                        # Use the hyperparameter_combinations as initial_trials since we no longer store them in optimization_data
                        initial_trials = training_request.hyperparameter_combinations

                        # Recreate parameter spaces
                        spaces = []
                        for param_name in param_names:
                            if param_name in param_types:
                                param_type = param_types[param_name]

                                # Get min and max values from initial trials
                                values = []
                                for trial in initial_trials:
                                    if param_name in trial:
                                        values.append(trial[param_name])

                                if values:
                                    min_val = min(values)
                                    max_val = max(values)

                                    if param_type == 'int':
                                        spaces.append(Integer(int(min_val), int(max_val), name=param_name))
                                    elif param_type == 'float':
                                        spaces.append(Real(float(min_val), float(max_val), name=param_name))
                                    else:  # categorical
                                        spaces.append(Categorical(values, name=param_name))

                        # Create optimizer
                        optimizer = Optimizer(spaces, base_estimator="GP", acq_func="EI", n_initial_points=3)

                        # Initialize with the initial trials
                        X_init = []
                        y_init = []

                        # Train and evaluate initial points
                        print(f"DEBUG - Training initial points for Bayesian optimization")
                        for i, hyperparams in enumerate(initial_trials):
                            print(f"Training initial combination {i+1}/{len(initial_trials)}: {hyperparams}")

                            # Update job status
                            await training_repo.update_training_job(job_id, {
                                "status": "running",
                                "progress": int((i / (len(initial_trials) + 7)) * 100),  # +7 for additional Bayesian trials
                                "current_trial": i + 1,
                                "total_trials": len(initial_trials) + 7,  # +7 for additional Bayesian trials
                                "current_hyperparams": hyperparams
                            })

                            # Create a new model with these hyperparameters
                            # Ensure integer parameters are integers
                            clean_hyperparams = {}
                            for param, value in hyperparams.items():
                                if param in param_types and param_types[param] == 'int':
                                    clean_hyperparams[param] = int(value)
                                else:
                                    clean_hyperparams[param] = value

                            trial_model = model_class(
                                "MlpPolicy",
                                env,
                                **clean_hyperparams
                            )

                            # Train the model
                            print(f"DEBUG - Starting training for initial combination {i+1}")

                            # Train model with callbacks
                            for step in range(0, training_request.timesteps, 1000):
                                # Train for 1000 steps at a time
                                steps_to_train = min(1000, training_request.timesteps - step)
                                if steps_to_train <= 0:
                                    break

                                trial_model.learn(
                                    total_timesteps=steps_to_train,
                                    callback=eval_callback,
                                    progress_bar=True
                                )

                                # Update progress
                                trial_progress = int(((step + steps_to_train) / training_request.timesteps) * 100)
                                total_progress = int(((i * 100) + trial_progress) / (len(initial_trials) + 7))
                                await training_repo.update_training_job(job_id, {
                                    "progress": total_progress,
                                    "trial_progress": trial_progress
                                })

                                # Check if job was cancelled
                                job = await training_repo.get_training_job(job_id)
                                if job.get("status") == "cancelled":
                                    logger.info(f"Training job {job_id} was cancelled")
                                    return

                            # Evaluate the model
                            trial_mean_reward, trial_std_reward = evaluate_policy(trial_model, eval_env, n_eval_episodes=10)

                            print(f"Initial combination {i+1} results: mean_reward={trial_mean_reward:.4f}, std_reward={trial_std_reward:.4f}")

                            # Save trial results
                            trial_results = {
                                f"trial_{i+1}_hyperparams": clean_hyperparams,
                                f"trial_{i+1}_mean_reward": float(trial_mean_reward),
                                f"trial_{i+1}_std_reward": float(trial_std_reward)
                            }
                            await training_repo.update_training_job(job_id, trial_results)

                            # Add to optimizer
                            x = []
                            for param_name in param_names:
                                if param_name in clean_hyperparams:
                                    x.append(clean_hyperparams[param_name])
                                else:
                                    # Use a default value if parameter is missing
                                    x.append(0)

                            X_init.append(x)
                            # We want to maximize reward, but optimizer minimizes, so negate
                            y_init.append(-float(trial_mean_reward))

                            # Track best model
                            if i == 0 or trial_mean_reward > best_mean_reward:
                                print(f"New best model found! Mean reward: {trial_mean_reward:.4f}")
                                best_mean_reward = trial_mean_reward
                                best_std_reward = trial_std_reward
                                best_hyperparams = clean_hyperparams

                                # Save this model
                                trial_model_path = f"{model_path}_trial_{i+1}"
                                trial_model.save(trial_model_path)
                                best_model = trial_model

                        # Tell the optimizer about the initial points
                        if X_init and y_init:
                            optimizer.tell(X_init, y_init)

                            # Now run Bayesian optimization for additional trials
                            num_bayesian_trials = 7  # Number of additional trials using Bayesian optimization
                            print(f"DEBUG - Running {num_bayesian_trials} Bayesian optimization trials")

                            for i in range(num_bayesian_trials):
                                # Ask the optimizer for the next point to try
                                next_x = optimizer.ask()

                                # Convert to hyperparameters
                                next_hyperparams = {}
                                for j, param_name in enumerate(param_names):
                                    if j < len(next_x):
                                        value = next_x[j]
                                        # Ensure integer parameters are integers
                                        if param_name in ['n_steps', 'batch_size', 'n_epochs'] or (param_name in param_types and param_types[param_name] == 'int'):
                                            value = int(value)
                                        next_hyperparams[param_name] = value

                                print(f"Training Bayesian combination {i+1}/{num_bayesian_trials}: {next_hyperparams}")

                                # Update job status
                                await training_repo.update_training_job(job_id, {
                                    "status": "running",
                                    "progress": int(((len(initial_trials) + i) / (len(initial_trials) + num_bayesian_trials)) * 100),
                                    "current_trial": len(initial_trials) + i + 1,
                                    "total_trials": len(initial_trials) + num_bayesian_trials,
                                    "current_hyperparams": next_hyperparams
                                })

                                # Create a new model with these hyperparameters
                                trial_model = model_class(
                                    "MlpPolicy",
                                    env,
                                    **next_hyperparams
                                )

                                # Train the model
                                print(f"DEBUG - Starting training for Bayesian combination {i+1}")

                                # Train model with callbacks
                                for step in range(0, training_request.timesteps, 1000):
                                    # Train for 1000 steps at a time
                                    steps_to_train = min(1000, training_request.timesteps - step)
                                    if steps_to_train <= 0:
                                        break

                                    trial_model.learn(
                                        total_timesteps=steps_to_train,
                                        callback=eval_callback,
                                        progress_bar=True
                                    )

                                    # Update progress
                                    trial_progress = int(((step + steps_to_train) / training_request.timesteps) * 100)
                                    total_progress = int(((len(initial_trials) + i) * 100 + trial_progress) / (len(initial_trials) + num_bayesian_trials))
                                    await training_repo.update_training_job(job_id, {
                                        "progress": total_progress,
                                        "trial_progress": trial_progress
                                    })

                                    # Check if job was cancelled
                                    job = await training_repo.get_training_job(job_id)
                                    if job.get("status") == "cancelled":
                                        logger.info(f"Training job {job_id} was cancelled")
                                        return

                                # Evaluate the model
                                trial_mean_reward, trial_std_reward = evaluate_policy(trial_model, eval_env, n_eval_episodes=10)

                                print(f"Bayesian combination {i+1} results: mean_reward={trial_mean_reward:.4f}, std_reward={trial_std_reward:.4f}")

                                # Save trial results
                                trial_results = {
                                    f"trial_{len(initial_trials) + i + 1}_hyperparams": next_hyperparams,
                                    f"trial_{len(initial_trials) + i + 1}_mean_reward": float(trial_mean_reward),
                                    f"trial_{len(initial_trials) + i + 1}_std_reward": float(trial_std_reward)
                                }
                                await training_repo.update_training_job(job_id, trial_results)

                                # Tell the optimizer the result
                                optimizer.tell(next_x, -float(trial_mean_reward))

                                # Track best model
                                if trial_mean_reward > best_mean_reward:
                                    print(f"New best model found! Mean reward: {trial_mean_reward:.4f}")
                                    best_mean_reward = trial_mean_reward
                                    best_std_reward = trial_std_reward
                                    best_hyperparams = next_hyperparams

                                    # Save this model
                                    trial_model_path = f"{model_path}_trial_{len(initial_trials) + i + 1}"
                                    trial_model.save(trial_model_path)
                                    best_model = trial_model

                            # Save the optimizer results
                            optimizer_results = {
                                "best_x": optimizer.Xi[np.argmin(optimizer.yi)],
                                "best_y": -min(optimizer.yi),
                                "all_x": optimizer.Xi,
                                "all_y": [-y for y in optimizer.yi]
                            }

                            await training_repo.update_training_job(job_id, {
                                "optimizer_results": optimizer_results
                            })

                    except ImportError as e:
                        print(f"ERROR - Failed to import scikit-optimize: {str(e)}")
                        print(f"Falling back to simple optimization")
                        # Fall back to simple optimization
                        await run_simple_optimization()
                    except Exception as e:
                        print(f"ERROR - Bayesian optimization failed: {str(e)}")
                        print(f"Falling back to simple optimization")
                        # Fall back to simple optimization
                        await run_simple_optimization()
                else:
                    # Use simple optimization
                    await run_simple_optimization()

                # Use the best model for the final result
                if best_model is not None:
                    print(f"Best model found with hyperparameters: {best_hyperparams}")
                    print(f"Best mean reward: {best_mean_reward:.4f}")

                    # Calculate total training time
                    end_time = time.time()
                    training_time = end_time - start_time
                    print(f"DEBUG - Optimization completed in {training_time:.2f} seconds")

                    # Save the best model as the final model
                    final_model_path = f"{model_path}_final"
                    print(f"DEBUG - Saving best model to {final_model_path}")
                    best_model.save(final_model_path)

                    # Set the final results
                    model = best_model
                    mean_reward = best_mean_reward
                    std_reward = best_std_reward
                else:
                    # This should never happen, but just in case
                    logger.error("No best model found after optimization")
                    raise ValueError("No best model found after optimization")
            else:
                # Regular training (no optimization)
                logger.info(f"{'Finetuning' if training_request.finetune else 'Training'} {training_request.algorithm} model for {training_request.timesteps} steps...")
                start_time = time.time()

                # Custom callback to update progress
                class ProgressCallback:
                    def __init__(self, job_id, total_timesteps):
                        self.job_id = job_id
                        self.total_timesteps = total_timesteps
                        self.last_update_time = time.time()
                        self.update_interval = 2.0  # Update every 2 seconds

                    async def __call__(self, locals_dict, globals_dict):
                        # Get current progress
                        current_step = locals_dict.get('step', 0)
                        progress = int((current_step / self.total_timesteps) * 100)

                        # Update progress in database every few seconds
                        current_time = time.time()
                        if current_time - self.last_update_time > self.update_interval:
                            await training_repo.update_training_job(self.job_id, {"progress": progress})
                            self.last_update_time = current_time

                        return True

                # Create progress callback
                progress_callback = ProgressCallback(job_id, training_request.timesteps)

                # Train model with callbacks
                for step in range(0, training_request.timesteps, 1000):
                    # Train for 1000 steps at a time
                    steps_to_train = min(1000, training_request.timesteps - step)
                    if steps_to_train <= 0:
                        break

                    model.learn(
                        total_timesteps=steps_to_train,
                        callback=eval_callback,
                        progress_bar=True
                    )

                    # Update progress
                    progress = int(((step + steps_to_train) / training_request.timesteps) * 100)
                    await training_repo.update_training_job(job_id, {"progress": progress})

                    # Check if job was cancelled
                    job = await training_repo.get_training_job(job_id)
                    if job.get("status") == "cancelled":
                        logger.info(f"Training job {job_id} was cancelled")
                        return

                training_time = time.time() - start_time

                # Save the final model
                final_model_path = f"{model_path}_final"
                model.save(final_model_path)

                # Evaluate the final model
                mean_reward, std_reward = evaluate_policy(model, eval_env, n_eval_episodes=10)

            # Debug print for evaluation results
            print(f"Evaluation results:")
            print(f"  - Mean reward: {mean_reward:.4f}")
            print(f"  - Standard deviation: {std_reward:.4f}")

            # Check if we should only save if the model improved
            save_only_if_improved = training_request.save_only_if_improved
            original_mean_reward = training_request.original_mean_reward
            original_model_path = training_request.original_model_path

            # Debug prints for comparison values
            print(f"COMPARISON DEBUG:")
            print(f"  - save_only_if_improved: {save_only_if_improved}")
            print(f"  - original_mean_reward: {original_mean_reward}")
            print(f"  - original_model_path: {original_model_path}")
            print(f"  - new_mean_reward: {mean_reward}")

            # If we don't have the original model info but we should be comparing,
            # try to get it from the model_to_finetune_id
            if save_only_if_improved and (original_mean_reward == 0 or original_model_path is None):
                model_to_finetune_id = training_request.model_to_finetune_id
                if model_to_finetune_id:
                    print(f"Getting original model info from model_to_finetune_id: {model_to_finetune_id}")
                    model_job = await training_repo.get_training_job(model_to_finetune_id)
                    if model_job:
                        original_mean_reward = model_job.get("mean_reward", 0)
                        original_model_path = model_job.get("model_path")
                        print(f"  - Updated original_mean_reward: {original_mean_reward}")
                        print(f"  - Updated original_model_path: {original_model_path}")
                        # Store for later use
                        training_request.original_mean_reward = original_mean_reward
                        training_request.original_model_path = original_model_path

            # Determine if we should save the model
            should_save = True

            if save_only_if_improved and original_mean_reward != 0:
                # For negative rewards (like -4.24), a higher value (less negative, like -4.20) is better
                # For positive rewards, a higher value is better
                # So we always want to check if the new reward is greater than the original
                print(f"REWARD COMPARISON:")
                print(f"  - New mean reward: {mean_reward:.4f}")
                print(f"  - Original mean reward: {original_mean_reward:.4f}")
                print(f"  - Difference: {mean_reward - original_mean_reward:.4f}")

                # Convert to float to avoid any type issues
                mean_reward_float = float(mean_reward)
                original_mean_reward_float = float(original_mean_reward)

                # Debug print for comparison
                is_better = mean_reward_float > original_mean_reward_float
                print(f"Is new reward better? {is_better} (new > original)")

                # Explicitly compare as floats
                if not is_better:
                    logger.info(f"New model did not improve mean reward ({mean_reward:.4f} <= {original_mean_reward:.4f}). Using original model.")
                    print(f"New model did not improve mean reward ({mean_reward:.4f} <= {original_mean_reward:.4f}). Using original model.")
                    should_save = False
                    # Only use original model path if it exists and the file exists
                    if original_model_path:
                        # Check if the model file exists
                        model_file_path = f"{original_model_path}.zip"
                        if os.path.exists(model_file_path):
                            final_model_path = original_model_path
                            print(f"Using original model path: {original_model_path}")
                            # Will set model_kept = "original" later
                        else:
                            print(f"Original model file not found at {model_file_path}, keeping new model but marking as not improved")
                            # We'll keep the new model but mark it as not improved
                            # Will set model_kept = "new (original not found)" later
                    else:
                        print(f"Original model path not found, keeping new model but marking as not improved")
                        # We'll keep the new model but mark it as not improved
                        # Will set model_kept = "new (original not found)" later
                else:
                    logger.info(f"New model improved mean reward ({mean_reward:.4f} > {original_mean_reward:.4f}). Saving new model.")
                    print(f"New model improved mean reward ({mean_reward:.4f} > {original_mean_reward:.4f}). Saving new model.")

            # Update job with results
            update_data = {
                "status": "completed",
                "progress": 100,
                "model_path": final_model_path,
                "log_path": log_path,
                "training_time": training_time,
                "mean_reward": float(mean_reward),
                "std_reward": float(std_reward) if std_reward is not None else 0.0,  # Ensure std_reward is never None
                "completed_at": datetime.now(pytz.UTC)
            }

            # Debug print for std_reward
            print(f"Standard deviation: {std_reward}")
            print(f"Stored std_reward value: {update_data['std_reward']}")

            # Add information about improvement if applicable
            if save_only_if_improved:
                update_data["improved"] = should_save
                update_data["original_mean_reward"] = original_mean_reward

                if not should_save:
                    # Check if the original model file exists
                    if original_model_path and os.path.exists(f"{original_model_path}.zip"):
                        update_data["model_kept"] = "original"
                        update_data["model_path"] = original_model_path
                    else:
                        # If the original model doesn't exist, we keep the new model but mark it as not improved
                        update_data["model_kept"] = "new (original not found)"
                        # Keep the new model path
                else:
                    update_data["model_kept"] = "new"

            await training_repo.update_training_job(job_id, update_data)

            # If this is a recurring job, add this attempt to the history
            job = await training_repo.get_training_job(job_id)
            if job and job.get("recurring_finetune"):
                # Create a record of this finetuning attempt
                attempt_data = {
                    "environment_id": job.get("environment_id"),
                    "environment_name": job.get("environment_name"),
                    "algorithm": job.get("algorithm"),
                    "timesteps": training_request.timesteps,
                    "training_data_path": training_request.training_data_path,
                    "finetune": True,
                    "training_time": training_time,
                    "mean_reward": float(mean_reward),
                    "std_reward": float(std_reward) if std_reward is not None else 0.0,  # Ensure std_reward is never None
                    "improved": should_save if save_only_if_improved else None,
                    "original_mean_reward": original_mean_reward if save_only_if_improved else None,
                    "model_kept": update_data.get("model_kept") if save_only_if_improved else "new",
                    "executed_at": datetime.now(pytz.UTC)
                }

                try:
                    # Add this attempt to the history
                    print(f"Adding finetuning attempt for job {job_id}")
                    await training_repo.add_finetuning_attempt(job_id, attempt_data)
                except Exception as e:
                    logger.error(f"Error adding finetuning attempt: {str(e)}")
                    print(f"Error adding finetuning attempt: {str(e)}")

                # Reset status to scheduled for next run if it has a schedule
                if job.get("schedule"):
                    logger.info(f"Resetting scheduled job {job_id} for next run")
                    await training_repo.update_training_job(job_id, {"status": "scheduled"})

            logger.info(f"Training completed for job {job_id}")
            logger.info(f"Mean reward: {mean_reward:.2f} ± {std_reward:.2f}")
            logger.info(f"Training time: {training_time:.2f} seconds")
            logger.info(f"Model saved to: {final_model_path}")

        except Exception as e:
            logger.error(f"Error during training: {str(e)}")
            logger.error(traceback.format_exc())

            # Update job with error
            await training_repo.update_training_job(job_id, {
                "status": "failed",
                "error": str(e)
            })
        finally:
            # Clean up the temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {str(e)}")
    except Exception as e:
        logger.error(f"Error setting up training: {str(e)}")
        logger.error(traceback.format_exc())

        # Update job with error
        await training_repo.update_training_job(job_id, {
            "status": "failed",
            "error": str(e)
        })
    finally:
        # Remove job from active tasks
        if job_id in active_training_tasks:
            del active_training_tasks[job_id]

@router.get("/")
async def list_training_jobs(environment_id: Optional[str] = None, status: Optional[str] = None):
    """List all training jobs, optionally filtered by environment_id and/or status"""
    try:
        repo = await RLTrainingRepository.create()
        jobs = await repo.list_training_jobs(environment_id, status)
        return jobs
    except Exception as e:
        logger.error(f"Error listing training jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list training jobs: {str(e)}")

@router.post("/")
async def create_training_job(training_request: TrainingRequest, background_tasks: BackgroundTasks):
    """Create a new training job"""
    try:
        # Create repositories
        training_repo = await RLTrainingRepository.create()
        env_repo = await RLEnvironmentRepository.create()

        # Debug print the training request
        print(f"DEBUG - Training request received:")
        print(f"DEBUG - environment_id: {training_request.environment_id}")
        print(f"DEBUG - algorithm: {training_request.algorithm}")
        print(f"DEBUG - timesteps: {training_request.timesteps}")
        print(f"DEBUG - hyperparameters: {training_request.hyperparameters}")
        print(f"DEBUG - hyperparameters_optimization: {training_request.hyperparameters_optimization}")
        print(f"DEBUG - smart_hyperparameters_optimization: {training_request.smart_hyperparameters_optimization}")

        # Check if smart_hyperparameters_optimization is nested inside hyperparameters
        # This is a common issue with the frontend sending the wrong structure
        if (not training_request.smart_hyperparameters_optimization and
            isinstance(training_request.hyperparameters, dict) and
            'smart_hyperparameters_optimization' in training_request.hyperparameters):

            print(f"DEBUG - Found smart_hyperparameters_optimization nested in hyperparameters, fixing...")

            # Extract it and set it as a top-level field
            nested_config = training_request.hyperparameters.pop('smart_hyperparameters_optimization')

            # Create a proper SmartHyperparametersOptimizationConfig
            if isinstance(nested_config, dict) and nested_config.get('enabled'):
                training_request.smart_hyperparameters_optimization = SmartHyperparametersOptimizationConfig(
                    enabled=nested_config.get('enabled', True),
                    parameters=nested_config.get('parameters', {}),
                    max_trials=nested_config.get('max_trials', 10),
                    strategy=nested_config.get('strategy', 'bayesian'),
                    depth=nested_config.get('depth', 'deep')
                )
                print(f"DEBUG - Fixed smart_hyperparameters_optimization: {training_request.smart_hyperparameters_optimization}")

        # If smart_hyperparameters_optimization is still not set, create a default one
        # This ensures we always use smart Bayesian optimization by default
        if not training_request.smart_hyperparameters_optimization:
            print(f"DEBUG - Creating default smart Bayesian optimization configuration")
            training_request.smart_hyperparameters_optimization = SmartHyperparametersOptimizationConfig(
                enabled=True,
                parameters={
                    "learning_rate": [1e-05, 0.0001, 0.0003, 0.001, 0.003],
                    "n_steps": [64, 128, 512, 1024, 2048],
                    "batch_size": [32, 64, 128, 256],
                    "n_epochs": [5, 10, 15, 20],
                    "gamma": [0.95, 0.97, 0.99],
                    "gae_lambda": [0.9, 0.95, 0.98],
                    "clip_range": [0.1, 0.2, 0.3],
                    "ent_coef": [0, 0.01, 0.05],
                    "vf_coef": [0.5, 0.7, 1.0],
                    "max_grad_norm": [0.5, 1.0, 2.0]
                },
                max_trials=1,  # Always use 1 trial for the optimized job
                strategy="bayesian",
                depth="deep"  # Default to 'deep' depth
            )
            print(f"DEBUG - Created default smart_hyperparameters_optimization: {training_request.smart_hyperparameters_optimization}")

        # Check if environment exists
        environment = await env_repo.get_environment(training_request.environment_id)
        if not environment:
            raise HTTPException(status_code=404, detail=f"Environment {training_request.environment_id} not found")

        # Check if smart hyperparameters optimization is enabled
        if training_request.smart_hyperparameters_optimization and training_request.smart_hyperparameters_optimization.enabled:
            print(f"DEBUG - Smart hyperparameter optimization is enabled")
            print(f"DEBUG - Strategy: {training_request.smart_hyperparameters_optimization.strategy}")
            print(f"DEBUG - Max trials: {training_request.smart_hyperparameters_optimization.max_trials}")
            print(f"DEBUG - Parameters: {training_request.smart_hyperparameters_optimization.parameters}")
            print(f"DEBUG - Single optimized job: {getattr(training_request.smart_hyperparameters_optimization, 'single_optimized_job', False)}")
            print(f"DEBUG - Optimization depth: {getattr(training_request.smart_hyperparameters_optimization, 'depth', 'deep')}")

            # Generate a limited number of hyperparameter trials using smart optimization
            combinations = generate_smart_hyperparameter_trials(training_request.smart_hyperparameters_optimization)

            if not combinations:
                print(f"DEBUG - No valid hyperparameter combinations generated")
                raise HTTPException(status_code=400, detail="No valid hyperparameter combinations generated")

            print(f"DEBUG - Generated {len(combinations)} smart hyperparameter trials using {training_request.smart_hyperparameters_optimization.strategy} strategy")
            for i, combo in enumerate(combinations):
                print(f"DEBUG - Combination {i+1}: {combo}")

            # Check if we should create a single optimized job
            single_optimized_job = getattr(training_request.smart_hyperparameters_optimization, 'single_optimized_job', False)

            if single_optimized_job:
                print(f"DEBUG - Creating a single optimized job that will internally evaluate all hyperparameter combinations")

                # Create a single job with the default hyperparameters
                # The actual optimization will happen internally during training
                print(f"DEBUG - Creating a single optimized job with default hyperparameters")

                job_data = {
                    "environment_id": training_request.environment_id,
                    "environment_name": environment["name"],
                    "algorithm": training_request.algorithm,
                    "timesteps": training_request.timesteps,
                    "training_data_path": training_request.training_data_path,
                    "train_ratio": training_request.train_ratio,
                    "finetune": training_request.finetune,
                    "hyperparameters": training_request.hyperparameters,
                    "description": f"{training_request.description or 'Smart Bayesian Hyperparameter Optimization'}",
                    "is_smart_optimization": True,
                    "optimization_strategy": training_request.smart_hyperparameters_optimization.strategy,
                    "optimization_depth": getattr(training_request.smart_hyperparameters_optimization, 'depth', 'deep'),
                    "single_optimized_job": True,
                    "optimization_trials": len(combinations),
                    # Store a sanitized copy of combinations in the job data
                    "hyperparameter_combinations": [
                        {k: v for k, v in combo.items() if k != "_optimization_data"}
                        for combo in combinations
                    ]
                }

                # Add model to finetune if specified
                if training_request.finetune and training_request.model_to_finetune_id:
                    job_data["model_to_finetune_id"] = training_request.model_to_finetune_id

                # Add recurring finetuning configuration if enabled
                if training_request.recurring_finetune:
                    job_data["recurring_finetune"] = True
                    job_data["save_only_if_improved"] = training_request.save_only_if_improved

                    if training_request.schedule:
                        job_data["schedule"] = {
                            "type": training_request.schedule.type,
                            "time": training_request.schedule.time
                        }

                        # Set status to "scheduled" for recurring jobs
                        job_data["status"] = "scheduled"

                        # Ensure the time format is correct (HH:MM)
                        time_parts = training_request.schedule.time.split(':')
                        if len(time_parts) == 2:
                            # Format time as HH:MM to ensure consistency
                            formatted_time = f"{int(time_parts[0]):02d}:{int(time_parts[1]):02d}"
                            job_data["schedule"]["time"] = formatted_time

                # Create the job
                job_id = await training_repo.create_training_job(job_data)

                print(f"Created single optimized job with ID: {job_id}")

                # Start training in background if not a scheduled job
                if not (job_data.get("status") == "scheduled" and job_data.get("recurring_finetune")):
                    print(f"Starting training immediately for job: {job_id}")

                    # Create a modified training request that includes the optimization combinations
                    optimized_request = TrainingRequest(
                        environment_id=training_request.environment_id,
                        algorithm=training_request.algorithm,
                        timesteps=training_request.timesteps,
                        training_data_path=training_request.training_data_path,
                        train_ratio=training_request.train_ratio,
                        finetune=training_request.finetune,
                        model_to_finetune_id=training_request.model_to_finetune_id,
                        recurring_finetune=training_request.recurring_finetune,
                        schedule=training_request.schedule,
                        save_only_if_improved=training_request.save_only_if_improved,
                        hyperparameters=training_request.hyperparameters,
                        description=job_data["description"]
                    )

                    # Add the optimization combinations to the request
                    # Make a deep copy to avoid any potential circular references
                    import copy
                    optimized_request.hyperparameter_combinations = copy.deepcopy(combinations)
                    # Remove any _optimization_data from the combinations to avoid circular references
                    for combo in optimized_request.hyperparameter_combinations:
                        if "_optimization_data" in combo:
                            del combo["_optimization_data"]
                    optimized_request.single_optimized_job = True
                    print(f"DEBUG - Created optimized_request with {len(optimized_request.hyperparameter_combinations)} combinations")

                    background_tasks.add_task(run_training_job, job_id, optimized_request)

                    # Add to active tasks
                    active_training_tasks[job_id] = {
                        "environment_id": training_request.environment_id,
                        "environment_name": environment["name"],
                        "algorithm": training_request.algorithm,
                        "start_time": datetime.now(pytz.UTC)
                    }

                # Return the job ID
                depth = getattr(training_request.smart_hyperparameters_optimization, 'depth', 'deep')
                depth_description = {
                    'deep': '5 values per parameter',
                    'deeper': '8 values per parameter',
                    'extra': '12 values per parameter'
                }.get(depth, '5 values per parameter')

                return {
                    "id": job_id,
                    "message": f"Created smart Bayesian optimization job with {depth} depth ({depth_description}) that will internally evaluate {len(combinations)} hyperparameter combinations and produce a single optimized model"
                }
            else:
                # Create a job for each combination (legacy mode)
                job_ids = []
                optimization_group_id = str(ObjectId())  # Generate a unique ID for this optimization group

                for i, hyperparams in enumerate(combinations):
                    # Create a copy of the training request
                    job_request = TrainingRequest(
                        environment_id=training_request.environment_id,
                        algorithm=training_request.algorithm,
                        timesteps=training_request.timesteps,
                        training_data_path=training_request.training_data_path,
                        train_ratio=training_request.train_ratio,
                        finetune=training_request.finetune,
                        model_to_finetune_id=training_request.model_to_finetune_id,
                        recurring_finetune=training_request.recurring_finetune,
                        schedule=training_request.schedule,
                        save_only_if_improved=training_request.save_only_if_improved,
                        hyperparameters=hyperparams,
                        description=f"{training_request.description or 'Smart Hyperparameter Optimization'} - Trial {i+1}/{len(combinations)}"
                    )

                    # Create job data
                    job_data = {
                        "environment_id": job_request.environment_id,
                        "environment_name": environment["name"],
                        "algorithm": job_request.algorithm,
                        "timesteps": job_request.timesteps,
                        "training_data_path": job_request.training_data_path,
                        "train_ratio": job_request.train_ratio,
                        "finetune": job_request.finetune,
                        "hyperparameters": job_request.hyperparameters,
                        "description": job_request.description,
                        "optimization_group": True,  # Mark as part of an optimization group
                        "optimization_group_id": optimization_group_id,  # Group ID for tracking related jobs
                        "optimization_strategy": training_request.smart_hyperparameters_optimization.strategy,
                        "optimization_depth": getattr(training_request.smart_hyperparameters_optimization, 'depth', 'deep'),
                        "optimization_index": i,  # Index in the optimization group
                        "optimization_total": len(combinations),  # Total number of combinations
                        "is_smart_optimization": True  # Flag to indicate this is a smart optimization job
                    }

                    # Add model to finetune if specified
                    if job_request.finetune and job_request.model_to_finetune_id:
                        job_data["model_to_finetune_id"] = job_request.model_to_finetune_id

                    # Add recurring finetuning configuration if enabled
                    if job_request.recurring_finetune:
                        job_data["recurring_finetune"] = True
                        job_data["save_only_if_improved"] = job_request.save_only_if_improved

                        if job_request.schedule:
                            job_data["schedule"] = {
                                "type": job_request.schedule.type,
                                "time": job_request.schedule.time
                            }

                            # Set status to "scheduled" for recurring jobs
                            job_data["status"] = "scheduled"

                            # Ensure the time format is correct (HH:MM)
                            time_parts = job_request.schedule.time.split(':')
                            if len(time_parts) == 2:
                                # Format time as HH:MM to ensure consistency
                                formatted_time = f"{int(time_parts[0]):02d}:{int(time_parts[1]):02d}"
                                job_data["schedule"]["time"] = formatted_time

                    # Create the job
                    job_id = await training_repo.create_training_job(job_data)
                    job_ids.append(job_id)

                    print(f"Created smart optimization trial {i+1}/{len(combinations)} with ID: {job_id}")

                    # Start training in background if not a scheduled job
                    if not (job_data.get("status") == "scheduled" and job_data.get("recurring_finetune")):
                        print(f"Starting training immediately for job: {job_id}")
                        background_tasks.add_task(run_training_job, job_id, job_request)

                        # Add to active tasks
                        active_training_tasks[job_id] = {
                            "environment_id": job_request.environment_id,
                            "environment_name": environment["name"],
                            "algorithm": job_request.algorithm,
                            "start_time": datetime.now(pytz.UTC)
                        }

                # Return all job IDs
                depth = getattr(training_request.smart_hyperparameters_optimization, 'depth', 'deep')
                depth_description = {
                    'deep': '5 values per parameter',
                    'deeper': '8 values per parameter',
                    'extra': '12 values per parameter'
                }.get(depth, '5 values per parameter')

                return {
                    "ids": job_ids,
                    "optimization_group_id": optimization_group_id,
                    "message": f"Created {len(job_ids)} training jobs for smart hyperparameter optimization using {training_request.smart_hyperparameters_optimization.strategy} strategy with {depth} depth ({depth_description})"
                }

        # Check if regular hyperparameters optimization is enabled
        elif training_request.hyperparameters_optimization and training_request.hyperparameters_optimization.enabled:
            # Generate all combinations of hyperparameters
            combinations = generate_hyperparameter_combinations(training_request.hyperparameters_optimization)

            if not combinations:
                raise HTTPException(status_code=400, detail="No valid hyperparameter combinations generated")

            print(f"Generated {len(combinations)} hyperparameter combinations")

            # Create a job for each combination
            job_ids = []
            optimization_group_id = str(ObjectId())  # Generate a unique ID for this optimization group

            for i, hyperparams in enumerate(combinations):
                # Create a copy of the training request
                job_request = TrainingRequest(
                    environment_id=training_request.environment_id,
                    algorithm=training_request.algorithm,
                    timesteps=training_request.timesteps,
                    training_data_path=training_request.training_data_path,
                    train_ratio=training_request.train_ratio,
                    finetune=training_request.finetune,
                    model_to_finetune_id=training_request.model_to_finetune_id,
                    recurring_finetune=training_request.recurring_finetune,
                    schedule=training_request.schedule,
                    save_only_if_improved=training_request.save_only_if_improved,
                    hyperparameters=hyperparams,
                    description=f"{training_request.description or 'Hyperparameter optimization'} - Combination {i+1}/{len(combinations)}"
                )

                # Create job data
                job_data = {
                    "environment_id": job_request.environment_id,
                    "environment_name": environment["name"],
                    "algorithm": job_request.algorithm,
                    "timesteps": job_request.timesteps,
                    "training_data_path": job_request.training_data_path,
                    "train_ratio": job_request.train_ratio,
                    "finetune": job_request.finetune,
                    "hyperparameters": job_request.hyperparameters,
                    "description": job_request.description,
                    "optimization_group": True,  # Mark as part of an optimization group
                    "optimization_group_id": optimization_group_id,  # Group ID for tracking related jobs
                    "optimization_index": i,  # Index in the optimization group
                    "optimization_total": len(combinations)  # Total number of combinations
                }

                # Add model to finetune if specified
                if job_request.finetune and job_request.model_to_finetune_id:
                    job_data["model_to_finetune_id"] = job_request.model_to_finetune_id

                # Add recurring finetuning configuration if enabled
                if job_request.recurring_finetune:
                    job_data["recurring_finetune"] = True
                    job_data["save_only_if_improved"] = job_request.save_only_if_improved

                    if job_request.schedule:
                        job_data["schedule"] = {
                            "type": job_request.schedule.type,
                            "time": job_request.schedule.time
                        }

                        # Set status to "scheduled" for recurring jobs
                        job_data["status"] = "scheduled"

                        # Ensure the time format is correct (HH:MM)
                        time_parts = job_request.schedule.time.split(':')
                        if len(time_parts) == 2:
                            # Format time as HH:MM to ensure consistency
                            formatted_time = f"{int(time_parts[0]):02d}:{int(time_parts[1]):02d}"
                            job_data["schedule"]["time"] = formatted_time

                # Create the job
                job_id = await training_repo.create_training_job(job_data)
                job_ids.append(job_id)

                print(f"Created optimization job {i+1}/{len(combinations)} with ID: {job_id}")

                # Start training in background if not a scheduled job
                if not (job_data.get("status") == "scheduled" and job_data.get("recurring_finetune")):
                    print(f"Starting training immediately for job: {job_id}")
                    background_tasks.add_task(run_training_job, job_id, job_request)

                    # Add to active tasks
                    active_training_tasks[job_id] = {
                        "environment_id": job_request.environment_id,
                        "environment_name": environment["name"],
                        "algorithm": job_request.algorithm,
                        "start_time": datetime.now(pytz.UTC)
                    }

            # Return all job IDs
            return {
                "ids": job_ids,
                "optimization_group_id": optimization_group_id,
                "message": f"Created {len(job_ids)} training jobs for hyperparameter optimization"
            }
        else:
            # Regular job creation (no optimization)
            # Create training job
            job_data = {
                "environment_id": training_request.environment_id,
                "environment_name": environment["name"],
                "algorithm": training_request.algorithm,
                "timesteps": training_request.timesteps,
                "training_data_path": training_request.training_data_path,
                "train_ratio": training_request.train_ratio,
                "finetune": training_request.finetune,
                "hyperparameters": training_request.hyperparameters,
                "description": training_request.description
            }

            # Add model to finetune if specified
            if training_request.finetune and training_request.model_to_finetune_id:
                job_data["model_to_finetune_id"] = training_request.model_to_finetune_id

            # Add recurring finetuning configuration if enabled
            if training_request.recurring_finetune:
                job_data["recurring_finetune"] = True
                job_data["save_only_if_improved"] = training_request.save_only_if_improved

                if training_request.schedule:
                    job_data["schedule"] = {
                        "type": training_request.schedule.type,
                        "time": training_request.schedule.time
                    }

                    # Set status to "scheduled" for recurring jobs
                    job_data["status"] = "scheduled"

                    # Debug prints
                    print(f"Creating recurring job with schedule: {training_request.schedule.time}")
                    print(f"Setting job status to: scheduled")
                    print(f"Full schedule config: {job_data['schedule']}")
                    print(f"Recurring finetune flag: {job_data['recurring_finetune']}")

                    # Ensure the time format is correct (HH:MM)
                    time_parts = training_request.schedule.time.split(':')
                    if len(time_parts) == 2:
                        # Format time as HH:MM to ensure consistency
                        formatted_time = f"{int(time_parts[0]):02d}:{int(time_parts[1]):02d}"
                        job_data["schedule"]["time"] = formatted_time
                        print(f"Formatted time to: {formatted_time}")

            job_id = await training_repo.create_training_job(job_data)

            # Debug print job data after creation
            print(f"Created job with ID: {job_id}")
            print(f"Job data: {job_data}")

            # Check if this is a scheduled job that should not run immediately
            if job_data.get("status") == "scheduled" and job_data.get("recurring_finetune"):
                print(f"This is a scheduled recurring job - NOT starting training immediately")
                # Skip starting the training for scheduled jobs
            else:
                print(f"Starting training immediately for job: {job_id}")
                # Start training in background
                background_tasks.add_task(run_training_job, job_id, training_request)

            # Add to active tasks only if we're starting the job immediately
            if not (job_data.get("status") == "scheduled" and job_data.get("recurring_finetune")):
                active_training_tasks[job_id] = {
                    "environment_id": training_request.environment_id,
                    "environment_name": environment["name"],
                    "algorithm": training_request.algorithm,
                    "start_time": datetime.now(pytz.UTC)
                }

            # Return appropriate message based on job type
            if job_data.get("status") == "scheduled" and job_data.get("recurring_finetune"):
                return {"id": job_id, "message": "Recurring training job scheduled successfully"}
            else:
                return {"id": job_id, "message": "Training job created and started"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating training job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create training job: {str(e)}")

@router.get("/{job_id}")
async def get_training_job(job_id: str):
    """Get a specific training job by ID"""
    try:
        repo = await RLTrainingRepository.create()
        job = await repo.get_training_job(job_id)

        if not job:
            raise HTTPException(status_code=404, detail="Training job not found")

        return job
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving training job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve training job: {str(e)}")

@router.put("/{job_id}")
async def update_training_job(job_id: str, update_data: TrainingUpdate):
    """Update an existing training job"""
    try:
        repo = await RLTrainingRepository.create()

        # Check if job exists
        job = await repo.get_training_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Training job not found")

        # Prepare update data
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}

        if not update_dict:
            return {"message": "No updates provided"}

        # Update job
        success = await repo.update_training_job(job_id, update_dict)

        if not success:
            raise HTTPException(status_code=404, detail="Training job not found or no changes made")

        return {"message": "Training job updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating training job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update training job: {str(e)}")

@router.delete("/jobs/delete-all")
async def delete_all_training_jobs(status: Optional[str] = None):
    """Delete all training jobs with the specified status, or all jobs if status is None"""
    try:
        repo = await RLTrainingRepository.create()

        # Delete all jobs with the specified status
        deleted_count = await repo.delete_all_training_jobs_by_status(status)

        status_msg = f"with status '{status}'" if status else ""
        return {"message": f"Successfully deleted {deleted_count} training jobs {status_msg}"}
    except Exception as e:
        logger.error(f"Error deleting all training jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete all training jobs: {str(e)}")

@router.delete("/{job_id}")
async def delete_training_job(job_id: str):
    """Delete a training job"""
    try:
        repo = await RLTrainingRepository.create()

        # Check if job exists
        job = await repo.get_training_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Training job not found")

        # If job is running, cancel it
        if job.get("status") == "running":
            await repo.update_training_job(job_id, {"status": "cancelled"})

        # Delete job
        success = await repo.delete_training_job(job_id)

        if not success:
            raise HTTPException(status_code=404, detail="Training job not found")

        return {"message": "Training job deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting training job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete training job: {str(e)}")

@router.get("/{job_id}/results")
async def get_training_results(job_id: str):
    """Get training results for a specific job"""
    try:
        repo = await RLTrainingRepository.create()
        results = await repo.get_training_results(job_id)
        return results
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting training results: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get training results: {str(e)}")

@router.get("/optimization-group/{group_id}/best")
async def get_best_hyperparameters(group_id: str):
    """Get the best hyperparameters from an optimization group based on mean reward"""
    try:
        repo = await RLTrainingRepository.create()

        # Get all jobs in the optimization group
        jobs = await repo.list_training_jobs_by_optimization_group(group_id)

        if not jobs:
            raise HTTPException(status_code=404, detail=f"No jobs found for optimization group {group_id}")

        # Filter to only completed jobs
        completed_jobs = [job for job in jobs if job.get("status") == "completed"]

        if not completed_jobs:
            return {
                "message": "No completed jobs found in this optimization group yet",
                "total_jobs": len(jobs),
                "completed_jobs": 0,
                "best_job": None,
                "best_hyperparameters": None,
                "best_mean_reward": None
            }

        # Sort by mean reward (descending)
        sorted_jobs = sorted(completed_jobs, key=lambda x: x.get("mean_reward", float("-inf")), reverse=True)

        # Get the best job
        best_job = sorted_jobs[0]

        return {
            "message": "Successfully found best hyperparameters",
            "total_jobs": len(jobs),
            "completed_jobs": len(completed_jobs),
            "best_job_id": best_job["_id"],
            "best_hyperparameters": best_job.get("hyperparameters"),
            "best_mean_reward": best_job.get("mean_reward"),
            "std_reward": best_job.get("std_reward"),
            "training_time": best_job.get("training_time"),
            "optimization_strategy": best_job.get("optimization_strategy", "grid_search"),
            "is_smart_optimization": best_job.get("is_smart_optimization", False)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting best hyperparameters: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get best hyperparameters: {str(e)}")


@router.post("/{job_id}/cancel")
async def cancel_training_job(job_id: str):
    """Cancel a running training job"""
    try:
        repo = await RLTrainingRepository.create()

        # Check if job exists
        job = await repo.get_training_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Training job not found")

        # Check if job is running
        if job.get("status") != "running":
            return {"message": f"Training job is not running (current status: {job.get('status')})"}

        # Update job status to cancelled
        success = await repo.update_training_job(job_id, {"status": "cancelled"})

        if not success:
            raise HTTPException(status_code=404, detail="Training job not found or no changes made")

        return {"message": "Training job cancelled successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling training job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel training job: {str(e)}")

@router.get("/environments/available")
async def get_available_environments():
    """Get list of available environments for training"""
    try:
        env_repo = await RLEnvironmentRepository.create()
        environments = await env_repo.list_environments()

        # Filter to only active environments
        active_environments = [env for env in environments if env.get("is_active", False)]

        return active_environments
    except Exception as e:
        logger.error(f"Error getting available environments: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get available environments: {str(e)}")

@router.get("/algorithms/available")
async def get_available_algorithms():
    """Get list of available RL algorithms"""
    algorithms = [
        {
            "id": "PPO",
            "name": "Proximal Policy Optimization",
            "description": "A policy gradient method that uses a clipped surrogate objective to improve sample efficiency and stability."
        },
        {
            "id": "A2C",
            "name": "Advantage Actor Critic",
            "description": "A synchronous, deterministic variant of Asynchronous Advantage Actor Critic (A3C)."
        },
        {
            "id": "DQN",
            "name": "Deep Q-Network",
            "description": "A value-based method that uses a neural network to approximate the Q-function."
        },
        {
            "id": "SAC",
            "name": "Soft Actor Critic",
            "description": "An off-policy maximum entropy deep reinforcement learning algorithm with a stochastic actor-critic architecture."
        },
        {
            "id": "TD3",
            "name": "Twin Delayed DDPG",
            "description": "An algorithm addressing function approximation error in actor-critic methods."
        }
    ]

    return algorithms

@router.get("/scheduler/run")
async def run_scheduled_jobs_get(background_tasks: BackgroundTasks):
    """Run scheduled jobs based on their schedule (GET method for easy testing)"""
    return await run_scheduled_jobs(background_tasks)

@router.post("/scheduler/run")
async def run_scheduled_jobs(background_tasks: BackgroundTasks):
    """Run scheduled jobs based on their schedule"""
    try:
        # Get repositories
        training_repo = await RLTrainingRepository.create()
        env_repo = await RLEnvironmentRepository.create()  # Keep this as it's used later

        # Debug print current time
        now = datetime.now(pytz.UTC)
        current_time = now.strftime("%H:%M")
        print(f"Scheduler running at current time: {current_time}")

        # Get scheduled jobs to run
        jobs = await training_repo.get_scheduled_jobs_to_run()

        print(f"Found {len(jobs)} scheduled jobs to run at this time")
        for job in jobs:
            print(f"  - Job ID: {job['_id']}, Schedule: {job.get('schedule')}")

        if not jobs:
            return {"message": "No scheduled jobs to run at this time"}

        # Start each job
        started_jobs = []
        for job in jobs:
            try:
                # Create a training request from the job data
                job_id = job["_id"]

                # Get the environment
                environment = await env_repo.get_environment(job["environment_id"])
                if not environment:
                    logger.error(f"Environment {job['environment_id']} not found for scheduled job {job_id}")
                    continue

                # For recurring jobs, always find the best model to finetune
                # This ensures we're always using the best model, even if the job was created with a specific model
                print(f"Finding best model for environment {job['environment_id']}")

                # Get all completed jobs for this environment
                completed_jobs = await training_repo.list_training_jobs(
                    environment_id=job["environment_id"],
                    status="completed"
                )

                # Find the job with the highest mean reward
                best_job = None
                best_mean_reward = float('-inf')

                for completed_job in completed_jobs:
                    mean_reward = completed_job.get("mean_reward")
                    if mean_reward is not None and mean_reward > best_mean_reward:
                        best_mean_reward = mean_reward
                        best_job = completed_job

                # Get the original model_to_finetune_id from the job
                original_model_id = job.get("model_to_finetune_id")

                if best_job:
                    model_to_finetune_id = best_job["_id"]
                    print(f"Found best model {model_to_finetune_id} with mean reward {best_mean_reward}")

                    # If the best model is different from the original, update the job
                    if original_model_id != model_to_finetune_id:
                        print(f"Updating job to use best model {model_to_finetune_id} instead of {original_model_id}")
                        await training_repo.update_training_job(job_id, {"model_to_finetune_id": model_to_finetune_id})
                else:
                    print(f"No completed jobs found for environment {job['environment_id']}")
                    model_to_finetune_id = original_model_id

                # Create a training request
                training_request = TrainingRequest(
                    environment_id=job["environment_id"],
                    algorithm=job["algorithm"],
                    timesteps=job["timesteps"],
                    training_data_path=job.get("training_data_path", "software/rl/training/data/sample_training.json"),
                    train_ratio=job["train_ratio"],
                    finetune=True,
                    model_to_finetune_id=model_to_finetune_id,
                    save_only_if_improved=job.get("save_only_if_improved", True),
                    hyperparameters=job["hyperparameters"]
                )

                print(f"Created training request for job {job_id}:")
                print(f"  - environment_id: {training_request.environment_id}")
                print(f"  - algorithm: {training_request.algorithm}")
                print(f"  - timesteps: {training_request.timesteps}")
                print(f"  - finetune: {training_request.finetune}")
                print(f"  - model_to_finetune_id: {training_request.model_to_finetune_id}")
                print(f"  - save_only_if_improved: {training_request.save_only_if_improved}")

                # Update job status to running
                await training_repo.update_training_job(job_id, {"status": "running"})

                # Start training in background
                background_tasks.add_task(run_training_job, job_id, training_request)

                # Add to active tasks
                active_training_tasks[job_id] = {
                    "environment_id": job["environment_id"],
                    "environment_name": environment["name"],
                    "algorithm": job["algorithm"],
                    "start_time": datetime.now(pytz.UTC)
                }

                # Add to started jobs
                started_jobs.append({
                    "job_id": job_id,
                    "environment_name": environment["name"],
                    "algorithm": job["algorithm"]
                })

                logger.info(f"Started scheduled job {job_id} for environment {environment['name']}")

                # After job completes, set status back to scheduled for next run
                # This will be done in the run_training_job function
            except Exception as e:
                logger.error(f"Error starting scheduled job {job['_id']}: {str(e)}")

        return {
            "message": f"Started {len(started_jobs)} scheduled jobs",
            "jobs": started_jobs
        }
    except Exception as e:
        logger.error(f"Error running scheduled jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to run scheduled jobs: {str(e)}")

@router.post("/jobs/{job_id}/force-run")
async def force_run_scheduled_job(job_id: str, background_tasks: BackgroundTasks):
    """Force run a scheduled job immediately"""
    try:
        # Get repositories
        training_repo = await RLTrainingRepository.create()
        env_repo = await RLEnvironmentRepository.create()  # Keep this as it's used later

        # Get the job
        job = await training_repo.get_training_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

        # Check if job is scheduled
        if job.get("status") != "scheduled":
            raise HTTPException(status_code=400, detail=f"Job {job_id} is not a scheduled job")

        # Get the environment
        environment = await env_repo.get_environment(job["environment_id"])
        if not environment:
            raise HTTPException(status_code=404, detail=f"Environment {job['environment_id']} not found")

        # For recurring jobs, always find the best model to finetune
        # This ensures we're always using the best model, even if the job was created with a specific model
        print(f"Finding best model for environment {job['environment_id']}")

        # Get all completed jobs for this environment
        completed_jobs = await training_repo.list_training_jobs(
            environment_id=job["environment_id"],
            status="completed"
        )

        # Find the job with the highest mean reward
        best_job = None
        best_mean_reward = float('-inf')

        for completed_job in completed_jobs:
            mean_reward = completed_job.get("mean_reward")
            if mean_reward is not None and mean_reward > best_mean_reward:
                best_mean_reward = mean_reward
                best_job = completed_job

        # Get the original model_to_finetune_id from the job
        original_model_id = job.get("model_to_finetune_id")

        if best_job:
            model_to_finetune_id = best_job["_id"]
            print(f"Found best model {model_to_finetune_id} with mean reward {best_mean_reward}")

            # If the best model is different from the original, update the job
            if original_model_id != model_to_finetune_id:
                print(f"Updating job to use best model {model_to_finetune_id} instead of {original_model_id}")
                await training_repo.update_training_job(job_id, {"model_to_finetune_id": model_to_finetune_id})
        else:
            print(f"No completed jobs found for environment {job['environment_id']}")
            model_to_finetune_id = original_model_id

        # Create a training request
        training_request = TrainingRequest(
            environment_id=job["environment_id"],
            algorithm=job["algorithm"],
            timesteps=job["timesteps"],
            training_data_path=job.get("training_data_path", "software/rl/training/data/sample_training.json"),
            train_ratio=job["train_ratio"],
            finetune=True,
            model_to_finetune_id=model_to_finetune_id,
            save_only_if_improved=job.get("save_only_if_improved", True),
            hyperparameters=job["hyperparameters"]
        )

        print(f"Created training request for job {job_id}:")
        print(f"  - environment_id: {training_request.environment_id}")
        print(f"  - algorithm: {training_request.algorithm}")
        print(f"  - timesteps: {training_request.timesteps}")
        print(f"  - finetune: {training_request.finetune}")
        print(f"  - model_to_finetune_id: {training_request.model_to_finetune_id}")
        print(f"  - save_only_if_improved: {training_request.save_only_if_improved}")

        # Update job status to running
        await training_repo.update_training_job(job_id, {"status": "running"})

        # Start training in background
        background_tasks.add_task(run_training_job, job_id, training_request)

        # Add to active tasks
        active_training_tasks[job_id] = {
            "environment_id": job["environment_id"],
            "environment_name": environment["name"],
            "algorithm": job["algorithm"],
            "start_time": datetime.now(pytz.UTC)
        }

        return {"message": f"Scheduled job {job_id} started successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error force running scheduled job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to force run scheduled job: {str(e)}")

@router.patch("/jobs/{job_id}/schedule")
async def update_job_schedule(job_id: str, schedule_data: dict):
    """Update a scheduled job with all parameters"""
    try:
        # Get repositories
        training_repo = await RLTrainingRepository.create()

        # Get the job
        job = await training_repo.get_training_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

        # Check if job is scheduled
        if job.get("status") != "scheduled":
            raise HTTPException(status_code=400, detail=f"Job {job_id} is not a scheduled job")

        # Update data
        update_data = {}

        # Schedule settings
        if "schedule_time" in schedule_data:
            # Format time as HH:MM to ensure consistency
            time_parts = schedule_data["schedule_time"].split(':')
            if len(time_parts) == 2:
                formatted_time = f"{int(time_parts[0]):02d}:{int(time_parts[1]):02d}"

                # Update schedule
                if "schedule" not in job:
                    job["schedule"] = {"type": "daily", "time": formatted_time}
                    update_data["schedule"] = job["schedule"]
                else:
                    update_data["schedule"] = {
                        "type": job["schedule"].get("type", "daily"),
                        "time": formatted_time
                    }

        # Update save_only_if_improved flag
        if "save_only_if_improved" in schedule_data:
            update_data["save_only_if_improved"] = schedule_data["save_only_if_improved"]

        # Basic settings
        if "timesteps" in schedule_data:
            update_data["timesteps"] = schedule_data["timesteps"]

        if "training_data_path" in schedule_data:
            update_data["training_data_path"] = schedule_data["training_data_path"]

        if "train_ratio" in schedule_data:
            update_data["train_ratio"] = schedule_data["train_ratio"]

        # Model to finetune
        if "model_to_finetune_id" in schedule_data:
            # Verify that the model exists
            model_job = await training_repo.get_training_job(schedule_data["model_to_finetune_id"])
            if not model_job:
                raise HTTPException(status_code=404, detail=f"Model {schedule_data['model_to_finetune_id']} not found")

            # Check if model is completed
            if model_job.get("status") != "completed":
                raise HTTPException(status_code=400, detail=f"Model {schedule_data['model_to_finetune_id']} is not completed")

            update_data["model_to_finetune_id"] = schedule_data["model_to_finetune_id"]

        # Hyperparameters
        if "hyperparameters" in schedule_data:
            update_data["hyperparameters"] = schedule_data["hyperparameters"]

        # Hyperparameters optimization
        if "hyperparameters_optimization" in schedule_data and schedule_data["hyperparameters_optimization"].get("enabled"):
            # This is a special case - we'll need to create multiple jobs
            # First, delete the current job
            await training_repo.delete_training_job(job_id)

            # Create a new training request with the updated data
            env_repo = await RLEnvironmentRepository.create()
            environment = await env_repo.get_environment(job["environment_id"])
            if not environment:
                raise HTTPException(status_code=404, detail=f"Environment {job['environment_id']} not found")

            # Create a base training request with all the updated values
            base_request = TrainingRequest(
                environment_id=job["environment_id"],
                algorithm=job["algorithm"],
                timesteps=update_data.get("timesteps", job["timesteps"]),
                training_data_path=update_data.get("training_data_path", job.get("training_data_path", "software/rl/training/data/sample_training.json")),
                train_ratio=update_data.get("train_ratio", job["train_ratio"]),
                finetune=job.get("finetune", False),
                model_to_finetune_id=update_data.get("model_to_finetune_id", job.get("model_to_finetune_id")),
                recurring_finetune=job.get("recurring_finetune", False),
                save_only_if_improved=update_data.get("save_only_if_improved", job.get("save_only_if_improved", False)),
                hyperparameters=update_data.get("hyperparameters", job["hyperparameters"]),
                description=update_data.get("description", job.get("description", "")),
                hyperparameters_optimization=schedule_data["hyperparameters_optimization"]
            )

            # If there's a schedule, add it
            if "schedule" in update_data or "schedule" in job:
                schedule = update_data.get("schedule", job.get("schedule"))
                if schedule:
                    base_request.schedule = ScheduleConfig(
                        type=schedule.get("type", "daily"),
                        time=schedule.get("time", "00:00")
                    )
                    base_request.recurring_finetune = True

            # Generate combinations and create jobs
            combinations = generate_hyperparameter_combinations(base_request.hyperparameters_optimization)

            if not combinations:
                raise HTTPException(status_code=400, detail="No valid hyperparameter combinations generated")

            print(f"Generated {len(combinations)} hyperparameter combinations for scheduled job update")

            # Create a job for each combination
            job_ids = []
            for i, hyperparams in enumerate(combinations):
                # Create a copy of the base request with these hyperparameters
                job_request = copy.deepcopy(base_request)
                job_request.hyperparameters = hyperparams
                job_request.description = f"{base_request.description or 'Hyperparameter optimization'} - Combination {i+1}/{len(combinations)}"

                # Create job data
                job_data = {
                    "environment_id": job_request.environment_id,
                    "environment_name": environment["name"],
                    "algorithm": job_request.algorithm,
                    "timesteps": job_request.timesteps,
                    "training_data_path": job_request.training_data_path,
                    "train_ratio": job_request.train_ratio,
                    "finetune": job_request.finetune,
                    "hyperparameters": job_request.hyperparameters,
                    "description": job_request.description,
                    "optimization_group": True,  # Mark as part of an optimization group
                    "optimization_index": i,  # Index in the optimization group
                    "optimization_total": len(combinations)  # Total number of combinations
                }

                # Add model to finetune if specified
                if job_request.finetune and job_request.model_to_finetune_id:
                    job_data["model_to_finetune_id"] = job_request.model_to_finetune_id

                # Add recurring finetuning configuration if enabled
                if job_request.recurring_finetune:
                    job_data["recurring_finetune"] = True
                    job_data["save_only_if_improved"] = job_request.save_only_if_improved

                    if job_request.schedule:
                        job_data["schedule"] = {
                            "type": job_request.schedule.type,
                            "time": job_request.schedule.time
                        }

                        # Set status to "scheduled" for recurring jobs
                        job_data["status"] = "scheduled"

                # Create the job
                new_job_id = await training_repo.create_training_job(job_data)
                job_ids.append(new_job_id)

                print(f"Created optimization job {i+1}/{len(combinations)} with ID: {new_job_id}")

            # Return all job IDs
            return {
                "ids": job_ids,
                "message": f"Created {len(job_ids)} training jobs for hyperparameter optimization"
            }

        # Description
        if "description" in schedule_data:
            update_data["description"] = schedule_data["description"]

        # Debug print
        print(f"Updating job {job_id} with data:")
        for key, value in update_data.items():
            print(f"  - {key}: {value}")

        # Update job
        if update_data:
            await training_repo.update_training_job(job_id, update_data)
            return {"message": f"Job {job_id} updated successfully"}
        else:
            return {"message": "No changes to apply"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating scheduled job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update scheduled job: {str(e)}")



from fastapi import APIRouter, HTTPException, Request, Query, status, Path
from fastapi.responses import PlainTextResponse, JSONResponse
from typing import List, Dict, Any, Optional
import os
import sys
import logging
import traceback
from datetime import datetime
import importlib
import importlib.util
import inspect
import unittest
from io import StringIO
from bson import ObjectId, errors as bson_errors
from db.data_loader_repository import DataLoaderRepository
import asyncio


# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.getLogger().setLevel(logging.WARNING)  # Only show warnings and errors

# Try to import ConsoleOutput with detailed error handling
try:
    from utils.console_output import ConsoleOutput
    CONSOLE_OUTPUT_AVAILABLE = True
except ImportError as e:
    logging.error(f"ERROR importing ConsoleOutput: {e}")
    logging.error(f"Current file location: {__file__}")
    logging.error(f"Project root: {project_root}")
    CONSOLE_OUTPUT_AVAILABLE = False

# Import MongoDB connection
from ..database import client, database

# Explicitly use the data_loaders collection
loader_collection = database.data_loaders

# Define the data loaders directory with multiple potential paths
def find_data_loaders_dir():
    potential_paths = [
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data_loaders'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'software', 'data_loaders'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'software', 'data')
    ]

    for path in potential_paths:
        if os.path.exists(path):
            logging.debug(f"Found data loaders directory: {path}")
            return path

    # If no directory found, create one
    default_path = potential_paths[0]
    os.makedirs(default_path, exist_ok=True)
    logging.debug(f"Created data loaders directory: {default_path}")
    return default_path

# Set the data loaders directory
DATA_LOADERS_DIR = find_data_loaders_dir()

# Import DATA_LOADER_CLASSES from the data module
from data import registry, DATA_LOADER_CLASSES

router = APIRouter()

# Modify the list_data_loaders function to use MongoDB
@router.get("/api/data-loaders", response_model=List[Dict[str, Any]])
async def list_data_loaders():
    """List all available data loaders from MongoDB."""
    try:
        # Direct MongoDB query with extensive logging
        try:
            # Use find() with explicit query to get all documents
            cursor = loader_collection.find({})
            all_loaders = await cursor.to_list(length=None)

            # logging.debug(f"Total Loaders Found: {len(all_loaders)}")

            # for loader in all_loaders:
            #     logging.debug("Loader Details:")
            #     logging.debug(f"  Name: {loader.get('name', 'N/A')}")
            #     logging.debug(f"  File Name: {loader.get('file_name', 'N/A')}")
            #     logging.debug(f"  Is Active: {loader.get('is_active', False)}")
            #     logging.debug(f"  ID: {loader.get('_id', 'No ID')}")
        except Exception as find_err:
            logging.error(f"Error finding loaders: {find_err}")
            raise

        # Transform loaders to include more details
        loader_details = [
            {
                "name": loader.get('name'),
                "file_name": os.path.basename(loader.get('file_path', '')),
                "is_active": loader.get('is_active', True),
                "description": loader.get('description', ''),
                "_id": str(loader.get('_id', ''))
            }
            for loader in all_loaders
            if loader.get('name')
        ]

        # logging.debug(f"Returned Loader Details: {loader_details}")

        return loader_details
    except Exception as e:
        error_msg = f"Comprehensive Error fetching data loaders: {str(e)}"
        logging.error(error_msg, exc_info=True)

        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/api/data-loaders/{loader_id}/details")
async def get_data_loader_details(loader_id: str):
    """Get detailed information about a specific data loader."""
    logging.info(f"Received request for loader details: {loader_id}")

    try:
        # Try to convert the loader_id to ObjectId
        try:
            object_id = ObjectId(loader_id)
            # First try to find by ID
            loader = await loader_collection.find_one({"_id": object_id})
        except:
            # If ID conversion fails or not found, try to find by name
            loader = await loader_collection.find_one({"name": loader_id})

        if not loader:
            logging.error(f"Loader not found: {loader_id}")
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Convert ObjectId to string for JSON serialization
        loader['_id'] = str(loader['_id'])

        # Ensure all required fields exist
        loader.setdefault('description', 'No description available')
        loader.setdefault('is_active', False)
        loader.setdefault('created_at', datetime.utcnow())
        loader.setdefault('updated_at', datetime.utcnow())
        loader.setdefault('api_keys', {})  # Ensure api_keys field exists

        return loader
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Error fetching loader details: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/api/run-tests")
async def run_loader_tests(request: Request):
    """Run test cases for one or more data loaders."""
    try:
        data = await request.json()
        loader_ids = data.get('loader_ids', [])

        if not loader_ids:
            raise HTTPException(status_code=400, detail="No loader IDs provided")

        # Convert to list if single ID is provided
        if isinstance(loader_ids, str):
            loader_ids = [loader_ids]

        results = []
        total_loaders = len(loader_ids)

        # Process each loader one at a time
        for index, loader_id in enumerate(loader_ids, 1):  # Start index from 1
            try:
                loader_doc = await loader_collection.find_one({"_id": ObjectId(loader_id)})
                if not loader_doc:
                    continue

                loader_name = loader_doc.get('name')
                if not loader_name:
                    continue

                # Import test module dynamically
                from tests.test_data import run_specific_test

                # Add delay for visual feedback
                await asyncio.sleep(1)

                test_result = run_specific_test(loader_name)

                result = {
                    'loader_id': str(loader_doc['_id']),
                    'loader_name': loader_name,
                    'test_index': index,
                    'total_tests': total_loaders,
                    **test_result
                }
                results.append(result)

                # Send partial results for progress tracking
                print(f"Completed test {index}/{total_loaders} for {loader_name}")

            except Exception as e:
                results.append({
                    'loader_id': str(loader_doc['_id']),
                    'loader_name': loader_name,
                    'test_index': index,
                    'total_tests': total_loaders,
                    'success': False,
                    'total_tests': 0,
                    'passed_tests': 0,
                    'failures': 1,
                    'errors': 1,
                    'details': [{
                        'test_name': 'test_execution',
                        'error_type': type(e).__name__,
                        'error_message': str(e)
                    }]
                })

        return {
            "success": all(r['success'] for r in results),
            "total_loaders": total_loaders,
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Error running tests: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.delete("/api/data-loaders/delete/{loader_id}")
async def delete_data_loader(loader_id: str):
    """Delete a data loader from MongoDB and its corresponding file."""
    try:
        try:
            obj_id = ObjectId(loader_id)
        except Exception as e:
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid MongoDB ID format"}
            )
        
        loader = await loader_collection.find_one({"_id": obj_id})
        
        if not loader:
            return JSONResponse(
                status_code=404,
                content={"error": "Loader not found"}
            )
            
        result = await loader_collection.delete_one({"_id": obj_id})
        
        if result.deleted_count == 0:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to delete from database"}
            )
        
        file_path = os.path.join(DATA_LOADERS_DIR, f"{loader['name']}.py")
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            # Don't fail if file deletion fails
            pass
            
        return JSONResponse(
            status_code=200,
            content={"message": "Deleted successfully"}
        )
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@router.get("/api/data-loaders/{loader_name}", response_class=PlainTextResponse)
async def get_data_loader_code(loader_name: str):
    """Get the code of a specific data loader from MongoDB."""
    try:
        # logging.debug(f"Attempting to fetch code for loader: {loader_name}")
        # logging.debug(f"Data Loaders Directory: {DATA_LOADERS_DIR}")

        # Fetch loader details from MongoDB
        loader = await loader_collection.find_one({"name": loader_name})

        if not loader:
            # logging.debug(f"No loader found with name: {loader_name}")
            # List all loaders for debugging
            # all_loaders = await loader_collection.find({}).to_list(length=None)
            # logging.debug("Available loaders:")
            # for l in all_loaders:
            #     logging.debug(f"  - {l.get('name')}")
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Attempt to read the file content
        file_name = loader.get('file_name')
        if not file_name:
            # If no file name, use loader name with .py extension
            file_name = f"{loader_name}.py"

        # Try multiple potential file paths
        potential_paths = [
            os.path.join(DATA_LOADERS_DIR, file_name),
            os.path.join(os.path.dirname(DATA_LOADERS_DIR), 'data', file_name),
            os.path.join(os.path.dirname(DATA_LOADERS_DIR), 'data_loaders', file_name)
        ]

        file_path = None
        for path in potential_paths:
            # logging.debug(f"Checking path: {path}")
            if os.path.exists(path):
                file_path = path
                break

        if not file_path:
            # logging.debug(f"File not found for loader: {loader_name}")
            # logging.debug("Potential paths checked:")
            # for path in potential_paths:
            #     logging.debug(f"  - {path}")

            # If file doesn't exist, create an empty file
            file_path = potential_paths[0]
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w') as f:
                f.write(f"# Data Loader: {loader_name}\n# Created automatically\n")
            # logging.debug(f"Created empty file: {file_path}")

        # Read file content
        try:
            with open(file_path, 'r') as file:
                code = file.read()
        except Exception as read_err:
            # logging.error(f"Error reading file {file_path}: {read_err}")
            raise HTTPException(status_code=500, detail=f"Error reading file: {str(read_err)}")

        return PlainTextResponse(content=code)
    except Exception as e:
        error_msg = f"Error fetching data loader code for {loader_name}: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/api/data-loaders/{loader_name}")
async def save_data_loader_code(loader_name: str, request: Request):
    """Save the code of a specific data loader to file and update MongoDB."""
    try:
        # Get the code from the request body
        code = await request.body()
        code_str = code.decode('utf-8')

        # Find the loader in MongoDB
        loader = await loader_collection.find_one({"name": loader_name})

        # If loader doesn't exist, create a new one
        if not loader:
            loader_data = {
                "name": loader_name,
                "file_name": f"{loader_name}.py",
                "description": "Automatically created data loader",
                "is_active": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            await loader_collection.insert_one(loader_data)
            loader = loader_data

        # Get the file name from MongoDB or generate one
        file_name = loader.get('file_name', f"{loader_name}.py")

        # Determine file path
        potential_paths = [
            os.path.join(DATA_LOADERS_DIR, file_name),
            os.path.join(os.path.dirname(DATA_LOADERS_DIR), 'data', file_name),
            os.path.join(os.path.dirname(DATA_LOADERS_DIR), 'data_loaders', file_name)
        ]

        # Choose the first path that is in an existing directory
        file_path = None
        for path in potential_paths:
            dir_path = os.path.dirname(path)
            if os.path.exists(dir_path):
                file_path = path
                break

        # If no existing directory, use the first path and create directories
        if not file_path:
            file_path = potential_paths[0]
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Write to file
        with open(file_path, 'w') as file:
            file.write(code_str)

        # Update MongoDB
        result = await loader_collection.update_one(
            {"name": loader_name},
            {"$set": {
                "updated_at": datetime.utcnow(),
                "file_name": os.path.basename(file_path)
            }}
        )

        return JSONResponse(content={
            "message": "Code saved successfully",
            "file_path": file_path
        })

    except Exception as e:
        error_msg = f"Error saving data loader code: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/reload-loader/{loader_name}")
async def reload_data_loader(loader_name: str):
    """
    Reload a specific data loader without restarting the application
    """
    try:
        if registry.reload_loader(loader_name):
            # Update DATA_LOADER_CLASSES reference
            global DATA_LOADER_CLASSES
            DATA_LOADER_CLASSES = registry.get_all_loader_classes()
            return {"status": "success", "message": f"Successfully reloaded {loader_name}"}
        else:
            raise HTTPException(status_code=404, detail=f"Loader {loader_name} not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reloading loader: {str(e)}")

@router.get("/api/debug-loaders")
async def debug_loaders():
    """Debugging endpoint to inspect loaders in MongoDB."""
    try:
        # Fetch all loaders
        all_loaders = await loader_collection.find({}).to_list(length=None)

        # Prepare debug information
        debug_info = {
            "total_loaders": len(all_loaders),
            "loaders": []
        }

        for loader in all_loaders:
            # Convert ObjectId to string
            loader['_id'] = str(loader.get('_id', ''))

            debug_info['loaders'].append({
                "name": loader.get('name', 'N/A'),
                "file_name": loader.get('file_name', 'N/A'),
                "is_active": loader.get('is_active', False),
                "_id": loader['_id']
            })

        return debug_info
    except Exception as e:
        error_msg = f"Error in debug-loaders endpoint: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/api/debug/insert-loader")
async def debug_insert_loader(loader_data: dict):
    """Debug endpoint to manually insert a loader into MongoDB."""
    try:
        # Ensure required fields are present
        if not loader_data.get('name'):
            raise HTTPException(status_code=400, detail="Loader name is required")

        # Set default values if not provided
        loader_data.setdefault('is_active', True)
        loader_data.setdefault('created_at', datetime.utcnow())
        loader_data.setdefault('updated_at', datetime.utcnow())

        # Insert the loader
        result = await loader_collection.insert_one(loader_data)

        return {
            "message": "Loader inserted successfully",
            "inserted_id": str(result.inserted_id)
        }
    except Exception as e:
        error_msg = f"Error inserting loader: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/api/manual-loader")
async def manually_add_loader(loader_data: dict):
    """Manually add a data loader to the database."""
    try:
        # Validate required fields
        if not loader_data.get('name'):
            raise HTTPException(status_code=400, detail="Loader name is required")

        # Set default values
        loader_data.setdefault('is_active', True)
        loader_data.setdefault('created_at', datetime.utcnow())
        loader_data.setdefault('updated_at', datetime.utcnow())

        # Insert the loader
        result = await loader_collection.insert_one(loader_data)

        # logging.debug(f"Manually inserted loader: {loader_data['name']}")

        return {
            "message": "Loader added successfully",
            "loader_name": loader_data['name'],
            "inserted_id": str(result.inserted_id)
        }
    except Exception as e:
        error_msg = f"Error adding manual loader: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/api/data-loaders/{loader_id}")
async def get_data_loader_by_id(loader_id: str):
    """Retrieve a specific data loader by its ID."""
    try:
        # Convert string ID to ObjectId
        from bson import ObjectId

        # Find the loader by ID
        loader = await loader_collection.find_one({"_id": ObjectId(loader_id)})

        if not loader:
            raise HTTPException(status_code=404, detail="Loader not found")

        # Convert ObjectId to string for JSON serialization
        loader['_id'] = str(loader.get('_id', ''))

        return loader
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving loader: {str(e)}")

@router.patch("/api/data-loaders/{loader_id}/toggle")
async def toggle_data_loader_status(loader_id: str):
    """Toggle the active status of a data loader."""
    try:
        # Convert string ID to ObjectId
        loader_id_obj = ObjectId(loader_id)

        # Find the loader
        loader = await loader_collection.find_one({"_id": loader_id_obj})
        if not loader:
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Toggle the is_active status
        new_status = not loader.get('is_active', False)

        # Update the loader's status
        result = await loader_collection.update_one(
            {"_id": loader_id_obj},
            {
                "$set": {
                    "is_active": new_status,
                    "updated_at": datetime.utcnow()
                }
            }
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update loader status")

        return {
            "message": f"Data loader {'activated' if new_status else 'deactivated'} successfully",
            "new_status": new_status
        }
    except Exception as e:
        error_msg = f"Error toggling data loader status: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/api/debug/loaders-info")
async def debug_loaders_info():
    """Comprehensive debug information about data loaders."""
    try:
        # Fetch all loaders from MongoDB
        all_loaders = await loader_collection.find({}).to_list(length=None)

        # Prepare debug information
        debug_info = {
            "total_loaders": len(all_loaders),
            "loaders": [],
            "data_loaders_dir": DATA_LOADERS_DIR,
            "files_in_dir": []
        }

        # Collect loader details
        for loader in all_loaders:
            loader_info = {
                "name": loader.get('name', 'N/A'),
                "file_name": loader.get('file_name', 'N/A'),
                "is_active": loader.get('is_active', False),
                "_id": str(loader.get('_id', 'No ID'))
            }

            # Check file existence
            if loader.get('file_name'):
                file_path = os.path.join(DATA_LOADERS_DIR, loader['file_name'])
                loader_info['file_exists'] = os.path.exists(file_path)
                if loader_info['file_exists']:
                    try:
                        with open(file_path, 'r') as file:
                            loader_info['file_size'] = os.path.getsize(file_path)
                    except Exception as e:
                        loader_info['file_read_error'] = str(e)

            debug_info['loaders'].append(loader_info)

        # List files in DATA_LOADERS_DIR
        try:
            debug_info['files_in_dir'] = os.listdir(DATA_LOADERS_DIR)
        except Exception as e:
            debug_info['files_in_dir_error'] = str(e)

        return debug_info
    except Exception as e:
        error_msg = f"Error in debug loaders info: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

###API Keys routers###

@router.post("/api/data-loaders/{loader_id}/api-keys")
async def add_api_key(loader_id: str, request: Request):
    """Add a new API key to a data loader."""
    try:
        key_data = await request.json()
        logging.info(f"Adding API key to loader {loader_id}")

        # Find the loader first
        loader = await loader_collection.find_one({"_id": ObjectId(loader_id)})
        if not loader:
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Prepare API key data
        api_key = {
            "key": key_data.get("key"),
            "description": key_data.get("description", ""),
            "is_active": key_data.get("is_active", True),
            "last_updated": datetime.utcnow()
        }

        # Update the loader with the new API key
        result = await loader_collection.update_one(
            {"_id": ObjectId(loader_id)},
            {
                "$set": {
                    f"api_keys.{key_data['name']}": api_key,
                    "updated_at": datetime.utcnow()
                }
            }
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=400, detail="Failed to add API key")

        return {"message": f"API key '{key_data['name']}' added successfully"}
    except Exception as e:
        logging.error(f"Error adding API key: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/api/data-loaders/{loader_id}/api-keys/{key_name}")
async def update_api_key(loader_id: str, key_name: str, request: Request):
    """Update an existing API key."""
    try:
        key_data = await request.json()
        logging.info(f"Updating API key {key_name} for loader {loader_id}")

        # Find the loader first
        loader = await loader_collection.find_one({"_id": ObjectId(loader_id)})
        if not loader:
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Check if API key exists
        if not loader.get("api_keys", {}).get(key_name):
            raise HTTPException(status_code=404, detail="API key not found")

        # Prepare updated API key data
        updated_key = {
            "key": key_data.get("key"),
            "description": key_data.get("description", ""),
            "is_active": key_data.get("is_active", True),
            "last_updated": datetime.utcnow()
        }

        # Update the API key
        result = await loader_collection.update_one(
            {"_id": ObjectId(loader_id)},
            {
                "$set": {
                    f"api_keys.{key_name}": updated_key,
                    "updated_at": datetime.utcnow()
                }
            }
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=400, detail="Failed to update API key")

        return {"message": f"API key '{key_name}' updated successfully"}
    except Exception as e:
        logging.error(f"Error updating API key: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/data-loaders/{loader_id}/api-keys/{key_name}")
async def delete_api_key(loader_id: str, key_name: str):
    """Delete an API key."""
    try:
        logging.info(f"Deleting API key {key_name} from loader {loader_id}")

        # Find the loader first
        loader = await loader_collection.find_one({"_id": ObjectId(loader_id)})
        if not loader:
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Check if API key exists
        if not loader.get("api_keys", {}).get(key_name):
            raise HTTPException(status_code=404, detail="API key not found")

        # Remove the API key
        result = await loader_collection.update_one(
            {"_id": ObjectId(loader_id)},
            {
                "$unset": {f"api_keys.{key_name}": ""},
                "$set": {"updated_at": datetime.utcnow()}
            }
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=400, detail="Failed to delete API key")

        return {"message": f"API key '{key_name}' deleted successfully"}
    except Exception as e:
        logging.error(f"Error deleting API key: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/api/data-loaders/{loader_id}/api-keys/{key_name}/toggle")
async def toggle_api_key(loader_id: str, key_name: str, request: Request):
    """Toggle the active status of an API key."""
    try:
        data = await request.json()
        new_status = data.get("is_active", True)
        logging.info(f"Toggling API key {key_name} to {new_status}")

        # Find the loader first
        loader = await loader_collection.find_one({"_id": ObjectId(loader_id)})
        if not loader:
            raise HTTPException(status_code=404, detail="Data loader not found")

        # Check if API key exists
        api_keys = loader.get("api_keys", {})
        if key_name not in api_keys:
            raise HTTPException(status_code=404, detail="API key not found")

        # Update the API key's active status
        api_keys[key_name]["is_active"] = new_status
        api_keys[key_name]["last_updated"] = datetime.utcnow()

        result = await loader_collection.update_one(
            {"_id": ObjectId(loader_id)},
            {
                "$set": {
                    f"api_keys.{key_name}.is_active": new_status,
                    f"api_keys.{key_name}.last_updated": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            }
        )

        if result.modified_count == 0:
            raise HTTPException(status_code=400, detail="Failed to toggle API key status")

        return {
            "message": f"API key '{key_name}' {'activated' if new_status else 'deactivated'} successfully",
            "is_active": new_status
        }
    except Exception as e:
        logging.error(f"Error toggling API key: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

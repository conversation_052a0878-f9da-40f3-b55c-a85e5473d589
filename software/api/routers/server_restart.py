from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse
import os
import signal
import subprocess
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/restart-server")
async def restart_server():
    """Simple endpoint to restart the server application"""
    try:
        # Get the current process ID
        pid = os.getpid()
        print(f"Current PID: {pid}")
        
        # Get the working directory - this is the Vero root directory
        working_dir = os.getcwd()
        print(f"Working directory: {working_dir}")
        
        # Create a simple restart script that properly kills existing processes
        restart_cmd = f"""#!/bin/bash
echo "Restarting server..."

# 1. Force kill any process using port 5001
echo "Finding processes using port 5001..."
PORT_PIDS=$(lsof -t -i:5001)
if [ ! -z "$PORT_PIDS" ]; then
    echo "Killing processes: $PORT_PIDS"
    kill -9 $PORT_PIDS
fi

# 2. Additional cleanup - kill any potential python processes running the web server
pkill -f "python.*software/run_web.py" || true
pkill -f "uvicorn.*api.main:app" || true

# 3. Sleep to ensure processes are terminated
sleep 2

# 4. Clear terminal
printf "\\033c"
clear

echo "Starting fresh server..."
cd "{working_dir}" 
poetry run python software/run_web.py
"""
        
        # Write script to temp file
        script_path = "/tmp/restart_server.sh"
        with open(script_path, "w") as f:
            f.write(restart_cmd)
        
        # Make executable
        os.chmod(script_path, 0o755)
        print(f"Created restart script: {script_path}")
        
        # Launch the script in a new process
        subprocess.Popen(
            f"sleep 1 && {script_path}",
            shell=True,
            start_new_session=True
        )
        
        print(f"Server will restart shortly...")
        
        # Return success before terminating
        response = {"status": "success", "message": "Server is restarting..."}
        
        # Kill current process to allow restart
        os.kill(pid, signal.SIGTERM)
        
        return response
    except Exception as e:
        print(f"Error restarting server: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to restart server: {str(e)}") 

@router.post("/shutdown-server")
async def shutdown_server():
    """Endpoint to shut down the server application"""
    try:
        pid = os.getpid()
        logger.info(f"Shutting down server PID: {pid}")
        response = {"status": "success", "message": "Server is shutting down..."}
        os.kill(pid, signal.SIGTERM)
        return response
    except Exception as e:
        logger.error(f"Error shutting down server: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to shutdown server: {str(e)}")
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Dict, Any, Optional
import pandas as pd
from ..database import loader_collection, database
from data import registry
import logging
from fastapi.responses import JSONResponse
import uuid
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from datetime import datetime
import traceback
from main import StockAnalysisApp
from db.report_repository import (
    ReportRepository,
    ReportFactory,
    DataLoaderSelection,
    LoaderConfig,
    JoinRequest,
    WorkflowStepRequest,
    WorkflowResponse,
    JoinResponse
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

tasks = {}
joined_data_store = {}

@router.get("/loaders")
async def get_data_loaders():
    """Get list of available data loaders."""
    try:
        # Get all data loaders from the registry
        loader_names = registry.get_all_loader_names()
        logger.info(f"Available data loaders: {loader_names}")
        return loader_names
    except Exception as e:
        logger.error(f"Error fetching data loaders: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch data loaders: {str(e)}"
        )

@router.get("/columns/{loader}")
async def get_loader_columns(loader: str, ticker: str):
    """
    Get available columns for a specific data loader, including data types.

    Args:
        loader (str): Name of the data loader
        ticker (str): Ticker for the data loader

    Returns:
        Dict: Available columns with their data types
    """
    try:
        # Get the loader class
        loader_class = registry.get_loader_class(loader)
        if not loader_class:
            raise ValueError(f"Loader {loader} not found")

        # Initialize loader with ticker
        data_loader = loader_class(ticker)
        
        # Try to load actual data first
        try:
            logger.info(f"Loading data for {ticker} using {loader}")
            df = data_loader.load_historical_data()
            
            # If data was loaded successfully, use it
            if df is not None and not df.empty:
                logger.info(f"Successfully loaded data: {df.shape} rows, {list(df.columns)} columns")
                # Get column information including data types
                columns_info = []

                # Add Date column (index) first
                columns_info.append({
                    "name": "Date",
                    "type": "datetime",
                    "description": "Date of the record"
                })

                # Add other columns with their types
                for col in df.columns:
                    dtype = str(df[col].dtype)
                    description = ""

                    # Add descriptions based on common financial column names
                    if "price" in col.lower() or col.lower() in ["open", "high", "low", "close", "adj close"]:
                        description = "Price value"
                    elif "volume" in col.lower():
                        description = "Trading volume"
                    elif any(metric in col.lower() for metric in ["change", "return", "pct", "percentage"]):
                        description = "Percentage change"

                    # Map numpy/pandas types to more readable types
                    if "float" in dtype:
                        dtype = "number"
                    elif "int" in dtype:
                        dtype = "integer"
                    elif "datetime" in dtype:
                        dtype = "datetime"
                    elif "bool" in dtype:
                        dtype = "boolean"
                    else:
                        dtype = "text"

                    columns_info.append({
                        "name": col,
                        "type": dtype,
                        "description": description
                    })

                logger.info(f"Returning column info from actual data: {[c['name'] for c in columns_info]}")
                return {"columns": columns_info}
        
        except Exception as e:
            logger.warning(f"Error loading data for {ticker} using {loader}: {str(e)}. Using defined columns instead.")
        
        # If we get here, we couldn't load actual data, so use defined columns
        logger.info(f"Using defined columns for {loader} since no data was loaded")
        
        # Get column information from the loader's additional_columns dictionary
        columns_info = []
        
        # Add Date column first
        columns_info.append({
            "name": "Date",
            "type": "datetime",
            "description": "Date of the record"
        })
        
        # Add other columns based on loader's defined columns
        for col in data_loader.additional_columns.keys():
            # Default to numeric type for financial data
            dtype = "number"
            description = ""
            
            # Determine descriptions and types based on column name
            if "price" in col.lower() or col.lower() in ["open", "high", "low", "close", "adj close"]:
                description = "Price value"
            elif "volume" in col.lower():
                description = "Trading volume"
            elif any(metric in col.lower() for metric in ["change", "return", "pct", "percentage"]):
                description = "Percentage change"
            elif col == "Stock Splits":
                dtype = "text"
                description = "Stock split information"
            
            columns_info.append({
                "name": col,
                "type": dtype,
                "description": description
            })
        
        logger.info(f"Returning columns based on defined structure: {[c['name'] for c in columns_info]}")
        return {"columns": columns_info}

    except Exception as e:
        logger.error(f"Error fetching columns: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch columns: {str(e)}"
        )

@router.post("/join")
async def join_data(request: JoinRequest):
    try:
        logger.debug(">>> join_data endpoint entered")
        logger.info(f"Request received: {request.model_dump()}")
        logger.debug(f"Received join_type: {request.join_type}")

        # Validate loader names
        left_loader_class = registry.get_loader_class(request.left_loader)
        right_loader_class = registry.get_loader_class(request.right_loader)

        logger.info(f"Left loader class: {left_loader_class}")
        logger.info(f"Right loader class: {right_loader_class}")

        if not left_loader_class or not right_loader_class:
            raise HTTPException(
                status_code=400,
                detail="Invalid data loader specified"
            )

        # Initialize loaders with tickers
        left_loader = left_loader_class(request.left_ticker)
        right_loader = right_loader_class(request.right_ticker)

        # Load data from both loaders
        left_df = left_loader.load_historical_data()
        right_df = right_loader.load_historical_data()

        if left_df is None or left_df.empty:
            raise HTTPException(
                status_code=400,
                detail=f"No data available for ticker {request.left_ticker} with loader {request.left_loader}"
            )

        if right_df is None or right_df.empty:
            raise HTTPException(
                status_code=400,
                detail=f"No data available for ticker {request.right_ticker} with loader {request.right_loader}"
            )

        logger.info(f"Left DataFrame shape: {left_df.shape}, columns: {left_df.columns.tolist()}")
        logger.info(f"Right DataFrame shape: {right_df.shape}, columns: {right_df.columns.tolist()}")

        # Perform join based on join_type
        if request.join_type == "forward_fill":
            logger.info("Performing forward fill join (merge_asof)")
            logger.debug(f"Left DF shape before processing: {left_df.shape}")
            logger.debug(f"Right DF shape before processing: {right_df.shape}")
            logger.debug(f"Left DF columns before set_index: {left_df.columns.tolist()}")
            logger.debug(f"Left DF index name before set_index: {left_df.index.name}")
            logger.debug(f"Right DF columns before set_index: {right_df.columns.tolist()}")
            logger.debug(f"Right DF index name before set_index: {right_df.index.name}")
            # Ensure Date column exists and is datetime
            try:
                if 'Date' not in left_df.index.name:
                    logger.debug("Attempting to set 'Date' as index for left_df")
                    left_df = left_df.set_index('Date')
                if 'Date' not in right_df.index.name:
                    logger.debug("Attempting to set 'Date' as index for right_df")
                    right_df = right_df.set_index('Date')
                logger.debug(f"Set index complete. Left index: {left_df.index.name}, Right index: {right_df.index.name}")
            except Exception as e_set_index:
                logger.error(f"Error setting index to 'Date': {e_set_index}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error processing join key 'Date': {e_set_index}")
            
            try:
                logger.debug(f"Attempting to convert indices to datetime. Left type: {left_df.index.dtype}, Right type: {right_df.index.dtype}")
                left_df.index = pd.to_datetime(left_df.index)
                right_df.index = pd.to_datetime(right_df.index)
                logger.debug(f"Indices converted. Left type: {left_df.index.dtype}, Right type: {right_df.index.dtype}")
            except Exception as e_datetime:
                logger.error(f"Error converting index to datetime: {e_datetime}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error converting join key to datetime: {e_datetime}")

            # Sort both dataframes by index (Date)
            try:
                logger.debug("Attempting to sort dataframes by index")
                left_df = left_df.sort_index()
                right_df = right_df.sort_index()
                logger.debug(f"Dataframes sorted. Left head:\n{left_df.head()}\nRight head:\n{right_df.head()}")
            except Exception as e_sort:
                logger.error(f"Error sorting dataframes by index: {e_sort}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error sorting data by date: {e_sort}")

            # Perform as-of merge
            try:
                logger.debug("Attempting pd.merge_asof with direction='forward'")
                logger.debug(f"Left index min: {left_df.index.min()}, max: {left_df.index.max()}")
                logger.debug(f"Right index min: {right_df.index.min()}, max: {right_df.index.max()}")
                # Sample the data to see what's being joined
                logger.debug(f"Left DF sample:\n{left_df.head(3)}")
                logger.debug(f"Right DF sample:\n{right_df.head(3)}")
                
                joined_df = pd.merge_asof(
                    left_df,
                    right_df,
                    left_index=True,
                    right_index=True,
                    direction='forward', # Forward fill (changed from 'backward')
                    suffixes=('', '_sub') # Suffix for overlapping columns from right_df
                )
                joined_df = joined_df.reset_index() # Reset index to keep Date as column
                logger.debug(f"Merge_asof complete. Joined DF shape: {joined_df.shape}")
                logger.debug(f"Joined DF sample:\n{joined_df.head(3)}")
                logger.debug(f"Null counts in joined DF:\n{joined_df.isna().sum().to_dict()}")
            except Exception as e_merge:
                logger.error(f"Error during merge_asof: {e_merge}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error performing as-of merge: {e_merge}")
        else:
            logger.info(f"Performing standard merge with type: {request.join_type}")
            # Standard merge (ensure join columns are specified)
            if not request.left_on or not request.right_on:
                 raise HTTPException(
                     status_code=400,
                     detail="Join columns (left_on, right_on) must be specified for standard join types."
                 )
            
            # Handle potential Date index if specified as join column
            if request.left_on == 'Date' and left_df.index.name == 'Date':
                 left_df = left_df.reset_index()
            if request.right_on == 'Date' and right_df.index.name == 'Date':
                 right_df = right_df.reset_index()

            joined_df = pd.merge(
                left_df,
                right_df,
                left_on=request.left_on,
                right_on=request.right_on,
                how=request.join_type,
                suffixes=('', '_sub')
            )

        logger.info(f"Joined DataFrame shape: {joined_df.shape}, columns: {joined_df.columns.tolist()}")

        # Convert to records for JSON serialization
        result = joined_df.to_dict(orient='records')

        return JoinResponse(
            data=result,
            message="Join completed successfully"
        )

    except Exception as e:
        logger.error(f"Error performing join: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to perform join: {str(e)}"
        )

@router.post("/workflow-step/run")
async def run_workflow_step(request: WorkflowStepRequest):
    """Run workflow step with selected features and model."""
    try:
        # Validate loaders exist
        left_loader_class = registry.get_loader_class(request.left_loader)
        right_loader_class = registry.get_loader_class(request.right_loader)

        if not left_loader_class or not right_loader_class:
            raise HTTPException(
                status_code=400,
                detail="Invalid data loader specified"
            )

        # Initialize loaders with tickers
        left_loader = left_loader_class(request.left_ticker)
        right_loader = right_loader_class(request.right_ticker)

        # Load data
        left_df = left_loader.load_historical_data()
        right_df = right_loader.load_historical_data()

        if left_df is None or left_df.empty:
            raise HTTPException(
                status_code=400,
                detail=f"No data available for ticker {request.left_ticker} with loader {request.left_loader}"
            )

        if right_df is None or right_df.empty:
            raise HTTPException(
                status_code=400,
                detail=f"No data available for ticker {request.right_ticker} with loader {request.right_loader}"
            )

        # Join data
        if request.join_type == "forward_fill":
            logger.info("Performing forward fill join (merge_asof) for workflow step")
            logger.debug(f"WF Step - Left DF shape before processing: {left_df.shape}")
            logger.debug(f"WF Step - Right DF shape before processing: {right_df.shape}")
            logger.debug(f"WF Step - Left DF columns before set_index: {left_df.columns.tolist()}")
            logger.debug(f"WF Step - Left DF index name before set_index: {left_df.index.name}")
            logger.debug(f"WF Step - Right DF columns before set_index: {right_df.columns.tolist()}")
            logger.debug(f"WF Step - Right DF index name before set_index: {right_df.index.name}")
            # Ensure Date column exists and is datetime
            if 'Date' not in left_df.index.name:
                logger.debug("WF Step - Setting 'Date' as index for left_df")
                left_df = left_df.set_index('Date')
            if 'Date' not in right_df.index.name:
                logger.debug("WF Step - Setting 'Date' as index for right_df")
                right_df = right_df.set_index('Date')
            
            logger.debug(f"WF Step - Converting indices to datetime. Left type: {left_df.index.dtype}, Right type: {right_df.index.dtype}")
            left_df.index = pd.to_datetime(left_df.index)
            right_df.index = pd.to_datetime(right_df.index)
            logger.debug(f"WF Step - Indices converted. Left type: {left_df.index.dtype}, Right type: {right_df.index.dtype}")

            # Sort both dataframes by index (Date)
            logger.debug("WF Step - Sorting dataframes by index")
            left_df = left_df.sort_index()
            right_df = right_df.sort_index()
            logger.debug(f"WF Step - Dataframes sorted.")

            # Perform as-of merge
            logger.debug("WF Step - Performing pd.merge_asof with direction='forward'")
            logger.debug(f"WF Step - Left index min: {left_df.index.min()}, max: {left_df.index.max()}")
            logger.debug(f"WF Step - Right index min: {right_df.index.min()}, max: {right_df.index.max()}")
            joined_df = pd.merge_asof(
                left_df,
                right_df,
                left_index=True,
                right_index=True,
                direction='forward', # Forward fill
                suffixes=('', '_sub')
            )
            joined_df = joined_df.reset_index()
            logger.debug(f"WF Step - Merge_asof complete. Joined DF shape: {joined_df.shape}")
            logger.debug(f"WF Step - Joined DF head:\n{joined_df.head(3)}")
            logger.debug(f"WF Step - Null counts in joined DF:\n{joined_df.isna().sum().to_dict()}")
        else:
            logger.info(f"Performing standard merge with type: {request.join_type} for workflow step")
            # Handle potential Date index before standard merge
            if left_df.index.name == 'Date':
                 left_df = left_df.reset_index()
            if right_df.index.name == 'Date':
                 right_df = right_df.reset_index()

            joined_df = pd.merge(
                left_df,
                right_df,
                on='Date',  # Always join on Date for workflow step standard joins
                how=request.join_type,
                suffixes=('', '_sub')
            )

        app = StockAnalysisApp(request.left_ticker)  # Use left ticker as primary
        app.data = joined_df  # Set the joined data
        app.data_loader_name = f"{request.left_loader}+{request.right_loader}"  # Set combined loader name
        app.ticker = request.left_ticker  # Set the ticker explicitly
        
        # Set the features
        app.rerun_features(request.features)

        # Get the model class
        model_class = next(
            (cls for cls in MODEL_CLASSES if cls.__name__ == request.model),
            None
        )

        if not model_class:
            raise ValueError(f"Model {request.model} not found")

        # Run the model
        result, metrics = app.run_model(
            model_class,
            predict=request.target,
            forecast_horizon=request.forecast_horizon
        )

        # Create report using factory
        report_data = ReportFactory.create_join_report({
            "main_df": left_df,
            "aux_df": right_df,
            "target_df": result,
            "main_datasource_id": request.left_loader,
            "aux_datasource_id": request.right_loader,
            "join_column": "Date",
            "join_type": request.join_type,
            "features": request.features
        })

        # Save report using repository
        repo = await ReportRepository.create()
        report_id = await repo.insert_report(report_data)

        return {
            "status": "success",
            "message": "Workflow step completed successfully",
            "task_id": str(uuid.uuid4()),
            "report_id": report_id,
            "metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error running workflow step: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to run workflow step: {str(e)}"
        )

@router.get("/workflow-step/columns/{task_id}")
async def get_joined_columns(task_id: str) -> Dict[str, List[str]]:
    """
    Get columns from the joined dataset for a specific task.
    
    Args:
        task_id (str): Task ID to get columns for

    Returns:
        Dict[str, List[str]]: List of column names from the joined dataset
    """
    try:
        if task_id not in joined_data_store:
            raise HTTPException(status_code=404, detail="Task not found or data expired")
        
        columns = joined_data_store[task_id].columns.tolist()
        return {"columns": columns}
        
    except Exception as e:
        logger.error(f"Error getting joined columns: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get joined columns: {str(e)}"
        )

@router.get("/workflow-step/status/{task_id}")
async def get_workflow_step_status(task_id: str) -> Dict[str, Any]:
    """
    Get the status of a workflow step task.
    
    Args:
        task_id (str): Unique identifier for the task

    Returns:
        Dict: Task status, progress, and optional message
    """
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Clean up joined data if task is complete
    if tasks[task_id]["status"] == "completed" and task_id in joined_data_store:
        del joined_data_store[task_id]
    
    return tasks[task_id]

@router.get("/workflow-step/options")
async def get_workflow_options() -> Dict[str, List[str]]:
    """
    Get available options for workflow configuration.

    Returns:
        Dict: Available features and models
    """
    try:
        # Get available features from FEATURE_CLASSES
        features = list(FEATURE_CLASSES.keys())
        
        # Get available models
        models = [cls.__name__ for cls in MODEL_CLASSES]
        
        logger.info(f"Retrieved {len(features)} features: {features}")
        logger.info(f"Retrieved {len(models)} models: {models}")
        
        if not features or not models:
            raise ValueError("No features or models found")
        
        return {
            "features": features,
            "models": models
        }
    except Exception as e:
        logger.error(f"Error getting workflow options: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(
            status_code=500, 
            detail=f"Could not retrieve workflow options: {str(e)}"
        )

async def execute_workflow_step(joined_data: pd.DataFrame, features: List[str], target: str, model: str, forecast_horizon: int, task_id: str):
    """
    Execute the workflow step in the background.
    """
    try:
        # Initialize StockAnalysisApp with the joined data
        app = StockAnalysisApp(data=joined_data)  # Use data directly

        # Set the features
        app.rerun_features(features)

        # Get the model class
        model_class = next(
            (cls for cls in MODEL_CLASSES if cls.__name__ == model),
            None
        )

        if not model_class:
            raise ValueError(f"Model {model} not found")

        # Run the model
        result, metrics = app.run_model(
            model_class,
            predict=target,
            forecast_horizon=forecast_horizon
        )

        # Generate report
        report_id = await app.generate_report_new()

        # Update task status
        tasks[task_id] = {
            "status": "completed",
            "report_id": report_id,
            "metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error in workflow execution: {str(e)}")
        tasks[task_id] = {
            "status": "failed",
            "error": str(e)
        }

async def run_workflow(request: WorkflowStepRequest, task_id: str):
    """Execute workflow in background."""
    try:
        # Get the data loaders
        left_loader_class = registry.get_loader_class(request.left_loader)
        right_loader_class = registry.get_loader_class(request.right_loader)
        
        if not left_loader_class or not right_loader_class:
            raise ValueError("Invalid data loader specified")
            
        # Initialize loaders with tickers
        left_loader = left_loader_class(request.left_ticker)
        right_loader = right_loader_class(request.right_ticker)
        
        # Load data
        left_df = left_loader.load_historical_data()
        right_df = right_loader.load_historical_data()
        
        if left_df is None or left_df.empty:
            raise ValueError(f"No data available for ticker {request.left_ticker} with loader {request.left_loader}")
            
        if right_df is None or right_df.empty:
            raise ValueError(f"No data available for ticker {request.right_ticker} with loader {request.right_loader}")
        
        # Join data
        if request.join_type == "forward_fill":
            logger.info("Performing forward fill join (merge_asof) for background task")
            logger.debug(f"BG Task - Left DF shape before processing: {left_df.shape}")
            logger.debug(f"BG Task - Right DF shape before processing: {right_df.shape}")
            logger.debug(f"BG Task - Left DF columns before set_index: {left_df.columns.tolist()}")
            logger.debug(f"BG Task - Left DF index name before set_index: {left_df.index.name}")
            logger.debug(f"BG Task - Right DF columns before set_index: {right_df.columns.tolist()}")
            logger.debug(f"BG Task - Right DF index name before set_index: {right_df.index.name}")
            if 'Date' not in left_df.index.name:
                 logger.debug("BG Task - Setting 'Date' as index for left_df")
                 left_df = left_df.set_index('Date')
            if 'Date' not in right_df.index.name:
                 right_df = right_df.set_index('Date')
            
            left_df.index = pd.to_datetime(left_df.index)
            right_df.index = pd.to_datetime(right_df.index)

            left_df = left_df.sort_index()
            right_df = right_df.sort_index()
            
            logger.debug(f"BG Task - Left index min: {left_df.index.min()}, max: {left_df.index.max()}")
            logger.debug(f"BG Task - Right index min: {right_df.index.min()}, max: {right_df.index.max()}")
            
            joined_df = pd.merge_asof(
                left_df,
                right_df,
                left_index=True,
                right_index=True,
                direction='forward',
                suffixes=('', '_sub')
            )
            joined_df = joined_df.reset_index()
            logger.debug(f"BG Task - Merge complete. Joined shape: {joined_df.shape}")
            logger.debug(f"BG Task - Null counts: {joined_df.isna().sum().sum()}")
        else:
            logger.info(f"Performing standard merge with type: {request.join_type} for background task")
            if left_df.index.name == 'Date':
                 left_df = left_df.reset_index()
            if right_df.index.name == 'Date':
                 right_df = right_df.reset_index()

            joined_df = pd.merge(
                left_df,
                right_df,
                on='Date',  # Always join on Date for workflow step standard joins
                how=request.join_type,
                suffixes=('', '_sub')
            )
        
        # Calculate features and run model
        app = StockAnalysisApp(request.left_ticker)  # Use left ticker as primary
        app.data = joined_df
        app.data_loader_name = f"{request.left_loader}+{request.right_loader}"
        app.rerun_features(request.features)
        
        model_class = next(
            (cls for cls in MODEL_CLASSES if cls.__name__ == request.model),
            None
        )
        
        if not model_class:
            raise ValueError(f"Model {request.model} not found")
            
        result, metrics = app.run_model(
            model_class,
            predict=request.target,
            forecast_horizon=request.forecast_horizon
        )
        
        # Create report using factory
        report_data = ReportFactory.create_join_report({
            "main_df": left_df,
            "aux_df": right_df,
            "target_df": result,
            "main_datasource_id": request.left_loader,
            "aux_datasource_id": request.right_loader,
            "join_column": "Date",
            "join_type": request.join_type,
            "features": request.features
        })
        
        # Save report
        repo = await ReportRepository.create()
        report_id = await repo.insert_report(report_data)
        
        tasks[task_id] = {
            "status": "completed",
            "report_id": report_id,
            "message": "Workflow completed successfully"
        }
        
    except Exception as e:
        logger.error(f"Error in workflow execution: {str(e)}")
        tasks[task_id] = {
            "status": "failed",
            "message": f"Workflow failed: {str(e)}"
        }

@router.post("/workflow-step/run")
async def execute_workflow_step_endpoint(request: WorkflowStepRequest, background_tasks: BackgroundTasks):
    task_id = str(uuid.uuid4())
    tasks[task_id] = {"status": "in_progress"}
    background_tasks.add_task(run_workflow, request, task_id)
    return WorkflowResponse(task_id=task_id, message="Workflow started")

@router.get("/workflow-step/status/{task_id}")
async def get_workflow_status(task_id: str):
    task = tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task
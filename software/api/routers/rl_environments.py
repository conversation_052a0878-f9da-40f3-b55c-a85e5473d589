from fastapi import APIRouter, HTTPException, Request, Body, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from pathlib import Path
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import os
import sys
import importlib
import importlib.util
import inspect
import logging
import tempfile
import traceback
from bson import ObjectId
from io import StringIO
import contextlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import RL Environment Repository
from db.rl_environment_repository import RLEnvironmentRepository

# Define the RL environments directory
RL_ENV_DIR = os.path.join(project_root, 'software', 'rl')

router = APIRouter()

class RLEnvironmentCreate(BaseModel):
    """Model for creating a new RL environment"""
    name: str
    description: str
    code: str
    is_active: bool = True

class RLEnvironmentUpdate(BaseModel):
    """Model for updating an existing RL environment"""
    name: Optional[str] = None
    description: Optional[str] = None
    code: Optional[str] = None
    is_active: Optional[bool] = None

@router.get("/")
async def list_environments():
    """List all RL environments"""
    try:
        repo = await RLEnvironmentRepository.create()
        environments = await repo.list_environments()
        return environments
    except Exception as e:
        logger.error(f"Error listing RL environments: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list environments: {str(e)}")

@router.post("/")
async def create_environment(environment: RLEnvironmentCreate):
    """Create a new RL environment"""
    try:
        repo = await RLEnvironmentRepository.create()

        # Convert Pydantic model to dict
        env_data = environment.model_dump()

        # Create environment
        env_id = await repo.create_environment(env_data)

        return {"id": env_id, "message": "Environment created successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating RL environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create environment: {str(e)}")

@router.get("/{env_id}")
async def get_environment(env_id: str):
    """Get a specific RL environment by ID"""
    try:
        repo = await RLEnvironmentRepository.create()
        environment = await repo.get_environment(env_id)

        if not environment:
            raise HTTPException(status_code=404, detail="Environment not found")

        return environment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving RL environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve environment: {str(e)}")

@router.put("/{env_id}")
async def update_environment(env_id: str, update_data: RLEnvironmentUpdate):
    """Update an existing RL environment"""
    try:
        repo = await RLEnvironmentRepository.create()

        # Convert Pydantic model to dict
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}

        if not update_dict:
            return {"message": "No updates provided"}

        # Update environment
        success = await repo.update_environment(env_id, update_dict)

        if not success:
            raise HTTPException(status_code=404, detail="Environment not found or no changes made")

        return {"message": "Environment updated successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating RL environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update environment: {str(e)}")

@router.delete("/{env_id}")
async def delete_environment(env_id: str):
    """Delete an RL environment"""
    try:
        repo = await RLEnvironmentRepository.create()
        success = await repo.delete_environment(env_id)

        if not success:
            raise HTTPException(status_code=404, detail="Environment not found")

        return {"message": "Environment deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting RL environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete environment: {str(e)}")

@router.get("/available-environments")
async def get_available_environments():
    """Get list of available RL environments from the filesystem"""
    try:
        repo = await RLEnvironmentRepository.create()
        environments = await repo.get_available_environments()
        return environments
    except Exception as e:
        logger.error(f"Error getting available environments: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get available environments: {str(e)}")

@router.get("/{env_id}/schema")
async def get_environment_schema(env_id: str):
    """Get schema information about an environment including observation space, action space, and reward mechanism"""
    try:
        # Get the environment using the repository
        repo = await RLEnvironmentRepository.create()
        environment = await repo.get_environment(env_id)

        if not environment:
            raise HTTPException(status_code=404, detail="Environment not found")

        # Create a temporary file with the environment code
        with tempfile.NamedTemporaryFile(suffix='.py', mode='w', delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(environment["code"])

        try:
            # Capture stdout and stderr
            output = StringIO()
            schema_info = {}

            with contextlib.redirect_stdout(output), contextlib.redirect_stderr(output):
                # Import the module
                spec = importlib.util.spec_from_file_location("temp_env_module", temp_file_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # Find the environment class
                env_class = None

                # First try to find classes that directly inherit from gym.Env
                for class_name, obj in inspect.getmembers(module):
                    if inspect.isclass(obj):
                        # Check if this is a gym.Env subclass
                        try:
                            if hasattr(obj, '__mro__'):
                                # Check direct inheritance from gym.Env
                                if any('gym.Env' in str(mro) or 'gymnasium.Env' in str(mro) for mro in obj.__mro__):
                                    env_class = obj
                                    break
                                # Check for inheritance from gym.Env through inheritance chain
                                for mro_class in obj.__mro__:
                                    if 'Env' in mro_class.__name__ and (
                                        'gym' in mro_class.__module__ or
                                        'gymnasium' in mro_class.__module__
                                    ):
                                        env_class = obj
                                        break
                        except Exception as e:
                            logger.warning(f"Error checking MRO for {class_name}: {str(e)}")

                # If no class found, try a more permissive approach
                if not env_class:
                    for class_name, obj in inspect.getmembers(module):
                        if inspect.isclass(obj) and hasattr(obj, 'step') and hasattr(obj, 'reset'):
                            # This looks like an environment class
                            env_class = obj
                            break

                if not env_class:
                    return {
                        "error": "No gym.Env subclass found in the environment code",
                        "output": output.getvalue()
                    }

                # Create an instance of the environment
                try:
                    # Initialize basic schema info from class
                    schema_info = {
                        "name": env_class.__name__,
                        "description": env_class.__doc__ or "No description available",
                    }

                    # Try to create an instance of the environment
                    try:
                        env = env_class()

                        # Safely add standard gym.Env attributes if they exist
                        try:
                            if hasattr(env, 'observation_space'):
                                schema_info["observation_space"] = str(env.observation_space)
                        except Exception as attr_err:
                            logger.warning(f"Error getting observation_space: {str(attr_err)}")

                        try:
                            if hasattr(env, 'action_space'):
                                schema_info["action_space"] = str(env.action_space)
                        except Exception as attr_err:
                            logger.warning(f"Error getting action_space: {str(attr_err)}")

                        try:
                            if hasattr(env, 'metadata'):
                                schema_info["metadata"] = env.metadata
                        except Exception as attr_err:
                            logger.warning(f"Error getting metadata: {str(attr_err)}")

                        try:
                            if hasattr(env, 'reward_range'):
                                schema_info["reward_range"] = str(env.reward_range)
                        except Exception as attr_err:
                            logger.warning(f"Error getting reward_range: {str(attr_err)}")

                        # Try to extract more information from the step method
                        try:
                            step_method = env_class.step
                            if step_method and step_method.__doc__:
                                schema_info["step_method_doc"] = step_method.__doc__

                                # Check if there's a docstring that explains the reward mechanism
                                if "reward" in step_method.__doc__.lower():
                                    schema_info["reward_mechanism"] = step_method.__doc__
                        except Exception as method_err:
                            logger.warning(f"Error getting step method info: {str(method_err)}")

                        # Try to extract training data schema from the code
                        try:
                            if hasattr(env, 'training_data') and env.training_data:
                                # Get a sample from training data if available
                                if isinstance(env.training_data, list) and len(env.training_data) > 0:
                                    schema_info["training_data_sample"] = env.training_data[0]
                        except Exception as data_err:
                            logger.warning(f"Error getting training data sample: {str(data_err)}")

                    except Exception as env_err:
                        logger.warning(f"Error creating environment instance: {str(env_err)}")
                        schema_info["error_creating_instance"] = str(env_err)

                except Exception as e:
                    schema_info = {
                        "error": f"Failed to extract schema information: {str(e)}",
                        "traceback": traceback.format_exc()
                    }

            return {
                "schema": schema_info,
                "output": output.getvalue()
            }
        except Exception as e:
            logger.error(f"Error getting environment schema: {str(e)}")
            return {
                "error": f"Error getting environment schema: {str(e)}",
                "traceback": traceback.format_exc(),
                "output": output.getvalue()
            }
        finally:
            # Clean up the temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting environment schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get environment schema: {str(e)}")

@router.get("/{env_id}/check")
async def check_environment(env_id: str):
    """Check if an environment is valid using stable_baselines3.common.env_checker"""
    try:
        # Get the environment using the repository
        repo = await RLEnvironmentRepository.create()
        environment = await repo.get_environment(env_id)

        if not environment:
            raise HTTPException(status_code=404, detail="Environment not found")

        # Create a temporary file with the environment code
        with tempfile.NamedTemporaryFile(suffix='.py', mode='w', delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(environment["code"])

        try:
            # Capture stdout and stderr
            output = StringIO()
            with contextlib.redirect_stdout(output), contextlib.redirect_stderr(output):
                # Import the module
                spec = importlib.util.spec_from_file_location("temp_env_module", temp_file_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # Find the environment class
                env_class = None

                # First try to find classes that directly inherit from gym.Env
                for class_name, obj in inspect.getmembers(module):
                    if inspect.isclass(obj):
                        # Check if this is a gym.Env subclass
                        try:
                            if hasattr(obj, '__mro__'):
                                # Check direct inheritance from gym.Env
                                if any('gym.Env' in str(mro) or 'gymnasium.Env' in str(mro) for mro in obj.__mro__):
                                    env_class = obj
                                    break
                                # Check for inheritance from gym.Env through inheritance chain
                                for mro_class in obj.__mro__:
                                    if 'Env' in mro_class.__name__ and (
                                        'gym' in mro_class.__module__ or
                                        'gymnasium' in mro_class.__module__
                                    ):
                                        env_class = obj
                                        break
                        except Exception as e:
                            logger.warning(f"Error checking MRO for {class_name}: {str(e)}")

                # If no class found, try a more permissive approach
                if not env_class:
                    for class_name, obj in inspect.getmembers(module):
                        if inspect.isclass(obj) and hasattr(obj, 'step') and hasattr(obj, 'reset'):
                            # This looks like an environment class
                            env_class = obj
                            break

                if not env_class:
                    return {
                        "valid": False,
                        "message": "No gym.Env subclass found in the environment code. Make sure your class inherits from gym.Env or gymnasium.Env.",
                        "output": output.getvalue()
                    }

                # Create an instance of the environment
                env = env_class()

                # Import check_env from stable_baselines3
                from stable_baselines3.common.env_checker import check_env

                # Check the environment
                check_env(env)

                return {
                    "valid": True,
                    "message": "Environment is valid",
                    "output": output.getvalue()
                }
        except Exception as e:
            logger.error(f"Error checking environment: {str(e)}")
            return {
                "valid": False,
                "message": f"Environment check failed: {str(e)}",
                "error": traceback.format_exc(),
                "output": output.getvalue()
            }
        finally:
            # Clean up the temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check environment: {str(e)}")

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import subprocess
import psutil
import socket
import signal
import os
import logging
from letta_client import Letta
from db.research_director_repository import ResearchDirectorRepository
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# Global variable to track server process
server_process = None

class ServerResponse(BaseModel):
    status: str
    message: str

class ServerStatusResponse(BaseModel):
    running: bool
    pid: int = None

class LettaAgent(BaseModel):
    id: str
    name: Optional[str] = None
    director_id: Optional[str] = None
    director_name: Optional[str] = None

class LettaAgentIdResponse(BaseModel):
    letta_agent_id: Optional[str] = None

@router.post("/run-server", response_model=ServerResponse)
async def run_letta_server():
    """Run the Letta server with ADE flag"""
    global server_process

    # Check if server is already running
    status = await check_server_status()
    if status["running"]:
        return {
            "status": "success",
            "message": "Letta server is already running"
        }

    try:
        # Run the letta server command
        process = subprocess.Popen(["letta", "server", "--ade"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE)

        server_process = process.pid

        return {
            "status": "success",
            "message": "Letta server started with ADE flag"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start Letta server: {str(e)}")

@router.get("/server-status", response_model=ServerStatusResponse)
async def get_server_status():
    """Check if the Letta server is running"""
    status = await check_server_status()
    return status

async def check_server_status():
    """Helper function to check if the server is running"""
    global server_process

    # First check if port 8283 is in use
    if is_port_in_use(8283):
        # If we have a stored process ID
        if server_process:
            try:
                # Check if process is still running
                process = psutil.Process(server_process)
                if process.is_running() and "letta" in " ".join(process.cmdline()):
                    return {"running": True, "pid": server_process}
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                server_process = None

        # Check for any running letta server processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and 'letta' in cmdline and 'server' in cmdline:
                    server_process = proc.info['pid']
                    return {"running": True, "pid": server_process}
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        # If port is in use but we couldn't find the process, return running with no PID
        return {"running": True}

    # Port is not in use
    server_process = None
    return {"running": False}

@router.post("/force-stop", response_model=ServerResponse)
async def force_stop_server():
    """Force stop the Letta server by killing processes on port 8283"""
    global server_process

    killed = False

    # First try to kill by stored PID
    if server_process:
        try:
            process = psutil.Process(server_process)
            process.terminate()
            process.wait(timeout=3)
            killed = True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, psutil.TimeoutExpired):
            pass

    # Then try to find and kill any process using port 8283
    for proc in psutil.process_iter(['pid']):
        try:
            for conn in proc.net_connections(kind='inet'):
                if conn.laddr.port == 8283:
                    proc.terminate()
                    try:
                        proc.wait(timeout=3)
                    except psutil.TimeoutExpired:
                        proc.kill()
                    killed = True
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue

    # Finally, try using lsof and kill (Unix/Linux/Mac only)
    if not killed and os.name != 'nt':
        try:
            # Find process using port 8283
            result = subprocess.run(['lsof', '-i', ':8283', '-t'],
                                   capture_output=True, text=True, check=False)
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        os.kill(int(pid), signal.SIGTERM)
                        killed = True
                    except (ProcessLookupError, ValueError):
                        pass
        except Exception:
            pass

    server_process = None

    if killed:
        return {
            "status": "success",
            "message": "Letta server stopped successfully"
        }
    else:
        return {
            "status": "warning",
            "message": "No running Letta server found on port 8283"
        }

def is_port_in_use(port):
    """Check if a port is in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

@router.get("/agents", response_model=List[LettaAgent])
async def list_letta_agents():
    """List all Letta agents and show which directors are linked to them"""
    try:
        # Initialize Letta client
        client = Letta(base_url="http://localhost:8283")

        # Get all agents from Letta server
        agents = client.agents.list()

        # Initialize director repository
        director_repo = ResearchDirectorRepository()

        # Get all directors
        directors = await director_repo.list_directors()

        # Create a mapping of Letta agent IDs to director information
        agent_to_director = {}
        for director in directors:
            letta_agent_id = director.get("memory", {}).get("letta_agent_id")
            if letta_agent_id:
                agent_to_director[letta_agent_id] = {
                    "director_id": director["id"],
                    "director_name": director["name"]
                }

        # Create response with agent and director information
        result = []
        for agent in agents:
            agent_info = {
                "id": agent.id,
                "name": agent.name
            }

            # Add director information if this agent is linked to a director
            if agent.id in agent_to_director:
                agent_info["director_id"] = agent_to_director[agent.id]["director_id"]
                agent_info["director_name"] = agent_to_director[agent.id]["director_name"]

            result.append(agent_info)

        return result
    except Exception as e:
        logger.error(f"Error listing Letta agents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list Letta agents: {str(e)}")

@router.delete("/agents/{agent_id}", response_model=ServerResponse)
async def delete_letta_agent(agent_id: str):
    """Delete a Letta agent by ID"""
    try:
        # Initialize Letta client
        client = Letta(base_url="http://localhost:8283")

        # Delete the agent
        client.agents.delete(agent_id=agent_id)

        # Initialize director repository
        director_repo = ResearchDirectorRepository()

        # Get all directors
        directors = await director_repo.list_directors()

        # Find directors linked to this agent and remove the link
        for director in directors:
            letta_agent_id = director.get("memory", {}).get("letta_agent_id")
            if letta_agent_id == agent_id:
                # Remove the link
                await director_repo.disconnect_from_letta_agent(director["id"])

        return {
            "status": "success",
            "message": f"Successfully deleted Letta agent {agent_id}"
        }
    except Exception as e:
        logger.error(f"Error deleting Letta agent: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete Letta agent: {str(e)}")

@router.get("/directors/{director_id}/agent-id", response_model=LettaAgentIdResponse)
async def get_director_letta_agent_id(director_id: str):
    """Get the Letta agent ID for a director"""
    try:
        # Initialize director repository
        director_repo = ResearchDirectorRepository()

        # Get the Letta agent ID for the director
        letta_agent_id = await director_repo.get_letta_agent_id(director_id)

        return {
            "letta_agent_id": letta_agent_id
        }
    except Exception as e:
        logger.error(f"Error getting Letta agent ID for director {director_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Letta agent ID: {str(e)}")

@router.post("/agents/{agent_id}/reset-messages", response_model=ServerResponse)
async def reset_letta_agent_messages(agent_id: str):
    """Reset all messages for a Letta agent"""
    try:
        # Initialize director repository
        director_repo = ResearchDirectorRepository()

        # Reset the agent's messages
        success = await director_repo.reset_letta_agent_messages(agent_id)

        if success:
            return {
                "status": "success",
                "message": f"Successfully reset messages for Letta agent {agent_id}"
            }
        else:
            return {
                "status": "error",
                "message": f"Failed to reset messages for Letta agent {agent_id}"
            }
    except Exception as e:
        logger.error(f"Error resetting messages for Letta agent {agent_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to reset Letta agent messages: {str(e)}")

@router.get("/agents/{agent_id}/memory-blocks/{block_label}")
async def get_memory_block(agent_id: str, block_label: str):
    """Get a specific memory block from a Letta agent"""
    try:
        # Initialize director repository
        director_repo = ResearchDirectorRepository()

        # Get the memory block
        result = await director_repo.get_memory_block(agent_id, block_label)

        if result.get("status") == "success":
            return result
        else:
            raise HTTPException(status_code=404, detail=result.get("message", "Memory block not found"))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting memory block '{block_label}' for Letta agent {agent_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get memory block: {str(e)}")

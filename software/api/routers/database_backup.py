from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from ..database import create_mongodb_backup, restore_mongodb_backup, list_backups, delete_backup, schedule_daily_backup
import logging
import os
import sys
import importlib.util

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/list")
async def get_backups() -> List[Dict[str, Any]]:
    """List all available database backups"""
    backups = list_backups()
    return backups

@router.post("/create")
async def create_backup(background_tasks: BackgroundTasks, backup_name: Optional[str] = None) -> Dict[str, Any]:
    """Create a new database backup"""
    # Run backup in background to avoid blocking the request
    def run_backup():
        result = create_mongodb_backup(backup_name, backup_type="manual")
        # No logging needed here as create_mongodb_backup handles all output with Rich
        
    background_tasks.add_task(run_backup)
    return {"status": "success", "message": "Backup started in background"}

@router.post("/restore/{backup_file}")
async def restore_backup(backup_file: str, background_tasks: BackgroundTasks) -> Dict[str, Any]:
    """Restore a database from backup"""
    # Check if backup exists before starting the restore
    from ..database import BACKUP_DIR
    
    backup_path = os.path.join(BACKUP_DIR, backup_file)
    if not os.path.exists(backup_path):
        raise HTTPException(status_code=404, detail=f"Backup file '{backup_file}' not found")
    
    # Run restore in background to avoid blocking the request
    def run_restore():
        result = restore_mongodb_backup(backup_file)
        # No logging needed here as restore_mongodb_backup now handles all output with Rich
        
    background_tasks.add_task(run_restore)
    return {"status": "success", "message": "Restore started in background"}

@router.delete("/delete/{backup_file}")
async def remove_backup(backup_file: str) -> Dict[str, Any]:
    """Delete a backup file"""
    success = delete_backup(backup_file)
    if not success:
        raise HTTPException(status_code=404, detail="Backup file not found")
    return {"status": "success", "message": f"Backup {backup_file} deleted successfully"}

@router.post("/schedule")
async def schedule_backup(hour: int = Query(...), minute: int = Query(...)) -> Dict[str, Any]:
    """Schedule a daily backup at the specified time"""
    try:
        # Validate input - must be integers
        if not isinstance(hour, int) or not isinstance(minute, int):
            raise HTTPException(status_code=400, detail="Hour and minute must be integers")
            
        # Validate hour and minute ranges
        if hour < 0 or hour > 23 or minute < 0 or minute > 59:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid time: hour must be 0-23, minute must be 0-59. Got hour={hour}, minute={minute}"
            )
            
        await schedule_daily_backup(hour, minute)
        
        return {
            "status": "success", 
            "message": f"Daily backup scheduled for {hour:02d}:{minute:02d}",
            "scheduled_time": {
                "hour": hour,
                "minute": minute
            }
        }
    except Exception as e:
        logger.error(f"Failed to schedule backup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to schedule backup: {str(e)}")
        
@router.get("/debug-scheduler")
async def debug_scheduler() -> Dict[str, Any]:
    """Debug endpoint to check scheduler status and fix issues"""
    result = {
        "status": "checking",
        "scheduler_info": {},
        "actions_taken": []
    }
    
    # Check if aioschedule is installed
    is_aioschedule_installed = importlib.util.find_spec("aioschedule") is not None
    result["scheduler_info"]["aioschedule_installed"] = is_aioschedule_installed
    
    if not is_aioschedule_installed:
        # Suggest installing with Poetry
        result["actions_taken"].append("aioschedule not installed")
        result["scheduler_info"]["install_instructions"] = "Run 'poetry install --extras \"backups\"' to install the package"
    else:
        # Get aioschedule version if installed
        try:
            import aioschedule
            result["scheduler_info"]["aioschedule_version"] = getattr(aioschedule, "__version__", "unknown")
        except Exception as e:
            result["scheduler_info"]["aioschedule_info_error"] = str(e)
    
    # Check if rich is installed (for nice panel prints)
    is_rich_installed = importlib.util.find_spec("rich") is not None
    result["scheduler_info"]["rich_installed"] = is_rich_installed
    
    # Check for scheduler config file
    from ..database import BACKUP_DIR
    import json
    
    scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")
    result["scheduler_info"]["config_file_exists"] = os.path.exists(scheduler_config_file)
    
    if result["scheduler_info"]["config_file_exists"]:
        try:
            with open(scheduler_config_file, 'r') as f:
                config = json.load(f)
            result["scheduler_info"]["config"] = config
            
            # Add formatted time for easier consumption by frontend
            if "daily_backup" in config and config["daily_backup"].get("enabled", False):
                hour = config["daily_backup"]["hour"]
                minute = config["daily_backup"]["minute"]
                result["scheduler_info"]["formatted_time"] = f"{hour:02d}:{minute:02d}"
                
                # Calculate time until next backup
                try:
                    from datetime import datetime, timedelta
                    
                    now = datetime.now()
                    next_backup = now.replace(hour=hour, minute=minute)
                    
                    # If the next backup time is in the past, add a day
                    if next_backup <= now:
                        next_backup += timedelta(days=1)
                        
                    time_until_next = next_backup - now
                    result["scheduler_info"]["next_backup_in"] = {
                        "hours": time_until_next.seconds // 3600,
                        "minutes": (time_until_next.seconds % 3600) // 60,
                        "seconds": time_until_next.seconds % 60,
                        "total_seconds": time_until_next.total_seconds()
                    }
                    result["scheduler_info"]["next_backup_at"] = next_backup.isoformat()
                except Exception as e:
                    result["scheduler_info"]["next_backup_calculation_error"] = str(e)
        except Exception as e:
            result["scheduler_info"]["config_error"] = str(e)
    
    # Provide some helpful commands
    result["help"] = {
        "schedule_backup": "POST to /api/database-backup/schedule with hour and minute parameters",
        "view_logs": "Check server logs for scheduler-related messages"
    }
    
    result["status"] = "success"
    return result

@router.post("/run-missed-backup-check")
async def run_missed_backup_check() -> Dict[str, Any]:
    """Run the check_missed_backups function to process any missed backups"""
    try:
        from ..database import check_missed_backups
        
        # Run the check
        await check_missed_backups()
        
        return {
            "status": "success",
            "message": "Missed backup check completed successfully"
        }
    except Exception as e:
        logger.error(f"Failed to run missed backup check: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to run missed backup check: {str(e)}"
        } 
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
import os
import logging
from typing import Optional, List, Dict, Any
import asyncio
import subprocess
from datetime import datetime
import shutil
import json
import tarfile
import re
import aioschedule as schedule

# Try to import rich for panel printing
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    has_rich = True
except ImportError:
    has_rich = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set MongoDB logger to WARNING level to suppress informational messages
logging.getLogger('pymongo').setLevel(logging.WARNING)
logging.getLogger('motor').setLevel(logging.WARNING)

load_dotenv()

MONGO_URI = os.getenv("MONGO_URI")
if not MONGO_URI:
    raise ValueError("MONGO_URI environment variable is not set")

# Global database variables
client = AsyncIOMotorClient(MONGO_URI)
database = client.vero
report_collection = database.reports
loader_collection = database.data_loaders
research_analysis_collection = database.research_analysis
research_directors_collection = database.research_directors
llm_collection = database.llm_models
ai_tools_collection = database.ai_tools
feature_requests_collection = database.feature_requests
forecast_revisit_collection = database.forecast_revisits
rl_environments_collection = database.rl_environments
rl_training_collection = database.rl_training

# Backup directory
BACKUP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "db", "backups")
os.makedirs(BACKUP_DIR, exist_ok=True)

async def init_db():
    """Initialize database connection"""
    try:
        # Test the connection
        await client.admin.command('ismaster')
        logger.info("Successfully connected to MongoDB")

        # List all collections
        collections = await database.list_collection_names()
        logger.info(f"Available collections: {collections}")

        # Ensure ai_tools collection exists
        if 'ai_tools' not in collections:
            await database.create_collection('ai_tools')
            logger.info("Created ai_tools collection")

        # Ensure feature_requests collection exists
        if 'feature_requests' not in collections:
            await database.create_collection('feature_requests')
            logger.info("Created feature_requests collection")

        # Create indexes for ai_tools collection
        try:
            # First check if the index already exists with a different name
            existing_indexes = await ai_tools_collection.list_indexes().to_list(None)

            # Check for an index on the 'name' field
            name_index_exists = False
            for idx in existing_indexes:
                for field_name, _ in idx.get('key', {}).items():
                    if field_name == 'name':
                        name_index_exists = True
                        logger.info(f"Index on 'name' field already exists with name: {idx.get('name')}")
                        break
                if name_index_exists:
                    break

            # Only create the index if no index exists on the 'name' field
            if not name_index_exists:
                await ai_tools_collection.create_index("name", name="tool_name_idx", unique=True)
                logger.info("Created index on ai_tools collection")
            else:
                logger.info("Skipping index creation as an index on 'name' field already exists")
        except Exception as e:
            logger.warning(f"Index creation warning: {str(e)}")
            # Continue execution even if index creation fails

    except Exception as e:
        logger.error(f"Failed to initialize MongoDB connection: {str(e)}")
        raise

async def drop_index(collection_name, index_name):
    """Utility function to drop an index from a collection."""
    try:
        collection = database[collection_name]
        await collection.drop_index(index_name)
        logger.info(f"Successfully dropped index '{index_name}' from collection '{collection_name}'")
        return True
    except Exception as e:
        logger.error(f"Error dropping index '{index_name}' from collection '{collection_name}': {str(e)}")
        return False

def create_mongodb_backup(backup_name: Optional[str] = None, backup_type: str = "manual") -> Optional[str]:
    """
    Create a backup of the MongoDB database using mongodump.

    Args:
        backup_name: Optional name for the backup (timestamp will be added)
        backup_type: Type of backup - 'manual', 'scheduled', or 'missed'

    Returns:
        Path to the backup file or None if backup failed
    """
    try:
        # First check if mongodump is available
        if not is_mongodb_tool_available('mongodump'):
            if has_rich:
                console = Console()
                text = Text("mongodump tool not found. Please install MongoDB tools.", style="red")
                panel = Panel(
                    text,
                    title="[bold red]Backup Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error("mongodump tool not found. Please install MongoDB tools.")
            return None

        # Generate timestamp for the backup filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if backup_name:
            backup_name = f"{backup_name}_{timestamp}"
        else:
            backup_name = f"vero_backup_{timestamp}"

        backup_folder = os.path.join(BACKUP_DIR, backup_name)

        # Panel styles based on backup type
        title_styles = {
            "manual": "[bold blue]Manual Backup[/bold blue]",
            "scheduled": "[bold green]Scheduled Backup[/bold green]",
            "missed": "[bold yellow]Missed Backup Recovery[/bold yellow]"
        }

        border_styles = {
            "manual": "blue",
            "scheduled": "green",
            "missed": "yellow"
        }

        title_style = title_styles.get(backup_type, title_styles["manual"])
        border_style = border_styles.get(backup_type, border_styles["manual"])

        # Print start backup panel
        if has_rich:
            console = Console()
            text = Text.assemble(
                Text(f"Creating backup: ", style="white"),
                Text(f"{backup_name}", style="cyan bold")
            )
            panel = Panel(
                text,
                title=title_style,
                border_style=border_style,
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)

        # Run mongodump command
        cmd = [
            "mongodump",
            f"--uri={MONGO_URI}",
            "--quiet",  # Add quiet flag to reduce verbosity
            f"--out={backup_folder}"
        ]

        # Print progress panel
        if has_rich:
            console = Console()
            text = Text("Running mongodump...", style="yellow")
            panel = Panel(
                text,
                title=title_style,
                border_style=border_style,
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)

        # Use subprocess with quiet flag for less verbose output
        result = subprocess.run(cmd, capture_output=True, text=True)

        # Filter stderr to only show actual errors
        stderr_filtered = ""
        if result.stderr:
            for line in result.stderr.splitlines():
                # Skip informational and progress lines
                if any(x in line for x in ["done dumping", "writing ", "creating collection", "creating index"]):
                    continue
                # Keep actual errors and warnings that aren't just deprecation notices
                if "error" in line.lower() or ("warning" in line.lower() and "deprecated" not in line.lower()):
                    stderr_filtered += line + "\n"

            # Only log if there are actual errors
            if stderr_filtered.strip():
                logger.warning(f"mongodump stderr: {stderr_filtered}")

        if result.returncode != 0:
            if has_rich:
                console = Console()
                text = Text.assemble(
                    Text(f"Backup command failed with code: {result.returncode}", style="red"),
                    Text("\n\n", style="white"),
                    Text(f"{stderr_filtered or 'No error details available'}", style="yellow")
                )
                panel = Panel(
                    text,
                    title="[bold red]Backup Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error(f"Backup failed: {stderr_filtered or result.stderr}")
            return None

        # Create a compressed archive
        archive_path = f"{backup_folder}.tar.gz"

        # Change directory to the backup folder's parent and create the archive from there
        original_dir = os.getcwd()
        try:
            os.chdir(BACKUP_DIR)
            # Use relative path for backup_name now that we're in BACKUP_DIR
            with tarfile.open(f"{backup_name}.tar.gz", "w:gz") as tar:
                tar.add(backup_name)
        finally:
            os.chdir(original_dir)

        # Remove the uncompressed backup folder
        shutil.rmtree(backup_folder)

        # Try to extract document count from stderr if available
        backup_stats = "Database backed up successfully"
        if result.stderr:
            doc_match = re.search(r'(\d+) document\(s\)', result.stderr)
            if doc_match:
                backup_stats = f"{doc_match.group(1)} documents backed up"

        # Completion title and border based on backup type
        completion_title_styles = {
            "manual": "[bold green]Manual Backup Completed[/bold green]",
            "scheduled": "[bold green]Scheduled Backup Completed[/bold green]",
            "missed": "[bold green]Missed Backup Recovery Completed[/bold green]"
        }

        completion_title = completion_title_styles.get(backup_type, completion_title_styles["manual"])

        # Show success panel
        if has_rich:
            console = Console()
            text = Text.assemble(
                Text("Backup created: ", style="white"),
                Text(f"{os.path.basename(archive_path)}", style="green bold"),
                Text("\n\n", style="white"),
                Text(f"{backup_stats}", style="cyan"),
                Text("\n", style="white"),
                Text(f"Backup type: {backup_type.capitalize()}", style="magenta")
            )
            panel = Panel(
                text,
                title=completion_title,
                border_style="green",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)
        else:
            logger.info(f"Successfully created backup: {os.path.basename(archive_path)}")

        return os.path.basename(archive_path)

    except Exception as e:
        if has_rich:
            console = Console()
            text = Text(f"Error during backup: {str(e)}", style="red")
            panel = Panel(
                text,
                title="[bold red]Backup Failed[/bold red]",
                border_style="red",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)
        else:
            logger.error(f"Failed to create backup: {str(e)}")
        return None

def is_mongodb_tool_available(tool_name):
    """
    Check if a MongoDB tool (mongodump/mongorestore) is available in the system path.

    Args:
        tool_name: Name of the tool to check (mongodump or mongorestore)

    Returns:
        True if the tool is available, False otherwise
    """
    try:
        result = subprocess.run(['which', tool_name], capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False

def restore_mongodb_backup(backup_file: str) -> bool:
    """
    Restore a MongoDB backup from a compressed archive.

    Args:
        backup_file: Name of the backup file in the backups directory

    Returns:
        True if restore was successful, False otherwise
    """
    try:
        # First check if mongorestore is available
        if not is_mongodb_tool_available('mongorestore'):
            if has_rich:
                console = Console()
                text = Text("mongorestore tool not found. Please install MongoDB tools.", style="red")
                panel = Panel(
                    text,
                    title="[bold red]Restore Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error("mongorestore tool not found. Please install MongoDB tools.")
            return False

        # Print start restore panel
        if has_rich:
            console = Console()
            text = Text.assemble(
                Text(f"Starting restore from: ", style="white"),
                Text(f"{backup_file}", style="yellow bold")
            )
            panel = Panel(
                text,
                title="[bold blue]MongoDB Restore[/bold blue]",
                border_style="blue",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)

        backup_path = os.path.join(BACKUP_DIR, backup_file)

        # Check if the backup file exists
        if not os.path.exists(backup_path):
            if has_rich:
                console = Console()
                text = Text(f"Backup file not found: {backup_file}", style="red")
                panel = Panel(
                    text,
                    title="[bold red]Restore Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error(f"Backup file not found: {backup_path}")
            return False

        # Extract the archive
        backup_name = backup_file.replace('.tar.gz', '')
        extract_path = os.path.join(BACKUP_DIR, "temp_restore")

        # Remove extract path if it exists
        if os.path.exists(extract_path):
            shutil.rmtree(extract_path)

        # Create extract directory
        os.makedirs(extract_path, exist_ok=True)

        # Extract archive
        try:
            shutil.unpack_archive(backup_path, extract_path, 'gztar')
        except Exception as extract_error:
            if has_rich:
                console = Console()
                text = Text(f"Failed to extract archive: {str(extract_error)}", style="red")
                panel = Panel(
                    text,
                    title="[bold red]Restore Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error(f"Failed to extract archive: {str(extract_error)}")
            return False

        # Find vero directory path
        vero_path = os.path.join(extract_path, backup_name, "vero")
        restore_success = False

        if os.path.exists(vero_path) and os.path.isdir(vero_path):
            # Run mongorestore with direct path to the vero directory
            cmd = [
                "mongorestore",
                f"--uri={MONGO_URI}",
                "--drop",  # Drop existing collections before restoring
                "--quiet",  # Add quiet flag to reduce verbosity
                vero_path  # Direct path to the vero directory
            ]

            # Print progress panel
            if has_rich:
                console = Console()
                text = Text("Running mongorestore...", style="yellow")
                panel = Panel(
                    text,
                    title="[bold blue]MongoDB Restore[/bold blue]",
                    border_style="blue",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)

            result = subprocess.run(cmd, capture_output=True, text=True)

            # Filter stderr to only show actual errors
            stderr_filtered = ""
            if result.stderr:
                for line in result.stderr.splitlines():
                    # Skip informational and progress lines
                    if any(x in line for x in ["finished restoring", "restoring ", "index:", "reading metadata",
                                             "building a list", "dropping collection", "document(s) restored",
                                             "restoring indexes", "no indexes to restore"]):
                        continue
                    # Keep actual errors and warnings that aren't just deprecation notices
                    if "error" in line.lower() or ("warning" in line.lower() and "deprecated" not in line.lower()):
                        stderr_filtered += line + "\n"

                # Only log if there are actual errors
                if stderr_filtered.strip():
                    logger.warning(f"mongorestore stderr: {stderr_filtered}")

            if result.returncode == 0:
                restore_success = True
            else:
                if has_rich:
                    console = Console()
                    text = Text.assemble(
                        Text(f"Restore command failed with code: {result.returncode}", style="red"),
                        Text("\n\n", style="white"),
                        Text(f"{stderr_filtered or 'No error details available'}", style="yellow")
                    )
                    panel = Panel(
                        text,
                        title="[bold red]Restore Failed[/bold red]",
                        border_style="red",
                        expand=False,
                        padding=(1, 2)
                    )
                    console.print(panel)
                else:
                    logger.error(f"Restore failed: {stderr_filtered or result.stderr}")
        else:
            # If vero directory not found, search for BSON files
            if has_rich:
                console = Console()
                text = Text("Expected 'vero' directory not found, searching for BSON files...", style="yellow")
                panel = Panel(
                    text,
                    title="[bold yellow]MongoDB Restore[/bold yellow]",
                    border_style="yellow",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)

            bson_found = False
            for root, dirs, files in os.walk(extract_path):
                bson_files = [f for f in files if f.endswith('.bson')]
                if bson_files:
                    # Try restoring from this directory
                    cmd = [
                        "mongorestore",
                        f"--uri={MONGO_URI}",
                        "--drop",
                        "--quiet",  # Add quiet flag to reduce verbosity
                        root
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True)

                    if result.returncode == 0:
                        bson_found = True
                        restore_success = True
                        break

            if not bson_found:
                if has_rich:
                    console = Console()
                    text = Text("No suitable BSON files found in the backup archive", style="red")
                    panel = Panel(
                        text,
                        title="[bold red]Restore Failed[/bold red]",
                        border_style="red",
                        expand=False,
                        padding=(1, 2)
                    )
                    console.print(panel)
                else:
                    logger.error("No suitable BSON files found in the backup archive")
                shutil.rmtree(extract_path)
                return False

        # Clean up
        shutil.rmtree(extract_path)

        # Show success or failure
        if restore_success:
            # Show success info
            restored_items = "Database collections restored successfully"
            if result.stderr:
                # Try to extract document count from stderr if available
                doc_match = re.search(r'(\d+) document\(s\) restored successfully', result.stderr)
                if doc_match:
                    restored_items = f"{doc_match.group(1)} documents restored successfully"

            if has_rich:
                console = Console()
                text = Text.assemble(
                    Text("Database successfully restored from: ", style="white"),
                    Text(f"{backup_file}", style="green bold"),
                    Text("\n\n", style="white"),
                    Text(f"{restored_items}", style="cyan")
                )
                panel = Panel(
                    text,
                    title="[bold green]Restore Completed[/bold green]",
                    border_style="green",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.info(f"Successfully restored backup: {backup_file}")
            return True
        else:
            if has_rich:
                console = Console()
                text = Text("Restore operation failed", style="red")
                panel = Panel(
                    text,
                    title="[bold red]Restore Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error(f"Failed to restore backup: {backup_file}")
            return False

    except Exception as e:
        if has_rich:
            console = Console()
            text = Text(f"Error during restore: {str(e)}", style="red")
            panel = Panel(
                text,
                title="[bold red]Restore Failed[/bold red]",
                border_style="red",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)
        else:
            logger.error(f"Failed to restore backup: {str(e)}")
        return False

def list_backups() -> List[Dict[str, Any]]:
    """
    List all available backups with metadata.

    Returns:
        List of dictionaries with backup information
    """
    try:
        backups = []
        for file in os.listdir(BACKUP_DIR):
            if file.endswith('.tar.gz'):
                file_path = os.path.join(BACKUP_DIR, file)
                file_stats = os.stat(file_path)

                # Extract timestamp from filename
                timestamp_str = file.split('_')[-2] + '_' + file.split('_')[-1].replace('.tar.gz', '')
                try:
                    timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                except ValueError:
                    timestamp = datetime.fromtimestamp(file_stats.st_ctime)

                backups.append({
                    'filename': file,
                    'size': file_stats.st_size,
                    'created_at': timestamp,
                    'size_formatted': format_size(file_stats.st_size)
                })

        # Sort by creation time (newest first)
        backups.sort(key=lambda x: x['created_at'], reverse=True)

        return backups
    except Exception as e:
        logger.error(f"Failed to list backups: {str(e)}")
        return []

def format_size(size_bytes: int) -> str:
    """Format file size in human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024 or unit == 'GB':
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024

def delete_backup(backup_file: str) -> bool:
    """
    Delete a backup file.

    Args:
        backup_file: Name of the backup file to delete

    Returns:
        True if deletion was successful, False otherwise
    """
    try:
        backup_path = os.path.join(BACKUP_DIR, backup_file)

        # Check if the backup file exists
        if not os.path.exists(backup_path):
            if has_rich:
                console = Console()
                text = Text(f"Backup file not found: {backup_file}", style="red")
                panel = Panel(
                    text,
                    title="[bold red]Delete Failed[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.error(f"Backup file not found: {backup_path}")
            return False

        # Print delete start panel
        if has_rich:
            console = Console()
            text = Text.assemble(
                Text(f"Deleting backup: ", style="white"),
                Text(f"{backup_file}", style="yellow bold")
            )
            panel = Panel(
                text,
                title="[bold yellow]MongoDB Backup[/bold yellow]",
                border_style="yellow",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)

        # Delete the file
        os.remove(backup_path)

        # Show success panel
        if has_rich:
            console = Console()
            text = Text.assemble(
                Text("Backup deleted: ", style="white"),
                Text(f"{backup_file}", style="green bold")
            )
            panel = Panel(
                text,
                title="[bold green]Delete Completed[/bold green]",
                border_style="green",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)
        else:
            logger.info(f"Successfully deleted backup: {backup_file}")

        return True

    except Exception as e:
        if has_rich:
            console = Console()
            text = Text(f"Error during delete: {str(e)}", style="red")
            panel = Panel(
                text,
                title="[bold red]Delete Failed[/bold red]",
                border_style="red",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)
        else:
            logger.error(f"Failed to delete backup: {str(e)}")
        return False

async def schedule_daily_backup(hour: int = 1, minute: int = 0, silent: bool = False):
    """
    Schedule a daily backup at the specified time.

    Args:
        hour: Hour of the day to run backup (0-23)
        minute: Minute of the hour to run backup (0-59)
        silent: If True, don't show success message (for rescheduling at startup)
    """
    try:
        # Ensure hour and minute are integers
        hour = int(hour)
        minute = int(minute)

        # Validate ranges
        if hour < 0 or hour > 23:
            error_msg = f"Invalid hour value {hour}, must be between 0-23"
            raise ValueError(error_msg)

        if minute < 0 or minute > 59:
            error_msg = f"Invalid minute value {minute}, must be between 0-59"
            raise ValueError(error_msg)

        # Try to import aioschedule, suggest using poetry if not present
        schedule = None
        try:
            import aioschedule
            schedule = aioschedule
            logger.info("Using existing aioschedule installation")
        except ImportError:
            if has_rich:
                console = Console()
                text = Text.assemble(
                    Text("MONGODB BACKUP SYSTEM - MISSING DEPENDENCY", style="bold white"),
                    Text("\n\n", style="white"),
                    Text("The aioschedule package is required for scheduled backups.", style="yellow"),
                    Text("\n\n", style="white"),
                    Text("To install it using Poetry, run:", style="white"),
                    Text("\n", style="white"),
                    Text("    poetry install --extras \"backups\"", style="green"),
                    Text("\n\n", style="white"),
                    Text("Or run the setup script:", style="white"),
                    Text("\n", style="white"),
                    Text("    ./setup_backup_system.sh", style="green")
                )
                panel = Panel(
                    text,
                    title="[bold red]Dependency Missing[/bold red]",
                    border_style="red",
                    expand=False,
                    padding=(1, 2)
                )
                console.print(panel)
            else:
                logger.warning("aioschedule not installed. Please install using poetry.")
                # Print simple message for non-rich environments
                border = "-" * 70
                print(f"\n{border}")
                print("MONGODB BACKUP SYSTEM - MISSING DEPENDENCY")
                print(f"{border}")
                print("The aioschedule package is required for scheduled backups.")
                print("\nTo install it using Poetry, run:")
                print("    poetry install --extras \"backups\"")
                print("\nOr run the setup script:")
                print("    ./setup_backup_system.sh")
                print(f"{border}\n")

            raise ImportError("aioschedule not installed. Please install with: 'poetry install --extras \"backups\"'")

        if not schedule:
            logger.error("Failed to get aioschedule module")
            raise ImportError("Failed to get aioschedule module")

        # Save the scheduled backup time to a file for persistence
        scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")

        # Read existing config if it exists
        existing_config = {}
        is_new_schedule = True
        if os.path.exists(scheduler_config_file):
            try:
                with open(scheduler_config_file, 'r') as f:
                    existing_config = json.load(f)
                    # Check if this is an update to an existing schedule
                    if 'daily_backup' in existing_config and existing_config['daily_backup'].get('enabled', False):
                        is_new_schedule = False
            except Exception as e:
                logger.warning(f"Error reading existing config: {str(e)}")

        # Create or update config
        if 'daily_backup' not in existing_config:
            # Create new config
            config = {
                "daily_backup": {
                    "hour": hour,
                    "minute": minute,
                    "last_run": datetime.now().isoformat(),
                    "last_scheduled": datetime.now().isoformat(),
                    "enabled": True
                }
            }
        else:
            # Update existing config, preserving last_run
            config = existing_config
            config['daily_backup']['hour'] = hour
            config['daily_backup']['minute'] = minute
            config['daily_backup']['last_scheduled'] = datetime.now().isoformat()
            config['daily_backup']['enabled'] = True

        with open(scheduler_config_file, 'w') as f:
            json.dump(config, f, indent=2)
            logger.info(f"Saved backup schedule configuration to {scheduler_config_file}")

        # Clear existing schedules
        schedule.clear()

        # Convert hour and minute to proper format
        time_str = f"{hour:02d}:{minute:02d}"

        # Define backup function that will be called directly, no wrapper needed
        async def backup_task():
            logger.info(f"[DEBUG] Running scheduled backup at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            try:
                logger.info("Starting scheduled backup task")
                # Create the backup with the "scheduled" type
                backup_name = create_mongodb_backup("scheduled", backup_type="scheduled")

                # Update the last run time in the config file
                scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")
                if os.path.exists(scheduler_config_file):
                    try:
                        with open(scheduler_config_file, 'r') as f:
                            config = json.load(f)

                        config['daily_backup']['last_run'] = datetime.now().isoformat()

                        with open(scheduler_config_file, 'w') as f:
                            json.dump(config, f, indent=2)

                        logger.info(f"Updated last run time in scheduler config")
                    except Exception as update_err:
                        logger.error(f"Failed to update last run time: {str(update_err)}")

                logger.info(f"Scheduled backup completed: {backup_name}")
                return backup_name
            except Exception as e:
                logger.error(f"Error in scheduled backup: {str(e)}")
                return None

        # Schedule the backup directly with the async function
        job = schedule.every().day.at(time_str).do(backup_task)
        logger.info(f"[DEBUG] Job scheduled for {time_str}, next run: {job.next_run}")

        # Show success message
        if has_rich and not silent:
            console = Console()
            if is_new_schedule:
                title = "[bold green]Backup Scheduled[/bold green]"
                message = f"Daily backup scheduled for {hour:02d}:{minute:02d}"
            else:
                title = "[bold blue]Backup Rescheduled[/bold blue]"
                message = f"Daily backup rescheduled for {hour:02d}:{minute:02d}"

            text = Text.assemble(
                Text(message, style="white")
            )
            panel = Panel(
                text,
                title=title,
                border_style="green" if is_new_schedule else "blue",
                expand=False,
                padding=(1, 2)
            )
            console.print(panel)
        else:
            logger.info(f"Scheduled daily backup at {hour:02d}:{minute:02d}")

        # Create a custom scheduler that properly handles async tasks for Python 3.13
        async def custom_scheduler():
            logger.info(f"[DEBUG] Starting custom scheduler loop for Python 3.13")
            while True:
                try:
                    # Manually check if any jobs are due
                    now = datetime.now()

                    # Safely check if jobs should run - avoid None comparison errors
                    due_jobs = []
                    for job in schedule.jobs:
                        try:
                            # Check if the job's next_run time exists and has already passed
                            if job.next_run is not None and now >= job.next_run:
                                due_jobs.append(job)
                                logger.info(f"[DEBUG] Job due: {job}, next_run={job.next_run}, now={now}")
                            else:
                                time_to_next = (job.next_run - now).total_seconds() if job.next_run else "unknown"
                                logger.info(f"[DEBUG] Job not due yet: next_run={job.next_run}, seconds until next run: {time_to_next}")
                        except Exception as job_err:
                            logger.error(f"[DEBUG] Error checking job: {str(job_err)}")

                    # Create tasks for due jobs and run them
                    if due_jobs:
                        logger.info(f"[DEBUG] Running {len(due_jobs)} due jobs at {now.strftime('%Y-%m-%d %H:%M:%S')}")
                        for job in due_jobs:
                            try:
                                # Create a task for the job and run it
                                logger.info(f"[DEBUG] Creating task for job scheduled at {job.at_time}")
                                job_coro = job.job_func()
                                task = asyncio.create_task(job_coro)
                                job.last_run = now
                                job.next_run = job._schedule_next_run()
                                logger.info(f"[DEBUG] Next run scheduled for: {job.next_run}")
                            except Exception as e:
                                logger.error(f"[DEBUG] Error running job: {str(e)}")

                    # Sleep for a bit before checking again
                    await asyncio.sleep(30)  # Check every 30 seconds
                except Exception as e:
                    logger.error(f"[DEBUG] Error in custom scheduler: {str(e)}")
                    await asyncio.sleep(60)  # Wait a bit longer if there's an error

        # Start the custom scheduler task
        logger.info(f"[DEBUG] Starting custom scheduler task")
        asyncio.create_task(custom_scheduler())

    except Exception as e:
        logger.error(f"Failed to schedule daily backup: {str(e)}")
        raise

async def check_missed_backups():
    """
    Check for missed scheduled backups when server starts and run them if needed.
    This ensures at least one backup is performed if the server was down during scheduled times.
    """
    try:
        # First check if required dependencies are installed
        try:
            import aioschedule
        except ImportError:
            logger.info("Skipping missed backup check: aioschedule not installed.")
            return

        scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")

        # If scheduler config doesn't exist, create a default one
        if not os.path.exists(scheduler_config_file):
            logger.info("No scheduled backups configured, creating default configuration")

            # Create a default scheduler config
            default_config = {
                "daily_backup": {
                    "hour": 1,
                    "minute": 0,
                    "last_run": datetime.now().isoformat(),
                    "last_scheduled": datetime.now().isoformat(),
                    "enabled": True
                }
            }

            # Save the default config
            with open(scheduler_config_file, 'w') as f:
                json.dump(default_config, f, indent=2)

            # Schedule the default backup (silently)
            await schedule_daily_backup(1, 0, silent=True)
            return

        # Load the scheduler configuration
        with open(scheduler_config_file, 'r') as f:
            config = json.load(f)

        if 'daily_backup' not in config or not config['daily_backup'].get('enabled', False):
            logger.info("Daily backup not enabled, skipping missed backup check")
            return

        # Get the current schedule (preserve user configuration)
        hour = config['daily_backup'].get('hour', 1)
        minute = config['daily_backup'].get('minute', 0)
        time_str = f"{hour:02d}:{minute:02d}"

        # Get last run time
        last_run_str = config['daily_backup'].get('last_run')
        if not last_run_str:
            logger.warning("No last run time found in scheduler config")

            # Update the config with current time
            config['daily_backup']['last_run'] = datetime.now().isoformat()
            with open(scheduler_config_file, 'w') as f:
                json.dump(config, f, indent=2)

            # Silently schedule with existing hour/minute
            logger.info(f"[DEBUG] Scheduling backup with hour={hour}, minute={minute}")
            await schedule_daily_backup(hour, minute, silent=True)
            return

        try:
            last_run = datetime.fromisoformat(last_run_str)

            # Calculate how many days have passed since last run
            now = datetime.now()
            days_passed = (now - last_run).days

            # Check for both:
            # 1. If more than 1 day has passed, run a backup
            # 2. If today's scheduled backup time has passed but no backup has been run today

            # Create today's scheduled time
            today_scheduled = now.replace(hour=hour, minute=minute, second=0, microsecond=0)

            # Check if last_run was earlier than today
            last_run_date = last_run.date()
            today_date = now.date()

            # We have a missed backup if:
            # a) The last backup was from a previous day AND
            # b) Current time is past today's scheduled backup time
            missed_backup = (last_run_date < today_date and now > today_scheduled)

            logger.info(f"[DEBUG] Missed backup check: last_run={last_run}, today_scheduled={today_scheduled}, now={now}, missed={missed_backup}")

            if days_passed >= 1 or missed_backup:
                # Print message for missed backup notification
                if has_rich:
                    console = Console()

                    if days_passed >= 1:
                        reason = f"Last backup: {last_run.strftime('%Y-%m-%d %H:%M:%S')} ({days_passed} days ago)"
                    else:
                        reason = f"Today's scheduled backup at {time_str} was missed (last backup: {last_run.strftime('%Y-%m-%d %H:%M:%S')})"

                    text = Text.assemble(
                        Text("MISSED BACKUP DETECTED", style="bold white"),
                        Text("\n\n"),
                        Text(reason, style="yellow"),
                    )
                    panel = Panel(
                        text,
                        title="[bold yellow]MongoDB Backup System[/bold yellow]",
                        border_style="yellow",
                        expand=False,
                        padding=(1, 2)
                    )
                    console.print(panel)

                logger.info(f"Detected missed backup. Running missed backup now.")
                backup_name = create_mongodb_backup("missed_scheduled", backup_type="missed")
                if backup_name:
                    # Update the last run time (but preserve hour/minute configuration)
                    config['daily_backup']['last_run'] = now.isoformat()
                    with open(scheduler_config_file, 'w') as f:
                        json.dump(config, f, indent=2)

                    # Print minimized completion message
                    if has_rich:
                        console = Console()
                        text = Text(f"Backup created: {backup_name}", style="green")
                        panel = Panel(
                            text,
                            title="[bold green]Missed Backup Recovery Completed[/bold green]",
                            border_style="green",
                            expand=False,
                            padding=(1, 2)
                        )
                        console.print(panel)

                    logger.info(f"Missed backup completed: {backup_name}")
                else:
                    if has_rich:
                        console = Console()
                        text = Text("Failed to create backup file. Check logs for details.", style="red")
                        panel = Panel(
                            text,
                            title="[bold red]Backup Failed[/bold red]",
                            border_style="red",
                            expand=False,
                            padding=(1, 2)
                        )
                        console.print(panel)

                    logger.error("Failed to create missed backup")
            else:
                logger.info(f"[DEBUG] No missed backups detected: last_run={last_run_date}, today={today_date}, now={now.strftime('%H:%M:%S')}, scheduled={today_scheduled.strftime('%H:%M:%S')}")

            # Schedule the next backup with the user's configured time
            logger.info(f"[DEBUG] Scheduling regular backup with hour={hour}, minute={minute}")
            await schedule_daily_backup(hour, minute, silent=True)

        except Exception as parse_err:
            logger.error(f"Failed to parse last run time: {str(parse_err)}")
            # Still try to schedule backup with configured time
            logger.info(f"[DEBUG] Scheduling backup after error with hour={hour}, minute={minute}")
            await schedule_daily_backup(hour, minute, silent=True)

    except Exception as e:
        logger.error(f"Error checking for missed backups: {str(e)}")

def get_sync_db():
    """Get a synchronous MongoDB database instance."""
    from pymongo import MongoClient
    import os

    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017")
    client = MongoClient(mongo_uri)
    return client.get_database("vero")

# No need to call init_db() here - it will be called in FastAPI startup event

async def schedule_daily_forecast_checks(hour: int = 10, minute: int = 0, silent: bool = False):
    """
    Schedule a daily task to check all due forecast revisits.
    This function reuses the same scheduler infrastructure as the backup system.

    Args:
        hour: Hour of the day to run the check (0-23)
        minute: Minute of the hour to run the check (0-59)
        silent: If True, don't log verbose messages
    """
    try:
        if not silent:
            logger.info(f"Setting up daily forecast revisit checks at {hour:02d}:{minute:02d}")

        # Create time string for scheduler
        time_str = f"{hour:02d}:{minute:02d}"

        # Update the scheduler config file
        scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")

        if os.path.exists(scheduler_config_file):
            try:
                with open(scheduler_config_file, 'r') as f:
                    config = json.load(f)

                # Add forecast_checks config if it doesn't exist
                if "forecast_checks" not in config:
                    config["forecast_checks"] = {
                        "hour": hour,
                        "minute": minute,
                        "last_run": None,
                        "last_scheduled": datetime.now().isoformat(),
                        "enabled": True
                    }
                else:
                    # Update existing configuration
                    config["forecast_checks"]["hour"] = hour
                    config["forecast_checks"]["minute"] = minute
                    config["forecast_checks"]["last_scheduled"] = datetime.now().isoformat()
                    config["forecast_checks"]["enabled"] = True

                # Save the updated config
                with open(scheduler_config_file, 'w') as f:
                    json.dump(config, f, indent=2)

                if not silent:
                    logger.info(f"Updated scheduler config with forecast checks at {time_str}")
            except Exception as e:
                logger.error(f"Error updating scheduler config for forecast checks: {str(e)}")
                # Continue even if config update fails

        # Define the forecast check function
        async def forecast_check_task():
            logger.info(f"Running scheduled forecast revisit checks at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            try:
                # Initialize repository - import here to avoid circular import
                from db.research_repository import ResearchRepository
                repo = ResearchRepository()

                # Run auto-check
                results = await repo.auto_check_due_revisits()

                # Log results
                logger.info(f"Forecast check results: Total: {results['total']}, Success: {results['successful']}, Failed: {results['failed']}")

                # Update the last run time in the config file
                if os.path.exists(scheduler_config_file):
                    try:
                        with open(scheduler_config_file, 'r') as f:
                            config = json.load(f)

                        config['forecast_checks']['last_run'] = datetime.now().isoformat()

                        with open(scheduler_config_file, 'w') as f:
                            json.dump(config, f, indent=2)

                        logger.info(f"Updated last run time for forecast checks in scheduler config")
                    except Exception as update_err:
                        logger.error(f"Failed to update last run time for forecast checks: {str(update_err)}")

                return results
            except Exception as e:
                logger.error(f"Error in scheduled forecast checks: {str(e)}")
                return {"error": str(e)}

        # Schedule the forecast check task with the scheduler
        job = schedule.every().day.at(time_str).do(forecast_check_task)
        logger.info(f"Forecast check job scheduled for {time_str}, next run: {job.next_run}")

    except Exception as e:
        logger.error(f"Failed to schedule daily forecast checks: {str(e)}")
        raise

async def check_missed_forecast_checks():
    """
    Check for missed scheduled forecast revisit checks when server starts and run them if needed.
    This ensures forecasts are properly validated if the server was down during scheduled times.
    """
    try:
        # First check if required dependencies are installed
        try:
            import aioschedule
        except ImportError:
            logger.info("Skipping missed forecast check: aioschedule not installed.")
            return

        scheduler_config_file = os.path.join(BACKUP_DIR, "scheduler_config.json")

        # If scheduler config doesn't exist, create a default one
        if not os.path.exists(scheduler_config_file):
            logger.info("No scheduled forecast checks configured, creating default configuration")
            return

        # Load the scheduler configuration
        with open(scheduler_config_file, 'r') as f:
            config = json.load(f)

        # If forecast_checks section doesn't exist, it means it was never configured
        if 'forecast_checks' not in config:
            # We'll create it when scheduling forecast checks
            return

        if not config['forecast_checks'].get('enabled', False):
            logger.info("Daily forecast checks not enabled, skipping missed check")
            return

        # Get the current schedule
        hour = config['forecast_checks'].get('hour', 10)
        minute = config['forecast_checks'].get('minute', 0)
        time_str = f"{hour:02d}:{minute:02d}"

        # Get last run time
        last_run_str = config['forecast_checks'].get('last_run')
        if not last_run_str:
            logger.warning("No last run time found for forecast checks in scheduler config")

            # Update the config with current time so next check will work correctly
            config['forecast_checks']['last_run'] = datetime.now().isoformat()
            with open(scheduler_config_file, 'w') as f:
                json.dump(config, f, indent=2)

            # Schedule with existing hour/minute
            logger.info(f"[DEBUG] Scheduling forecast checks with hour={hour}, minute={minute}")
            await schedule_daily_forecast_checks(hour, minute, silent=True)
            return

        try:
            last_run = datetime.fromisoformat(last_run_str)

            # Calculate how many days have passed since last run
            now = datetime.now()
            days_passed = (now - last_run).days

            # Create today's scheduled time
            today_scheduled = now.replace(hour=hour, minute=minute, second=0, microsecond=0)

            # Check if last_run was earlier than today
            last_run_date = last_run.date()
            today_date = now.date()

            # We have a missed check if:
            # a) The last check was from a previous day AND
            # b) Current time is past today's scheduled check time
            missed_check = (last_run_date < today_date and now > today_scheduled)

            logger.info(f"[DEBUG] Missed forecast check: last_run={last_run}, today_scheduled={today_scheduled}, now={now}, missed={missed_check}")

            if days_passed >= 1 or missed_check:
                # Log that we detected a missed check
                if days_passed >= 1:
                    reason = f"Last forecast check: {last_run.strftime('%Y-%m-%d %H:%M:%S')} ({days_passed} days ago)"
                else:
                    reason = f"Today's scheduled forecast check at {time_str} was missed (last run: {last_run.strftime('%Y-%m-%d %H:%M:%S')})"

                logger.info(f"Detected missed forecast check. {reason}. Running missed check now.")

                # Run the missed check - import ResearchRepository here to avoid circular import
                from db.research_repository import ResearchRepository
                repo = ResearchRepository()
                results = await repo.auto_check_due_revisits()

                # Log results
                logger.info(f"Missed forecast check results: Total: {results['total']}, Success: {results['successful']}, Failed: {results['failed']}")

                # Update the last run time (but preserve hour/minute configuration)
                config['forecast_checks']['last_run'] = now.isoformat()
                with open(scheduler_config_file, 'w') as f:
                    json.dump(config, f, indent=2)

                logger.info(f"Missed forecast check completed and config updated")
            else:
                logger.info(f"[DEBUG] No missed forecast checks detected: last_run={last_run_date}, today={today_date}, now={now.strftime('%H:%M:%S')}, scheduled={today_scheduled.strftime('%H:%M:%S')}")

            # Schedule the next check with the configured time
            logger.info(f"[DEBUG] Scheduling regular forecast check with hour={hour}, minute={minute}")
            await schedule_daily_forecast_checks(hour, minute, silent=True)

        except Exception as parse_err:
            logger.error(f"Failed to parse last run time for forecast checks: {str(parse_err)}")
            # Still try to schedule forecast check with configured time
            logger.info(f"[DEBUG] Scheduling forecast check after error with hour={hour}, minute={minute}")
            await schedule_daily_forecast_checks(hour, minute, silent=True)

    except Exception as e:
        logger.error(f"Error checking for missed forecast checks: {str(e)}")

# Update init_scheduled_tasks function to also check for missed forecast checks
async def init_scheduled_tasks():
    """Initialize all scheduled tasks when the application starts"""
    try:
        # Check for missed backups
        await check_missed_backups()

        # Check for missed forecast checks
        await check_missed_forecast_checks()

        # Schedule daily database backups (the backup check will handle this)

        # Schedule daily forecast revisit checks (the forecast check will handle this)

        logger.info("All scheduled tasks initialized")
    except Exception as e:
        logger.error(f"Error initializing scheduled tasks: {str(e)}")
        # Don't crash the app if scheduling fails

from pydantic import BaseModel, Field
from typing import Dict, Any, List
from datetime import datetime

class WorkflowMetadata(BaseModel):
    data_loader: str
    features: List[str]
    start_date: str
    end_date: str
    model: Dict[str, Any]  # Update to include model details

class ReportData(BaseModel):
    timeseries: List[Dict[str, Any]]

class ReportModel(BaseModel):
    id: str
    ticker: str
    workflow: WorkflowMetadata
    performance: Dict[str, Any]
    data: ReportData
    created_at: datetime

    class Config:
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

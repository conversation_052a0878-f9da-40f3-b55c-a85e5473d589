from fastapi import APIRouter, HTTPException, Request
from typing import Callable, List, Optional, Union, Dict, Any
from dataclasses import dataclass
from .routers import (
    simple_workflow,
    simple_join_workflow,
    simple_report,
    data_loaders,
    todo,
    pages,
    add_data_loader,
    research_agents,
    toolbox,
    llm,
    graphs,
    extract_workflow,
    database_backup,
    add_agent,
    feature_requests,
    server_restart,
    forecast_revisits,
    letta,
    rl_environments,
    backtest_strategy,
    backtest_strategy_eval
)
# Import the RL training router
from .routers.rl_training import router as rl_training_router
from .database import database
from bson import ObjectId
import json
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

logger.debug("Initializing URL patterns...")

@dataclass
class Path:
    route: str
    endpoint: Callable
    methods: Optional[List[str]] = None
    name: Optional[str] = None

    def __post_init__(self):
        if self.methods is None:
            self.methods = ["GET"]
        if self.name is None:
            self.name = self.endpoint.__name__

class Include:
    def __init__(self, router: APIRouter, prefix: str = ""):
        self.router = router
        self.prefix = prefix
        logger.debug(f"Including router with prefix: {prefix}")

class URLPattern:
    def __init__(self):
        self.api_router = APIRouter()
        logger.debug("Created API Router")

    def path(self, route: str, endpoint: Callable, methods: Optional[List[str]] = None, name: Optional[str] = None) -> Path:
        logger.debug(f"Adding path: {route} with methods: {methods}")
        return Path(route, endpoint, methods, name)

    def include(self, router: APIRouter, prefix: str = "") -> Include:
        logger.debug(f"Including router at prefix: {prefix}")
        return Include(router, prefix)

    def register_routes(self, patterns: List[Union[Path, Include]]):
        logger.debug("Registering routes...")
        for pattern in patterns:
            if isinstance(pattern, Path):
                logger.debug(f"Registering path: {pattern.route}")
                self.api_router.add_api_route(
                    pattern.route,
                    pattern.endpoint,
                    methods=pattern.methods,
                    name=pattern.name
                )
            elif isinstance(pattern, Include):
                logger.debug(f"Including router at: {pattern.prefix}")
                self.api_router.include_router(
                    pattern.router,
                    prefix=pattern.prefix
                )
        logger.debug("Routes registered successfully")

# Initialize URL patterns
urls = URLPattern()

# Define URL patterns (Django-style)
urlpatterns = [
    # Include routers with prefixes
    urls.include(pages.router, prefix=""),
    urls.include(simple_workflow.router, prefix="/api/simple-workflow"),
    urls.include(simple_join_workflow.router, prefix="/api/simple-join-workflow"),
    urls.include(extract_workflow.router, prefix="/api/extract-workflow"),
    urls.include(simple_report.router, prefix="/api/reports"),
    urls.include(data_loaders.router, prefix="/api/data-loaders"),
    urls.include(todo.router, prefix="/api/todos"),
    urls.include(add_data_loader.router, prefix="/api/add-data-loader"),
    urls.include(research_agents.router, prefix="/api/research-agents"),
    urls.include(add_agent.router, prefix="/api/add-agent"),
    urls.include(toolbox.router, prefix="/api/toolbox"),
    urls.include(llm.router, prefix="/api/llm"),
    urls.include(graphs.router, prefix="/api/graphs"),
    urls.include(database_backup.router, prefix="/api/database-backup"),
    urls.include(feature_requests.router, prefix=""),  # No prefix since routes already include /api/feature-requests
    urls.include(server_restart.router, prefix=""),  # Server restart router
    urls.include(forecast_revisits.router, prefix=""),  # No prefix since routes already include /api/forecast-revisits
    urls.include(letta.router, prefix="/api/letta"),  # Simplified Letta router
    urls.include(rl_environments.router, prefix="/api/rl-environments"),  # RL Environments router
    urls.include(rl_training_router, prefix="/api/rl-training"),  # RL Training router (fixed version)
    urls.include(backtest_strategy.router, prefix="/api/backtest-strategy"),  # Backtest Strategy router
    urls.include(backtest_strategy_eval.router, prefix="/api/backtest-strategy-eval"),  # Backtest Strategy Evaluation router

    # Letta endpoints
    urls.path("/api/letta/directors/{director_id}/agent-id", letta.get_director_letta_agent_id, ["GET"], "get_director_letta_agent_id"),
    urls.path("/api/letta/agents/{agent_id}/reset-messages", letta.reset_letta_agent_messages, ["POST"], "reset_letta_agent_messages"),
    urls.path("/api/letta/agents/{agent_id}/memory-blocks/{block_label}", letta.get_memory_block, ["GET"], "get_memory_block"),

    # Server restart endpoint
    urls.path("/restart-server", pages.restart_server, ["POST"], "restart_server"),

    # Simple Workflow paths
    urls.path("/api/simple-workflow", simple_workflow.run_simple_workflow, ["POST"], "run_simple_workflow"),
    urls.path("/api/simple-workflow/status/{task_id}", simple_workflow.get_workflow_status, ["GET"], "get_workflow_status"),
    urls.path("/api/options/features", simple_workflow.get_features, ["GET"], "get_features"),
    urls.path("/api/options/data-loaders", simple_workflow.get_data_loaders, ["GET"], "get_data_loaders"),
    urls.path("/api/options/models", simple_workflow.get_models, ["GET"], "get_models"),
    urls.path("/api/options/columns", simple_workflow.get_columns, ["POST"], "get_columns"),

    # Data Loader paths
    urls.path("/api/data-loaders", data_loaders.list_data_loaders, ["GET"], "list_data_loaders"),
    urls.path("/api/data-loaders/{loader_name}", data_loaders.get_data_loader_code, ["GET"], "get_data_loader_code"),
    urls.path("/api/data-loaders/{loader_name}", data_loaders.save_data_loader_code, ["POST"], "save_data_loader_code"),
    urls.path("/api/data-loaders/delete/{loader_id}", data_loaders.delete_data_loader, ["DELETE"], "delete_data_loader"),
    urls.path("/api/data-loaders/{loader_id}/details", data_loaders.get_data_loader_details, ["GET"], "get_data_loader_details"),
    urls.path("/api/data-loaders/{loader_id}/toggle", data_loaders.toggle_data_loader_status, ["PATCH"], "toggle_data_loader"),
    urls.path("/api/run-tests", data_loaders.run_loader_tests, ["POST"], "run_loader_tests"),

    # Data Loader Builder paths
    urls.path("/api/add-data-loader/generate-code", add_data_loader.generate_data_loader_code, ["POST"], "generate_data_loader_code"),
    urls.path("/api/add-data-loader/resume-generation", add_data_loader.resume_data_loader_generation, ["POST"], "resume_data_loader_generation"),

    # API Key Management paths
    urls.path("/api/data-loaders/{loader_id}/api-keys", data_loaders.add_api_key, ["POST"], "add_api_key"),
    urls.path("/api/data-loaders/{loader_id}/api-keys/{key_name}", data_loaders.update_api_key, ["PUT"], "update_api_key"),
    urls.path("/api/data-loaders/{loader_id}/api-keys/{key_name}", data_loaders.delete_api_key, ["DELETE"], "delete_api_key"),
    urls.path("/api/data-loaders/{loader_id}/api-keys/{key_name}/toggle", data_loaders.toggle_api_key, ["PATCH"], "toggle_api_key"),

    # Todo paths
    urls.path("/api/todos", todo.list_todo_items, ["GET"], "list_todo_items"),
    urls.path("/api/todos", todo.create_todo_item, ["POST"], "create_todo_item"),
    urls.path("/api/todos/{todo_id}", todo.get_todo_item, ["GET"], "get_todo_item"),
    urls.path("/api/todos/{todo_id}", todo.update_todo_item, ["PUT"], "update_todo_item"),
    urls.path("/api/todos/{todo_id}", todo.delete_todo_item, ["DELETE"], "delete_todo_item"),
    urls.path("/api/todos/order", todo.update_task_order, ["POST"], "update_task_order"),

    # Feature Request paths
    urls.path("/api/feature-requests/{request_id}/toggle", feature_requests.toggle_feature_request_status, ["PATCH"], "toggle_feature_request_status"),
    urls.path("/api/feature-requests/{request_id}", feature_requests.delete_feature_request, ["DELETE"], "delete_feature_request"),

    # Research Agents paths
    urls.path("/api/research-agents/directors", research_agents.list_directors, ["GET"], "list_directors"),
    urls.path("/api/research-agents/directors", research_agents.create_director, ["POST"], "create_director"),
    urls.path("/api/research-agents/directors/{director_id}", research_agents.get_director, ["GET"], "get_director"),
    urls.path("/api/research-agents/directors/{director_id}/deploy", research_agents.deploy_director, ["POST"], "deploy_director"),
    urls.path("/api/research-agents/reports/{task_id}", research_agents.get_report, ["GET"], "get_report"),
    urls.path("/api/research-agents/tasks/{task_id}/status", research_agents.get_task_status, ["GET"], "get_task_status"),
    urls.path("/research-agents/{director_id}", pages.research_agent_detail_page, ["GET"], "research_agent_detail_page"),
    urls.path("/api/research-agents/tasks/{task_id}/workflow", research_agents.get_workflow_stages, ["GET"], "get_workflow_stages"),
    urls.path("/api/research-agents/directors/{director_id}/graph", research_agents.update_director_graph, ["PATCH"], "update_director_graph"),
    urls.path("/api/research-agents/directors/{director_id}/auto-deploy", research_agents.toggle_auto_deploy, ["PATCH"], "toggle_auto_deploy"),
    urls.path("/api/research-agents/directors/{director_id}/auto-deploy-stats", research_agents.get_auto_deploy_stats, ["GET"], "get_auto_deploy_stats"),
    urls.path("/api/research-agents/directors/{director_id}/reset-in-progress", research_agents.reset_in_progress_counter, ["POST"], "reset_in_progress_counter"),
    urls.path("/api/research-agents/directors/{director_id}/clean-memory", research_agents.clean_director_memory, ["POST"], "clean_director_memory"),

    # Graph paths
    urls.path("/api/graphs/available", graphs.list_available_graphs, ["GET"], "list_available_graphs"),
    urls.path("/api/graphs/llm-models", graphs.list_llm_models, ["GET"], "list_llm_models"),
    urls.path("/api/graphs/graphs", graphs.list_graphs, ["GET"], "list_graphs"),
    urls.path("/api/graphs/graphs", graphs.create_graph, ["POST"], "create_graph"),
    urls.path("/api/graphs/graphs/{graph_id}", graphs.get_graph, ["GET"], "get_graph"),
    urls.path("/api/graphs/graphs/{graph_id}/stages", graphs.update_graph_stages, ["PUT"], "update_graph_stages"),
    urls.path("/api/graphs/graphs/{graph_id}/run", graphs.run_graph, ["POST"], "run_graph"),

    # Database Backup paths
    urls.path("/api/database-backup/list", database_backup.get_backups, ["GET"], "get_backups"),
    urls.path("/api/database-backup/create", database_backup.create_backup, ["POST"], "create_backup"),
    urls.path("/api/database-backup/restore/{backup_file}", database_backup.restore_backup, ["POST"], "restore_backup"),
    urls.path("/api/database-backup/delete/{backup_file}", database_backup.delete_backup, ["DELETE"], "delete_backup"),
    urls.path("/api/database-backup/schedule", database_backup.schedule_backup, ["POST"], "schedule_backup"),

    # LLM Test endpoint
    urls.path("/api/llm/llm/{llm_id}/test", llm.test_llm, ["POST"], "test_llm"),

    # Forecast Revisits paths
    urls.path("/api/forecast-revisits/director/{director_id}", forecast_revisits.delete_director_forecast_revisits, ["DELETE"], "delete_director_forecast_revisits"),
]

logger.debug("Registering all routes...")
# Register all routes
urls.register_routes(urlpatterns)

logger.debug("Routes registered, exporting router...")
# Export the router
api_router = urls.api_router
logger.debug("URL configuration complete")

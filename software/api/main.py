from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
import os
from pathlib import Path
from dotenv import load_dotenv
from .urls import api_router
from .routers import pages
from db.research_director_repository import ResearchDirectorRepository
from db.research_repository import ResearchRepository
from db.todo_repository import TodoRepository
from .database import init_db, schedule_daily_backup, check_missed_backups, init_scheduled_tasks
from ai.tools import registry
import logging
import asyncio
import importlib.util

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

app = FastAPI()

# Mount static files
static_dir = Path(__file__).parent.parent / "frontend"
app.mount("/frontend", StaticFiles(directory=static_dir), name="frontend")

# Mount server-restart static files
server_restart_dir = static_dir / "server-restart"
app.mount("/server-restart", StaticFiles(directory=server_restart_dir), name="server-restart")

# Setup templates
templates = Jinja2Templates(directory=str(static_dir))

# Include API router
app.include_router(api_router)

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    try:
        logger.info("Starting application initialization...")
        
        # Check for required dependencies
        try:
            # Basic terminal colors for highlighting
            def highlight(message, color="yellow"):
                colors = {
                    "red": "\033[91m",
                    "green": "\033[92m",
                    "yellow": "\033[93m", 
                    "blue": "\033[94m",
                    "bold": "\033[1m",
                    "reset": "\033[0m"
                }
                if color in colors:
                    return f"{colors[color]}{message}{colors['reset']}"
                return message
                
            # Check for aioschedule
            aioschedule_installed = importlib.util.find_spec("aioschedule") is not None
            if not aioschedule_installed:
                border = "-" * 70
                print(f"\n{border}")
                print(highlight("MONGODB BACKUP SYSTEM - DEPENDENCY CHECK", "bold"))
                print(f"{border}")
                print(highlight("WARNING: aioschedule package is not installed.", "red"))
                print("Daily backups will not work until dependencies are installed.")
                print("\nTo install using Poetry, run:")
                print(highlight("    poetry install --extras \"backups\" --no-root", "green"))
                print("\nOr run the setup script:")
                print(highlight("    ./setup_backup_system.sh", "green"))
                print(highlight("\nIMPORTANT: After installing, restart the server using:", "yellow"))
                print(highlight("    poetry run python software/run_web.py", "green"))
                print(f"{border}\n")
                logger.warning("aioschedule package is missing. Daily backups disabled.")
            
            # Check for rich (optional but recommended)
            rich_installed = importlib.util.find_spec("rich") is not None
            if not rich_installed:
                logger.info("rich package is not installed. Console output will use plain formatting.")
                print(highlight("TIP: Install rich for better console output:", "blue"))
                print(highlight("    poetry install --extras \"backups\" --no-root", "green"))
                print(highlight("    poetry run python software/run_web.py", "green"))
        except Exception as e:
            logger.warning(f"Dependency check warning: {str(e)}")
        
        # Initialize database connection first
        try:
            await init_db()
            logger.info("Database connection initialized")
        except Exception as e:
            # Check if this is an index conflict error
            if "Index already exists with a different name" in str(e):
                logger.warning(f"Database initialization warning (continuing): {str(e)}")
            else:
                # For other errors, re-raise
                raise
        
        # Check for missed scheduled backups
        try:
            await check_missed_backups()
            logger.info("Missed backup check completed")
        except Exception as e:
            logger.warning(f"Failed to check for missed backups: {str(e)}")
            # Continue execution even if check fails
        
        # Initialize scheduled tasks (including forecast checks)
        try:
            await init_scheduled_tasks()
            logger.info("Scheduled tasks initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize scheduled tasks: {str(e)}")
            # Continue execution even if initialization fails
        
        # Create indexes for research repository
        try:
            research_repo = ResearchRepository()
            await research_repo.create_indexes()
            logger.info("Research repository indexes created")
        except Exception as e:
            if "Index already exists with a different name" in str(e):
                logger.warning(f"Research repository index warning (continuing): {str(e)}")
            else:
                raise
        
        # Create indexes for director repository
        try:
            director_repo = ResearchDirectorRepository()
            await director_repo.create_indexes()
            logger.info("Director repository indexes created")
        except Exception as e:
            if "Index already exists with a different name" in str(e):
                logger.warning(f"Director repository index warning (continuing): {str(e)}")
            else:
                raise
        
        # Create indexes for todo repository
        try:
            todo_repo = TodoRepository()
            await todo_repo.init_indexes()
            logger.info("Todo repository indexes created")
        except Exception as e:
            if "Index already exists with a different name" in str(e):
                logger.warning(f"Todo repository index warning (continuing): {str(e)}")
            else:
                raise
        
        # Load tools
        try:
            await registry.load_tools()
            logger.info("Tools loaded")
        except Exception as e:
            if "Index already exists with a different name" in str(e):
                logger.warning(f"Tool loading warning (continuing): {str(e)}")
            else:
                raise
        
        # We don't need to force schedule the daily backup here since check_missed_backups already does it
        # The hardcoded values here were overriding the user's preferences
        
        logger.info("Application initialization complete")
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise

# The frontend routes have been consolidated in pages.py
# and are included in the app via urls.py

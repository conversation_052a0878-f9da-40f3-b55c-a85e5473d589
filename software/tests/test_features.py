import unittest
import pandas as pd
import numpy as np
import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from features import FEATURE_CLASSES
from features.base_feature import Feature

class TestFeatures(unittest.TestCase):
    def setUp(self):
        # Create sample data
        dates = pd.date_range(start='2022-01-01', end='2022-12-31', freq='D')
        self.data = pd.DataFrame({
            'Open': np.random.randn(len(dates)) + 100,
            'High': np.random.randn(len(dates)) + 101,
            'Low': np.random.randn(len(dates)) + 99,
            'Close': np.random.randn(len(dates)) + 100,
            'Volume': np.random.randint(1000, 10000, len(dates)),
            'title': np.random.choice([
                "Earnings fall short of expectations",
                "New product line boosts company profile",
                "Tech stocks soar amid market rally",
                "Regulatory changes impact revenues",
                "Market analysis: A bearish outlook"], len(dates))
        }, index=dates)
        self.data.name = 'SAMPLE'  # Set a name for the DataFrame

    def test_feature_structure(self):
        for name, feature_class in FEATURE_CLASSES.items():
            with self.subTest(feature=name):
                # Test class name
                self.assertTrue(name in FEATURE_CLASSES, f"{name} should be a known feature name")

                # Test required methods
                self.assertTrue(hasattr(feature_class, 'calculate'),
                                f"{name} missing 'calculate' method")

                # Test for description (if implemented)
                if hasattr(feature_class, 'get_description'):
                    description = feature_class.get_description()
                    self.assertIsInstance(description, str,
                                          f"{name}.get_description() should return a string")

    def test_feature_calculation(self):
        for name, feature_class in FEATURE_CLASSES.items():
            with self.subTest(feature=name):
                feature = feature_class()
                result = feature.calculate(self.data)
                self.assertIsNotNone(result, f"{name} calculation returned None")
                if isinstance(result, pd.DataFrame):
                    self.assertEqual(len(result), len(self.data),
                                     f"{name} result length doesn't match input data")
                elif isinstance(result, pd.Series):
                    self.assertEqual(len(result), len(self.data),
                                     f"{name} result length doesn't match input data")

    def test_feature_description(self):
        for name, feature_class in FEATURE_CLASSES.items():
            with self.subTest(feature=name):
                if hasattr(feature_class, 'get_description'):
                    description = feature_class.get_description()
                    self.assertIsInstance(description, str,
                                          f"{name}.get_description() should return a string")

if __name__ == '__main__':
    unittest.main()

import unittest
import pandas as pd
import numpy as np
from datetime import datetime
from software.data.base_data import BaseDataLoader

class MockDataLoader(BaseDataLoader):
    def __init__(self, ticker: str, include_additional: bool = False):
        super().__init__(ticker)
        if include_additional:
            # Example of adding Twitter sentiment data
            self.additional_columns = {
                'TweetCount': lambda x: (x >= 0).all(),  # Must be non-negative
                'Sentiment': lambda x: x.between(-1, 1).all(),  # Must be between -1 and 1
                'Mentions': None  # No validation needed
            }

    def load_historical_data(self) -> pd.DataFrame:
        dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
        data = {
            'Open': np.random.uniform(100, 110, len(dates)),
            'High': np.random.uniform(110, 120, len(dates)),
            'Low': np.random.uniform(90, 100, len(dates)),
            'Close': np.random.uniform(100, 110, len(dates)),
            'Volume': np.random.randint(1000, 10000, len(dates))
        }
        
        if self.additional_columns:
            data.update({
                'TweetCount': np.random.randint(0, 1000, len(dates)),
                'Sentiment': np.random.uniform(-1, 1, len(dates)),
                'Mentions': np.random.randint(0, 100, len(dates))
            })
        
        df = pd.DataFrame(data, index=dates)
        # Ensure High >= Low for all rows
        df['High'] = df[['High', 'Low', 'Open', 'Close']].max(axis=1)
        return df

class TestBaseDataLoader(unittest.TestCase):
    def setUp(self):
        self.ticker = "AAPL"
        self.loader = MockDataLoader(self.ticker)
        self.enhanced_loader = MockDataLoader(self.ticker, include_additional=True)

    def test_required_columns(self):
        """Test that required columns are present and valid"""
        data = self.loader.load_historical_data()
        self.assertTrue(all(col in data.columns for col in self.loader.required_columns))

    def test_additional_columns(self):
        """Test handling of additional columns with validation"""
        data = self.enhanced_loader.load_historical_data()
        self.assertTrue('TweetCount' in data.columns)
        self.assertTrue('Sentiment' in data.columns)
        self.assertTrue('Mentions' in data.columns)
        
        # Verify validation works
        data.loc[0, 'TweetCount'] = -1  # Invalid value
        with self.assertRaises(ValueError):
            self.enhanced_loader._validate_data(data)

    def test_price_validation(self):
        """Test price relationship validation"""
        data = self.loader.load_historical_data()
        # Deliberately break price relationship
        data.loc[data.index[0], 'High'] = data.loc[data.index[0], 'Low'] - 1
        with self.assertRaises(ValueError):
            self.loader._validate_data(data)

    def test_empty_data(self):
        """Test handling of empty DataFrames"""
        empty_df = self.loader._prepare_empty_df()
        self.assertTrue(empty_df.empty)
        self.assertTrue(all(col in empty_df.columns for col in self.loader.required_columns))

    def test_timezone_handling(self):
        """Test timezone handling"""
        data = self.loader.load_historical_data()
        # Add timezone
        data.index = data.index.tz_localize('UTC')
        naive_data = self.loader._ensure_timezone_naive(data)
        self.assertIsNone(naive_data.index.tz)

if __name__ == '__main__':
    unittest.main()

import unittest
import pandas as pd
import numpy as np
import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import MODEL_CLASSES

class TestModels(unittest.TestCase):
    def setUp(self):
        # Create sample data
        dates = pd.date_range(start='2022-01-01', end='2022-12-31', freq='D')
        np.random.seed(42)  # for reproducibility
        self.data = pd.DataFrame({
            'Date': dates,
            'Target': np.cumsum(np.random.randn(len(dates))) + 100,
            'Feature1': np.random.randn(len(dates)),
            'Feature2': np.random.randn(len(dates)),
            'Feature3': np.sin(np.linspace(0, 4*np.pi, len(dates))),
            'Feature4': np.random.randint(1, 100, len(dates)),
            'Feature5': np.cumsum(np.random.randn(len(dates))),
            'NonNumeric': ['A' if i % 2 == 0 else 'B' for i in range(len(dates))]
        })
        self.data.set_index('Date', inplace=True)

        # Add some correlation between features and target
        self.data['Target'] += 0.5 * self.data['Feature1'] + 0.3 * self.data['Feature3'] + 0.2 * self.data['Feature5']

    def test_model_structure(self):
        for model_class in MODEL_CLASSES:
            model = model_class()
            self.assertTrue(hasattr(model, 'run'), f"{model_class.__name__} missing 'run' method")

    def test_model_run(self):
        for model_class in MODEL_CLASSES:
            model = model_class()
            result, metrics = model.run(self.data, predict_column='Target', forecast_horizon=5)

            self.assertIsInstance(result, pd.DataFrame, f"{model_class.__name__} result is not a DataFrame")
            self.assertIsInstance(metrics, dict, f"{model_class.__name__} metrics is not a dictionary")

            self.assertGreaterEqual(len(result), len(self.data),
                                    f"{model_class.__name__} result does not include all original data")

            self.assertIn('Target_Predicted', result.columns,
                          f"{model_class.__name__} result does not include 'Target_Predicted' column")

            non_null_forecasts = result['Target_Predicted'].dropna()
            self.assertGreaterEqual(len(non_null_forecasts), 5,
                                    f"{model_class.__name__} does not have at least 5 forecast values")

    def test_forecast_horizon(self):
        for model_class in MODEL_CLASSES:
            model = model_class()
            forecast_horizon = 15
            result, _ = model.run(self.data, predict_column='Target', forecast_horizon=forecast_horizon)

            self.assertGreaterEqual(len(result) - len(self.data), forecast_horizon,
                                    f"{model_class.__name__}: Expected at least {forecast_horizon} future predictions")

            future_predictions = result['Target_Predicted'].iloc[-forecast_horizon:]
            if not future_predictions.isnull().all():
                self.assertGreater(future_predictions.nunique(), 1,
                                   f"{model_class.__name__}: Future predictions are constant, expected varying values")

            if len(result) > len(self.data):
                expected_last_date = self.data.index[-1] + pd.Timedelta(days=forecast_horizon)
                self.assertEqual(result.index[-1], expected_last_date,
                                 f"{model_class.__name__}: Expected last date to be {expected_last_date}, but got {result.index[-1]}")

    def test_non_numeric_data_handling(self):
        for model_class in MODEL_CLASSES:
            model = model_class()
            result, metrics = model.run(self.data, predict_column='Target', forecast_horizon=5)

            # Check that non-numeric column is not used in the model
            if hasattr(model, 'feature_importance'):
                self.assertNotIn('NonNumeric', model.feature_importance.index,
                                 f"{model_class.__name__} incorrectly used non-numeric feature")

    def test_non_numeric_target_handling(self):
        for model_class in MODEL_CLASSES:
            model = model_class()
            with self.assertRaises(ValueError):
                model.run(self.data, predict_column='NonNumeric', forecast_horizon=5)

    def test_predict_column_not_used_as_feature(self):
        for model_class in MODEL_CLASSES:
            model = model_class()
            result, _ = model.run(self.data, predict_column='Target', forecast_horizon=5)
            if hasattr(model, 'feature_importance'):
                self.assertNotIn('Target', model.feature_importance.index,
                                 f"{model_class.__name__} incorrectly used predict column as a feature")

    def test_minimum_feature_requirement(self):
        for model_class in MODEL_CLASSES:
            model = model_class()

            # Test with sufficient features
            result, _ = model.run(self.data, predict_column='Target', forecast_horizon=5)
            self.assertIsNotNone(result, f"{model_class.__name__} failed with sufficient features")

            # Test with insufficient features
            insufficient_data = self.data[['Target', 'Feature1']]
            with self.assertRaises(ValueError):
                model.run(insufficient_data, predict_column='Target', forecast_horizon=5)

if __name__ == '__main__':
    unittest.main()

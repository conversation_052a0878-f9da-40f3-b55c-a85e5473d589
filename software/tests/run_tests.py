import unittest
import os
import sys

# Add the parent directory of 'tests' to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def run_tests():
    # Use the current directory (which should be the 'tests' directory)
    tests_dir = current_dir

    # Discover and run tests
    loader = unittest.TestLoader()
    suite = loader.discover(tests_dir)

    runner = unittest.TextTestRunner()
    result = runner.run(suite)

    return result

if __name__ == '__main__':
    result = run_tests()
    if result.wasSuccessful():
        print("All tests passed!")
        sys.exit(0)
    else:
        print("Some tests failed.")
        sys.exit(1)

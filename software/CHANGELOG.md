# Changelog

## [Unreleased]

### Added
- Created AI module structure with subfolders: graphs, llm, agents, and tools
- Implemented centralized Groq LLM configuration in `ai/llm/groq_config.py`
  - Added singleton GroqLLMConfig class for managing Groq interactions
  - Implemented configurable completion settings
- Implemented `researcher_agent_graph.py` using LangGraph and Groq LLM
  - Added research agent graph implementation with state management
  - Integrated Groq LLM for processing research queries
  - Implemented basic workflow with query processing capabilities
- Created `researcher_agent.py` in agents folder
  - Implemented ResearcherAgent class with conversation history management
  - Added methods for query processing and history management
- Converted `test_agent.py` into an interactive chatbot
  - Added simple command-line interface
  - Implemented commands for quitting and clearing history
  - Added error handling for robustness
- Added `test_agent.py` with comprehensive test suite
  - Included tests for agent creation, execution, and state transitions
  - Added test cases for basic research query processing
- Implemented recursion limit in `process_query` methods in `researcher_agent.py` and `researcher_agent_graph.py` to prevent infinite recursion.
- Added logging to track query processing depth.
- Documented changes made to the process_query functions in CHANGELOG.md.

### Changed
- Refactored `researcher_agent_graph.py` to use centralized Groq LLM configuration
- Moved agent logic from graphs to dedicated agents folder
- Refactored `researcher_agent_graph.py` to use `StateGraph` with proper START and END handling
- Updated `researcher_agent_graph.py` to use current langgraph API without START/END constants
- Added proper end state handling in `researcher_agent_graph.py` with dedicated end node
- Simplified state graph in `researcher_agent_graph.py` to use boolean conditionals with None end state
- Rewrote state graph in `researcher_agent_graph.py` to use router function for simpler end state handling
- Updated state graph in `researcher_agent_graph.py` to use dedicated should_end node
- Simplified graph structure in `researcher_agent_graph.py` to use a single conditional edge
- Further simplified graph in `researcher_agent_graph.py` to use direct edges and empty dict return
- Rewrote graph in `researcher_agent_graph.py` to use boolean-based should_continue function
- Simplified graph in `researcher_agent_graph.py` to use self-looping end node
- Updated `process_query` in `researcher_agent.py` to include a recursion depth check.
- Updated `process_query` in `researcher_agent_graph.py` to include a recursion depth check and end conversation conditions.

### Fixed
- Added proper end node action in `ai/graphs/researcher_agent_graph.py` to fix graph compilation error
- Refactored graph in `ai/graphs/researcher_agent_graph.py` to use conditional edges for proper end state handling

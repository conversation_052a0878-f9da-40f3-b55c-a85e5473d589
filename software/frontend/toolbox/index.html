{% extends "base.html" %}

{% block title %}AI Toolbox{% endblock %}

{% block header %}AI Toolbox{% endblock %}

{% block extra_head %}
<!-- Monaco Editor -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs/loader.min.js"></script>
<style>
    .editor-container {
        height: 400px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    [x-cloak] { display: none !important; }
    
    /* Monaco editor custom styles */
    .monaco-editor .margin,
    .monaco-editor .monaco-editor-background {
        @apply bg-slate-50 dark:bg-slate-900;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto p-6 space-y-6">
    <!-- Debug Info -->
    <div id="debug-info" class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4 border-2 border-red-500">
        <h3 class="text-sm font-bold mb-2 text-red-600">Debug Info:</h3>
        <div class="grid grid-cols-2 gap-2 mb-2">
            <div>
                <span class="font-medium">API Status:</span> <span id="debug-api-status">Pending...</span>
            </div>
            <div>
                <span class="font-medium">Tools Count:</span> <span id="debug-tools-count">0</span>
            </div>
            <div>
                <span class="font-medium">Last Updated:</span> <span id="debug-timestamp">-</span>
            </div>
            <div>
                <span class="font-medium">Rendering:</span> <span id="debug-rendering">Not Started</span>
            </div>
        </div>
        <div class="mb-2">
            <span class="font-medium">API Errors:</span> <span id="debug-api-errors">None</span>
        </div>
        <div class="mb-2">
            <span class="font-medium">Tool Names:</span> <span id="debug-tool-names">-</span>
        </div>
        <div>
            <button onclick="window.toolbox.loadTools()" class="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs mb-2">
                Reload Tools
            </button>
            <button onclick="toggleDebugPanel()" class="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 text-xs ml-2">
                Toggle Details
            </button>
        </div>
        <div id="debug-details" class="hidden">
            <pre id="debug-tools" class="text-xs overflow-auto max-h-40 bg-white dark:bg-gray-900 p-2 rounded"></pre>
        </div>
    </div>

    <!-- Header Section -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-semibold tracking-tight text-slate-900 dark:text-slate-50">
            <div class="flex items-center gap-2">
                <i class="fas fa-tools text-slate-600 dark:text-slate-400"></i>
                <span>AI Tools</span>
            </div>
        </h2>
        <a href="/add-tool" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-slate-50 bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i>
            <span>Add Tool</span>
        </a>
    </div>
    
    <!-- System Description -->
    <div class="bg-white dark:bg-slate-950 p-4 rounded-lg border border-slate-200 dark:border-slate-800 shadow-sm">
        <p class="text-sm text-slate-600 dark:text-slate-400">
            Tools are stored as Python files in the <code class="bg-slate-100 dark:bg-slate-800 px-1 py-0.5 rounded">ai/tools</code> directory.
            Each tool is loaded dynamically when the application starts. Toggle tools on/off using the switch icon.
        </p>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Panel: Tools List -->
        <div class="lg:col-span-1 flex flex-col">
            <div class="rounded-lg border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 shadow-sm flex-1">
                <div class="p-4 border-b border-slate-200 dark:border-slate-800 flex justify-between items-center">
                    <h3 class="text-sm font-medium text-slate-900 dark:text-slate-50">Available Tools</h3>
                    <button onclick="window.toolbox.loadTools()" class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div id="tools-list" class="divide-y divide-slate-200 dark:divide-slate-800">
                    <!-- Tools will be inserted here by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Right Panel: Tool Details and Code Editor -->
        <div class="lg:col-span-2 flex flex-col">
            <div class="rounded-lg border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 shadow-sm h-full">
                <!-- Tool Details -->
                <div id="tool-details" class="p-5 border-b border-slate-200 dark:border-slate-800">
                    <div class="text-center text-slate-500 dark:text-slate-400 py-6">
                        <i class="fas fa-arrow-left text-lg mb-2"></i>
                        <p>Select a tool from the list to view details</p>
                    </div>
                </div>

                <!-- Code Editor -->
                <div class="relative">
                    <div id="editor-container" class="hidden">
                        <div class="px-5 py-3 bg-slate-50 dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 flex justify-between items-center">
                            <h4 class="text-sm font-medium text-slate-900 dark:text-slate-50 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                </svg>
                                Source Code
                            </h4>
                        </div>
                        <div id="editor" class="h-[calc(100vh-28rem)] min-h-[400px] w-full rounded-md overflow-hidden"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notification -->
    <div id="notification" class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transition-all duration-300 hidden"></div>
</div>

<script>
    // Function to toggle debug details visibility
    function toggleDebugPanel() {
        const detailsPanel = document.getElementById('debug-details');
        if (detailsPanel.classList.contains('hidden')) {
            detailsPanel.classList.remove('hidden');
        } else {
            detailsPanel.classList.add('hidden');
        }
    }
    
    // Debug function to display tools data
    function updateDebugInfo(data) {
        // Update timestamp
        const debugTimestamp = document.getElementById('debug-timestamp');
        debugTimestamp.textContent = data.timestamp || new Date().toISOString();
        
        // Update API status
        const debugApiStatus = document.getElementById('debug-api-status');
        if (data.error) {
            debugApiStatus.textContent = 'Error: ' + data.error;
            debugApiStatus.className = 'text-red-600 font-bold';
            
            // Show API errors
            const debugApiErrors = document.getElementById('debug-api-errors');
            debugApiErrors.textContent = data.error;
            debugApiErrors.className = 'text-red-600';
        } else if (data.warning) {
            debugApiStatus.textContent = 'Warning: ' + data.warning;
            debugApiStatus.className = 'text-yellow-600 font-bold';
        } else if (data.success) {
            debugApiStatus.textContent = 'Success';
            debugApiStatus.className = 'text-green-600 font-bold';
        } else if (data.status) {
            debugApiStatus.textContent = data.status;
            debugApiStatus.className = 'text-blue-600';
        }
        
        // Update tools count
        const debugToolsCount = document.getElementById('debug-tools-count');
        if (data.count !== undefined) {
            debugToolsCount.textContent = data.count;
        } else if (data.tools && Array.isArray(data.tools)) {
            debugToolsCount.textContent = data.tools.length;
        } else if (Array.isArray(data)) {
            debugToolsCount.textContent = data.length;
        } else {
            debugToolsCount.textContent = 'N/A';
        }
        
        // Update tool names
        const debugToolNames = document.getElementById('debug-tool-names');
        if (data.tools && Array.isArray(data.tools) && data.tools.length > 0) {
            debugToolNames.textContent = data.tools.map(t => t.name).join(', ');
        } else if (Array.isArray(data) && data.length > 0) {
            debugToolNames.textContent = data.map(t => t.name).join(', ');
        } else {
            debugToolNames.textContent = 'No tools found';
        }
        
        // Update rendering status
        const debugRendering = document.getElementById('debug-rendering');
        if (document.getElementById('tools-list').children.length > 0) {
            debugRendering.textContent = `Rendered ${document.getElementById('tools-list').children.length} tools`;
            debugRendering.className = 'text-green-600';
        } else {
            debugRendering.textContent = 'No tools rendered';
            debugRendering.className = 'text-red-600';
        }
        
        // Update full JSON
        const debugTools = document.getElementById('debug-tools');
        if (data.raw_response) {
            debugTools.textContent = data.raw_response;
        } else {
            debugTools.textContent = JSON.stringify(data, null, 2);
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', () => {
        if (window.toolbox && window.toolbox.loadTools) {
            window.toolbox.loadTools();
        } else {
            updateDebugInfo({error: 'Toolbox JS not loaded properly'});
        }
    });
</script>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
<script src="/frontend/toolbox/js/monaco.js"></script>
<script src="/frontend/toolbox/js/toolbox.js"></script>
{% endblock %} 
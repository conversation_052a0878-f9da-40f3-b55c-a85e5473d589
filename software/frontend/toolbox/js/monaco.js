// Monaco Editor Initialization
require.config({ paths: { 'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs' }});

// Initialize Monaco editor
require(['vs/editor/editor.main'], function() {
    // Function to initialize the editor
    function initEditor() {
        const editorContainer = document.getElementById('editor');
        if (!editorContainer) {
            console.error('Editor container not found');
            return;
        }
        
        // Create editor
        window.monacoEditor = monaco.editor.create(editorContainer, {
            language: 'python',
            theme: document.documentElement.classList.contains('dark') ? 'vs-dark' : 'vs',
            readOnly: true,
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            fontSize: 14,
            fontFamily: 'Menlo, Monaco, "Courier New", monospace',
            wordWrap: 'on'
        });
        
        // Force layout update after creation
        setTimeout(() => {
            if (window.monacoEditor) {
                window.monacoEditor.layout();
            }
        }, 100);
    }
    
    // Initialize the editor
    initEditor();
    
    // Resize editor when window resizes
    window.addEventListener('resize', function() {
        if (window.monacoEditor) {
            window.monacoEditor.layout();
        }
    });
    
    // Handle dark mode changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.attributeName === 'class') {
                const isDarkMode = document.documentElement.classList.contains('dark');
                monaco.editor.setTheme(isDarkMode ? 'vs-dark' : 'vs');
            }
        });
    });
    
    observer.observe(document.documentElement, { attributes: true });
}); 
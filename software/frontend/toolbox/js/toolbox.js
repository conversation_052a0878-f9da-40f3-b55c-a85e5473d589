// Initial load of tools
async function loadTools() {
    // Update debug panel with loading status
    updateDebugInfo({status: "Loading tools...", timestamp: new Date().toISOString()});
    
    try {
        const startTime = performance.now();
        const response = await fetch('/api/toolbox/list');
        const endTime = performance.now();
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to load tools: ${response.status} - ${errorText}`);
        }
        
        const tools = await response.json();
        
        // Check if tools is empty or not an array
        if (!Array.isArray(tools)) {
            updateDebugInfo({
                error: "Invalid data format - not an array", 
                data: tools,
                timestamp: new Date().toISOString()
            });
            return [];
        }
        
        if (tools.length === 0) {
            updateDebugInfo({
                warning: "No tools returned from API", 
                timestamp: new Date().toISOString()
            });
        }
        
        // Update debug info and render tools list
        updateDebugInfo({
            success: true,
            count: tools.length,
            tools: tools,
            timestamp: new Date().toISOString()
        });
        
        renderToolsList(tools);
        
        return tools;
    } catch (error) {
        // Try to fetch the raw response to diagnose API issues
        try {
            const rawResponse = await fetch('/api/toolbox/list');
            const rawText = await rawResponse.text();
            updateDebugInfo({
                error: error.message,
                raw_response: rawText.substring(0, 500) + (rawText.length > 500 ? '...' : ''),
                timestamp: new Date().toISOString()
            });
        } catch (e) {
            updateDebugInfo({
                error: error.message,
                fetch_error: e.message,
                timestamp: new Date().toISOString()
            });
        }
        
        return [];
    }
}

// Handle tool click
async function handleToolClick(toolId) {
    try {
        const response = await fetch(`/api/toolbox/${toolId}`);
        if (!response.ok) {
            throw new Error(`Failed to load tool: ${response.status}`);
        }
        
        const tool = await response.json();
        
        // Render tool details
        renderToolDetails(tool);
        
        // Show editor container first
        const editorContainer = document.getElementById('editor-container');
        editorContainer.classList.remove('hidden');
        
        // Initialize code editor if needed
        if (window.monacoEditor) {
            window.monacoEditor.setValue(tool.source_code || '');
            // Force layout update to ensure editor is visible
            window.monacoEditor.layout();
        } else {
            console.error('Monaco editor not initialized');
        }
    } catch (error) {
        showNotification(`Error: ${error.message}`, 'error');
    }
}

// Toggle tool enabled state
async function toggleTool(toolId, event) {
    // Prevent clicking the toggle from also triggering the tool click
    event.stopPropagation();
    
    try {
        const response = await fetch(`/api/toolbox/${toolId}/toggle`, {
            method: 'PATCH'
        });
        
        if (!response.ok) {
            throw new Error(`Failed to toggle tool: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Show notification
        showNotification(`Tool ${toolId} ${result.is_enabled ? 'enabled' : 'disabled'}`);
        
        // Reload tools to refresh the list
        await loadTools();
        
    } catch (error) {
        showNotification(`Error: ${error.message}`, 'error');
    }
}

// Render tool details
function renderToolDetails(tool) {
    const detailsContainer = document.getElementById('tool-details');
    const enabledStatus = tool.enabled !== false ? 
        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">Enabled</span>' : 
        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">Disabled</span>';
    
    // Get parameters for listing
    const params = tool.parameters || {};
    const paramKeys = Object.keys(params);
    
    // Generate parameters HTML
    let paramsHtml = '';
    if (paramKeys.length > 0) {
        paramsHtml = `
            <div class="mt-6">
                <h4 class="text-sm font-medium text-slate-900 dark:text-slate-50 mb-3 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Parameters
                </h4>
                <div class="bg-white dark:bg-slate-950 border border-slate-200 dark:border-slate-800 rounded-lg shadow-sm divide-y divide-slate-200 dark:divide-slate-800">
                    ${paramKeys.map(key => {
                        const param = params[key];
                        const requiredBadge = param.required ? 
                            '<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">Required</span>' : 
                            '<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300">Optional</span>';
                        
                        return `
                            <div class="p-4">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-center">
                                        <span class="font-medium text-slate-900 dark:text-slate-50">${key}</span>
                                        ${requiredBadge}
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-md bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 font-mono">${param.type || 'any'}</span>
                                </div>
                                <p class="mt-1.5 text-sm text-slate-600 dark:text-slate-400">${param.description || 'No description'}</p>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }
    
    detailsContainer.innerHTML = `
        <div class="space-y-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
                <div class="flex items-center gap-2">
                    <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-slate-900 dark:text-slate-50">${tool.name}</h3>
                </div>
                <div class="flex gap-2">
                    ${enabledStatus}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                        ${tool.category}
                    </span>
                </div>
            </div>
            
            <div class="bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-800 rounded-lg p-4">
                <p class="text-sm text-slate-700 dark:text-slate-300">${tool.description}</p>
                <div class="flex items-center mt-3 pt-3 border-t border-slate-200 dark:border-slate-800 text-xs text-slate-500 dark:text-slate-400">
                    <div class="flex items-center mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        Version: <span class="ml-1 font-medium">${tool.version}</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                        </svg>
                        Parameters: <span class="ml-1 font-medium">${paramKeys.length}</span>
                    </div>
                </div>
            </div>
            
            ${paramsHtml}
        </div>
    `;
}

// Function to show notifications
function showNotification(message, type = 'success') {
    const notificationContainer = document.getElementById('notification');
    if (!notificationContainer) {
        return;
    }
    
    notificationContainer.className = 'fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transition-all duration-300';
    
    if (type === 'success') {
        notificationContainer.classList.add('bg-green-100', 'dark:bg-green-800', 'text-green-800', 'dark:text-green-100');
    } else {
        notificationContainer.classList.add('bg-red-100', 'dark:bg-red-800', 'text-red-800', 'dark:text-red-100');
    }
    
    notificationContainer.textContent = message;
    
    // Show notification
    notificationContainer.classList.remove('hidden');
    
    // Hide after 3 seconds
    setTimeout(() => {
        notificationContainer.classList.add('hidden');
    }, 3000);
}

// Function to render tools list
function renderToolsList(tools) {
    const toolsList = document.getElementById('tools-list');
    
    // Check if element exists
    if (!toolsList) {
        return;
    }
    
    // Empty state handling
    if (!Array.isArray(tools) || tools.length === 0) {
        toolsList.innerHTML = `
            <div class="p-4 text-center text-slate-500 dark:text-slate-400">
                <i class="fas fa-exclamation-circle text-xl mb-2"></i>
                <p>No tools available</p>
                <p class="text-sm mt-2">Try reloading or check server logs</p>
            </div>
        `;
        
        // Update debug rendering status
        const debugRendering = document.getElementById('debug-rendering');
        if (debugRendering) {
            debugRendering.textContent = 'No tools to render';
            debugRendering.className = 'text-yellow-600';
        }
        return;
    }
    
    try {
        // Map tools to HTML and join
        const toolsHtml = tools.map(tool => {
            const enabledClass = tool.enabled !== false ? 
                'text-green-500 dark:text-green-400' : 
                'text-red-500 dark:text-red-400';
            const enabledIcon = tool.enabled !== false ? 
                'fa-toggle-on' : 
                'fa-toggle-off';
            
            return `
                <div class="p-4 hover:bg-slate-50 dark:hover:bg-slate-900 transition-colors">
                    <div class="flex items-center justify-between">
                        <a href="javascript:void(0)"
                           onclick="window.toolbox.handleToolClick('${tool._id}')"
                           class="flex items-center gap-2 text-slate-900 dark:text-slate-50 hover:text-slate-600 dark:hover:text-slate-400">
                            <i class="fas fa-wrench text-slate-400"></i>
                            <div>
                                <div class="font-medium">${tool.name}</div>
                                <div class="text-sm text-slate-500 dark:text-slate-400">${tool.category}</div>
                            </div>
                        </a>
                        <button onclick="window.toolbox.toggleTool('${tool._id}', event)" 
                                class="text-2xl ${enabledClass} hover:opacity-80 transition-opacity">
                            <i class="fas ${enabledIcon}"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        // Update DOM
        toolsList.innerHTML = toolsHtml;
        
        // Update debug rendering status
        const debugRendering = document.getElementById('debug-rendering');
        if (debugRendering) {
            debugRendering.textContent = `Rendered ${tools.length} tools`;
            debugRendering.className = 'text-green-600';
        }
    } catch (error) {
        toolsList.innerHTML = `
            <div class="p-4 text-center text-red-500">
                <i class="fas fa-exclamation-triangle text-xl mb-2"></i>
                <p>Error rendering tools: ${error.message}</p>
            </div>
        `;
        
        // Update debug rendering status
        const debugRendering = document.getElementById('debug-rendering');
        if (debugRendering) {
            debugRendering.textContent = `Error: ${error.message}`;
            debugRendering.className = 'text-red-600';
        }
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', async () => {
    await loadTools();
});

// Export functions for global use
window.toolbox = {
    handleToolClick,
    loadTools,
    toggleTool
}; 
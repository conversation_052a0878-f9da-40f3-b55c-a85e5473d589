{% extends "base.html" %}

{% block title %}Extract Workflow{% endblock %}

{% block content %}
<div class="min-h-screen bg-background dark:bg-slate-950 transition-colors duration-300">
    <div class="container mx-auto py-10 px-4 sm:px-6 lg:px-8 space-y-8">
        <!-- Header -->
        <div class="flex flex-col gap-3">
            <h1 class="text-4xl font-bold tracking-tight text-slate-900 dark:text-slate-50">Extract Workflow</h1>
            <p class="text-base text-slate-500 dark:text-slate-400">
                Extract financial data with optional date range for snapshot analysis
            </p>
        </div>

        <!-- Main Form Card -->
        <div class="rounded-xl border bg-card dark:bg-slate-900 text-card-foreground shadow-lg dark:shadow-slate-900/50 hover:shadow-xl transition-shadow duration-300">
            <div class="p-6 sm:p-8">
                <form id="extractForm" class="space-y-6">
                    <!-- Ticker Input -->
                    <div class="space-y-2">
                        <label for="ticker" class="text-sm font-medium text-slate-900 dark:text-slate-200">
                            Ticker Symbol
                        </label>
                        <div class="relative">
                            <input 
                                type="text" 
                                id="ticker" 
                                name="ticker"
                                class="flex h-11 w-full rounded-md border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900"
                                placeholder="Enter ticker symbol (e.g., AAPL)"
                                required
                            />
                            <div id="tickerValidationIcon" class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <!-- Icon will be inserted here by JS -->
                            </div>
                        </div>
                        <p id="tickerError" class="text-sm text-red-500 dark:text-red-400 hidden"></p>
                        
                        <!-- Ticker Metadata Card -->
                        <div id="tickerMetadata" class="mt-4 rounded-lg border border-slate-200 dark:border-slate-800 bg-slate-50 dark:bg-slate-900/50 p-4 hidden transform transition-all duration-200 hover:border-slate-300 dark:hover:border-slate-700">
                            <h4 class="text-sm font-semibold text-slate-900 dark:text-slate-200 mb-3">Available Data</h4>
                            <div class="space-y-2 text-sm text-slate-500 dark:text-slate-400">
                                <p id="dateRange" class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <span></span>
                                </p>
                                <p id="dataPoints" class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <span></span>
                                </p>
                                <p id="frequency" class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span></span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Data Loader Select -->
                    <div class="space-y-2">
                        <label for="dataLoader" class="text-sm font-medium text-slate-900 dark:text-slate-200">
                            Data Source
                        </label>
                        <select 
                            id="dataLoader" 
                            name="dataLoader"
                            class="flex h-11 w-full rounded-md border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900"
                            required
                        >
                            <option value="">Select a data source</option>
                        </select>
                    </div>

                    <!-- Date Range Section -->
                    <div class="grid gap-6 md:grid-cols-2">
                        <!-- Start Date -->
                        <div class="space-y-2">
                            <label for="startDate" class="text-sm font-medium text-slate-900 dark:text-slate-200">
                                Start Date & Time
                            </label>
                            <input 
                                type="datetime-local" 
                                id="startDate" 
                                name="startDate"
                                step="1"
                                class="flex h-11 w-full rounded-md border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900"
                            />
                        </div>

                        <!-- End Date -->
                        <div class="space-y-2">
                            <label for="endDate" class="text-sm font-medium text-slate-900 dark:text-slate-200">
                                End Date & Time
                            </label>
                            <input 
                                type="datetime-local" 
                                id="endDate" 
                                name="endDate"
                                step="1"
                                class="flex h-11 w-full rounded-md border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900"
                            />
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button 
                        type="submit"
                        class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90 h-11 px-8 transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
                    >
                        Extract Data
                    </button>
                </form>
            </div>
        </div>

        <!-- Result Section -->
        <div id="result" class="hidden transform transition-all duration-300">
            <div class="rounded-xl border bg-card dark:bg-slate-900 text-card-foreground shadow-lg dark:shadow-slate-900/50 p-6">
                <div class="flex flex-col gap-3">
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-50">Extraction Status</h3>
                    <div id="resultMessage" class="text-sm text-slate-500 dark:text-slate-400"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let tickerValidationTimeout;
let tickerMetadata = null;

document.addEventListener('DOMContentLoaded', async () => {
    // Load data loaders
    const response = await fetch('/api/options/data-loaders');
    const data = await response.json();
    const dataLoaderSelect = document.getElementById('dataLoader');
    
    data.data_loaders.forEach(loader => {
        const option = document.createElement('option');
        option.value = loader;
        option.textContent = loader;
        dataLoaderSelect.appendChild(option);
    });

    // Add ticker validation
    const ticker = document.getElementById('ticker');
    const dataLoader = document.getElementById('dataLoader');
    
    // Debounced ticker validation
    function debouncedValidateTicker() {
        clearTimeout(tickerValidationTimeout);
        tickerValidationTimeout = setTimeout(validateTicker, 500);
    }
    
    // Validate ticker
    async function validateTicker() {
        const tickerValue = ticker.value.trim().toUpperCase();
        const loaderValue = dataLoader.value;
        const tickerError = document.getElementById('tickerError');
        const tickerValidationIcon = document.getElementById('tickerValidationIcon');
        const metadataCard = document.getElementById('tickerMetadata');
        const submitBtn = document.querySelector('button[type="submit"]');
        const resultDiv = document.getElementById('result');
        
        // Reset validation state
        tickerValidationIcon.innerHTML = `
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-slate-900 dark:border-slate-50"></div>
        `;
        submitBtn.disabled = true;
        
        if (!tickerValue || !loaderValue) {
            tickerError.textContent = 'Please enter a ticker and select a data source.';
            tickerError.classList.remove('hidden');
            tickerValidationIcon.innerHTML = '';
            metadataCard.classList.add('hidden');
            return;
        }
        
        try {
            const response = await fetch(`/api/extract-workflow/validate-ticker/${loaderValue}/${tickerValue}`);
            if (!response.ok) {
                const error = await response.json();
                // Extract just the relevant error message
                const errorMessage = error.detail.includes('No data available for ticker') 
                    ? `Invalid ticker symbol: ${tickerValue}. Please enter a valid ticker.`
                    : error.detail;
                throw new Error(errorMessage);
            }
            
            tickerMetadata = await response.json();
            
            // Update UI for valid ticker
            tickerValidationIcon.innerHTML = `
                <svg class="h-5 w-5 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            `;
            // Clear all error messages on success
            tickerError.classList.add('hidden');
            resultDiv.classList.add('hidden');
            submitBtn.disabled = false;
            
            // Update metadata card
            document.getElementById('dateRange').textContent = 
                `Date Range: ${tickerMetadata.date_range.start} to ${tickerMetadata.date_range.end}`;
            document.getElementById('dataPoints').textContent = 
                `Data Points: ${tickerMetadata.data_points}`;
            document.getElementById('frequency').textContent = 
                `Frequency: ${tickerMetadata.frequency}`;
            metadataCard.classList.remove('hidden');
            
            // Update date input constraints
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            startDate.min = tickerMetadata.date_range.start;
            startDate.max = tickerMetadata.date_range.end;
            endDate.min = tickerMetadata.date_range.start;
            endDate.max = tickerMetadata.date_range.end;
            
        } catch (error) {
            tickerValidationIcon.innerHTML = `
                <svg class="h-5 w-5 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            `;
            tickerError.textContent = error.message;
            tickerError.classList.remove('hidden');
            submitBtn.disabled = true;
            metadataCard.classList.add('hidden');
            
            // Clear any previous success state
            resultDiv.classList.add('hidden');
        }
    }
    
    // Add event listeners for validation
    ticker.addEventListener('input', debouncedValidateTicker);
    
    // Handle blur event to set default ticker
    ticker.addEventListener('blur', () => {
        const value = ticker.value.trim();
        if (!value) {
            ticker.value = 'AAPL';
            validateTicker();
        }
    });
    
    dataLoader.addEventListener('change', () => {
        if (ticker.value) {
            validateTicker();
        }
    });
    
    // Modify the existing form validation to include ticker validation
    document.getElementById('extractForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        if (!tickerMetadata) {
            showError("Please enter a valid ticker symbol");
            return;
        }
        
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        
        // Validate date range before submission
        const start = startDate.value;
        const end = endDate.value;
        
        if (!start || !end) {
            showError("Please select both start and end dates");
            return;
        }

        const startTs = new Date(start);
        const endTs = new Date(end);
        const minDate = tickerMetadata?.date_range?.start ? new Date(tickerMetadata.date_range.start) : new Date('2024-02-01');
        const maxDate = tickerMetadata?.date_range?.end ? new Date(tickerMetadata.date_range.end) : new Date('2025-01-31');

        if (endTs <= startTs) {
            showError("End date must be after start date");
            return;
        }

        if (startTs < minDate || endTs > maxDate) {
            showError(`Dates must be within available range (${formatDateForDisplay(minDate)} to ${formatDateForDisplay(maxDate)})`);
            return;
        }
        
        // Format dates to ISO string with proper timezone handling
        const formData = {
            ticker: document.getElementById('ticker').value,
            data_loader: document.getElementById('dataLoader').value,
            start_date: start ? formatDateToUTC(start) : null,
            end_date: end ? formatDateToUTC(end) : null
        };

        try {
            const response = await fetch('/api/extract-workflow/run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (response.ok) {
                document.getElementById('result').classList.remove('hidden');
                document.getElementById('resultMessage').textContent = result.message;
                
                // Poll for status
                const taskId = result.report_id;
                pollStatus(taskId);
            } else {
                throw new Error(result.detail || 'Failed to start extraction');
            }
        } catch (error) {
            showError(error.message);
        }
    });

    // Date validation
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    startDate.addEventListener('change', validateDates);
    endDate.addEventListener('change', validateDates);

    function validateDates() {
        const start = startDate.value;
        const end = endDate.value;
        const submitBtn = document.querySelector('button[type="submit"]');

        // Only validate if both dates are set
        if (start && end) {
            const startTs = new Date(start);
            const endTs = new Date(end);
            const minDate = tickerMetadata?.date_range?.start ? new Date(tickerMetadata.date_range.start) : new Date('2024-02-01');
            const maxDate = tickerMetadata?.date_range?.end ? new Date(tickerMetadata.date_range.end) : new Date('2025-01-31');

            let errorMessage = '';

            if (endTs <= startTs) {
                errorMessage = 'End date must be after start date';
            } else if (startTs < minDate || endTs > maxDate) {
                errorMessage = `Dates must be within available range (${formatDateForDisplay(minDate)} to ${formatDateForDisplay(maxDate)})`;
            }

            if (errorMessage) {
                showError(errorMessage);
                submitBtn.disabled = true;
            } else {
                // Clear error if dates are valid
                document.getElementById('result').classList.add('hidden');
                submitBtn.disabled = false;
            }
        }
    }

    function formatDateForDisplay(date) {
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // Set default date range (last available day)
    const maxDate = new Date('2025-01-31');
    const prevDate = new Date(maxDate);
    prevDate.setDate(prevDate.getDate() - 1);
    
    startDate.value = formatDateForInput(prevDate);
    endDate.value = formatDateForInput(maxDate);
});

// Helper function to format date for datetime-local input
function formatDateForInput(date) {
    return date.toISOString().slice(0, 19);  // Format: YYYY-MM-DDTHH:mm:ss
}

// Helper function to format date to UTC ISO string
function formatDateToUTC(dateString) {
    const date = new Date(dateString);
    return date.toISOString();  // This will convert to UTC
}

async function pollStatus(taskId) {
    try {
        const response = await fetch(`/api/extract-workflow/status/${taskId}`);
        const result = await response.json();
        
        const resultMessage = document.getElementById('resultMessage');
        
        if (result.status === 'completed') {
            resultMessage.innerHTML = `
                <div class="flex items-center gap-2 text-green-500 dark:text-green-400">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>${result.message}</span>
                </div>
            `;
        } else if (result.status === 'failed') {
            resultMessage.innerHTML = `
                <div class="flex items-center gap-2 text-red-500 dark:text-red-400">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Error: ${result.error}</span>
                </div>
            `;
        } else {
            resultMessage.innerHTML = `
                <div class="flex items-center gap-2 text-slate-500 dark:text-slate-400">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-slate-900 dark:border-slate-50"></div>
                    <span>Processing your request...</span>
                </div>
            `;
            setTimeout(() => pollStatus(taskId), 1000);
        }
    } catch (error) {
        showError(`Error checking status: ${error.message}`);
    }
}

function showError(message) {
    const resultDiv = document.getElementById('result');
    resultDiv.classList.remove('hidden');
    resultDiv.classList.add('animate-in', 'fade-in-0', 'slide-in-from-top-5');
    
    document.getElementById('resultMessage').innerHTML = `
        <div class="flex items-center gap-2 text-red-500 dark:text-red-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
}
</script>
{% endblock %}

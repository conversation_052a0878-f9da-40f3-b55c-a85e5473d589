let listElement; // Declare listElement globally
let selectedLoaders = new Set(); // Global state for selected loaders

document.addEventListener("DOMContentLoaded", () => {
  listElement = document.getElementById("data-loaders-list");
  fetchDataLoaders();

  // Ensure action bar exists
  if (!document.getElementById("loader-action-bar")) {
    const actionBar = document.createElement("div");
    actionBar.id = "loader-action-bar";
    actionBar.className =
      "hidden fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white dark:bg-slate-800 shadow-lg rounded-lg border border-slate-200 dark:border-slate-700 p-4 z-50";
    document.body.appendChild(actionBar);
  }

  // Add event listener for checkboxes after list is populated
  document.addEventListener("change", (event) => {
    if (event.target.classList.contains("loader-checkbox")) {
      const loaderId = event.target.dataset.loaderId;

      if (loaderId) {
        if (event.target.checked) {
          selectedLoaders.add(loaderId);
        } else {
          selectedLoaders.delete(loaderId);
        }
        updateActionBar();
      }
    }
  });
});

async function fetchDataLoaders() {
  try {
    // Show loading state
    if (listElement) {
      listElement.innerHTML = `
                <li class="p-4 text-slate-600 dark:text-slate-400">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading data loaders...
                    </div>
                </li>
            `;
    }

    const response = await fetch("/api/data-loaders");
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const dataLoaders = await response.json();

    if (!Array.isArray(dataLoaders)) {
      throw new Error(
        "Expected array of data loaders but got: " + typeof dataLoaders,
      );
    }

    updateLoaderList(dataLoaders);
  } catch (error) {
    console.error("Error fetching data loaders:", error);
    if (listElement) {
      listElement.innerHTML = `
                <li class="p-4 text-red-600 dark:text-red-400">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-exclamation-circle"></i>
                        Error loading data loaders: ${error.message}
                    </div>
                </li>
            `;
    }
  }
}

function updateLoaderList(dataLoaders) {
  if (!listElement) {
    console.error("List element not found");
    return;
  }

  // Remove existing select/deselect buttons if any
  const existingButtonContainer = document.getElementById(
    "select-deselect-container",
  );
  if (existingButtonContainer) {
    existingButtonContainer.remove();
  }

  // Create and insert select/deselect buttons
  const buttonContainer = createSelectDeselectButtons();
  buttonContainer.id = "select-deselect-container";
  listElement.parentNode.insertBefore(buttonContainer, listElement);

  if (dataLoaders.length === 0) {
    listElement.innerHTML = `
            <li class="p-4 text-slate-600 dark:text-slate-400">
                <div class="flex items-center gap-2">
                    <i class="fas fa-info-circle"></i>
                    No data loaders available
                </div>
            </li>
        `;
    return;
  }

  const htmlContent = dataLoaders
    .map((loader) => {
      const isActive = loader.is_active;
      const statusLabel = isActive ? "Active" : "Inactive";
      const statusColor = isActive
        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";

      return `
            <li class="group">
                <div class="flex items-center justify-between p-4 hover:bg-slate-50 dark:hover:bg-slate-900/50">
                    <div class="flex items-center space-x-4 w-full">
                        <!-- Checkbox -->
                        <input
                            type="checkbox"
                            class="loader-checkbox form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500 border-gray-300"
                            id="loader-checkbox-${loader._id}"
                            data-loader-id="${loader._id}"
                        >

                        <!-- Loader Name and Details -->
                        <div class="flex-grow">
                            <a href="javascript:void(0)"
                               onclick="window.handleLoaderClick('${loader._id}')"
                               class="flex items-center gap-2 text-slate-900 dark:text-slate-50 hover:text-slate-600 dark:hover:text-slate-400 font-medium">
                                <i class="fas fa-file-code text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300"></i>
                                <span>${loader.name}</span>
                            </a>
                            <p class="text-sm text-slate-500 dark:text-slate-400">${loader.description || "No description"}</p>
                        </div>

                        <!-- Status Label -->
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                            ${statusLabel}
                        </span>
                    </div>
                </div>
            </li>
            `;
    })
    .join("");

  listElement.innerHTML = htmlContent;

  // Add event listeners for checkboxes
  document.querySelectorAll(".loader-checkbox").forEach((checkbox) => {
    checkbox.addEventListener("change", (event) => {
      const loaderId = event.target.dataset.loaderId;

      if (event.target.checked) {
        selectedLoaders.add(loaderId);
      } else {
        selectedLoaders.delete(loaderId);
      }

      updateActionBar();
    });
  });
}

async function deleteDataLoader(loaderIds) {
    const loadersToDelete = Array.isArray(loaderIds) ? loaderIds : [loaderIds];
    const errors = [];

    for (const id of loadersToDelete) {
        try {
            const response = await fetch(`/api/data-loaders/delete/${id}`, {
                method: 'DELETE',
                headers: {
                    'Accept': 'application/json'
                }
            });

            const data = await response.json();
            
            if (!response.ok) {
                errors.push(`Failed to delete loader ${id}: ${data.error || 'Unknown error'}`);
            }
        } catch (e) {
            errors.push(`Error deleting loader ${id}: ${e.message}`);
        }
    }

    // Show any errors
    if (errors.length > 0) {
        alert(errors.join('\n'));
    }

    // Always refresh the list
    await fetchDataLoaders();
    clearSelectedLoaders();
}

function createLoaderToggleSwitch(loader, isActive) {
  const switchContainer = document.createElement("div");
  switchContainer.className = "relative inline-flex items-center";

  const switchInput = document.createElement("input");
  switchInput.type = "checkbox";
  switchInput.checked = isActive;
  switchInput.id = `loader-toggle-${loader}`;
  switchInput.className = "sr-only peer";

  const switchLabel = document.createElement("label");
  switchLabel.htmlFor = `loader-toggle-${loader}`;
  switchLabel.className = `
    w-11 h-6 bg-gray-200 peer-focus:outline-none
    peer-focus:ring-4 peer-focus:ring-blue-300
    dark:peer-focus:ring-blue-800 rounded-full
    peer dark:bg-gray-700
    peer-checked:after:translate-x-full
    peer-checked:after:border-white
    after:content-[''] after:absolute after:top-[2px]
    after:left-[2px] after:bg-white after:border-gray-300
    after:border after:rounded-full after:h-5 after:w-5
    after:transition-all dark:border-gray-600
    peer-checked:bg-blue-600
  `;

  switchContainer.appendChild(switchInput);
  switchContainer.appendChild(switchLabel);

  // Add toggle event listener
  switchInput.addEventListener("change", async () => {
    await toggleDataLoader(loader);
  });

  return switchContainer;
}

// Update action bar visibility and selected loaders
function updateActionBar() {
  const actionBar = document.getElementById("loader-action-bar");
  if (!actionBar) {
    console.error("Action bar element not found");
    return;
  }

  if (selectedLoaders.size > 0) {
    const actionButtonsContainer = document.createElement("div");
    actionButtonsContainer.className = "flex space-x-2";

    const actions = [
      {
        label: "Toggle Active",
        icon: "fas fa-toggle-on",
        action: async () => {
          try {
            const promises = Array.from(selectedLoaders).map((loaderId) => {
              if (!loaderId) {
                console.error("Invalid loader ID:", loaderId);
                return Promise.reject(new Error("Invalid loader ID"));
              }
              return toggleDataLoader(loaderId);
            });

            await Promise.allSettled(promises);
            await fetchDataLoaders();
            clearSelectedLoaders();
          } catch (error) {
            console.error("Error toggling loaders:", error);
            showError("Failed to toggle some loaders", error.message);
          }
        },
      },
      {
        label: "Test",
        icon: "fas fa-flask",
        action: () => runTests(Array.from(selectedLoaders)),
      },
      {
        label: "Delete",
        icon: "fas fa-trash",
        action: () => {
          if (
            confirm(
              "Are you sure you want to delete the selected data loaders?",
            )
          ) {
            deleteDataLoader(Array.from(selectedLoaders));
          }
        },
      },
      {
        label: "Clear",
        icon: "fas fa-times",
        action: clearSelectedLoaders,
      },
    ];

    actions.forEach(({ label, icon, action }) => {
      const button = document.createElement("button");
      button.innerHTML = `<i class="${icon} mr-2"></i>${label}`;
      button.className = `
        inline-flex items-center justify-center
        px-4 py-2
        bg-slate-100 text-slate-900
        hover:bg-slate-200
        dark:bg-slate-700 dark:text-slate-50
        dark:hover:bg-slate-600
        rounded-md
        text-sm font-medium
        transition-colors
      `;
      button.addEventListener("click", action);
      actionButtonsContainer.appendChild(button);
    });

    actionBar.innerHTML = "";
    actionBar.appendChild(actionButtonsContainer);
    actionBar.classList.remove("hidden");
  } else {
    actionBar.classList.add("hidden");
  }
}

// Run tests for selected loaders
async function runTests(loaderIds) {
  try {
    const response = await fetch("/api/run-tests", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        loader_ids: loaderIds,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to run tests");
    }

    const testResults = await response.json();

    if (!testResults.results || testResults.results.length === 0) {
      throw new Error("No test results found");
    }

    // Create and show the test results modal
    const modal = document.createElement("div");
    modal.className =
      "fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 overflow-y-auto";

    const modalContent = document.createElement("div");
    modalContent.className =
      "bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto p-6";

    // Add test results content
    modalContent.innerHTML = `
            <h2 class="text-2xl font-bold mb-4 text-slate-900 dark:text-slate-50">
                Test Results
                <span class="text-sm font-normal ml-2 ${testResults.success ? "text-green-500" : "text-red-500"}">
                    (${testResults.results.filter((r) => r.success).length}/${testResults.total_loaders} passed)
                </span>
            </h2>
            ${testResults.results
              .map(
                (result) => `
                    <div class="mb-4 p-4 rounded-lg ${
                      result.success
                        ? "bg-green-50 dark:bg-green-900/30"
                        : "bg-red-50 dark:bg-red-900/30"
                    }">
                        <div class="flex justify-between items-center mb-2">
                            <h3 class="text-lg font-semibold ${
                              result.success
                                ? "text-green-800 dark:text-green-300"
                                : "text-red-800 dark:text-red-300"
                            }">${result.loader_name}</h3>
                            <span class="text-sm font-medium ${
                              result.success ? "text-green-600" : "text-red-600"
                            }">${result.success ? "Passed" : "Failed"}</span>
                        </div>
                        <p class="text-sm text-slate-600 dark:text-slate-400 mb-2">
                            <strong>Total Tests:</strong> ${result.total_tests || 0}
                            | <strong>Passed Tests:</strong> ${result.passed_tests || 0}
                            | <strong>Failures:</strong> ${result.failures || 0}
                            | <strong>Errors:</strong> ${result.errors || 0}
                        </p>
                        ${
                          result.details && result.details.length > 0
                            ? `
                            <div class="mt-2 space-y-2">
                                <p class="text-sm font-medium text-red-800 dark:text-red-300">Failed Tests:</p>
                                <ul class="text-sm text-red-700 dark:text-red-300 list-disc pl-5 space-y-1">
                                    ${result.details
                                      .map(
                                        (detail) => `
                                            <li>
                                                <strong>${detail.test_name}</strong>:
                                                <span class="text-red-600 dark:text-red-400">
                                                    ${detail.error_type} - ${detail.error_message}
                                                </span>
                                            </li>
                                        `,
                                      )
                                      .join("")}
                                </ul>
                            </div>`
                            : ""
                        }
                    </div>
                `,
              )
              .join("")}
            <button onclick="this.closest('.fixed').remove()"
                    class="mt-4 w-full py-2 bg-slate-100 text-slate-900 hover:bg-slate-200
                           dark:bg-slate-700 dark:text-slate-50 dark:hover:bg-slate-600 rounded-md">
                Close
            </button>
        `;

    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener("click", (e) => {
      if (e.target === modal) modal.remove();
    });

    // Show success/error message
    if (testResults.success) {
      showSuccess(
        `All ${testResults.total_loaders} loaders passed their tests`,
      );
    } else {
      showError(
        "Test Results",
        `${testResults.results.filter((r) => !r.success).length} out of ${testResults.total_loaders} loaders failed tests`,
      );
    }

    // Clear selected loaders after running tests
    clearSelectedLoaders();
  } catch (error) {
    console.error("Error running tests:", error);
    showError("Failed to run tests", error.message);
  }
}

// Clear selected loaders
function clearSelectedLoaders() {
  selectedLoaders.clear();
  document.querySelectorAll(".loader-checkbox").forEach((checkbox) => {
    checkbox.checked = false;
  });
  updateActionBar();
}

// Function to create select/deselect buttons with Shadcn-inspired design
function createSelectDeselectButtons() {
  const buttonContainer = document.createElement("div");
  buttonContainer.className = "flex items-center space-x-2 mb-4";

  // Button base classes for Shadcn-like design
  const baseButtonClasses = `
    inline-flex items-center justify-center
    rounded-md text-sm font-medium
    transition-colors
    focus-visible:outline-none focus-visible:ring-2
    focus-visible:ring-ring focus-visible:ring-offset-2
    disabled:opacity-50 disabled:pointer-events-none
    ring-offset-background
    h-10 px-4 py-2
  `;

  // Select All Button
  const selectAllButton = document.createElement("button");
  selectAllButton.textContent = "Select All";
  selectAllButton.className = `
    ${baseButtonClasses}
    bg-primary text-primary-foreground
    hover:bg-primary/90
    dark:bg-slate-50 dark:text-slate-900
    dark:hover:bg-slate-50/90
  `;
  selectAllButton.addEventListener("click", () => {
    document.querySelectorAll(".loader-checkbox").forEach((checkbox) => {
      checkbox.checked = true;
      const loaderId = checkbox.dataset.loaderId;
      if (loaderId) {
        selectedLoaders.add(loaderId);
      }
    });
    updateActionBar();
  });

  // Deselect All Button
  const deselectAllButton = document.createElement("button");
  deselectAllButton.textContent = "Deselect All";
  deselectAllButton.className = `
    ${baseButtonClasses}
    bg-secondary text-secondary-foreground
    hover:bg-secondary/80
    border border-input
    dark:border-slate-800
    dark:bg-slate-800
    dark:text-slate-50
    dark:hover:bg-slate-800/80
  `;
  deselectAllButton.addEventListener("click", clearSelectedLoaders);

  // Optional: Add icons to buttons
  const selectIcon = document.createElement("i");
  selectIcon.className = "fas fa-check-square mr-2";
  selectAllButton.insertBefore(selectIcon, selectAllButton.firstChild);

  const deselectIcon = document.createElement("i");
  deselectIcon.className = "fas fa-square mr-2";
  deselectAllButton.insertBefore(deselectIcon, deselectAllButton.firstChild);

  buttonContainer.appendChild(selectAllButton);
  buttonContainer.appendChild(deselectAllButton);

  return buttonContainer;
}

async function toggleDataLoader(loaderId) {
  if (!loaderId) {
    throw new Error("Loader ID is required");
  }

  try {
    const response = await fetch(`/api/data-loaders/${loaderId}/toggle`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to toggle loader status");
    }

    const result = await response.json();
    showSuccess(
      `Data loader ${result.new_status ? "activated" : "deactivated"} successfully`,
    );

    return result;
  } catch (error) {
    console.error("Error toggling loader:", error);
    showError("Failed to toggle loader status", error.message);
    throw error;
  }
}

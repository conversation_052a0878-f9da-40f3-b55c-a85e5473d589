// utils.js
function formatTestOutput(output, summary) {
  const sections = output.split(/(?=={2,})/);
  const formattedSections = sections.map((section) => {
    const lines = section.split("\n");
    const formattedLines = lines.map((line) => {
      if (line.includes("PASS")) {
        return `<div class="text-green-500 flex items-center gap-2">
                    <i class="fas fa-check-circle"></i>${line}
                </div>`;
      } else if (line.includes("FAIL")) {
        return `<div class="text-red-500 flex items-center gap-2">
                    <i class="fas fa-times-circle"></i>${line}
                </div>`;
      } else if (line.startsWith("===")) {
        return `<div class="text-blue-500 font-bold border-b border-blue-200 dark:border-blue-800 pb-1 mb-2">
                    ${line}
                </div>`;
      }
      return `<div class="text-gray-600 dark:text-gray-400">${line}</div>`;
    });

    return `<div class="test-output-section mb-4">${formattedLines.join("\n")}</div>`;
  });

  const summaryClass =
    summary.failures === 0 && summary.errors === 0
      ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
      : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800";

  return `
        <div class="space-y-4">
            ${formattedSections.join("\n")}

            <div class="mt-6 rounded-lg border ${summaryClass} p-4">
                <div class="font-medium mb-2">Test Summary</div>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Total Tests</div>
                        <div class="text-lg font-semibold">${summary.total_tests}</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Failures</div>
                        <div class="text-lg font-semibold ${summary.failures > 0 ? "text-red-500" : "text-green-500"}">
                            ${summary.failures}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Errors</div>
                        <div class="text-lg font-semibold ${summary.errors > 0 ? "text-red-500" : "text-green-500"}">
                            ${summary.errors}
                        </div>
                    </div>
                </div>
            </div>
        </div>`;
}

function formatErrorOutput(error, details) {
  return `
        <div class="rounded-lg border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20 p-4">
            <div class="flex items-center gap-2 text-red-500 font-bold mb-2">
                <i class="fas fa-exclamation-circle"></i>
                <span>${error}</span>
            </div>
            <div class="mt-2 text-red-600 dark:text-red-400 font-mono">${details}</div>
        </div>`;
}

function showSuccessMessage(message) {
  const successMessageElement = document.getElementById("success-message");
  if (successMessageElement) {
    successMessageElement.textContent = message;
    successMessageElement.classList.remove("hidden");

    // Auto-hide after 3 seconds
    setTimeout(() => {
      successMessageElement.classList.add("hidden");
    }, 3000);
  }
}

function showError(title, message) {
  console.error(title, message);
  const errorContainer = document.createElement("div");
  errorContainer.className =
    "fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-lg";
  errorContainer.innerHTML = `
        <h4 class="font-bold">${title}</h4>
        <p>${message}</p>
    `;
  document.body.appendChild(errorContainer);
  setTimeout(() => errorContainer.remove(), 5000);
}
function showSuccess(message) {
  const successContainer = document.createElement("div");
  successContainer.className =
    "fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-lg z-50";
  successContainer.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <p>${message}</p>
        </div>
    `;
  document.body.appendChild(successContainer);
  setTimeout(() => successContainer.remove(), 5000);
}
// Make it globally available
window.showError = showError;
window.showSuccess = showSuccess;

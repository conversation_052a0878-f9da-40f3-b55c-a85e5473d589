let editor;
let currentLoaderName = "";
let isEditorDirty = false;

// Initialize Monaco Editor with editor setup
require.config({
  paths: {
    vs: "https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs",
  },
});

require(["vs/editor/editor.main"], function () {
  editor = monaco.editor.create(document.getElementById("editor"), {
    value: "",
    language: "python",
    theme: "vs-dark",
    automaticLayout: true,
    minimap: { enabled: true },
    fontSize: 14,
    lineNumbers: "on",
    scrollBeyondLastLine: false,
    renderWhitespace: "selection",
    formatOnPaste: true,
    formatOnType: true,
  });

  // Show save button when editor content changes
  editor.onDidChangeModelContent(() => {
    isEditorDirty = true;
    document.getElementById("save-button").classList.remove("hidden");
  });
});

// Define the global handleLoaderClick function
window.handleLoaderClick = async function (loaderId) {
  try {
    // First try to get the loader details using the ID
    await displayDataLoaderDetails(loaderId);

    // Get the name from the loader details response and use it to fetch the code
    const detailsResponse = await fetch(
      `/api/data-loaders/${loaderId}/details`,
    );
    if (!detailsResponse.ok)
      throw new Error(`HTTP error! status: ${detailsResponse.status}`);
    const loaderDetails = await detailsResponse.json();

    // Now fetch the code using the loader name
    await fetchDataLoaderCode(loaderDetails.name);
  } catch (error) {
    showError("Failed to handle loader click", error.message);
  }
};

async function fetchDataLoaderCode(loaderName) {
  try {
    const response = await fetch(`/api/data-loaders/${loaderName}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const code = await response.text();

    if (editor) {
      editor.setValue(code);
      currentLoaderName = loaderName;
      isEditorDirty = false;

      document.getElementById("editor-container").classList.remove("hidden");
      document.getElementById("save-button").classList.add("hidden");

      // Highlight the selected loader
      document.querySelectorAll("#data-loaders-list li").forEach((li) => {
        li.classList.remove("ring-2", "ring-blue-500");
      });

      const selectedLoader = document.querySelector(
        `[onclick="window.handleLoaderClick('${loaderName}')"]`,
      );

      if (selectedLoader) {
        const listItem = selectedLoader.closest("li");
        if (listItem) {
          listItem.classList.add("ring-2", "ring-blue-500");
        }
      }
    } else {
    }
  } catch (error) {
    showError("Failed to load code", error.message);
  }
}

async function saveCode() {
  if (!currentLoaderName || !isEditorDirty) return;

  const button = document.getElementById("save-button");
  button.disabled = true;
  button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

  try {
    const response = await fetch(`/api/data-loaders/${currentLoaderName}`, {
      method: "POST",
      headers: { "Content-Type": "text/plain" },
      body: editor.getValue(),
    });

    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

    isEditorDirty = false;
    button.classList.add("hidden");

    const saveIcon = document.getElementById(`save-icon-${currentLoaderName}`);
    if (saveIcon) {
      saveIcon.classList.remove("hidden");
      setTimeout(() => saveIcon.classList.add("hidden"), 2000);
    }

    showSuccess("Changes saved successfully");
  } catch (error) {
    console.error(
      `Error saving code for data loader ${currentLoaderName}:`,
      error,
    );
    showError("Failed to save changes", error.message);
  } finally {
    button.disabled = false;
    button.innerHTML = '<i class="fas fa-save"></i> Save Changes';
  }
}

// Add event listeners when the DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  const saveButton = document.getElementById("save-button");
  if (saveButton) {
    saveButton.addEventListener("click", saveCode);
  }
});

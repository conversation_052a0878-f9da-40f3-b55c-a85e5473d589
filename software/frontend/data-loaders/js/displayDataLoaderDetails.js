async function displayDataLoaderDetails(loaderId) {
  try {
    const response = await fetch(`/api/data-loaders/${loaderId}/details`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.detail || `HTTP error! status: ${response.status}`,
      );
    }

    const loaderDetails = await response.json();
    const apiKeys = loaderDetails.api_keys || {};

    // Get the container and create it if it doesn't exist
    let detailsContainer = document.getElementById("loader-details-container");
    if (!detailsContainer) {
      detailsContainer = document.createElement("div");
      detailsContainer.id = "loader-details-container";
      detailsContainer.className = "p-4";
      const editorContainer = document.getElementById("editor-container");
      editorContainer.parentNode.insertBefore(
        detailsContainer,
        editorContainer,
      );
    }

    // Clear existing content
    detailsContainer.innerHTML = "";

    // Add loader details section with improved design
    const detailsSection = `
      <div class="mb-6 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
        <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">Loader Details</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-1">
            <p class="text-sm font-medium text-slate-500 dark:text-slate-400">Name</p>
            <p class="text-sm font-semibold text-slate-900 dark:text-slate-100">${loaderDetails.name}</p>
          </div>
          <div class="space-y-1">
            <p class="text-sm font-medium text-slate-500 dark:text-slate-400">File Name</p>
            <p class="text-sm font-semibold text-slate-900 dark:text-slate-100">${loaderDetails.file_name}</p>
          </div>
          <div class="space-y-1">
            <p class="text-sm font-medium text-slate-500 dark:text-slate-400">Status</p>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              ${
                loaderDetails.is_active
                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                  : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
              }">
              ${loaderDetails.is_active ? "Active" : "Inactive"}
            </span>
          </div>
          <div class="space-y-1">
            <p class="text-sm font-medium text-slate-500 dark:text-slate-400">Last Updated</p>
            <p class="text-sm font-semibold text-slate-900 dark:text-slate-100">
              ${new Date(loaderDetails.updated_at).toLocaleString()}
            </p>
          </div>
          <div class="col-span-2 space-y-1">
            <p class="text-sm font-medium text-slate-500 dark:text-slate-400">Description</p>
            <p class="text-sm font-semibold text-slate-900 dark:text-slate-100">
              ${loaderDetails.description || "No description"}
            </p>
          </div>
        </div>
      </div>
    `;

    // Add the API keys section with improved design
    const apiKeysSection = `
      <div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
        <div class="p-4 border-b border-slate-200 dark:border-slate-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100">API Keys</h3>
            <button onclick="addNewApiKey('${loaderDetails._id}')"
                    class="inline-flex items-center justify-center rounded-md text-sm font-medium
                           min-h-10 px-4 py-2
                           bg-slate-900 text-white hover:bg-slate-800 dark:bg-slate-100 dark:text-slate-900 dark:hover:bg-slate-200
                           shadow-sm hover:shadow
                           transition-colors duration-200
                           focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2
                           disabled:pointer-events-none disabled:opacity-50">
              <i class="fas fa-plus mr-2 h-4 w-4"></i>
              Add New Key
            </button>
          </div>
        </div>

        <div class="divide-y divide-slate-200 dark:divide-slate-700">
          ${
            Object.entries(apiKeys).length === 0
              ? `
            <div class="p-4 text-center text-sm text-slate-500 dark:text-slate-400">
              No API keys configured. Click "Add New Key" to create one.
            </div>
          `
              : Object.entries(apiKeys)
                  .map(
                    ([keyName, keyData]) => `
            <div class="p-4">
              <div class="flex justify-between items-start mb-2">
                <div>
                  <h4 class="font-medium text-slate-900 dark:text-slate-100">${keyName}</h4>
                  <p class="text-sm text-slate-500 dark:text-slate-400">${
                    keyData.description || "No description"
                  }</p>
                </div>
                <div class="flex items-center gap-2">
                  <button onclick="toggleApiKey('${loaderDetails._id}', '${keyName}', ${!keyData.is_active})"
                          class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium
                                 ${
                                   keyData.is_active
                                     ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                     : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                                 }">
                    ${keyData.is_active ? "Active" : "Inactive"}
                  </button>
                  <button onclick="editApiKey('${loaderDetails._id}', '${keyName}')"
                          class="text-slate-400 hover:text-slate-500 transition-colors">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button onclick="deleteApiKey('${loaderDetails._id}', '${keyName}')"
                          class="text-slate-400 hover:text-red-500 transition-colors">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <div class="mt-2">
                  <div class="relative">
                      <form style="display: contents">
                          <input type="password"
                                 value="${keyData.key}"
                                 autocomplete="off"
                                 readonly
                                 data-form-type="password"
                                 class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                        bg-slate-50 dark:bg-slate-700 text-slate-900 dark:text-slate-100
                                        focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      </form>
                      <button type="button"
                              onclick="toggleKeyVisibility(this)"
                              class="absolute right-2 top-1/2 transform -translate-y-1/2
                                     text-slate-400 hover:text-slate-600 transition-colors
                                     flex items-center justify-center w-8 h-8">
                          <i class="fas fa-eye"></i>
                      </button>
                  </div>
                  <p class="mt-1 text-xs text-slate-400">
                      Last updated: ${new Date(keyData.last_updated).toLocaleString()}
                  </p>
              </div>
            </div>
          `,
                  )
                  .join("")
          }
        </div>
      </div>
    `;

    // Update the container with both sections
    detailsContainer.innerHTML = detailsSection + apiKeysSection;
  } catch (error) {
    console.error("Error displaying loader details:", error);
    showError("Failed to load loader details", error.message);
  }
}

// Make the function globally available
window.displayDataLoaderDetails = displayDataLoaderDetails;

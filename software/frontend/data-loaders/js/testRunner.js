async function runTests(loaderIds) {
  // Create and show progress overlay
  const progressOverlay = createProgressOverlay(loaderIds.length);
  document.body.appendChild(progressOverlay);

  // Hide action bar
  const actionBar = document.getElementById("loader-action-bar");
  if (actionBar) actionBar.classList.add("hidden");

  try {
    updateProgress(progressOverlay, 0, "Initializing test suite...");

    const response = await fetch("/api/run-tests", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        loader_ids: loaderIds,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Test response error:", errorData);
      throw new Error(errorData.detail || "Failed to run tests");
    }

    const testResults = await response.json();

    if (!testResults.results || testResults.results.length === 0) {
      throw new Error("No test results found");
    }

    // Process results and update progress
    testResults.results.forEach((result, index) => {
      const progress = ((index + 1) / testResults.total_loaders) * 100;
      updateProgress(
        progressOverlay,
        progress,
        `Completed ${result.loader_name} (${index + 1}/${testResults.total_loaders})`,
      );
    });

    // Update final progress
    updateProgress(progressOverlay, 100, "Tests completed");

    // Remove progress overlay with fade out
    progressOverlay.classList.add("opacity-0");
    setTimeout(() => progressOverlay.remove(), 300);

    // Show results modal
    showTestResultsModal(testResults);
    return testResults;
  } catch (error) {
    console.error("Error running tests:", error);
    progressOverlay.remove();
    showError("Test Execution Failed", error.message);
    throw error;
  } finally {
    // Re-enable action bar if test selection is still active
    const selectedCheckboxes = document.querySelectorAll(
      ".loader-checkbox:checked",
    );
    if (selectedCheckboxes.length > 0 && actionBar) {
      actionBar.classList.remove("hidden");
    }
  }
}

function createProgressOverlay(totalLoaders) {
  const overlay = document.createElement("div");
  overlay.className =
    "fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center transition-opacity duration-300";

  const content = document.createElement("div");
  content.className =
    "bg-white dark:bg-slate-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4 space-y-4";

  content.innerHTML = `
        <div class="space-y-2">
            <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Running Tests
            </h3>
            <div class="flex justify-between items-center">
                <p id="progress-status" class="text-sm text-slate-500 dark:text-slate-400">
                    Initializing...
                </p>
                <p id="progress-counter" class="text-sm font-medium text-slate-700 dark:text-slate-300">
                    0/${totalLoaders}
                </p>
            </div>
        </div>

        <div class="relative w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5 overflow-hidden">
            <div id="progress-bar"
                 class="absolute top-0 h-full bg-blue-500 transition-all duration-300 ease-out rounded-full"
                 style="width: 0%">
            </div>
            <div class="absolute top-0 h-full w-full">
                ${Array.from(
                  { length: totalLoaders },
                  (_, i) =>
                    `<div class="absolute h-full border-l border-slate-300 dark:border-slate-600"
                          style="left: ${((i + 1) * 100) / totalLoaders}%"></div>`,
                ).join("")}
            </div>
        </div>

        <div class="text-xs text-slate-400 dark:text-slate-500 text-center">
            Please wait while tests are being executed
        </div>
    `;

  overlay.appendChild(content);
  return overlay;
}

function updateProgress(overlay, percent, status) {
  const progressBar = overlay.querySelector("#progress-bar");
  const statusText = overlay.querySelector("#progress-status");
  const counterText = overlay.querySelector("#progress-counter");

  if (progressBar) {
    progressBar.style.width = `${percent}%`;
  }

  if (statusText) {
    statusText.textContent = status;
  }

  if (counterText) {
    const [_, total] = counterText.textContent.split("/");
    const current = Math.floor((percent / 100) * parseInt(total));
    counterText.textContent = `${current}/${total}`;
  }

  // Force a reflow to ensure animation plays
  progressBar.offsetHeight;
}

function showTestResultsModal(testResults) {
  const modal = document.createElement("div");
  modal.className =
    "fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm overflow-y-auto transition-opacity duration-300";

  const modalContent = document.createElement("div");
  modalContent.className =
    "bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto m-4";

  modalContent.innerHTML = `
        <div class="sticky top-0 z-10 bg-white dark:bg-slate-800 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-semibold text-slate-900 dark:text-slate-100">
                        Test Results
                    </h2>
                    <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">
                        ${testResults.results.filter((r) => r.success).length}/${testResults.total_loaders} tests passed
                    </p>
                </div>
                <button onclick="this.closest('.fixed').remove()"
                        class="text-slate-400 hover:text-slate-500 dark:hover:text-slate-300 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="p-6 space-y-4">
            ${testResults.results
              .map((result) => createTestResultCard(result))
              .join("")}
        </div>

        <div class="sticky bottom-0 bg-white dark:bg-slate-800 px-6 py-4 border-t border-slate-200 dark:border-slate-700">
            <button onclick="this.closest('.fixed').remove()"
                    class="w-full inline-flex items-center justify-center rounded-md text-sm font-medium h-10 px-4 py-2
                           bg-slate-900 text-white hover:bg-slate-800
                           dark:bg-slate-200 dark:text-slate-900 dark:hover:bg-slate-300
                           transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2
                           dark:focus:ring-offset-slate-900">
                Close
            </button>
        </div>
    `;

  modal.appendChild(modalContent);
  document.body.appendChild(modal);

  // Animation
  requestAnimationFrame(() => {
    modal.style.opacity = "0";
    requestAnimationFrame(() => {
      modal.style.opacity = "1";
    });
  });

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.style.opacity = "0";
      setTimeout(() => modal.remove(), 300);
    }
  });

  // Show overall success/error message
  if (testResults.success) {
    showSuccess(`All ${testResults.total_loaders} loaders passed their tests`);
  } else {
    showError(
      "Test Results",
      `${testResults.results.filter((r) => !r.success).length} out of ${testResults.total_loaders} loaders failed tests`,
    );
  }
}

function createTestResultCard(result) {
  return `
        <div class="rounded-lg border ${
          result.success
            ? "border-green-200 dark:border-green-900 bg-green-50/50 dark:bg-green-900/20"
            : "border-red-200 dark:border-red-900 bg-red-50/50 dark:bg-red-900/20"
        } overflow-hidden">
            <div class="p-4">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-2">
                        <div class="${
                          result.success
                            ? "text-green-500 dark:text-green-400"
                            : "text-red-500 dark:text-red-400"
                        }">
                            <i class="fas ${result.success ? "fa-check-circle" : "fa-times-circle"} text-lg"></i>
                        </div>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">
                            ${result.loader_name}
                        </h3>
                    </div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      result.success
                        ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300"
                        : "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300"
                    }">
                        ${result.success ? "Passed" : "Failed"}
                    </span>
                </div>

                <div class="grid grid-cols-3 gap-4 text-sm mb-4">
                    <div>
                        <p class="text-slate-500 dark:text-slate-400">Total Tests</p>
                        <p class="font-medium text-slate-900 dark:text-slate-100">${result.total_tests}</p>
                    </div>
                    <div>
                        <p class="text-slate-500 dark:text-slate-400">Passed</p>
                        <p class="font-medium text-green-600 dark:text-green-400">${result.passed_tests}</p>
                    </div>
                    <div>
                        <p class="text-slate-500 dark:text-slate-400">Failed</p>
                        <p class="font-medium text-red-600 dark:text-red-400">${result.total_tests - result.passed_tests}</p>
                    </div>
                </div>

                ${
                  result.details && result.details.length > 0
                    ? `
                        <div class="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                            <p class="font-medium text-slate-900 dark:text-slate-100 mb-2">Failed Tests</p>
                            <div class="space-y-2">
                                ${result.details
                                  .map(
                                    (detail) => `
                                        <div class="text-sm rounded-md bg-red-50 dark:bg-red-900/20 p-3">
                                            <p class="font-medium text-red-800 dark:text-red-300">
                                                ${detail.test_name}
                                            </p>
                                            <p class="text-red-600 dark:text-red-400 mt-1">
                                                ${detail.error_type}: ${detail.error_message}
                                            </p>
                                        </div>
                                    `,
                                  )
                                  .join("")}
                            </div>
                        </div>
                        `
                    : ""
                }
            </div>
        </div>
    `;
}

// Make the function available globally
window.runTests = runTests;

// apiKeyManager.js

/**
 * API Key Management Module
 * Handles all API key related operations for data loaders
 */

const API_KEY_DEBUG = false; // Toggle debugging

/**
 * Debug logger
 * @param {string} message - Debug message
 * @param {any} data - Optional data to log
 */
function debugLog(message, data = null) {
  if (API_KEY_DEBUG) {
    console.log(`🔑 [API Key Manager] ${message}`);
    if (data) console.log(data);
  }
}

/**
 * Add a new API key to a data loader
 * @param {string} loaderId - The ID of the data loader
 */
async function addNewApiKey(loaderId) {
  debugLog("Opening Add API Key modal", { loaderId });

  const modal = document.createElement("div");
  modal.className =
    "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
  modal.innerHTML = `
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl transform transition-all">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100">Add New API Key</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-slate-400 hover:text-slate-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="api-key-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Key Name
                    </label>
                    <input type="text" name="name" required
                           class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                  bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100
                                  focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        API Key
                    </label>
                    <div class="relative">
                        <input type="password"
                               name="key"
                               required
                               autocomplete="new-password"
                               form="api-key-form"
                               data-form-type="password"
                               class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                      bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100
                                      focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent">
                                      <button type="button"
                                              onclick="toggleKeyVisibility(this)"
                                              class="absolute right-2 top-1/2 transform -translate-y-1/2
                                                     text-slate-400 hover:text-slate-600 transition-colors
                                                     flex items-center justify-center w-8 h-8">
                                          <i class="fas fa-eye"></i>
                                      </button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Description
                    </label>
                    <textarea name="description" rows="2"
                            class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                   bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100
                                   focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"></textarea>
                </div>
                <div class="flex justify-end gap-2 pt-4">
                    <button type="button" onclick="this.closest('.fixed').remove()"
                            class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300
                                   bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600
                                   rounded-md hover:bg-slate-50 dark:hover:bg-slate-600">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                                   hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2
                                   focus:ring-blue-500 dark:focus:ring-offset-slate-800">
                        Add Key
                    </button>
                </div>
            </form>
        </div>
    `;

  document.body.appendChild(modal);

  // Handle form submission
  document
    .getElementById("api-key-form")
    .addEventListener("submit", async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      const keyData = {
        name: formData.get("name"),
        key: formData.get("key"),
        description: formData.get("description"),
        is_active: true,
      };

      debugLog("Submitting new API key", { ...keyData, key: "[REDACTED]" });

      try {
        const response = await fetch(`/api/data-loaders/${loaderId}/api-keys`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(keyData),
        });

        if (!response.ok) throw new Error("Failed to add API key");

        debugLog("API key added successfully");
        modal.remove();
        await displayDataLoaderDetails(loaderId);
        showSuccess("API key added successfully");
      } catch (error) {
        debugLog("Error adding API key", error);
        showError("Failed to add API key", error.message);
      }
    });
}

/**
 * Toggle visibility of an API key
 * @param {HTMLElement} button - The toggle button element
 */
function toggleKeyVisibility(button) {
  try {
    debugLog("Toggle visibility called", { buttonHTML: button.innerHTML });

    const container = button.closest(".relative");
    if (!container) {
      throw new Error("Could not find input container");
    }

    const input = container.querySelector("input");
    if (!input) {
      throw new Error("Could not find input element");
    }

    // Get or create the icon element
    let icon = button.firstElementChild;
    if (!icon || !icon.classList.contains("fas")) {
      debugLog("Creating new icon element");
      icon = document.createElement("i");
      icon.className = "fas fa-eye";
      button.innerHTML = ""; // Clear button content
      button.appendChild(icon);
    }

    const currentType = input.type;
    debugLog("Current input type", { type: currentType });

    if (currentType === "password") {
      input.type = "text";
      icon.className = "fas fa-eye-slash";
    } else {
      input.type = "password";
      icon.className = "fas fa-eye";
    }

    debugLog("Toggled key visibility", {
      newType: input.type,
      iconClass: icon.className,
    });
  } catch (error) {
    debugLog("Error toggling key visibility", error);
    console.error("Error toggling password visibility:", error);
  }
}

/**
 * Edit an existing API key
 * @param {string} loaderId - The ID of the data loader
 * @param {string} keyName - The name of the API key to edit
 */
async function editApiKey(loaderId, keyName) {
  debugLog("Opening Edit API Key modal", { loaderId, keyName });

  try {
    // Use the correct endpoint for fetching loader details
    const response = await fetch(`/api/data-loaders/${loaderId}/details`);
    if (!response.ok) throw new Error("Failed to fetch loader details");

    const loader = await response.json();
    const keyData = loader.api_keys[keyName];

    const modal = document.createElement("div");
    modal.className =
      "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    modal.innerHTML = `
         <div class="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl transform transition-all">
             <div class="flex justify-between items-center mb-4">
                 <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100">Edit API Key</h3>
                 <button type="button" onclick="this.closest('.fixed').remove()" class="text-slate-400 hover:text-slate-500">
                     <i class="fas fa-times"></i>
                 </button>
             </div>
             <form id="edit-api-key-form" class="space-y-4" autocomplete="off">
                 <input type="hidden" name="original_name" value="${keyName}">
                 <div>
                     <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                         Key Name
                     </label>
                     <input type="text"
                            name="name"
                            value="${keyName}"
                            required
                            readonly
                            disabled
                            class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                   bg-slate-100 dark:bg-slate-800 text-slate-900 dark:text-slate-100
                                   cursor-not-allowed opacity-75">
                 </div>
                 <div>
                     <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                         API Key
                     </label>
                     <div class="relative">
                         <input type="password"
                                name="key"
                                value="${keyData.key}"
                                required
                                autocomplete="new-password"
                                form="edit-api-key-form"
                                data-form-type="password"
                                class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                       bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100
                                       focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent">
                                       <button type="button"
                                               onclick="toggleKeyVisibility(this)"
                                               class="absolute right-2 top-1/2 transform -translate-y-1/2
                                                      text-slate-400 hover:text-slate-600 transition-colors
                                                      flex items-center justify-center w-8 h-8">
                                           <i class="fas fa-eye"></i>
                                       </button>
                     </div>
                 </div>
                 <div>
                     <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                         Description
                     </label>
                     <textarea name="description"
                               rows="2"
                               autocomplete="off"
                               class="w-full px-3 py-2 border border-slate-200 dark:border-slate-600 rounded-md
                                      bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100
                                      focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent">${keyData.description || ""}</textarea>
                 </div>
                 <div class="flex justify-end gap-2 pt-4">
                     <button type="button"
                             onclick="this.closest('.fixed').remove()"
                             class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300
                                    bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600
                                    rounded-md hover:bg-slate-50 dark:hover:bg-slate-600">
                         Cancel
                     </button>
                     <button type="submit"
                             class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                                    hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2
                                    focus:ring-blue-500 dark:focus:ring-offset-slate-800">
                         Save Changes
                     </button>
                 </div>
             </form>
         </div>
     `;

    document.body.appendChild(modal);

    // Handle form submission - Fix the form ID here
    document
      .getElementById("edit-api-key-form")
      .addEventListener("submit", async (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const updatedKeyData = {
          name: keyName, // Use original keyName instead of form value
          key: formData.get("key"),
          description: formData.get("description"),
          is_active: keyData.is_active,
        };

        debugLog("Updating API key", {
          originalName: formData.get("original_name"),
          newData: { ...updatedKeyData, key: "[REDACTED]" },
        });

        try {
          const response = await fetch(
            `/api/data-loaders/${loaderId}/api-keys/${keyName}`,
            {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(updatedKeyData),
            },
          );

          if (!response.ok) throw new Error("Failed to update API key");

          debugLog("API key updated successfully");
          modal.remove();
          await displayDataLoaderDetails(loaderId);
          showSuccess("API key updated successfully");
        } catch (error) {
          debugLog("Error updating API key", error);
          showError("Failed to update API key", error.message);
        }
      });
  } catch (error) {
    debugLog("Error in editApiKey", error);
    showError("Failed to edit API key", error.message);
  }
}

/**
 * Delete an API key
 * @param {string} loaderId - The ID of the data loader
 * @param {string} keyName - The name of the API key to delete
 */
async function deleteApiKey(loaderId, keyName) {
  debugLog("Attempting to delete API key", { loaderId, keyName });

  if (!confirm(`Are you sure you want to delete the API key "${keyName}"?`)) {
    debugLog("Delete cancelled by user");
    return;
  }

  try {
    const response = await fetch(
      `/api/data-loaders/${loaderId}/api-keys/${keyName}`,
      {
        method: "DELETE",
      },
    );

    if (!response.ok) throw new Error("Failed to delete API key");

    debugLog("API key deleted successfully");
    await displayDataLoaderDetails(loaderId);
    showSuccess("API key deleted successfully");
  } catch (error) {
    debugLog("Error deleting API key", error);
    showError("Failed to delete API key", error.message);
  }
}

/**
 * Toggle the active status of an API key
 * @param {string} loaderId - The ID of the data loader
 * @param {string} keyName - The name of the API key
 * @param {boolean} newStatus - The new active status
 */
async function toggleApiKey(loaderId, keyName, newStatus) {
  debugLog("Toggling API key status", { loaderId, keyName, newStatus });

  try {
    const response = await fetch(
      `/api/data-loaders/${loaderId}/api-keys/${keyName}/toggle`,
      {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ is_active: newStatus }),
      },
    );

    if (!response.ok) throw new Error("Failed to toggle API key status");

    debugLog("API key status toggled successfully");
    await displayDataLoaderDetails(loaderId);
    showSuccess(
      `API key ${newStatus ? "activated" : "deactivated"} successfully`,
    );
  } catch (error) {
    debugLog("Error toggling API key status", error);
    showError("Failed to toggle API key status", error.message);
  }
}

// Export all functions
window.addNewApiKey = addNewApiKey;
window.editApiKey = editApiKey;
window.deleteApiKey = deleteApiKey;
window.toggleApiKey = toggleApiKey;
window.toggleKeyVisibility = toggleKeyVisibility;

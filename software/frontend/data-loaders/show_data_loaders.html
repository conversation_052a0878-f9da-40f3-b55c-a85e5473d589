{% extends "base.html" %}

{% block title %}Data Loaders{% endblock %}

{% block header %}Data Loaders{% endblock %}

{% block content %}
<div class="container mx-auto p-6 space-y-6">
    <!-- Header Section -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-semibold tracking-tight text-slate-900 dark:text-slate-50">
            <div class="flex items-center gap-2">
                <i class="fas fa-code text-slate-600 dark:text-slate-400"></i>
                <span>Data Loaders</span>
            </div>
        </h2>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Panel: Data Loaders List -->
        <div class="lg:col-span-1 flex flex-col">
            <div class="rounded-lg border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 shadow-sm flex-1 flex flex-col">
                <div class="p-4 border-b border-slate-200 dark:border-slate-800 flex justify-between items-center">
                    <h3 class="text-sm font-medium text-slate-900 dark:text-slate-50">Available Loaders</h3>
                </div>
                <ul id="data-loaders-list" class="divide-y divide-slate-200 dark:divide-slate-800 max-h-[60vh] overflow-y-auto flex-1">
                    {% for loader in data_loaders %}
                    <div class="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-800 hover:bg-slate-100 dark:hover:bg-slate-900 transition-colors">
                        <div class="flex items-center space-x-4 w-full min-w-0">
                            <input
                                type="checkbox"
                                class="loader-checkbox form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500 border-gray-300 flex-shrink-0"
                                id="loader-checkbox-{{ loader.name }}"
                                data-loader-name="{{ loader.name }}"
                            >
                            <div class="flex-grow min-w-0">
                                <a href="javascript:void(0)"
                                   onclick="window.handleLoaderClick('{{ loader._id }}')"  <!-- Use _id instead of name -->
                                   class="flex items-center gap-2 text-slate-900 dark:text-slate-50 hover:text-slate-600 dark:hover:text-slate-400 font-medium truncate">
                                    <i class="fas fa-file-code text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300"></i>
                                    <span>{{ loader.name }}</span>
                                </a>
                                <p class="text-sm text-slate-500 dark:text-slate-400 truncate">{{ loader.description or 'No description' }}</p>
                            </div>
                            <!-- Status Label -->
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0
                                {{ 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' if loader.is_active else 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                {{ 'Active' if loader.is_active else 'Inactive' }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <!-- Right Panel: Code Editor -->
        <div class="lg:col-span-2 flex flex-col">
            <div class="rounded-lg border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-950 shadow-sm h-full flex-1">
                <!-- div for loader details -->
                <div id="loader-details-container" class="p-4"></div>

                <div class="relative">
                    <div id="editor-container" class="hidden">
                        <div class="absolute top-4 right-4 z-10">
                            <button id="save-button"
                                class="hidden inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300 bg-green-600 text-white hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 h-9 px-4 py-2 gap-2">
                                <i class="fas fa-save"></i>
                                <span>Save Changes</span>
                            </button>
                        </div>
                        <div id="editor" class="h-[calc(100vh-24rem)] min-h-[400px] rounded-md overflow-hidden"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .test-output-section {
            @apply border-l-2 my-2 pl-4;
        }

        .test-success {
            @apply border-green-500;
        }

        .test-failure {
            @apply border-red-500;
        }

        /* Monaco editor custom styles */
        .monaco-editor .margin,
        .monaco-editor .monaco-editor-background {
            @apply bg-slate-50 dark:bg-slate-900;
        }
    </style>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs/loader.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
<script src="/frontend/data-loaders/js/apiKeyManager.js"></script>
<script src="/frontend/data-loaders/js/displayDataLoaderDetails.js"></script>
<script src="/frontend/data-loaders/js/dataLoaders.js"></script>
<script src="/frontend/data-loaders/js/codeEditor.js"></script>
<script src="/frontend/data-loaders/js/testRunner.js"></script>
<script src="/frontend/data-loaders/js/utils.js"></script>
{% endblock %}

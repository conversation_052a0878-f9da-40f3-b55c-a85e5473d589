<!DOCTYPE html>
<html lang="en" class="h-full"
      x-data="{ darkMode: localStorage.getItem('darkMode') === 'true', isMobileMenuOpen: false }"
      :class="{ 'dark': darkMode }"
      x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Welcome{% endblock %} - Vero</title>
    <!-- Meta tags for SEO -->
    <meta name="description" content="{% block meta_description %}Vero Platform{% endblock %}">
    <meta name="keywords" content="stock, analysis, finance, market, reports">
    <meta name="author" content="Your Company Name">
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="{% block og_title %}Vero{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Vero Platform{% endblock %}">
    <meta property="og:image" content="{% block og_image %}/static/logo.png{% endblock %}">
    <meta property="og:url" content="{% block og_url %}{{ request.build_absolute_uri }}{% endblock %}">
    <meta property="og:type" content="website">

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Tailwind CSS Configuration for dark mode -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                    animation: {
                        'spin': 'spin 1s linear infinite',
                        'pulse': 'pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                },
            },
        }
    </script>
    <!-- Inter font for better typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    {% block extra_head %}{% endblock %}
</head>
<body class="h-full bg-gray-50 antialiased dark:bg-gray-900 dark:text-gray-100">
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only">Skip to main content</a>

    <div class="min-h-screen flex">
        <!-- Mobile Navigation Overlay -->
        <div x-show="isMobileMenuOpen"
             class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="isMobileMenuOpen = false">
        </div>

        <!-- Sidebar -->
        <aside :class="{'translate-x-0': isMobileMenuOpen, '-translate-x-full lg:translate-x-0': !isMobileMenuOpen}"
               class="fixed inset-y-0 left-0 z-50 w-64 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 shadow-xl dark:bg-gray-900/95 dark:supports-[backdrop-filter]:bg-gray-900/60 transition-transform duration-300 ease-in-out lg:sticky lg:h-screen flex-none border-r border-gray-200 dark:border-gray-800">
            <div class="flex flex-col h-full">
                <!-- Sidebar Header -->
                <div class="flex items-center h-16 px-4 border-b border-gray-200 dark:border-gray-800">
                    <div class="flex items-center">
                        <svg class="size-8" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                          <!-- Elegant background glow -->
                          <defs>
                            <filter id="glow">
                              <feGaussianBlur stdDeviation="1" result="blur"/>
                              <feComposite in="blur" operator="over" in2="SourceGraphic"/>
                            </filter>

                            <!-- Complex gradients -->
                            <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%">
                                <animate attributeName="stop-color"
                                         values="#4F46E5;#06B6D4;#6366F1;#4F46E5"
                                         dur="8s"
                                         repeatCount="indefinite"/>
                              </stop>
                              <stop offset="50%">
                                <animate attributeName="stop-color"
                                         values="#06B6D4;#6366F1;#4F46E5;#06B6D4"
                                         dur="8s"
                                         repeatCount="indefinite"/>
                              </stop>
                              <stop offset="100%">
                                <animate attributeName="stop-color"
                                         values="#6366F1;#4F46E5;#06B6D4;#6366F1"
                                         dur="8s"
                                         repeatCount="indefinite"/>
                              </stop>
                            </linearGradient>

                            <linearGradient id="orbGradient" x1="0%" y1="0%" x2="100%">
                              <stop offset="0%">
                                <animate attributeName="stop-color"
                                         values="#4F46E5;#06B6D4;#4F46E5"
                                         dur="6s"
                                         repeatCount="indefinite"/>
                                <animate attributeName="stop-opacity"
                                         values="0.6;0.8;0.6"
                                         dur="4s"
                                         repeatCount="indefinite"/>
                              </stop>
                              <stop offset="100%">
                                <animate attributeName="stop-color"
                                         values="#06B6D4;#4F46E5;#06B6D4"
                                         dur="6s"
                                         repeatCount="indefinite"/>
                                <animate attributeName="stop-opacity"
                                         values="0.8;0.6;0.8"
                                         dur="4s"
                                         repeatCount="indefinite"/>
                              </stop>
                            </linearGradient>

                            <!-- Particle system -->
                            <radialGradient id="particleGradient">
                              <stop offset="0%" stop-color="#ffffff" stop-opacity="0.8"/>
                              <stop offset="100%" stop-color="#ffffff" stop-opacity="0"/>
                            </radialGradient>
                          </defs>

                          <!-- Dynamic background rings -->
                          <g class="background-rings">
                            <circle cx="50" cy="50" r="48" fill="none" stroke="url(#orbGradient)" stroke-width="0.25" opacity="0.3">
                              <animateTransform attributeName="transform"
                                                type="rotate"
                                                from="0 50 50"
                                                to="360 50 50"
                                                dur="30s"
                                                repeatCount="indefinite"/>
                            </circle>
                            <circle cx="50" cy="50" r="46" fill="none" stroke="url(#orbGradient)" stroke-width="0.25" opacity="0.3">
                              <animateTransform attributeName="transform"
                                                type="rotate"
                                                from="360 50 50"
                                                to="0 50 50"
                                                dur="25s"
                                                repeatCount="indefinite"/>
                            </circle>
                          </g>

                          <!-- Main outer ring with complex animation -->
                          <circle cx="50" cy="50" r="47" fill="none" stroke="url(#orbGradient)" stroke-width="2.5" filter="url(#glow)">
                            <animate attributeName="stroke-dasharray"
                                     from="0,300" to="300,0"
                                     dur="3s"
                                     fill="freeze"/>
                            <animate attributeName="stroke-opacity"
                                     values="0.8;1;0.8"
                                     dur="4s"
                                     repeatCount="indefinite"/>
                          </circle>

                          <!-- Inner geometric patterns -->
                          <g class="inner-geometry" opacity="0.4">
                            <path d="M50 50 L20 25 L80 25 Z" fill="none" stroke="url(#orbGradient)" stroke-width="0.25">
                              <animateTransform attributeName="transform"
                                                type="rotate"
                                                from="0 50 50"
                                                to="360 50 50"
                                                dur="20s"
                                                repeatCount="indefinite"/>
                            </path>
                            <path d="M50 50 L20 75 L80 75 Z" fill="none" stroke="url(#orbGradient)" stroke-width="0.25">
                              <animateTransform attributeName="transform"
                                                type="rotate"
                                                from="360 50 50"
                                                to="0 50 50"
                                                dur="20s"
                                                repeatCount="indefinite"/>
                            </path>
                          </g>

                          <!-- Enhanced V shape with dynamic effects -->
                          <g filter="url(#glow)">
                            <path class="v-shape"
                                  d="M50 80 L20 25 L50 65 L80 25 L50 80"
                                  fill="none"
                                  stroke="url(#mainGradient)"
                                  stroke-width="3"
                                  stroke-linejoin="round"
                                  stroke-linecap="round">
                              <animate attributeName="stroke-dasharray"
                                       from="0,200" to="200,0"
                                       dur="2s"
                                       fill="freeze"/>
                              <animate attributeName="stroke-opacity"
                                       values="0.9;1;0.9"
                                       dur="3s"
                                       repeatCount="indefinite"/>
                            </path>
                          </g>

                          <!-- Subtle accent rings -->
                          <circle cx="50" cy="50" r="44" fill="none" stroke="url(#orbGradient)" stroke-width="0.5" opacity="0.3">
                            <animate attributeName="r"
                                     values="44;46;44"
                                     dur="4s"
                                     repeatCount="indefinite"/>
                            <animate attributeName="stroke-opacity"
                                     values="0.3;0.5;0.3"
                                     dur="4s"
                                     repeatCount="indefinite"/>
                          </circle>

                          <!-- Particle effects -->
                          <g class="particles">
                            <circle cx="50" cy="25" r="0.5" fill="url(#particleGradient)">
                              <animate attributeName="opacity"
                                       values="0;1;0"
                                       dur="3s"
                                       repeatCount="indefinite"/>
                              <animateMotion
                                path="M0 0 Q10 10 0 20 Q-10 30 0 40"
                                dur="3s"
                                repeatCount="indefinite"/>
                            </circle>
                            <circle cx="20" cy="25" r="0.5" fill="url(#particleGradient)">
                              <animate attributeName="opacity"
                                       values="0;1;0"
                                       dur="4s"
                                       repeatCount="indefinite"/>
                              <animateMotion
                                path="M0 0 Q-10 10 0 20 Q10 30 0 40"
                                dur="4s"
                                repeatCount="indefinite"/>
                            </circle>
                            <circle cx="80" cy="25" r="0.5" fill="url(#particleGradient)">
                              <animate attributeName="opacity"
                                       values="0;1;0"
                                       dur="5s"
                                       repeatCount="indefinite"/>
                              <animateMotion
                                path="M0 0 Q10 10 0 20 Q-10 30 0 40"
                                dur="5s"
                                repeatCount="indefinite"/>
                            </circle>
                          </g>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-gray-900 dark:text-white">Vero</span>
                    </div>
                    <button @click="isMobileMenuOpen = false" class="ml-auto lg:hidden text-gray-500 hover:text-gray-600 dark:text-gray-400">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                            <title>Close sidebar</title>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <!-- Navigation -->
                <nav class="flex-1 px-2 py-4 overflow-y-auto space-y-6">
                    <!-- Dashboard -->
                    <div>
                        <a href="/" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>Dashboard</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                            </svg>
                            Dashboard
                        </a>
                    </div>

                    <!-- Data Loader Section -->
                    <div class="space-y-1">
                        <h3 class="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Data Loaders</h3>
                        <div class="space-y-1">
                            <a href="/show-data-loaders" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Data Loaders</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                View All
                            </a>
                            <a href="/add-data-loader" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Add Data Loader</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Add New
                            </a>
                        </div>
                    </div>

                    <!-- Workflows Section -->
                    <div class="space-y-1">
                        <h3 class="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Workflows</h3>
                        <div class="space-y-1">
                            <a href="/simple-workflow" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Simple Workflow</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                Simple Workflow
                            </a>
                            <a href="/extract-workflow" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Extract Workflow</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                                </svg>
                                Extract Workflow
                            </a>
                            <a href="/simple-join-workflow" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <title>Simple Join Workflow</title>  <!-- Add title -->
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M12 4v16m8-8H4"/> <!-- Example icon path -->
                                </svg>
                                Join Workflows
                            </a>
                            <a href="/backtest-strategy" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <title>Backtest Strategy</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                                Backtest Strategy
                            </a>
                        </div>
                    </div>

                    <!-- Reports Section -->
                    <div class="space-y-1">
                        <h3 class="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Reports</h3>
                        <div class="space-y-1">
                            <a href="/simple-report" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Simple Report</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                Simple Report
                            </a>
                            <a href="/forecast-revisits" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Forecast Revisits</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"/>
                                </svg>
                                Forecast Revisits
                            </a>
                            <a href="/backtesting-evals" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Backtesting Evals</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                                Backtesting Evals
                            </a>

                        </div>
                    </div>

                    <!-- Database Section -->
                    <div class="space-y-1">
                        <h3 class="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Database</h3>
                        <div class="space-y-1">
                            <a href="/database-backup" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Database Backup</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"/>
                                </svg>
                                Backup
                            </a>
                        </div>
                    </div>

                    <!-- RL Section -->
                    <div class="space-y-1">
                        <h3 class="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Reinforcement Learning</h3>
                        <div class="space-y-1">
                            <a href="/rl-environments" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>RL Environments</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5"/>
                                </svg>
                                Environments
                            </a>
                            <a href="/rl-training" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>RL Training</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75"
                                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                                Training
                            </a>
                        </div>
                    </div>

                    <!-- AGENTS Section -->
                    <div class="space-y-1">
                        <div class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                            Agents
                        </div>
                        <a href="/research-agents" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>Research Agent</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"/>
                            </svg>
                            Research Agent
                        </a>
                        <a href="/feature-request-agent" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>Feature Request Agent</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"/>
                            </svg>
                            Feature Request Agent
                        </a>
                        <a href="/graphs" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>Graphs</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"/>
                            </svg>
                            Graphs
                        </a>
                        <a href="/toolbox" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>AI Toolbox</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                            </svg>
                            AI Toolbox
                        </a>
                        <a href="/letta" class="flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <div class="flex items-center">
                                <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                    <title>Letta</title>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Letta
                            </div>
                            <span id="letta-status-indicator" class="h-2.5 w-2.5 rounded-full bg-orange-500 transition-colors duration-300" title="Letta server status"></span>
                        </a>
                        <a href="/add-agent" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>Add Agent</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM4 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 0110.374 21c-2.331 0-4.512-.645-6.374-1.766z"/>
                            </svg>
                            Add Agent
                        </a>
                        <a href="/llm" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>LLM Models</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"/>
                            </svg>
                            LLM Models
                        </a>
                        <a href="/add-tool" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/60 transition-colors group">
                            <svg class="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                                <title>Add Tool</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M12 4.5v15m7.5-7.5h-15"/>
                            </svg>
                            Add Tool
                        </a>
                    </div>

                    <!-- Theme Toggle -->
                    <div class="border-t border-gray-200 p-4 dark:border-gray-800">
                        <button @click="darkMode = !darkMode"
                                class="group relative flex w-full items-center justify-center gap-2 rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 ease-in-out
                                       hover:bg-gray-100/70 dark:hover:bg-gray-800/70
                                       active:scale-[0.98] active:duration-75
                                       dark:text-gray-300 dark:hover:text-white">
                            <div class="relative">
                                <svg x-show="!darkMode"
                                     x-transition:enter="transition-transform duration-200 ease-out"
                                     x-transition:enter-start="scale-75 opacity-0"
                                     x-transition:enter-end="scale-100 opacity-100"
                                     class="h-5 w-5 text-amber-500 transition-colors duration-200"
                                     fill="none"
                                     stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="4" stroke-width="2"/>
                                    <path stroke-linecap="round" stroke-width="2" d="M12 2v2m0 16v2M4 12H2m20 0h-2m-2.828-6.828l-1.414 1.414M6.242 17.758l-1.414 1.414m0-12.728l1.414 1.414m12.728 12.728l-1.414-1.414"/>
                                </svg>
                                <svg x-show="darkMode"
                                     x-transition:enter="transition-transform duration-200 ease-out"
                                     x-transition:enter-start="scale-75 rotate-90 opacity-0"
                                     x-transition:enter-end="scale-100 rotate-0 opacity-100"
                                     class="h-5 w-5 text-indigo-400 transition-colors duration-200"
                                     fill="none"
                                     stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646"/>
                                </svg>
                            </div>
                            <span x-text="darkMode ? 'Light Mode' : 'Dark Mode'"
                                  class="font-medium text-gray-700 dark:text-gray-300"></span>
                            <div class="absolute inset-0 transform transition-transform duration-200 ease-in-out group-hover:scale-105 rounded-lg ring-1 ring-gray-200 dark:ring-gray-800 group-hover:ring-gray-300 dark:group-hover:ring-gray-700 -z-10"></div>
                        </button>
                    </div>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col min-h-screen">
            <!-- Top Navigation -->
            <header class="sticky top-0 z-10 flex items-center h-16 bg-white shadow dark:bg-gray-800">
                <button @click="isMobileMenuOpen = true"
                        class="px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 lg:hidden">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" aria-hidden="true" viewBox="0 0 24 24">
                        <title>Open sidebar</title>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
                <div class="flex flex-1 justify-between px-4">
                    <div class="flex flex-1">
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white my-auto">
                            {% block header %}{% endblock %}
                        </h1>
                    </div>
                    <!-- Server restart button -->
                    <div class="flex items-center">
                        <button
                            id="server-restart-btn"
                            class="relative flex items-center justify-center w-10 h-10 rounded-full bg-zinc-900 text-white hover:bg-zinc-800 dark:bg-zinc-800 dark:hover:bg-zinc-700 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-zinc-500 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-all duration-200 group"
                            aria-label="Reboot Server">
                            <!-- Tooltip -->
                            <span class="absolute invisible opacity-0 right-full mr-2 px-2 py-1 text-xs font-medium rounded bg-zinc-900 text-white dark:bg-zinc-700 whitespace-nowrap transition-all group-hover:visible group-hover:opacity-100">
                                Reboot Server
                            </span>
                            <!-- Default restart icon -->
                            <span class="group-[.animate-loading]:hidden">
                                <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.93 11.5H3.5V8.07" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M4.39 16.5C5.77 19.64 8.95 21.75 12.5 21.75C17.33 21.75 21.25 17.83 21.25 13C21.25 8.17 17.33 4.25 12.5 4.25C9.13 4.25 6.19 6.1 4.75 8.87" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12.5 7.5V13L15.75 16.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <!-- Loading spinner -->
                            <span class="hidden group-[.animate-loading]:block animate-spin">
                                <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button>
                        <button id="server-shutdown-btn" type="button" class="group ml-2 inline-flex items-center px-3 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 disabled:opacity-60 disabled:pointer-events-none relative overflow-hidden">
                            <span class="inline-flex items-center">
                                <span class="mr-2">Shutdown</span>
                                <span class="group-[.animate-loading]:hidden">
                                    <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M16.56,5.44L15.11,6.89C16.84,7.94 18,9.83 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12C6,9.83 7.16,7.94 8.88,6.88L7.44,5.44C5.36,6.88 4,9.28 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12C20,9.28 18.64,6.88 16.56,5.44M13,3H11V13H13" fill="currentColor"/>
                                    </svg>
                                </span>
                                <span class="hidden group-[.animate-loading]:block animate-spin">
                                    <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                            </span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main id="main-content" class="flex-1">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {% block content %}{% endblock %}
                </div>
            </main>

            <!-- Footer -->
            <footer class="bg-white shadow dark:bg-gray-800">
                <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                    <p class="text-center text-sm text-gray-500 dark:text-gray-400">
                        {% block footer %}
                        &copy; {{ current_year }} Vero. All rights reserved.
                        {% endblock %}
                    </p>
                </div>
            </footer>
        </div>
    </div>

    {% block scripts %}{% endblock %}

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.store('darkMode', localStorage.getItem('darkMode') === 'true');
        });
    </script>

    <!-- Server scripts -->
    <script src="/server-restart/restart.js"></script>
    <script src="/server-restart/shutdown.js"></script>
    <script src="/server-restart/letta-status.js"></script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>

// --- DOM Element References ---
const graphTitleEl = document.getElementById('graphTitle');
const graphDescriptionEl = document.getElementById('graphDescription');
const graphConfigSectionEl = document.getElementById('graphConfigurationSection');
const savedGraphsSectionEl = document.getElementById('savedGraphsSection');
const graphTypesListEl = document.getElementById('graphTypesList');
const savedGraphsListEl = document.getElementById('savedGraphsList');
const graphFormAreaEl = document.getElementById('graphFormArea');
const runGraphBtnEl = document.getElementById('runGraphBtn');
const mermaidDiagramEl = document.getElementById('mermaidDiagram');
const executionInfoEl = document.getElementById('executionInfo');
const graphOutputEl = document.getElementById('graphOutput');
const toastEl = document.getElementById('toast');
// ---------------------------

document.addEventListener('DOMContentLoaded', () => {
    // Initialize Mermaid
    mermaid.initialize({
        startOnLoad: true,
        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'default',
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        }
    });

    // Set initial view to Saved Graphs
    if (graphTitleEl) graphTitleEl.textContent = 'Saved Graphs';
    if (graphDescriptionEl) graphDescriptionEl.textContent = 'View and manage your saved graph configurations.';
    if (graphConfigSectionEl) graphConfigSectionEl.classList.add('hidden');
    if (savedGraphsSectionEl) savedGraphsSectionEl.classList.remove('hidden');

    // Load available graphs for the sidebar
    loadAvailableGraphs();
    // Load saved graphs for the main initial view
    loadSavedGraphs();

    // Initialize run button handler
    if (runGraphBtnEl) {
        runGraphBtnEl.addEventListener('click', async () => {
            try {
                await runGraph();
            } catch (error) {
                console.error('[ERROR] Error in run button handler:', error);
                showToast('Error running graph: ' + error.message, 'error');
            }
        });
    } else {
        console.error('[ERROR] Run button not found');
    }

    // Sidebar toggle handler is now in sidebar.js
});

// --- Sidebar Management now in sidebar.js ---

async function loadAvailableGraphs() {
    try {
        const response = await ApiClient.getAvailableGraphs();
        renderGraphTypes(response.data);
    } catch (error) {
        console.error('Error loading available graphs:', error);
        showToast('Error loading available graphs', 'error');
    }
}

function renderGraphTypes(graphs) {
    if (!graphTypesListEl) return;
    graphTypesListEl.innerHTML = '';

    // Add "View All Saved Graphs" link first
    const viewAllLink = document.createElement('a');
    viewAllLink.href = '#';
    viewAllLink.id = 'viewAllSavedGraphsLink'; // Add ID for potential styling/selection
    viewAllLink.className = 'flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-muted-foreground hover:text-primary hover:bg-accent transition-all group border-b border-border mb-2'; // Added border and margin
    viewAllLink.innerHTML = `
        <svg class="mr-3 h-4 w-4 text-muted-foreground group-hover:text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/> <!-- List icon -->
        </svg>
        View All Saved Graphs
    `;
    viewAllLink.addEventListener('click', (e) => {
        e.preventDefault();
        showSavedGraphs();
        // Optionally highlight this link
        document.querySelectorAll('#graphTypesList a').forEach(item => item.classList.remove('bg-accent', 'text-primary'));
        viewAllLink.classList.add('bg-accent', 'text-primary');
    });
    graphTypesListEl.appendChild(viewAllLink);

    graphs.forEach(graph => {
        const item = document.createElement('a');
        item.href = '#';
        item.className = 'flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-muted-foreground hover:text-primary hover:bg-accent transition-all group'; // Adjusted styling
        item.dataset.graphId = graph.id;
        
        item.innerHTML = `
            <svg class="mr-3 h-4 w-4 text-muted-foreground group-hover:text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            ${graph.name}
        `;

        item.addEventListener('click', (e) => {
            e.preventDefault();
            selectGraphType(graph);
        });

        graphTypesListEl.appendChild(item);
    });
}

let llmModels = []; // Store LLM models globally

async function selectGraphType(graph) {
    // Update active state in menu
    document.querySelectorAll('#graphTypesList a').forEach(item => {
        item.classList.remove('bg-accent', 'text-primary'); // Use accent/primary for active
    });
    const activeItem = document.querySelector(`#graphTypesList a[data-graph-id="${graph.id}"]`);
    if (activeItem) activeItem.classList.add('bg-accent', 'text-primary');

    // Show configuration section, hide list view
    if (savedGraphsSectionEl) savedGraphsSectionEl.classList.add('hidden');
    if (graphConfigSectionEl) graphConfigSectionEl.classList.remove('hidden');

    // Update title and description for configuration view
    if (graphTitleEl) graphTitleEl.textContent = `Configure: ${graph.name}`;
    if (graphDescriptionEl) graphDescriptionEl.textContent = `Set up stages and LLMs for the ${graph.name.toLowerCase()} graph type.`;

    // Close the sidebar when a graph type is selected - now using SidebarManager
    window.SidebarManager.closeSidebar();

    // Load LLM models if not already loaded
    if (!llmModels.length) {
        try {
            const response = await ApiClient.getLLMModels();
            llmModels = response.data;
        } catch (error) {
            console.error('Error loading LLM models:', error);
            showToast('Error loading LLM models', 'error');
            return;
        }
    }

    // Try to load existing graph configuration
    let existingGraph;
    try {
        const response = await ApiClient.listGraphs();
        existingGraph = response.data.find(g => g.name === graph.id);
    } catch (error) {
        console.error('Error loading existing graph:', error);
    }

    // Store the MongoDB ID in a data attribute and render form
    if (!graphFormAreaEl) return;
    graphFormAreaEl.innerHTML = `
        <form id="graphConfigForm" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-muted-foreground mb-1"> <!-- Shadcn -->
                    Graph Type (Readonly)
                </label>
                <input type="text" 
                       name="name" 
                       value="${graph.id}"
                       readonly
                       class="w-full px-3 py-2 bg-input border border-border rounded-lg shadow-sm focus:ring-2 focus:ring-ring focus:border-transparent transition-shadow cursor-not-allowed text-muted-foreground"> <!-- Shadcn -->
                <input type="hidden" 
                       name="graph_mongo_id" 
                       value="${existingGraph?._id || ''}"
                       data-graph-type="${graph.id}">
            </div>
            
            <!-- Stages Section -->
            <div class="space-y-4">
                <div class="flex justify-between items-center mb-3">
                    <label class="block text-sm font-medium text-card-foreground">
                        Stages Configuration
                    </label>
                    <button type="button" 
                            onclick="addStage()"
                            class="inline-flex items-center px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring transition-colors">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add Stage
                    </button>
                </div>
                <div id="stagesContainer" class="space-y-4">
                    <!-- Stages will be dynamically added here -->
                </div>
            </div>

            <div class="flex justify-end">
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground text-sm font-medium rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                    </svg>
                    Save Graph
                </button>
            </div>
        </form>
    `;

    // Add form submission handler
    const form = document.getElementById('graphConfigForm');
    if (form) {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const stages = getStagesData();
            
            const data = {
                name: graph.id, // Use graph type ID as name
                stages: stages
            };
            generateGraph(data);
        });
    } else {
        console.error("Could not find graphConfigForm to attach listener");
    }

    // Add existing stages or one empty stage
    const stagesContainer = document.getElementById('stagesContainer');
    if (stagesContainer) {
        if (existingGraph && existingGraph.stages) {
            existingGraph.stages.forEach(stage => addStage(stage)); // Pass existing stage data
        } else {
            addStage(); // Add one empty stage
        }
    } else {
        console.error("Could not find stagesContainer");
    }

    // Visualize the graph using our new visualizer
    await window.graphVisualizer.visualizeGraph(graph.id);

    // Enable the run button
    if (runGraphBtnEl) runGraphBtnEl.disabled = false;
}

// Modified addStage to accept optional data
function addStage(stageData = null) {
    const stagesContainer = document.getElementById('stagesContainer');
    if (!stagesContainer) return;
    const stageIndex = stagesContainer.children.length;
    
    const stageElement = document.createElement('div');
    // Add draggable attributes and styles
    stageElement.className = 'flex gap-4 items-center p-4 bg-muted/50 rounded-lg border border-border cursor-move'; 
    stageElement.draggable = true;
    stageElement.setAttribute('data-stage-index', stageIndex);
    
    const llmOptions = llmModels.map(model => {
        const isSelected = stageData && model.id === stageData.llm_id;
        return `<option value="${model.id}" ${isSelected ? 'selected' : ''}>${model.name}</option>`;
    }).join('');

    stageElement.innerHTML = `
        <div class="flex items-center gap-2 flex-1">
            <svg class="w-5 h-5 text-muted-foreground cursor-move" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
            <div class="flex-1">
                <label class="sr-only" for="stage_name_${stageIndex}">Stage Name</label>
                <input type="text" 
                       id="stage_name_${stageIndex}" 
                       name="stage_name_${stageIndex}" 
                       placeholder="Enter Stage Name"
                       value="${stageData?.name || ''}" 
                       class="w-full px-3 py-2 bg-input border border-border rounded-lg shadow-sm focus:ring-2 focus:ring-ring focus:border-transparent transition-shadow" 
                       required>
            </div>
        </div>
        <div class="flex-1">
            <label class="sr-only" for="stage_llm_${stageIndex}">Select LLM Model</label>
            <select id="stage_llm_${stageIndex}" 
                    name="stage_llm_${stageIndex}"
                    class="w-full px-3 py-2 bg-input border border-border rounded-lg shadow-sm focus:ring-2 focus:ring-ring focus:border-transparent transition-shadow appearance-none pr-8 bg-no-repeat bg-right" 
                    style="background-image: url('data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 fill=%22none%22 viewBox=%220 0 20 20%22%3E%3Cpath stroke=%22%236B7280%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22 stroke-width=%221.5%22 d=%22M6 8l4 4 4-4%22/%3E%3C/svg%3E'); background-position: right 0.5rem center; background-size: 1.5em 1.5em;" 
                    required>
                <option value="">Select LLM Model</option>
                ${llmOptions}
            </select>
        </div>
        <button type="button" 
                onclick="removeStage(this)"
                title="Remove Stage"
                class="p-2 text-muted-foreground hover:text-destructive rounded-lg hover:bg-destructive/10 transition-colors flex-shrink-0">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </button>
    `;

    // Add drag event listeners
    stageElement.addEventListener('dragstart', handleDragStart);
    stageElement.addEventListener('dragover', handleDragOver);
    stageElement.addEventListener('drop', handleDrop);
    stageElement.addEventListener('dragend', handleDragEnd);

    stagesContainer.appendChild(stageElement);
}

// Add drag and drop handlers
function handleDragStart(e) {
    e.target.classList.add('opacity-50');
    e.dataTransfer.setData('text/plain', e.target.dataset.stageIndex);
}

function handleDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
}

function handleDrop(e) {
    e.preventDefault();
    const fromIndex = e.dataTransfer.getData('text/plain');
    const toElement = e.target.closest('[data-stage-index]');
    if (!toElement) return;
    const toIndex = toElement.dataset.stageIndex;
    
    const stagesContainer = document.getElementById('stagesContainer');
    const stages = Array.from(stagesContainer.children);
    
    // Swap the stages
    const fromStage = stages[fromIndex];
    const toStage = stages[toIndex];
    
    if (fromIndex < toIndex) {
        toStage.after(fromStage);
    } else {
        toStage.before(fromStage);
    }
    
    // Update data-stage-index attributes
    stagesContainer.querySelectorAll('[data-stage-index]').forEach((stage, index) => {
        stage.dataset.stageIndex = index;
    });
}

function handleDragEnd(e) {
    e.target.classList.remove('opacity-50');
}

function removeStage(button) {
    // Find the closest parent div that represents the stage and remove it
    const stageElement = button.closest('.flex.gap-4.items-center'); 
    if (stageElement) {
        stageElement.remove();
    }
}

function getStagesData() {
    const stages = [];
    const stagesContainer = document.getElementById('stagesContainer');
    if (!stagesContainer) return stages;
    
    // Use a more specific selector for the stage wrapper divs
    stagesContainer.querySelectorAll('div.flex.gap-4.items-center').forEach((stageElement) => {
        // Find inputs/selects *within* this specific stageElement
        const nameInput = stageElement.querySelector('input[type="text"][name^="stage_name_"]');
        const llmSelect = stageElement.querySelector('select[name^="stage_llm_"]');
        
        if (nameInput && llmSelect && nameInput.value.trim() !== '' && llmSelect.value !== '') { 
            stages.push({
                name: nameInput.value.trim(),
                llm_id: llmSelect.value
            });
        } else {
            console.warn("Skipping incomplete stage data");
        }
    });
    
    return stages;
}

async function generateGraph(data) {
    try {
        // Create or update the graph with stages
        await ApiClient.createGraph(data);
        showToast('Graph configuration saved', 'success');
        
        // Show saved graphs view and open sidebar
        showSavedGraphs();
    } catch (error) {
        console.error('Error saving graph:', error);
        showToast('Error saving graph: ' + error.message, 'error');
    }
}

function showSavedGraphs() {
    // Hide configuration and show saved graphs list
    if (graphConfigSectionEl) graphConfigSectionEl.classList.add('hidden');
    if (savedGraphsSectionEl) savedGraphsSectionEl.classList.remove('hidden');
    
    // Update title and description for the list view
    if (graphTitleEl) graphTitleEl.textContent = 'Saved Graphs';
    if (graphDescriptionEl) graphDescriptionEl.textContent = 'View and manage your saved graph configurations.';
    
    // Deactivate sidebar selection
    document.querySelectorAll('#graphTypesList a').forEach(item => {
        item.classList.remove('bg-accent', 'text-primary');
    });

    // Open the sidebar when returning to the saved graphs view - now using SidebarManager
    window.SidebarManager.openSidebar();

    // Reload saved graphs to ensure the list is up-to-date
    loadSavedGraphs();
}

function showToast(message, type = 'success') {
    if (!toastEl) return;
    const bgColor = type === 'success' ? 'bg-green-600' : 'bg-red-600'; // Darker shades for better contrast
    
    toastEl.className = `fixed bottom-4 right-4 px-5 py-3 rounded-lg shadow-lg text-white ${bgColor} transition-all duration-300 ease-out transform translate-y-2 opacity-0 z-50`; // Start hidden
    toastEl.textContent = message;
    
    // Animate in
    requestAnimationFrame(() => { 
        toastEl.classList.remove('translate-y-2', 'opacity-0');
    });
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        toastEl.classList.add('opacity-0', 'translate-y-2');
        // Remove from DOM after animation
        toastEl.addEventListener('transitionend', () => toastEl.classList.add('hidden'), { once: true });
    }, 3000);
}

async function loadSavedGraphs() {
    try {
        // Load LLM models first if not already loaded
        if (!llmModels.length) {
            const llmResponse = await ApiClient.getLLMModels();
            llmModels = llmResponse.data;
        }

        const response = await ApiClient.listGraphs();
        if (Array.isArray(response.data)) {
            renderGraphs(response.data);
        } else {
            console.error("Invalid data received from listGraphs:", response);
            renderGraphs([]); // Render empty state
        }
    } catch (error) {
        console.error('Error loading saved graphs:', error);
        showToast('Error loading saved graphs: ' + error.message, 'error');
        renderGraphs([]); // Render empty state on error
    }
}

function renderGraphs(graphs) {
    if (!savedGraphsListEl) {
        console.error('savedGraphsList element not found');
        return;
    }

    savedGraphsListEl.innerHTML = ''; // Clear previous list

    if (!graphs || graphs.length === 0) {
        savedGraphsListEl.innerHTML = `
            <div class="col-span-1 sm:col-span-2 lg:col-span-3 text-center text-muted-foreground py-12">
                <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 13h6m-3-3v6m-8 6V7a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2z" />
                </svg>
                <h3 class="mt-2 text-lg font-semibold">No Saved Graphs Yet</h3>
                <p class="mt-1 text-sm">Select a graph type from the sidebar to create your first configuration.</p>
            </div>
        `;
        return;
    }

    graphs.forEach(graph => {
        const item = document.createElement('div');
        // Apply Shadcn-inspired styling for grid cards
        item.className = 'bg-card text-card-foreground border rounded-lg shadow-sm hover:shadow-md transition-shadow flex flex-col'; 

        item.innerHTML = `
            <div class="p-6 flex-grow flex flex-col">
                <div class="border-b border-border pb-4 mb-4 flex items-center justify-between">
                    <h3 class="text-lg font-semibold flex items-center text-foreground">
                        <svg class="w-5 h-5 mr-2 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <span class="truncate">${graph.name}</span>
                    </h3>
                    <!-- Add Edit/Delete buttons here if needed -->
                </div>
                <div class="space-y-3 flex-grow mb-4"> <!-- Reduced stage spacing -->
                    <h4 class="text-sm font-medium text-muted-foreground mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-1.5 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                        Configured Stages
                    </h4>
                    <div class="space-y-2 pl-5 text-sm"> <!-- Indented stages -->
                        ${graph.stages && graph.stages.length > 0 ? graph.stages.map(stage => {
                            const model = llmModels.find(m => m.id === stage.llm_id);
                            return `
                                <div class="flex items-center space-x-2 py-1.5 px-3 bg-muted/50 rounded-md border border-border">
                                    <span class="font-medium text-foreground truncate flex-1" title="${stage.name}">${stage.name}</span>
                                    ${model ? `
                                        <span class="flex items-center text-xs text-muted-foreground bg-background px-2 py-0.5 rounded border border-border flex-shrink-0" title="${model.name}">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                            </svg>
                                            <span class="truncate">${model.name}</span>
                                        </span>
                                    ` : '<span class="text-destructive text-xs px-2 py-0.5 rounded border border-destructive/50 bg-destructive/10 flex-shrink-0">Unknown LLM</span>'}
                                </div>
                            `;
                        }).join('') : '<div class="text-muted-foreground italic text-xs pl-1">No stages configured</div>'}
                    </div>
                </div>
                <!-- Removed Footer -->
            </div>
        `;
        savedGraphsListEl.appendChild(item);
    });
}

async function runGraph() {
    // Get both the graph type and MongoDB ID
    const graphMongoIdInput = document.querySelector('#graphConfigForm input[name="graph_mongo_id"]'); // More specific selector
    const graphType = graphMongoIdInput?.dataset.graphType;
    const graphMongoId = graphMongoIdInput?.value;
    
    if (!graphType) {
        showToast('Cannot run: Graph type is missing.', 'error');
        return;
    }

    try {
        let graphIdToRun = graphMongoId;

        // Get current stages from the form
        const currentStages = getStagesData();

        // If graph hasn't been saved (no mongo ID), or if stages changed, save/update first
        let needsSave = !graphMongoId;
        if (graphMongoId) {
            // Compare current form stages with potentially loaded stages (need to fetch)
            // For simplicity, we will *always* save before running if in config mode
            // This ensures the run uses the *exact* config shown on screen.
            needsSave = true; 
        }

        if (needsSave) {
            showToast('Saving configuration before running...', 'info');
            const saveResponse = await ApiClient.createGraph({
                name: graphType,
                stages: currentStages
            });
            graphIdToRun = saveResponse.id;
            // Update the hidden input just in case
            if(graphMongoIdInput) graphMongoIdInput.value = graphIdToRun;
            showToast('Configuration saved. Starting run...', 'success');
            await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for user to see message
        } else {
             showToast('Starting graph run...', 'info');
        }

        // Now run with the confirmed graph ID and current stages
        const inputData = {
            // Minimal input, specific graphs might need more
            // Consider adding a generic input field?
        }; 
        
        if (!graphIdToRun) {
            throw new Error("Failed to get a valid Graph ID to run.");
        }

        // Start the graph execution
        const result = await ApiClient.runGraph(graphIdToRun, inputData);
        
        // Show the result
        if (graphOutputEl) {
            graphOutputEl.innerHTML = `
                <div class="mt-4 p-4 bg-muted/50 rounded-lg border border-border">
                    <h4 class="text-base font-semibold text-foreground mb-2">
                        Execution Output (Task ID: ${result.task_id})
                    </h4>
                    <pre class="overflow-auto text-sm bg-background p-3 rounded-md border border-border">${JSON.stringify(result.data, null, 2)}</pre>
                </div>
            `;
        } else {
             console.error("graphOutputEl not found");
        }
        
        showToast('Graph execution finished.', 'success');
        
    } catch (error) {
        console.error('[ERROR] Graph execution failed:', error);
        showToast(`Error running graph: ${error.message || 'Unknown error'}`, 'error');
    }
}

// Utility function for copying to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showToast('Failed to copy text', 'error');
    });
} 
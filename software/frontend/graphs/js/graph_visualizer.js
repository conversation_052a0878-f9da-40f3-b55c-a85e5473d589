class GraphVisualizer {
    constructor() {
        // Initialize Mermaid with enhanced configuration
        mermaid.initialize({
            startOnLoad: true,
            theme: document.documentElement.classList.contains('dark') ? 'dark' : 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
                nodeSpacing: 50,
                rankSpacing: 50,
                padding: 10
            },
            themeVariables: {
                // Light theme colors
                primaryColor: '#4f46e5',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#4338ca',
                lineColor: '#9ca3af',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#e5e7eb',
                // Dark theme specific overrides
                darkMode: document.documentElement.classList.contains('dark'),
                background: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff',
                mainBkg: document.documentElement.classList.contains('dark') ? '#374151' : '#ffffff',
                nodeBorder: document.documentElement.classList.contains('dark') ? '#4b5563' : '#e5e7eb',
                clusterBkg: document.documentElement.classList.contains('dark') ? '#1f2937' : '#f9fafb',
                clusterBorder: document.documentElement.classList.contains('dark') ? '#4b5563' : '#e5e7eb'
            }
        });

        // Listen for theme changes
        this.setupThemeListener();
    }

    setupThemeListener() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'class') {
                    const isDark = document.documentElement.classList.contains('dark');
                    mermaid.initialize({
                        theme: isDark ? 'dark' : 'default',
                        themeVariables: {
                            darkMode: isDark,
                            background: isDark ? '#1f2937' : '#ffffff',
                            mainBkg: isDark ? '#374151' : '#ffffff',
                            nodeBorder: isDark ? '#4b5563' : '#e5e7eb',
                            clusterBkg: isDark ? '#1f2937' : '#f9fafb',
                            clusterBorder: isDark ? '#4b5563' : '#e5e7eb'
                        }
                    });
                    const currentDiagram = document.querySelector('.mermaid');
                    if (currentDiagram) {
                        this.renderMermaidDiagram(currentDiagram.textContent);
                    }
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true
        });
    }

    async visualizeGraph(graphId) {
        try {
            const diagram = document.getElementById('mermaidDiagram');
            if (!diagram) {
                console.error('Mermaid diagram container not found');
                return;
            }

            // Show loading state
            diagram.innerHTML = `
                <div class="flex items-center justify-center p-8 text-gray-500 dark:text-gray-400">
                    <div class="animate-spin h-8 w-8 mr-3">
                        <svg class="w-full h-full" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    Loading graph visualization...
                </div>
            `;

            // Fetch graph visualization from backend
            const response = await ApiClient.getGraphVisualization(graphId);
            let mermaidCode = response.data;

            // Enhance the Mermaid code with better styling
            mermaidCode = this.enhanceMermaidCode(mermaidCode);

            // Render the diagram
            await this.renderMermaidDiagram(mermaidCode);

        } catch (error) {
            console.error('Error visualizing graph:', error);
            const diagram = document.getElementById('mermaidDiagram');
            if (diagram) {
                diagram.innerHTML = `
                    <div class="text-red-500 dark:text-red-400 p-4 text-center">
                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Error loading graph visualization: ${error.message}
                    </div>
                `;
            }
        }
    }

    enhanceMermaidCode(code) {
        // Add custom styling directives
        const styleDirectives = `%%{init: {'theme': '${document.documentElement.classList.contains('dark') ? 'dark' : 'default'}'}}%%\n`;

        // Add class definitions for different node types
        const classDefinitions = `
            classDef default stroke:#d1d5db,stroke-width:2px;
            classDef start fill:#22c55e,stroke:#16a34a,color:white,stroke-width:2px;
            classDef end fill:#ef4444,stroke:#dc2626,color:white,stroke-width:2px;
            classDef decision fill:#3b82f6,stroke:#2563eb,color:white,stroke-width:2px;
            classDef process fill:#f3f4f6,stroke:#6366f1,stroke-width:2px;
            classDef io fill:#8b5cf6,stroke:#7c3aed,color:white,stroke-width:2px;
            classDef model fill:#ec4899,stroke:#db2777,color:white,stroke-width:2px;
            classDef subgraph fill:transparent,stroke:#6366f1,stroke-width:2px,stroke-dasharray:5 5;
        `;

        // Start with the base code
        let enhancedCode = styleDirectives + code;

        // Add class definitions if they don't exist
        if (!enhancedCode.includes('classDef default')) {
            enhancedCode = classDefinitions + enhancedCode;
        }

        // Replace node definitions with styled versions
        enhancedCode = enhancedCode
            // Style start/end nodes
            .replace(/\[Start\]/g, '[🚀 Start]:::start')
            .replace(/\[End\]/g, '[🏁 End]:::end')
            // Style decision nodes
            .replace(/\{([^}]+)\}/g, '{$1}:::decision')
            // Style process nodes
            .replace(/\[(Process[^\]]+)\]/g, '[$1]:::process')
            .replace(/\[(Analyze[^\]]+)\]/g, '[$1]:::process')
            // Style I/O nodes
            .replace(/\[(Generate[^\]]+)\]/g, '[⚡ $1]:::io')
            .replace(/\[(Create[^\]]+)\]/g, '[⚡ $1]:::io')
            .replace(/\[(Save[^\]]+)\]/g, '[💾 $1]:::io')
            .replace(/\[(Load[^\]]+)\]/g, '[📂 $1]:::io')
            .replace(/\[(Read[^\]]+)\]/g, '[📖 $1]:::io')
            .replace(/\[(Write[^\]]+)\]/g, '[✍️ $1]:::io')
            // Style model/LLM nodes
            .replace(/\[(Model[^\]]+)\]/g, '[🤖 $1]:::model')
            .replace(/\[(LLM[^\]]+)\]/g, '[🤖 $1]:::model')
            .replace(/\[(Call[^\]]+)\]/g, '[📞 $1]:::model')
            // Style subgraphs
            .replace(/subgraph ([^\n]+)/g, 'subgraph $1:::subgraph');

        return enhancedCode;
    }

    async renderMermaidDiagram(mermaidCode) {
        const diagram = document.getElementById('mermaidDiagram');
        diagram.innerHTML = '';

        const diagramDiv = document.createElement('div');
        diagramDiv.className = 'mermaid';
        diagramDiv.textContent = mermaidCode;
        diagram.appendChild(diagramDiv);

        try {
            await mermaid.init(undefined, '.mermaid');
        } catch (error) {
            console.error('Error rendering Mermaid diagram:', error);
            diagram.innerHTML = `
                <div class="text-red-500 dark:text-red-400 p-4 text-center">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Error rendering diagram: ${error.message}
                </div>
            `;
        }
    }
}

// Create a global instance
window.graphVisualizer = new GraphVisualizer(); 
// Create a new file with sidebar functionality

// Sidebar DOM Elements
const sidebarEl = document.getElementById('sidebar');
const mainContentEl = document.getElementById('mainContent');
const toggleSidebarBtnEl = document.getElementById('toggleSidebarBtn');
const sidebarIconEl = document.getElementById('sidebarIcon');

// Track sidebar state - starts open by default
let isSidebarOpen = true;

// Apply correct classes and styles based on state
function applyStateStyles(isOpen) {
    if (!sidebarEl || !mainContentEl || !toggleSidebarBtnEl) return; // Ensure elements exist

    if (isOpen) {
        sidebarEl.classList.remove('-translate-x-full');
        mainContentEl.classList.add('ml-64');
        toggleSidebarBtnEl.style.left = '17rem'; // Position button next to open sidebar (16rem width + 1rem spacing)
        if (sidebarIconEl) sidebarIconEl.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"/>'; // Collapse icon
    } else {
        sidebarEl.classList.add('-translate-x-full');
        mainContentEl.classList.remove('ml-64');
        toggleSidebarBtnEl.style.left = '1rem'; // Position button near edge when sidebar is closed (matches left-4)
        if (sidebarIconEl) sidebarIconEl.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>'; // Expand icon
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // Add toggle event listener
    if (toggleSidebarBtnEl) {
        toggleSidebarBtnEl.addEventListener('click', toggleSidebar);
    } else {
        console.error('[ERROR] Toggle sidebar button not found');
    }

    // Set initial visual state
    applyStateStyles(isSidebarOpen);
});

// Toggle sidebar open/closed
function toggleSidebar() {
    isSidebarOpen = !isSidebarOpen;
    applyStateStyles(isSidebarOpen);
}

// Keep functions accessible if needed, though direct calls are less likely now
window.SidebarManager = {
    toggleSidebar,
    // Expose open/close if direct control might be needed externally
    openSidebar: () => { if (!isSidebarOpen) toggleSidebar(); },
    closeSidebar: () => { if (isSidebarOpen) toggleSidebar(); }
}; 
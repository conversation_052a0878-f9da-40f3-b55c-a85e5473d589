function initializeUIHandlers() {
    // Modal handlers
    const createGraphBtn = document.getElementById('createGraphBtn');
    const createGraphModal = document.getElementById('createGraphModal');
    const closeModal = document.getElementById('closeModal');
    const cancelCreate = document.getElementById('cancelCreate');
    const createGraphForm = document.getElementById('createGraphForm');

    // Modal event listeners
    createGraphBtn.addEventListener('click', () => {
        createGraphModal.classList.remove('hidden');
    });

    [closeModal, cancelCreate].forEach(element => {
        element.addEventListener('click', () => {
            createGraphModal.classList.add('hidden');
            createGraphForm.reset();
        });
    });

    // Close modal on outside click
    createGraphModal.addEventListener('click', (e) => {
        if (e.target === createGraphModal) {
            createGraphModal.classList.add('hidden');
            createGraphForm.reset();
        }
    });

    // Form submission
    createGraphForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(createGraphForm);
        const graphData = {
            name: formData.get('name')
        };

        try {
            await ApiClient.createGraph(graphData);
            createGraphModal.classList.add('hidden');
            createGraphForm.reset();
            loadSavedGraphs(); // Reload graphs after creation
            showToast('Graph created successfully');
        } catch (error) {
            console.error('Error creating graph:', error);
            showToast('Error creating graph: ' + error.message, 'error');
        }
    });
}

async function editGraph(graphId) {
    try {
        const response = await ApiClient.getGraph(graphId);
        const graph = response.data;
        
        // Update title and description
        document.getElementById('graphTitle').textContent = `Edit ${graph.name}`;
        document.getElementById('graphDescription').textContent = 'Edit your graph configuration';

        // Get form area and create form
        const formArea = document.getElementById('graphFormArea');
        formArea.innerHTML = `
            <form id="graphConfigForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Graph Name
                    </label>
                    <input type="text" 
                           name="name" 
                           value="${graph.name}"
                           readonly
                           class="w-full px-3 py-2 border rounded-md dark:border-gray-600 dark:bg-gray-700 bg-gray-50 dark:bg-gray-600 cursor-not-allowed"
                           placeholder="Graph name">
                </div>
                
                <!-- Stages Section -->
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Stages
                        </label>
                        <button type="button" 
                                onclick="addStage()"
                                class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Stage
                        </button>
                    </div>
                    <div id="stagesContainer" class="space-y-4">
                        <!-- Stages will be dynamically added here -->
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                        </svg>
                        Save Changes
                    </button>
                </div>
            </form>
        `;

        // Add form submission handler
        document.getElementById('graphConfigForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const stages = getStagesData();
            
            try {
                await ApiClient.updateGraphStages(graphId, stages);
                showToast('Graph stages updated successfully');
                loadSavedGraphs(); // Reload graphs after update
            } catch (error) {
                console.error('Error updating graph stages:', error);
                showToast('Error updating graph stages: ' + error.message, 'error');
            }
        });

        // Load existing stages
        if (graph.stages && graph.stages.length > 0) {
            graph.stages.forEach(stage => {
                addStage();
                const stagesContainer = document.getElementById('stagesContainer');
                const lastStage = stagesContainer.lastElementChild;
                const nameInput = lastStage.querySelector('input[type="text"]');
                const llmSelect = lastStage.querySelector('select');
                
                if (nameInput && llmSelect) {
                    nameInput.value = stage.name;
                    llmSelect.value = stage.llm_id;
                }
            });
        } else {
            // Add one empty stage if none exist
            addStage();
        }
    } catch (error) {
        console.error('Error getting graph details:', error);
        showToast('Error getting graph details: ' + error.message, 'error');
    }
}

async function deleteGraph(graphId) {
    if (!confirm('Are you sure you want to delete this graph?')) {
        return;
    }

    try {
        await ApiClient.deleteGraph(graphId);
        showToast('Graph deleted successfully');
        loadSavedGraphs(); // Reload graphs after deletion
    } catch (error) {
        console.error('Error deleting graph:', error);
        showToast('Error deleting graph: ' + error.message, 'error');
    }
} 
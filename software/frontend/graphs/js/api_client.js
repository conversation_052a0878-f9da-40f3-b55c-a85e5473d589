const ApiClient = {
    async listGraphs() {
        const response = await fetch('/api/graphs/graphs');
        if (!response.ok) {
            throw new Error('Failed to fetch graphs');
        }
        return response.json();
    },

    async createGraph(graphData) {
        const response = await fetch('/api/graphs/graphs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(graphData),
        });
        if (!response.ok) {
            throw new Error('Failed to create graph');
        }
        return response.json();
    },

    async getGraph(graphId) {
        const response = await fetch(`/api/graphs/graphs/${graphId}`);
        if (!response.ok) {
            throw new Error('Failed to fetch graph');
        }
        return response.json();
    },

    async updateGraph(graphId, graphData) {
        const response = await fetch(`/api/graphs/graphs/${graphId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(graphData),
        });
        if (!response.ok) {
            throw new Error('Failed to update graph');
        }
        return response.json();
    },

    async deleteGraph(graphId) {
        const response = await fetch(`/api/graphs/graphs/${graphId}`, {
            method: 'DELETE',
        });
        if (!response.ok) {
            throw new Error('Failed to delete graph');
        }
        return response.json();
    },

    async getGraphVisualization(graphId) {
        const response = await fetch(`/api/graphs/graphs/${graphId}/visualization`);
        if (!response.ok) {
            throw new Error('Failed to fetch graph visualization');
        }
        return response.json();
    },

    async getAvailableGraphs() {
        const response = await fetch('/api/graphs/available');
        if (!response.ok) {
            throw new Error('Failed to fetch available graphs');
        }
        return response.json();
    },

    async getLLMModels() {
        const response = await fetch('/api/graphs/llm-models');
        if (!response.ok) {
            throw new Error('Failed to fetch LLM models');
        }
        return response.json();
    },

    async updateGraphStages(graphId, stages) {
        const response = await fetch(`/api/graphs/graphs/${graphId}/stages`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(stages),
        });
        if (!response.ok) {
            throw new Error('Failed to update graph stages');
        }
        return response.json();
    },

    async runGraph(graphId, inputData) {
        const response = await fetch(`/api/graphs/graphs/${graphId}/run`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(inputData),
        });
        return this.handleResponse(response);
    },

    async getGraphExecutionStatus(taskId) {
        const response = await fetch(`/api/graphs/executions/${taskId}`);
        return this.handleResponse(response);
    },

    // Helper method to handle errors
    async handleResponse(response) {
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'API request failed');
        }
        return response.json();
    }
}; 
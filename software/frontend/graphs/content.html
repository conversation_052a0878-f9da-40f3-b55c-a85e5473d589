<!-- software/frontend/graphs/content.html -->
<!-- Main Content Area -->
<div id="mainContent" class="flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out ml-64">
    <!-- Header -->
    <div class="h-16 bg-card border-b border-border flex items-center justify-between px-4 pl-12">
        <div class="flex items-center">
            <div>
                <h2 id="graphTitle" class="text-lg font-medium">Saved Graphs</h2>
                <p id="graphDescription" class="text-xs text-muted-foreground">View and manage your saved graph configurations.</p>
            </div>
        </div>
    </div>

    <!-- Main Scrollable Area -->
    <div class="flex-1 overflow-auto p-3 md:p-4 bg-background">
        <div class="mx-auto">

            <!-- Saved Graphs Section (Visible by default) -->
            <div id="savedGraphsSection">
                <div id="savedGraphsList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    <!-- Saved graphs cards populated by JS -->
                </div>
            </div>

            <!-- Graph Configuration Section (Hidden by default) -->
            <div id="graphConfigurationSection" class="hidden space-y-4">
                <!-- Form Area -->
                <div id="graphFormArea" class="bg-card p-3 rounded-lg border border-border shadow-sm">
                    <!-- Form fields populated by JS -->
                </div>
                
                <!-- Graph Visualization -->
                <div class="bg-card p-3 rounded-lg border border-border shadow-sm">
                    <h3 class="text-base font-semibold mb-3 border-b border-border pb-2">Workflow Visualization</h3>
                    <div class="flex items-center mb-3">
                        <button id="runGraphBtn" 
                                class="inline-flex items-center px-3 py-1.5 bg-primary text-primary-foreground text-sm font-medium rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                disabled>
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            Run Graph
                        </button>
                    </div>
                    <div id="mermaidDiagram" class="overflow-auto min-h-[180px] bg-muted/50 p-3 rounded-md">
                        <!-- Mermaid diagram populated by JS -->
                    </div>
                    <div id="executionInfo" class="mt-3">
                        <!-- Execution status populated by JS -->
                    </div>
                    <div id="graphOutput" class="mt-3">
                        <!-- Graph execution output populated by JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 
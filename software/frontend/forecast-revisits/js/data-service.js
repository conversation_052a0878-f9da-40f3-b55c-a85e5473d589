// Data service functions for the forecast revisit module

// Alpine.js data object methods that handle data fetching and processing
const forecastDataService = {
    // Fetch directors
    async fetchDirectors() {
        try {
            const response = await fetch('/api/directors');
            const data = await response.json();
            if (data.status === 'success') {
                this.directors = data.data;
                console.log('Directors fetched successfully:', this.directors.length);
                return this.directors;
            } else {
                this.error = data.message || 'Failed to fetch directors';
                console.error('Error fetching directors:', this.error);
                return [];
            }
        } catch (error) {
            this.error = error.message || 'An error occurred while fetching directors';
            console.error('Exception fetching directors:', error);
            return [];
        }
    },

    // Fetch pending revisits
    async fetchPendingRevisits() {
        console.log('Fetching pending revisits...');
        this.isLoading = true;
        try {
            let url = '/api/forecast-revisits';
            if (this.selectedDirector) {
                url += `?director_id=${this.selectedDirector}`;
            }
            const response = await fetch(url);
            const data = await response.json();
            console.log('Pending response status:', response.status);
            console.log('Pending data received:', data);
            console.log('First pending item (if exists):', data.length > 0 ? data[0] : 'No items');

            if (data.status === 'success') {
                this.pendingRevisits = data.data;

                // Make sure data is in the correct format and convert date strings to proper format
                this.pendingRevisits = this.pendingRevisits.map(item => {
                    // Ensure forecasted_horizon is in a format Highcharts can parse
                    if (item.forecasted_horizon) {
                        const date = new Date(item.forecasted_horizon);
                        if (isNaN(date.getTime())) {
                            console.error('Invalid date format for pending revisit:', item.forecasted_horizon);
                            // Use current date if the date is invalid
                            item.forecasted_horizon = new Date().toISOString();
                        } else {
                            item.forecasted_horizon = date.toISOString();
                        }
                    } else {
                        console.error('Missing forecasted_horizon for pending revisit:', item);
                        item.forecasted_horizon = new Date().toISOString();
                    }
                    return item;
                });

                console.log('Processed pending revisits:', this.pendingRevisits.length);

                // Initialize chart if we're on the chart tab
                if (this.activeTab === 'chart' && !this.isLoading) {
                    this.initializeChart();
                }

                return this.pendingRevisits;
            } else {
                this.error = data.message || 'Failed to fetch pending revisits';
                console.error('Error in response:', this.error);
                return [];
            }
        } catch (error) {
            console.error('Error fetching pending revisits:', error);
            this.error = error.message || 'An error occurred while fetching pending revisits';
            this.pendingRevisits = [];
            return [];
        } finally {
            this.isLoading = false;
        }
    },

    // Fetch historical revisits
    async fetchHistoricalRevisits() {
        console.log('Fetching historical revisits...');
        this.isLoading = true;
        try {
            let url = '/api/forecast-revisits/history';
            if (this.selectedDirector) {
                url += `?director_id=${this.selectedDirector}`;
            }
            const response = await fetch(url);
            const data = await response.json();
            console.log('Historical response status:', response.status);
            console.log('Historical data received:', data);
            console.log('First historical item (if exists):', data.length > 0 ? data[0] : 'No items');

            if (data.status === 'success') {
                this.historicalRevisits = data.data;

                // Make sure data is in the correct format and convert date strings to proper format
                this.historicalRevisits = this.historicalRevisits.map(item => {
                    // Calculate residual as percentage difference
                    if (item.actual_price && item.forecasted_price) {
                        item.residual = ((item.actual_price - item.forecasted_price) / item.forecasted_price) * 100;
                    }

                    // Ensure forecasted_horizon is in a format Highcharts can parse
                    if (item.forecasted_horizon) {
                        const date = new Date(item.forecasted_horizon);
                        if (isNaN(date.getTime())) {
                            console.error('Invalid date format for historical revisit:', item.forecasted_horizon);
                            // Use current date if the date is invalid
                            item.forecasted_horizon = new Date().toISOString();
                        } else {
                            item.forecasted_horizon = date.toISOString();
                        }
                    } else {
                        console.error('Missing forecasted_horizon for historical revisit:', item);
                        item.forecasted_horizon = new Date().toISOString();
                    }

                    // Ensure created_at is properly formatted
                    if (item.created_at) {
                        const date = new Date(item.created_at);
                        if (isNaN(date.getTime())) {
                            console.error('Invalid date format for created_at:', item.created_at);
                            // Use a default date if invalid
                            item.created_at = new Date().toISOString();
                        } else {
                            item.created_at = date.toISOString();
                        }
                    } else {
                        // If created_at is missing, use closing_price_date as a fallback
                        if (item.closing_price_date) {
                            item.created_at = new Date(item.closing_price_date).toISOString();
                        } else {
                            console.error('Missing created_at for historical revisit:', item);
                            item.created_at = new Date().toISOString();
                        }
                    }
                    return item;
                });

                console.log('Processed historical revisits:', this.historicalRevisits.length);

                // Initialize chart if we're on the chart tab
                if (this.activeTab === 'chart' && !this.isLoading) {
                    this.initializeChart();
                }

                return this.historicalRevisits;
            } else {
                this.error = data.message || 'Failed to fetch historical revisits';
                console.error('Error in response:', this.error);
                return [];
            }
        } catch (error) {
            console.error('Error fetching historical revisits:', error);
            this.error = error.message || 'An error occurred while fetching historical revisits';
            this.historicalRevisits = [];
            return [];
        } finally {
            this.isLoading = false;
        }
    },

    // Check all due revisits
    async checkAllDueRevisits() {
        if (this.isCheckingAll) return;

        this.isCheckingAll = true;
        try {
            const response = await fetch('/api/forecast-revisits/check-all-due', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.status === 'success') {
                // Show a temporary success message
                this.error = null;
                // Refresh the lists
                await this.fetchPendingRevisits();
                await this.fetchHistoricalRevisits();
                return true;
            } else {
                this.error = data.message || 'Failed to check due revisits';
                console.error('Error checking due revisits:', this.error);
                return false;
            }
        } catch (error) {
            this.error = error.message || 'An error occurred while checking due revisits';
            console.error('Exception checking due revisits:', error);
            return false;
        } finally {
            this.isCheckingAll = false;
        }
    },

    // Complete a specific revisit
    async completeRevisit(revisitId) {
        try {
            const response = await fetch(`/api/forecast-revisits/${revisitId}/complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.status === 'success') {
                // Refresh the lists
                await this.fetchPendingRevisits();
                await this.fetchHistoricalRevisits();
                return true;
            } else {
                this.error = data.message || 'Failed to complete revisit';
                console.error('Error completing revisit:', this.error);
                return false;
            }
        } catch (error) {
            this.error = error.message || 'An error occurred while completing revisit';
            console.error('Exception completing revisit:', error);
            return false;
        }
    },

    // Delete a specific forecast revisit
    async deleteRevisit(revisitId) {
        try {
            if (!confirm('Are you sure you want to delete this forecast revisit? This action cannot be undone.')) {
                return false;
            }

            const response = await fetch(`/api/forecast-revisits/${revisitId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.status === 'success') {
                // Refresh the lists
                await this.fetchPendingRevisits();
                await this.fetchHistoricalRevisits();
                return true;
            } else {
                this.error = data.message || 'Failed to delete revisit';
                console.error('Error deleting revisit:', this.error);
                return false;
            }
        } catch (error) {
            this.error = error.message || 'An error occurred while deleting revisit';
            console.error('Exception deleting revisit:', error);
            return false;
        }
    }
};

// Export the service
window.forecastDataService = forecastDataService;
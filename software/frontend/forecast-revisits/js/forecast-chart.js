// Forecast Chart Module
console.log('Forecast chart module loaded');

// Check if Highcharts is available
console.log('Initial Highcharts check:', typeof Highcharts !== 'undefined');

document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, Highcharts available:', typeof Highcharts !== 'undefined');
});

/**
 * Initializes the forecast chart
 * @param {string} elementId - The ID of the container element
 * @param {Array} pendingRevisits - Pending forecast revisits
 * @param {Array} historicalRevisits - Completed forecast revisits
 */
function initForecastChart(elementId, pendingRevisits = [], historicalRevisits = []) {
  console.log('initForecastChart called with:', { 
    elementId, 
    pendingCount: pendingRevisits.length, 
    historicalCount: historicalRevisits.length 
  });
  
  // Check if Highcharts is loaded
  if (typeof Highcharts === 'undefined') {
    console.error('Highcharts is not loaded. Make sure the script is included and loaded before calling this function.');
    return null;
  }
  
  // Check if container exists
  const container = document.getElementById(elementId);
  if (!container) {
    console.error(`Chart container with ID "${elementId}" not found`);
    return null;
  }
  
  // Check container dimensions
  const containerRect = container.getBoundingClientRect();
  console.log('Container dimensions:', {
    width: containerRect.width,
    height: containerRect.height,
    visible: containerRect.width > 0 && containerRect.height > 0
  });
  
  if (containerRect.width === 0 || containerRect.height === 0) {
    console.error('Chart container has zero width or height. Applying default dimensions.');
    container.style.width = '100%';
    container.style.height = '400px';
    container.style.minHeight = '400px';
    container.style.border = '1px solid #ccc';
  }
  
  const allRevisits = [...pendingRevisits, ...historicalRevisits];
  console.log('All revisits combined:', allRevisits.length);
  
  // Check if we have any data
  if (allRevisits.length === 0) {
    console.warn('No revisit data available for chart');
    container.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;"><p>No forecast data available</p></div>';
    return null;
  }
  
  // Prepare series data
  try {
    const series = prepareSeries(pendingRevisits, historicalRevisits);
    console.log('Series prepared:', series);
    
    // Validate series data to prevent Highcharts error #14
    series.forEach(seriesItem => {
      if (!Array.isArray(seriesItem.data)) {
        console.error('Series data is not an array:', seriesItem);
        seriesItem.data = []; // Set to empty array to prevent errors
      }
      
      // Ensure each data point has x and y values
      seriesItem.data.forEach((point, index) => {
        if (typeof point.x === 'undefined' || isNaN(point.x)) {
          console.error(`Invalid x value in data point at index ${index}:`, point);
          point.x = 0; // Set default value to prevent errors
        }
        
        if (typeof point.y === 'undefined' || isNaN(point.y)) {
          console.error(`Invalid y value in data point at index ${index}:`, point);
          point.y = 0; // Set default value to prevent errors
        }
      });
    });
    
    // Create chart with Highcharts
    console.log('Creating Highcharts chart...');
    
    // Chart configuration with zoom functionality
    const chartOptions = {
      chart: {
        type: 'scatter',
        zooming: {
          type: 'x'
        }
      },
      title: {
        text: 'Forecast Performance'
      },
      subtitle: {
        text: document.ontouchstart === undefined ? 
          'Click and drag in the plot area to zoom in' : 
          'Pinch the chart to zoom in'
      },
      xAxis: {
        type: 'datetime',
        title: {
          text: 'Forecast Horizon Date'
        }
      },
      yAxis: {
        title: {
          text: 'Residual (%)'
        },
        labels: {
          format: '{value}%'
        }
      },
      tooltip: {
        formatter: function() {
          const point = this.point;
          if (!point.revisit) {
            return 'No data available';
          }
          
          const revisit = point.revisit;
          const residual = point.residual !== undefined
            ? point.residual.toFixed(2) + '%'
            : 'Pending';
          
          // Format date for display in tooltip
          const dateFormatted = Highcharts.dateFormat('%Y-%m-%d', point.x);
            
          return [
            `<b>Ticker: ${revisit.ticker}</b>`,
            `Date: ${dateFormatted}`,
            `Forecast: $${revisit.forecasted_price}`,
            `Actual: ${revisit.actual_price ? '$' + revisit.actual_price : 'Pending'}`,
            `Residual: ${residual}`,
            `Recommendation: ${revisit.recommendation}`
          ].join('<br>');
        }
      },
      plotOptions: {
        scatter: {
          marker: {
            radius: 6,
            symbol: 'circle'
          }
        }
      },
      series: series,
      legend: {
        enabled: true
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: true
      }
    };
    
    try {
      return Highcharts.chart(elementId, chartOptions);
    } catch (chartError) {
      console.error('Highcharts chart creation error:', chartError);
      
      // Simplified fallback chart with minimal options
      console.log('Trying simplified chart as fallback...');
      return Highcharts.chart(elementId, {
        chart: { type: 'scatter' },
        title: { text: 'Forecast Performance' },
        series: [{ name: 'Pending', data: [] }, { name: 'Historical', data: [] }]
      });
    }
  } catch (error) {
    console.error('Error creating chart:', error);
    container.innerHTML = `<div style="display:flex;justify-content:center;align-items:center;height:100%;color:red;">
      Error creating chart: ${error.message}
    </div>`;
    return null;
  }
}

/**
 * Prepare series data for Highcharts
 */
function prepareSeries(pendingRevisits, historicalRevisits) {
  console.log('prepareSeries called with:', { 
    pendingCount: pendingRevisits.length, 
    historicalCount: historicalRevisits.length 
  });
  
  try {
    // Check if arrays are valid
    if (!Array.isArray(pendingRevisits)) {
      console.error('pendingRevisits is not an array:', pendingRevisits);
      pendingRevisits = [];
    }
    
    if (!Array.isArray(historicalRevisits)) {
      console.error('historicalRevisits is not an array:', historicalRevisits);
      historicalRevisits = [];
    }
    
    // Log sample data for debugging
    if (pendingRevisits.length > 0) {
      console.log('Sample pending revisit:', pendingRevisits[0]);
    }
    
    if (historicalRevisits.length > 0) {
      console.log('Sample historical revisit:', historicalRevisits[0]);
    }
    
    const series = [];
    
    // Process pending revisits - create a single series with all pending data
    if (pendingRevisits.length > 0) {
      const pendingData = pendingRevisits.map(revisit => {
        try {
          // Ensure we have a valid date
          let horizonDate;
          try {
            horizonDate = new Date(revisit.forecasted_horizon).getTime();
            if (isNaN(horizonDate)) {
              console.error(`Invalid date format for ${revisit.ticker}: ${revisit.forecasted_horizon}`);
              horizonDate = new Date().getTime(); // Fallback to current date
            }
          } catch (dateError) {
            console.error(`Error parsing date for ${revisit.ticker}:`, dateError);
            horizonDate = new Date().getTime(); // Fallback to current date
          }
          
          console.log(`Horizon date for ${revisit.ticker}: ${revisit.forecasted_horizon} -> ${horizonDate}`);
          
          return {
            x: horizonDate,
            y: 0, // Pending items are placed at y=0
            revisit: revisit,
            marker: {
              fillColor: 'transparent', // Open circle (only border)
              lineColor: getRecommendationColor(revisit.recommendation || 'UNKNOWN', true),
              lineWidth: 2,
              radius: 6
            }
          };
        } catch (pointError) {
          console.error(`Error creating data point for ${revisit.ticker}:`, pointError);
          // Return a valid point to prevent Highcharts errors
          return { x: new Date().getTime(), y: 0 };
        }
      });
      
      series.push({
        name: 'Pending Forecasts',
        data: pendingData
      });
    }
    
    // Process historical revisits - create a single series with all historical data
    if (historicalRevisits.length > 0) {
      const historicalData = historicalRevisits.map(revisit => {
        try {
          // Calculate residual as percentage difference
          let residual = 0;
          try {
            if (revisit.actual_price && revisit.forecasted_price) {
              residual = ((revisit.actual_price - revisit.forecasted_price) / revisit.forecasted_price) * 100;
            }
          } catch (calcError) {
            console.error(`Error calculating residual for ${revisit.ticker}:`, calcError);
          }
          
          // Ensure we have a valid date
          let horizonDate;
          try {
            horizonDate = new Date(revisit.forecasted_horizon).getTime();
            if (isNaN(horizonDate)) {
              console.error(`Invalid date format for ${revisit.ticker}: ${revisit.forecasted_horizon}`);
              horizonDate = new Date().getTime(); // Fallback to current date
            }
          } catch (dateError) {
            console.error(`Error parsing date for ${revisit.ticker}:`, dateError);
            horizonDate = new Date().getTime(); // Fallback to current date
          }
          
          console.log(`Historical: Horizon date for ${revisit.ticker}: ${revisit.forecasted_horizon} -> ${horizonDate}, residual: ${residual}%`);
          
          return {
            x: horizonDate,
            y: residual,
            residual: residual,
            revisit: revisit,
            marker: {
              fillColor: getRecommendationColor(revisit.recommendation || 'UNKNOWN', true),
              lineColor: getRecommendationColor(revisit.recommendation || 'UNKNOWN', true),
              lineWidth: 1,
              radius: 6
            }
          };
        } catch (pointError) {
          console.error(`Error creating data point for ${revisit.ticker}:`, pointError);
          // Return a valid point to prevent Highcharts errors
          return { x: new Date().getTime(), y: 0 };
        }
      });
      
      series.push({
        name: 'Historical Forecasts',
        data: historicalData
      });
    }
    
    // If no data was created, return a default empty series
    if (series.length === 0) {
      series.push({ name: 'No Data', data: [] });
    }
    
    console.log('Final series data:', series);
    return series;
  } catch (error) {
    console.error('Error in prepareSeries:', error);
    return [{ name: 'No Data', data: [] }]; // Return empty series on error
  }
}

/**
 * Group revisits by recommendation
 */
function groupByRecommendation(revisits) {
  console.log('groupByRecommendation called with:', revisits.length);
  
  try {
    return revisits.reduce((groups, revisit) => {
      const rec = revisit.recommendation || 'UNKNOWN';
      if (!groups[rec]) {
        groups[rec] = [];
      }
      groups[rec].push(revisit);
      return groups;
    }, {});
  } catch (error) {
    console.error('Error in groupByRecommendation:', error);
    throw error;
  }
}

/**
 * Generate color based on recommendation type
 */
function getRecommendationColor(recommendation, filled = true) {
  console.log('getRecommendationColor called with:', { recommendation, isHistorical: filled });
  
  try {
    const opacity = filled ? 1 : 0.3;
    
    switch ((recommendation || 'UNKNOWN').toUpperCase()) {
      case 'BUY':
        return `rgba(16, 185, 129, ${opacity})`;  // Green
      case 'SELL':
        return `rgba(239, 68, 68, ${opacity})`;   // Red
      case 'HOLD':
        return `rgba(245, 158, 11, ${opacity})`;  // Orange
      default:
        return `rgba(107, 114, 128, ${opacity})`; // Gray
    }
  } catch (error) {
    console.error('Error in getRecommendationColor:', error);
    return `rgba(107, 114, 128, ${opacity})`;
  }
}

/**
 * Filter chart data by tickers
 */
function filterChartByTickers(chart, tickers) {
  console.log('filterChartByTickers called with:', { 
    chartExists: !!chart, 
    tickers: tickers 
  });
  
  if (!chart) {
    console.error('Chart is not initialized');
    return;
  }
  
  try {
    // If no tickers selected, show all
    if (!tickers || tickers.length === 0) {
      // Show all series
      chart.series.forEach(series => {
        // Check if allData exists and is valid
        if (series.options.allData && Array.isArray(series.options.allData)) {
          series.setData(series.options.allData, false);
        }
      });
      chart.redraw();
      return;
    }
    
    // Filter data for each series
    chart.series.forEach(series => {
      // Store original data if not already stored
      if (!series.options.allData) {
        series.options.allData = [...series.options.data];
      }
      
      // Filter data based on ticker
      const filteredData = series.options.allData.filter(point => {
        return point.revisit && tickers.includes(point.revisit.ticker);
      });
      
      // Update series data
      series.setData(filteredData, false);
    });
    
    // Redraw chart only once after all series updates
    chart.redraw();
  } catch (error) {
    console.error('Error in filterChartByTickers:', error);
  }
}

/**
 * Create ticker filter buttons
 */
function createTickerFilters(containerId, allRevisits, chart) {
  console.log('createTickerFilters called with:', { 
    containerId, 
    revisitsCount: allRevisits.length, 
    chartExists: !!chart 
  });
  
  try {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`Ticker filter container with ID "${containerId}" not found`);
      return;
    }
    
    if (!chart) {
      console.error('Chart is not initialized');
      return;
    }
    
    // Get unique tickers
    const tickers = [...new Set(allRevisits.map(r => r.ticker))];
    console.log('Unique tickers:', tickers);
    
    // Clear container
    container.innerHTML = '';
    
    // Add "All" button
    const allButton = document.createElement('button');
    allButton.textContent = 'All';
    allButton.className = 'px-3 py-1 m-1 rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400 text-xs font-medium active-filter';
    allButton.onclick = () => {
      document.querySelectorAll('#' + containerId + ' button').forEach(btn => {
        btn.classList.remove('active-filter');
        btn.classList.remove('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900/30', 'dark:text-indigo-400');
        btn.classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-300');
      });
      allButton.classList.add('active-filter');
      allButton.classList.remove('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-300');
      allButton.classList.add('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900/30', 'dark:text-indigo-400');
      filterChartByTickers(chart, []);
    };
    container.appendChild(allButton);
    
    // Add ticker buttons
    tickers.forEach(ticker => {
      const button = document.createElement('button');
      button.textContent = ticker;
      button.className = 'px-3 py-1 m-1 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 text-xs font-medium';
      button.onclick = () => {
        document.querySelectorAll('#' + containerId + ' button').forEach(btn => {
          btn.classList.remove('active-filter');
          btn.classList.remove('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900/30', 'dark:text-indigo-400');
          btn.classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-300');
        });
        button.classList.add('active-filter');
        button.classList.remove('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-300');
        button.classList.add('bg-indigo-100', 'text-indigo-800', 'dark:bg-indigo-900/30', 'dark:text-indigo-400');
        filterChartByTickers(chart, [ticker]);
      };
      container.appendChild(button);
    });
  } catch (error) {
    console.error('Error in createTickerFilters:', error);
  }
}

// Add a global debugging function to help diagnose issues
window.debugForecastChart = function() {
  console.group('Forecast Chart Debug Info');
  console.log('Highcharts available:', typeof Highcharts !== 'undefined');
  
  const container = document.getElementById('forecastChart');
  console.log('Chart container exists:', !!container);
  
  if (container) {
    const rect = container.getBoundingClientRect();
    console.log('Container dimensions:', {
      width: rect.width,
      height: rect.height,
      visible: rect.width > 0 && rect.height > 0
    });
  }
  
  // Check if Alpine.js exists and get data
  if (typeof Alpine !== 'undefined') {
    try {
      const alpineData = document.querySelector('[x-data]').__x.$data;
      console.log('Alpine.js data available:', !!alpineData);
      
      if (alpineData) {
        console.log('Active tab:', alpineData.activeTab);
        console.log('Pending revisits count:', alpineData.pendingRevisits?.length || 0);
        console.log('Historical revisits count:', alpineData.historicalRevisits?.length || 0);
        console.log('Chart initialized:', !!alpineData.forecastChart);
      }
    } catch (e) {
      console.error('Error accessing Alpine.js data:', e);
    }
  } else {
    console.log('Alpine.js not available');
  }
  
  console.groupEnd();
  
  // Try to reinitialize the chart
  console.log('Attempting to reinitialize chart...');
  try {
    if (typeof Alpine !== 'undefined') {
      const alpineData = document.querySelector('[x-data]').__x.$data;
      if (alpineData && typeof alpineData.initializeChart === 'function') {
        alpineData.initializeChart();
        return 'Chart reinitialization attempted';
      }
    }
    
    return 'Could not reinitialize chart - Alpine.js data not available';
  } catch (e) {
    console.error('Error reinitializing chart:', e);
    return 'Error reinitializing chart: ' + e.message;
  }
};
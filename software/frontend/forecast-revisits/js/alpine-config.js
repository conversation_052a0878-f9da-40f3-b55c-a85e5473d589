// Alpine.js configuration and initialization

// Wait for page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded, configuring Alpine.js...');
});

// Alpine.js main data object configuration
window.initAlpineData = function() {
    return {
        // Data properties
        pendingRevisits: [],
        historicalRevisits: [],
        directors: [],
        selectedDirector: null,
        isLoading: true,
        activeTab: localStorage.getItem('forecastActiveTab') || 'pending',
        error: null,
        isCheckingAll: false,
        forecastChart: null,

        // Initialize Alpine data
        init() {
            console.log('Alpine.js initialized with default configuration');
            this.fetchDirectors().then(() => console.log('Directors fetched'));

            this.fetchPendingRevisits().then(() => {
                console.log('Pending revisits fetched:', this.pendingRevisits.length);

                // Only initialize chart if we have data and are on the chart tab
                if (this.activeTab === 'chart') {
                    console.log('Initializing chart from init since active tab is chart');
                    // Longer timeout to ensure DOM is ready
                    setTimeout(() => {
                        if (this.pendingRevisits.length > 0 || this.historicalRevisits.length > 0) {
                            this.initializeChart();
                        } else {
                            console.log('Waiting for historical data before initializing chart');
                        }
                    }, 400);
                }
            });

            this.fetchHistoricalRevisits().then(() => {
                console.log('Historical revisits fetched:', this.historicalRevisits.length);

                // Initialize chart after both pending and historical data is loaded
                if (this.activeTab === 'chart' && !this.forecastChart) {
                    console.log('Initializing chart after both datasets loaded');
                    setTimeout(() => this.initializeChart(), 300);
                }
            });
        },

        // Import all methods from separate files
        ...window.forecastDataService,
        ...window.chartController,
        ...window.forecastUtils,
        ...window.directorService,

        // Delete all forecast revisits for a director
        async deleteDirectorRevisits(directorId) {
            if (!directorId) return;

            try {
                await window.directorService.deleteDirectorRevisits(directorId);
                // Refresh data after deletion
                await this.fetchPendingRevisits();
                await this.fetchHistoricalRevisits();

                // Refresh chart if on chart tab
                if (this.activeTab === 'chart') {
                    setTimeout(() => this.initializeChart(), 300);
                }
            } catch (error) {
                console.error('Error in deleteDirectorRevisits:', error);
                this.error = `Failed to delete forecast revisits: ${error.message}`;
            }
        }
    };
};
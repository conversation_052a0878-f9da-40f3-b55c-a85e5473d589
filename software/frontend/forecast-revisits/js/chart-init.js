// Chart initialization and highcharts loading checks

// Check if Highcharts is loaded
console.log('Highcharts loaded check:', typeof Highcharts !== 'undefined');
if (typeof Highcharts === 'undefined') {
    console.error('Highcharts failed to load. Trying to load again...');
    // Create a new script element and add it to the head
    const script = document.createElement('script');
    script.src = 'https://code.highcharts.com/highcharts.js';
    script.onload = function() {
        console.log('Highcharts loaded dynamically:', typeof Highcharts !== 'undefined');
        // If we're on the chart tab, try to initialize chart
        if (document.querySelector('[x-data]').__x.$data.activeTab === 'chart') {
            console.log('Reinitializing chart after dynamic load');
            document.querySelector('[x-data]').__x.$data.initializeChart();
        }
    };
    script.onerror = function() {
        console.error('Failed to load Highcharts dynamically');
        // Display error message in chart container
        const chartContainer = document.getElementById('forecastChart');
        if (chartContainer) {
            chartContainer.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load Highcharts library</div>';
        }
    };
    document.head.appendChild(script);
}

// Add a trigger function to reinitialize chart on demand
window.triggerChartInit = function() {
    if (document.querySelector('[x-data]').__x.$data) {
        document.querySelector('[x-data]').__x.$data.initializeChart();
        return "Chart initialization triggered";
    }
    return "Alpine component not found";
}; 
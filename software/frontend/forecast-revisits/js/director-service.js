// Director management functions

const directorService = {
    // Select a director to filter data
    selectDirector(directorId) {
        this.selectedDirector = directorId;
        this.fetchPendingRevisits();
        this.fetchHistoricalRevisits();
    },

    // Clear director filter
    clearDirectorFilter() {
        this.selectedDirector = null;
        this.fetchPendingRevisits();
        this.fetchHistoricalRevisits();
    },

    // Delete all forecast revisits for a director
    async deleteDirectorRevisits(directorId) {
        if (!directorId) return;

        // Confirm before deleting
        if (!confirm('Are you sure you want to delete all forecast revisits for this director? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/api/forecast-revisits/director/${directorId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`Failed to delete forecast revisits: ${response.statusText}`);
            }

            const result = await response.json();

            // Show success message
            dataService.showToast(`Successfully deleted ${result.deleted_count} forecast revisits`, 'success');

            // Refresh the data
            this.fetchPendingRevisits();
            this.fetchHistoricalRevisits();

            return result;
        } catch (error) {
            console.error('Error deleting forecast revisits:', error);
            dataService.showToast(`Error deleting forecast revisits: ${error.message}`, 'error');
            throw error;
        }
    }
};

// Export the service
window.directorService = directorService;
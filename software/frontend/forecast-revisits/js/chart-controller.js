// Chart controller functions

const chartController = {
    // Initialize the chart
    initializeChart() {
        console.log('initializeChart called', {
            pendingCount: this.pendingRevisits?.length || 0,
            historicalCount: this.historicalRevisits?.length || 0,
            activeTab: this.activeTab,
            chartExists: !!this.forecastChart
        });
        
        // Ensure the chart tab is visible before initializing
        if (this.activeTab !== 'chart') {
            console.log('Not initializing chart because we are not on the chart tab');
            return;
        }
        
        // Wait longer for the DOM to be fully rendered
        setTimeout(() => {
            // Check if chart container exists
            const chartCanvas = document.getElementById('forecastChart');
            console.log('Chart container element:', chartCanvas);
            if (!chartCanvas) {
                console.error('Chart container element not found!');
                return;
            }
            
            // Force display of the container to ensure it has dimensions
            const tabContent = document.querySelector('.tab-content.active');
            if (tabContent) {
                tabContent.style.display = 'block';
            }
            
            // Set explicit dimensions on the chart container
            chartCanvas.style.width = '100%';
            chartCanvas.style.height = '500px';
            chartCanvas.style.minHeight = '400px';
            
            // If chart already exists, destroy it
            if (this.forecastChart) {
                console.log('Destroying existing chart');
                this.forecastChart.destroy();
            }
            
            console.log('Data before initializing chart:', {
                pendingCount: this.pendingRevisits?.length || 0,
                historicalCount: this.historicalRevisits?.length || 0,
            });
            
            try {
                // Make sure we have the required Highcharts library
                if (typeof Highcharts === 'undefined') {
                    console.error('Highcharts library not available!');
                    chartCanvas.innerHTML = '<div class="p-4 text-center text-red-500">Highcharts library failed to load</div>';
                    return;
                }
                
                // Initialize chart
                console.log('Calling initForecastChart...');
                this.forecastChart = initForecastChart('forecastChart', this.pendingRevisits, this.historicalRevisits);
                console.log('Chart initialized:', !!this.forecastChart);
                
                // Only create ticker filters if chart was successfully created
                if (this.forecastChart) {
                    createTickerFilters('tickerFilters', [...this.pendingRevisits, ...this.historicalRevisits], this.forecastChart);
                }
            } catch (error) {
                console.error('Error initializing chart:', error);
                chartCanvas.innerHTML = '<div class="p-4 text-center text-red-500">Error initializing chart: ' + error.message + '</div>';
            }
        }, 300); // Increased timeout to ensure DOM is fully rendered
    },
    
    // Switch between tabs (pending, history, chart)
    switchTab(tab) {
        console.log(`Switching to tab: ${tab} from ${this.activeTab}`);
        this.activeTab = tab;
        localStorage.setItem('forecastActiveTab', tab);
        
        // Initialize chart after a short delay if switching to chart tab
        if (tab === 'chart') {
            console.log('Switching to chart tab, initializing chart...');
            // Use a slightly longer delay to ensure DOM updates first
            setTimeout(() => this.initializeChart(), 250);
        }
    }
};

// Export the controller
window.chartController = chartController; 
// Utility functions for the forecast revisit module

const forecastUtils = {
    // Format date for display
    formatDate(dateString) {
        if (!dateString) return 'N/A';

        try {
            // Handle 3 common date formats:

            // 1. YYYY-MM-DD format (simple date string)
            if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                const [year, month, day] = dateString.split('-').map(Number);

                // Create date using local timezone to avoid shifts
                const date = new Date(year, month - 1, day);
                const options = { year: 'numeric', month: 'short', day: 'numeric' };
                return date.toLocaleDateString(undefined, options);
            }

            // 2. ISO date string with time component
            if (typeof dateString === 'string' && dateString.includes('T')) {
                // Just extract the date part for display
                const datePart = dateString.split('T')[0];
                const [year, month, day] = datePart.split('-').map(Number);

                // Create date using local timezone
                const date = new Date(year, month - 1, day);
                const options = { year: 'numeric', month: 'short', day: 'numeric' };
                return date.toLocaleDateString(undefined, options);
            }

            // 3. Date object or other format
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateString);
                return 'Invalid date';
            }

            const options = { year: 'numeric', month: 'short', day: 'numeric' };
            return date.toLocaleDateString(undefined, options);
        } catch (error) {
            console.error('Error formatting date:', error, dateString);
            return String(dateString);
        }
    },

    // Calculate price change percentage between actual and forecasted price
    calculatePriceChange(item) {
        if (!item.actual_price || !item.forecasted_price) return 'N/A';
        const diff = ((item.actual_price - item.forecasted_price) / item.forecasted_price * 100).toFixed(2);
        return `${diff}%`;
    },

    // Calculate forecast percentage change between starting price and forecasted price
    calculateForecastPercentage(item) {
        if (!item.forecasted_price || !item.last_closing_price) return 'N/A';
        const diff = ((item.forecasted_price - item.last_closing_price) / item.last_closing_price * 100).toFixed(2);
        return `${diff}%`;
    },

    // Get CSS class for price change display
    getPriceChangeClass(item) {
        if (!item.actual_price || !item.forecasted_price) return '';
        const diff = item.actual_price - item.forecasted_price;
        return diff >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
    },

    // Get CSS class for forecast percentage display
    getForecastPercentageClass(item) {
        if (!item.forecasted_price || !item.last_closing_price) return '';
        const diff = item.forecasted_price - item.last_closing_price;
        return diff >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
    },

    // Calculate forecast duration in days
    calculateForecastDuration(item) {
        if (!item.forecasted_horizon) return 'N/A';

        try {
            // Parse dates
            let startDate, endDate;

            // First try to use created_at
            let startDateStr = item.created_at;

            // If created_at is not available, fall back to closing_price_date
            if (!startDateStr && item.closing_price_date) {
                startDateStr = item.closing_price_date;
            }

            // If we still don't have a start date, return N/A
            if (!startDateStr) return 'N/A';

            // Handle start date
            if (typeof startDateStr === 'string' && startDateStr.includes('T')) {
                startDate = new Date(startDateStr);
            } else if (typeof startDateStr === 'string') {
                const [year, month, day] = startDateStr.split('-').map(Number);
                startDate = new Date(year, month - 1, day);
            } else {
                startDate = new Date(startDateStr);
            }

            // Handle forecasted_horizon
            if (typeof item.forecasted_horizon === 'string' && item.forecasted_horizon.includes('T')) {
                endDate = new Date(item.forecasted_horizon);
            } else if (typeof item.forecasted_horizon === 'string') {
                const [year, month, day] = item.forecasted_horizon.split('-').map(Number);
                endDate = new Date(year, month - 1, day);
            } else {
                endDate = new Date(item.forecasted_horizon);
            }

            // Validate dates
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                console.error('Invalid date in forecast duration calculation:', { startDate, endDate });
                return 'N/A';
            }

            // Calculate difference in days
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            return `${diffDays} days`;
        } catch (error) {
            console.error('Error calculating forecast duration:', error, item);
            return 'N/A';
        }
    },

    // Check if scheduled date has been reached
    isScheduledDateReached(dateString) {
        if (!dateString) return false;

        try {
            let scheduledDate;
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

            // Parse the date based on format
            if (typeof dateString === 'string' && dateString.includes('T')) {
                // ISO date string with time component
                scheduledDate = new Date(dateString);
            } else if (typeof dateString === 'string') {
                // Simple date string (YYYY-MM-DD)
                const [year, month, day] = dateString.split('-').map(Number);
                scheduledDate = new Date(year, month - 1, day);
            } else {
                // Assume it's already a date object
                scheduledDate = new Date(dateString);
            }

            scheduledDate.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

            return today >= scheduledDate;
        } catch (error) {
            console.error('Error checking if scheduled date is reached:', error, dateString);
            return false;
        }
    }
};

// Export the utilities
window.forecastUtils = forecastUtils;
{% extends "base.html" %}

{% block title %}Forecast Revisits{% endblock %}

{% block header %}Forecast Revisits{% endblock %}

{% block content %}
<div x-data="initAlpineData()"
     x-init="init()">

    <!-- Responsive container for sidebar and main content -->
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar with Directors list -->
        <div class="w-full md:w-64 bg-white dark:bg-gray-800 shadow rounded-lg md:mr-4 mb-4 md:mb-0 flex-shrink-0">
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Directors
                </h3>
            </div>
            <div class="p-4">
                <button
                    @click="clearDirectorFilter()"
                    class="w-full mb-2 px-3 py-2 text-left text-sm rounded-md transition-colors duration-150 ease-in-out"
                    :class="selectedDirector === null ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400' : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'"
                >
                    All Directors
                </button>

                <div x-show="directors.length === 0" class="py-2 text-sm text-gray-500 dark:text-gray-400">
                    No directors found
                </div>

                <template x-for="director in directors" :key="director.id">
                    <div class="mb-2 flex flex-col">
                        <button
                            @click="selectDirector(director.id)"
                            class="w-full px-3 py-2 text-left text-sm rounded-md transition-colors duration-150 ease-in-out flex items-center"
                            :class="selectedDirector === director.id ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400' : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'"
                        >
                            <span x-text="director.name" class="flex-grow truncate"></span>
                            <span x-show="director.total_reports > 0" class="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300" x-text="director.total_reports"></span>
                        </button>
                        <div x-show="selectedDirector === director.id" class="mt-1 flex justify-end px-3">
                            <button
                                @click="deleteDirectorRevisits(director.id)"
                                class="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                            >
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete All Forecast Revisits
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Main content area -->
        <div class="flex-grow w-full overflow-hidden">
            <!-- Page Header with tabs -->
            <div class="mb-6 border-b border-gray-200 dark:border-gray-700">
                <ul class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400">
                    <li class="mr-2">
                        <button @click="switchTab('pending')"
                            :class="{'text-indigo-600 border-indigo-600 dark:text-indigo-500 dark:border-indigo-500': activeTab === 'pending', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300': activeTab !== 'pending'}"
                            class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg group">
                            <svg class="w-5 h-5 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Pending Revisits
                        </button>
                    </li>
                    <li class="mr-2">
                        <button @click="switchTab('history')"
                            :class="{'text-indigo-600 border-indigo-600 dark:text-indigo-500 dark:border-indigo-500': activeTab === 'history', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300': activeTab !== 'history'}"
                            class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg group">
                            <svg class="w-5 h-5 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
                            </svg>
                            Historical Data
                        </button>
                    </li>
                    <li class="mr-2">
                        <button @click="switchTab('chart')"
                            :class="{'text-indigo-600 border-indigo-600 dark:text-indigo-500 dark:border-indigo-500': activeTab === 'chart', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300': activeTab !== 'chart'}"
                            class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg group">
                            <svg class="w-5 h-5 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 13v-1m4 1v-3m4 3V8M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                            Forecast Chart
                        </button>
                    </li>
                    <li class="ml-auto">
                        <button
                            @click="checkAllDueRevisits"
                            class="inline-flex items-center justify-center p-4 text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-200"
                            :disabled="isCheckingAll"
                        >
                            <svg class="w-5 h-5 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span x-text="isCheckingAll ? 'Checking...' : 'Check All Due'"></span>
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Selected Director info -->
            <div x-show="selectedDirector !== null" class="mb-4">
                <div class="flex items-center bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                    <div class="flex-grow">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            <span x-text="directors.find(d => d.id === selectedDirector)?.name || 'Selected Director'"></span>
                        </h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            <span x-text="directors.find(d => d.id === selectedDirector)?.title || ''"></span>
                        </p>
                    </div>
                    <button @click="clearDirectorFilter()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Error Message if any -->
            <div x-show="error" class="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700 dark:bg-red-900/30 dark:text-red-400">
                <p x-text="error"></p>
                <button @click="error = null" class="ml-2 text-sm underline">Dismiss</button>
            </div>

            <!-- Loading State -->
            <div x-show="isLoading" class="flex justify-center items-center py-20">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
                <span class="ml-3 text-lg text-indigo-600 dark:text-indigo-400">Loading...</span>
            </div>

            <!-- Pending Revisits Tab -->
            <div x-show="activeTab === 'pending' && !isLoading" class="w-full">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden w-full">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Pending Forecast Revisits
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Forecasts that need to be compared with actual market performance
                        </p>
                    </div>

                    <!-- Pending Revisits Table -->
                    <div class="overflow-x-auto w-full">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Action
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Ticker
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Forecasted Price
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Forecast Horizon
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Scheduled Date
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Recommendation
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Confidence
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                                <template x-if="pendingRevisits.length === 0">
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                            No pending forecast revisits found
                                        </td>
                                    </tr>
                                </template>
                                <template x-for="(item, index) in pendingRevisits" :key="item._id">
                                    <tr>
                                        <!-- Action -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button @click="deleteRevisit(item._id)"
                                                    class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                                                    title="Delete this forecast revisit">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </td>
                                        <!-- Ticker -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white" x-text="item.ticker"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="'$' + item.forecasted_price"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(item.forecasted_horizon)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(item.scheduled_date)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span x-text="item.recommendation"
                                                  :class="{
                                                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400': item.recommendation === 'BUY',
                                                    'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400': item.recommendation === 'SELL',
                                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400': item.recommendation === 'HOLD'
                                                  }"
                                                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="item.confidence_level + '%'"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                                @click="completeRevisit(item._id)"
                                                :class="{
                                                    'text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300': isScheduledDateReached(item.scheduled_date),
                                                    'text-gray-400 cursor-not-allowed dark:text-gray-600': !isScheduledDateReached(item.scheduled_date)
                                                }"
                                                :disabled="!isScheduledDateReached(item.scheduled_date)"
                                                :title="!isScheduledDateReached(item.scheduled_date) ? 'Cannot check actuals before scheduled date' : ''"
                                            >
                                                Check Actuals
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Historical Revisits Tab -->
            <div x-show="activeTab === 'history' && !isLoading" class="w-full">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden w-full">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Historical Forecast Performance
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Completed forecast revisits with accuracy metrics
                        </p>
                    </div>

                    <!-- Historical Revisits Table -->
                    <div class="overflow-x-auto w-full">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Action
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Ticker
                                    </th>
                                    <!-- Date columns grouped together -->
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Analysis Date
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Forecast Horizon
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Completed At
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Forecast Duration
                                    </th>
                                    <!-- Price columns grouped together -->
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Starting Price
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Forecasted Price
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Forecast %
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Actual Price
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Difference
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Accuracy
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Error
                                    </th>
                                    <!-- Recommendation columns -->
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Recommendation
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                                        Confidence
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                                <template x-if="historicalRevisits.length === 0">
                                    <tr>
                                        <td colspan="15" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                            No historical forecast revisits found
                                        </td>
                                    </tr>
                                </template>
                                <template x-for="(item, index) in historicalRevisits" :key="item._id">
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <!-- Action -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button @click="deleteRevisit(item._id)"
                                                    class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                                                    title="Delete this forecast revisit">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </td>
                                        <!-- Ticker -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white" x-text="item.ticker"></td>

                                        <!-- Date columns grouped together -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(item.created_at)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(item.forecasted_horizon)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(item.completed_at)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="calculateForecastDuration(item)"></td>

                                        <!-- Price columns grouped together -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="'$' + item.last_closing_price"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="'$' + item.forecasted_price"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm" :class="getForecastPercentageClass(item)" x-text="calculateForecastPercentage(item)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="'$' + item.actual_price"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm" :class="getPriceChangeClass(item)" x-text="calculatePriceChange(item)"></td>

                                        <!-- Accuracy -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="(item.accuracy || 0).toFixed(2) + '%'"></span>
                                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                                    <div class="bg-indigo-600 h-2.5 rounded-full dark:bg-indigo-500" :style="`width: ${item.accuracy || 0}%`"></div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Error (100% - Accuracy) -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="(100 - (item.accuracy || 0)).toFixed(2) + '%'"></span>
                                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                                    <div class="bg-red-600 h-2.5 rounded-full dark:bg-red-500" :style="`width: ${100 - (item.accuracy || 0)}%`"></div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Recommendation columns -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span x-text="item.recommendation"
                                                  :class="{
                                                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400': item.recommendation === 'BUY',
                                                    'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400': item.recommendation === 'SELL',
                                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400': item.recommendation === 'HOLD'
                                                  }"
                                                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="item.confidence_level + '%'"></td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Forecast Chart Tab -->
            <div x-show="activeTab === 'chart'" class="tab-content w-full" :class="{'active': activeTab === 'chart'}" style="display: none;">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden w-full">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Forecast Performance Chart
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                            Visual representation of forecast accuracy over time
                        </p>
                    </div>

                    <div class="p-4">
                        <!-- Ticker filters -->
                        <div class="mt-4 mb-4">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Filter by Ticker:</h4>
                            <div id="tickerFilters" class="flex flex-wrap"></div>
                        </div>

                        <!-- Chart container -->
                        <div id="forecastChart" class="chart-container"></div>

                        <!-- Legend -->
                        <div class="mt-4 flex flex-wrap items-center gap-4">
                            <div class="flex items-center">
                                <span class="inline-block w-3 h-3 rounded-full bg-opacity-30 bg-green-500 border border-green-500 mr-2"></span>
                                <span class="text-xs text-gray-600 dark:text-gray-400">Pending Forecasts</span>
                            </div>
                            <div class="flex items-center">
                                <span class="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                                <span class="text-xs text-gray-600 dark:text-gray-400">Historical Forecasts</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Load Highcharts first -->
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>

<!-- Load our modular JavaScript files -->
<script src="/frontend/forecast-revisits/js/utils.js"></script>
<script src="/frontend/forecast-revisits/js/data-service.js"></script>
<script src="/frontend/forecast-revisits/js/director-service.js"></script>
<script src="/frontend/forecast-revisits/js/forecast-chart.js"></script>
<script src="/frontend/forecast-revisits/js/chart-controller.js"></script>
<script src="/frontend/forecast-revisits/js/chart-init.js"></script>
<script src="/frontend/forecast-revisits/js/alpine-config.js"></script>
<script src="/frontend/forecast-revisits/js/alpine-init.js"></script>
{% endblock %}

<style>
.chart-container {
    height: 500px;
    width: 100%;
    min-height: 400px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
    position: relative; /* Important for Highcharts positioning */
}

/* Make sure the chart tab content is visible when active */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
</style>
function handleServerShutdown() {
    const shutdownButton = document.getElementById('server-shutdown-btn');
    if (!shutdownButton) return;
    shutdownButton.addEventListener('click', async (e) => {
        e.preventDefault();
        shutdownButton.disabled = true;
        try {
            const response = await fetch('/shutdown-server', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
            });
            if (response.ok) {
                shutdownButton.textContent = 'Shutting down...';
            } else {
                shutdownButton.disabled = false;
            }
        } catch (error) {
            shutdownButton.disabled = false;
        }
    });
}
document.addEventListener('DOMContentLoaded', handleServerShutdown);

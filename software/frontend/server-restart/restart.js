// Server restart functionality
function handleServerRestart() {
    const restartButton = document.getElementById('server-restart-btn');
    
    if (!restartButton) return;
    
    restartButton.addEventListener('click', async (e) => {
        e.preventDefault();
        
        // Show loading state
        restartButton.disabled = true;
        restartButton.classList.add('animate-loading');
        
        try {
            const response = await fetch('/restart-server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            
            if (response.ok) {
                // Wait for server to restart (approx 5 seconds)
                setTimeout(() => {
                    // Poll the server to check when it's back online
                    const checkServer = () => {
                        fetch('/', { method: 'HEAD' })
                            .then(() => {
                                // Server is back online, reload the page
                                window.location.reload();
                            })
                            .catch(() => {
                                // Server still restarting, try again
                                setTimeout(checkServer, 1000);
                            });
                    };
                    
                    // Start polling
                    checkServer();
                }, 2000);
            } else {
                // Reset button state
                restartButton.disabled = false;
                restartButton.classList.remove('animate-loading');
            }
        } catch (error) {
            console.error('Error restarting server:', error);
            
            // Reset button state
            restartButton.disabled = false;
            restartButton.classList.remove('animate-loading');
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', handleServerRestart); 
// Letta server status checker
document.addEventListener('DOMContentLoaded', function() {
    const statusIndicator = document.getElementById('letta-status-indicator');
    
    if (!statusIndicator) return;
    
    // Check status immediately
    checkLettaStatus();
    
    // Check status every 10 seconds
    setInterval(checkLettaStatus, 10000);
    
    async function checkLettaStatus() {
        try {
            const response = await fetch('/api/letta/server-status');
            if (!response.ok) {
                throw new Error('Failed to check server status');
            }
            
            const result = await response.json();
            
            if (result.running) {
                // Server is running - green indicator
                statusIndicator.classList.remove('bg-orange-500', 'bg-red-500');
                statusIndicator.classList.add('bg-green-500');
                statusIndicator.title = 'Letta server is running';
            } else {
                // Server is not running - orange indicator
                statusIndicator.classList.remove('bg-green-500', 'bg-red-500');
                statusIndicator.classList.add('bg-orange-500');
                statusIndicator.title = 'Letta server is not running';
            }
        } catch (error) {
            console.error('Error checking Letta server status:', error);
            // Error checking status - red indicator
            statusIndicator.classList.remove('bg-green-500', 'bg-orange-500');
            statusIndicator.classList.add('bg-red-500');
            statusIndicator.title = 'Error checking Letta server status';
        }
    }
});

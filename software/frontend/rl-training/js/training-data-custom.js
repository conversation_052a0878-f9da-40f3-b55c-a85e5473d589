/**
 * Training Data Custom Path UI Component
 * Handles entering a custom path for training data
 */

const TrainingDataCustomUI = {
    /**
     * Initialize the custom path input
     * @param {HTMLElement} container Container element for the input
     * @param {function} onSelect Callback function when a path is entered
     * @param {string} defaultPath Default path to use
     */
    init(container, onSelect, defaultPath = 'software/rl/training/data/sample_training.json') {
        // Clear container
        container.innerHTML = '';

        // Create custom path input
        const customPathInput = document.createElement('input');
        customPathInput.type = 'text';
        customPathInput.className = 'mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white';
        customPathInput.value = defaultPath;
        customPathInput.placeholder = 'Enter training data path';

        // Add event listener for input changes
        customPathInput.addEventListener('input', () => {
            if (typeof onSelect === 'function') {
                onSelect(customPathInput.value);
            }
        });

        // Add custom path input to container
        container.appendChild(customPathInput);

        // Call onSelect with default path
        if (typeof onSelect === 'function') {
            onSelect(customPathInput.value);
        }
    }
};

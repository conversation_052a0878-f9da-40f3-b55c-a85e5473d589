/**
 * Training Data UI for RL Training
 * Handles fetching and displaying training data options
 */

const TrainingDataUI = {
    /**
     * Initialize the training data selector with all options
     * @param {HTMLElement} container Container element for the selector
     * @param {function} onSelect Callback function when a data source is selected
     * @param {string} defaultPath Default path to select
     */
    async initSelector(container, onSelect, defaultPath = null) {
        // Clear container
        container.innerHTML = '';

        // Create tabs container
        const tabsContainer = document.createElement('div');
        tabsContainer.className = 'mb-4';

        // Create tabs
        const tabs = document.createElement('div');
        tabs.className = 'flex border-b border-gray-200 dark:border-gray-700';

        // Create JSON Files tab
        const filesTab = document.createElement('button');
        filesTab.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400 flex-1';
        filesTab.textContent = 'JSON Files';
        filesTab.dataset.tab = 'files';

        // Create Custom Path tab
        const customTab = document.createElement('button');
        customTab.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 flex-1';
        customTab.textContent = 'Custom Path';
        customTab.dataset.tab = 'custom';

        // Create MongoDB tab
        const mongodbTab = document.createElement('button');
        mongodbTab.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 flex-1';
        mongodbTab.textContent = 'MongoDB';
        mongodbTab.dataset.tab = 'mongodb';

        // Add tabs to container
        tabs.appendChild(filesTab);
        tabs.appendChild(customTab);
        tabs.appendChild(mongodbTab);
        tabsContainer.appendChild(tabs);

        // Create content containers
        const contentContainer = document.createElement('div');
        contentContainer.className = 'mt-4';

        // Create JSON Files content
        const filesContent = document.createElement('div');
        filesContent.className = 'tab-content';
        filesContent.dataset.tab = 'files';

        // Create Custom Path content
        const customContent = document.createElement('div');
        customContent.className = 'tab-content hidden';
        customContent.dataset.tab = 'custom';

        // Create MongoDB content
        const mongodbContent = document.createElement('div');
        mongodbContent.className = 'tab-content hidden';
        mongodbContent.dataset.tab = 'mongodb';

        // Add content containers
        contentContainer.appendChild(filesContent);
        contentContainer.appendChild(customContent);
        contentContainer.appendChild(mongodbContent);

        // Add tabs and content to main container
        container.appendChild(tabsContainer);
        container.appendChild(contentContainer);

        // Initialize tab content
        let activeTab = 'files';
        let activePath = defaultPath;

        // Check if default path is a MongoDB URI
        if (defaultPath && defaultPath.startsWith('mongodb://')) {
            activeTab = 'mongodb';
        }
        // Check if default path is not in the list of files
        else if (defaultPath && !defaultPath.includes('software/rl/training/data/')) {
            activeTab = 'custom';
        }

        // Set active tab
        tabs.querySelectorAll('button').forEach(tab => {
            if (tab.dataset.tab === activeTab) {
                tab.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400 flex-1';
            } else {
                tab.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 flex-1';
            }
        });

        // Show active content
        contentContainer.querySelectorAll('.tab-content').forEach(content => {
            if (content.dataset.tab === activeTab) {
                content.classList.remove('hidden');
            } else {
                content.classList.add('hidden');
            }
        });

        // Initialize JSON Files tab
        await TrainingDataFilesUI.init(filesContent, (path) => {
            if (activeTab === 'files') {
                activePath = path;
                if (typeof onSelect === 'function') {
                    onSelect(path);
                }
            }
        }, activeTab === 'files' ? defaultPath : null);

        // Initialize Custom Path tab
        TrainingDataCustomUI.init(customContent, (path) => {
            if (activeTab === 'custom') {
                activePath = path;
                if (typeof onSelect === 'function') {
                    onSelect(path);
                }
            }
        }, activeTab === 'custom' ? defaultPath : 'software/rl/training/data/sample_training.json');

        // Initialize MongoDB tab
        await TrainingDataMongoDBUI.init(mongodbContent, (path) => {
            if (activeTab === 'mongodb') {
                activePath = path;
                if (typeof onSelect === 'function') {
                    onSelect(path);
                }
            }
        }, activeTab === 'mongodb' ? defaultPath.replace('mongodb://', '') : null);

        // Add tab click event listeners
        tabs.querySelectorAll('button').forEach(tab => {
            tab.addEventListener('click', () => {
                // Update active tab
                activeTab = tab.dataset.tab;

                // Update tab styles
                tabs.querySelectorAll('button').forEach(t => {
                    if (t.dataset.tab === activeTab) {
                        t.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400 flex-1';
                    } else {
                        t.className = 'py-2 px-4 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600 flex-1';
                    }
                });

                // Show active content
                contentContainer.querySelectorAll('.tab-content').forEach(content => {
                    if (content.dataset.tab === activeTab) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });

                // Reset active path based on active tab
                if (activeTab === 'files') {
                    // Get selected file path
                    const selectedFile = filesContent.querySelector('.bg-blue-50');
                    if (selectedFile) {
                        activePath = selectedFile.dataset.path;
                    } else {
                        // Get first file path
                        const firstFile = filesContent.querySelector('[data-path]');
                        if (firstFile) {
                            activePath = firstFile.dataset.path;
                        }
                    }
                } else if (activeTab === 'custom') {
                    // Get custom path
                    const customInput = customContent.querySelector('input');
                    if (customInput) {
                        activePath = customInput.value;
                    }
                } else if (activeTab === 'mongodb') {
                    // Get MongoDB path
                    const collectionSelect = mongodbContent.querySelector('select');
                    const vectorField = mongodbContent.querySelector('#mongodb-vector-field');
                    const labelField = mongodbContent.querySelector('#mongodb-label-field');

                    if (collectionSelect && collectionSelect.value && vectorField && labelField) {
                        activePath = `mongodb://${collectionSelect.value}?vector_field=${vectorField.value}&label_field=${labelField.value}`;
                    }
                }

                // Call onSelect with active path
                if (typeof onSelect === 'function' && activePath) {
                    onSelect(activePath);
                }
            });
        });
    }
};

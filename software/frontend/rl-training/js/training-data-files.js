/**
 * Training Data Files UI Component
 * Handles displaying and selecting training data files from the file system
 */

const TrainingDataFilesUI = {
    /**
     * Fetch training data files from the API
     * @returns {Promise<Array>} Array of training data file objects
     */
    async fetchTrainingDataFiles() {
        try {
            console.log('TrainingDataFilesUI: Fetching training data files...');
            const files = await RLTrainingAPI.getTrainingDataFiles();
            console.log('TrainingDataFilesUI: Received files:', files);
            return files || [];
        } catch (error) {
            console.error('TrainingDataFilesUI: Error fetching training data files:', error);
            return [];
        }
    },

    /**
     * Format file size in human-readable format
     * @param {number} bytes File size in bytes
     * @returns {string} Formatted file size
     */
    formatFileSize(bytes) {
        if (bytes < 1024) {
            return `${bytes} B`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} KB`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
        }
    },

    /**
     * Format date in human-readable format
     * @param {number} timestamp Unix timestamp
     * @returns {string} Formatted date
     */
    formatDate(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleString();
    },

    /**
     * Initialize the training data files selector
     * @param {HTMLElement} container Container element for the selector
     * @param {function} onSelect Callback function when a file is selected
     * @param {string} defaultPath Default path to select
     */
    async init(container, onSelect, defaultPath = null) {
        // Fetch training data files
        const files = await this.fetchTrainingDataFiles();

        // Clear container
        container.innerHTML = '';

        if (files.length === 0) {
            // No files found
            const noFilesMessage = document.createElement('div');
            noFilesMessage.className = 'text-gray-500 dark:text-gray-400 text-sm italic p-2';
            noFilesMessage.textContent = 'No training data files found';
            container.appendChild(noFilesMessage);
            return;
        }

        // Create file list container
        const fileList = document.createElement('div');
        fileList.className = 'mt-1 border border-gray-300 rounded-md shadow-sm overflow-y-auto max-h-60 dark:border-gray-600';

        // Add files to list
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'p-2 border-b border-gray-200 last:border-b-0 cursor-pointer hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-700 transition-colors';
            fileItem.dataset.path = file.path;

            // Create file info container
            const fileInfo = document.createElement('div');
            fileInfo.className = 'flex justify-between items-center';

            // Create file name container
            const fileNameContainer = document.createElement('div');
            fileNameContainer.className = 'flex items-center';

            // Add file icon
            const fileIcon = document.createElement('div');
            fileIcon.className = 'mr-2 text-blue-500 dark:text-blue-400';
            fileIcon.innerHTML = `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            `;

            // Add file name
            const fileName = document.createElement('div');
            fileName.className = 'text-sm font-medium text-gray-900 dark:text-white';
            fileName.textContent = file.filename;

            // Add file name to container
            fileNameContainer.appendChild(fileIcon);
            fileNameContainer.appendChild(fileName);

            // Create file details container
            const fileDetails = document.createElement('div');
            fileDetails.className = 'text-xs text-gray-500 dark:text-gray-400 flex items-center';

            // Add file size
            const fileSize = document.createElement('span');
            fileSize.className = 'mr-2';
            fileSize.textContent = this.formatFileSize(file.size);

            // Add file date
            const fileDate = document.createElement('span');
            fileDate.textContent = this.formatDate(file.modified);

            // Add data count if available
            if (file.data_count) {
                const dataCount = document.createElement('span');
                dataCount.className = 'ml-2 px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200';
                dataCount.textContent = `${file.data_count} items`;
                fileDetails.appendChild(dataCount);
            }

            // Add file details to container
            fileDetails.appendChild(fileSize);
            fileDetails.appendChild(fileDate);

            // Add to file info
            fileInfo.appendChild(fileNameContainer);
            fileInfo.appendChild(fileDetails);

            // Add to file item
            fileItem.appendChild(fileInfo);

            // Add click event
            fileItem.addEventListener('click', () => {
                // Remove selected class from all items
                fileList.querySelectorAll('.bg-blue-50').forEach(item => {
                    item.classList.remove('bg-blue-50', 'dark:bg-blue-900');
                    item.classList.add('hover:bg-gray-50', 'dark:hover:bg-gray-700');
                });

                // Add selected class to clicked item
                fileItem.classList.remove('hover:bg-gray-50', 'dark:hover:bg-gray-700');
                fileItem.classList.add('bg-blue-50', 'dark:bg-blue-900');

                // Call onSelect with file path
                if (typeof onSelect === 'function') {
                    onSelect(file.path);
                }
            });

            // Add to file list
            fileList.appendChild(fileItem);
        });

        // Add file list to container
        container.appendChild(fileList);

        // Select default path or first file by default
        if (files.length > 0) {
            const pathToSelect = defaultPath || files[0].path;
            const fileItem = fileList.querySelector(`[data-path="${pathToSelect}"]`) || 
                             fileList.querySelector('[data-path]');
            
            if (fileItem) {
                fileItem.classList.remove('hover:bg-gray-50', 'dark:hover:bg-gray-700');
                fileItem.classList.add('bg-blue-50', 'dark:bg-blue-900');

                // Call onSelect with selected file path
                if (typeof onSelect === 'function') {
                    onSelect(pathToSelect);
                }
            }
        }
    }
};

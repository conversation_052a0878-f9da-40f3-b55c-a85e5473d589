/**
 * Hyperparameters Optimizer for RL Training
 *
 * This component provides an advanced interface for optimizing hyperparameters
 * by allowing users to define multiple values for each parameter to create combinations.
 */

const HyperparametersOptimizer = {
    // Default hyperparameters for PPO algorithm
    defaultHyperparameters: {
        PPO: {
            learning_rate: 0.0003,
            n_steps: 2048,
            batch_size: 64,
            n_epochs: 10,
            gamma: 0.99,
            gae_lambda: 0.95,
            clip_range: 0.2,
            ent_coef: 0.0,
            vf_coef: 0.5,
            max_grad_norm: 0.5
        }
    },

    // Smart optimization settings - always enabled by default
    smartOptimization: {
        enabled: true,
        maxTrials: 1,
        strategy: 'bayesian' // Always use Bayesian optimization
    },

    // Parameter descriptions and recommended ranges
    parameterInfo: {
        learning_rate: {
            description: "Step size for the optimizer",
            recommendedRange: [0.00001, 0.001],
            isLogarithmic: true,
            unit: ""
        },
        n_steps: {
            description: "Number of steps to run for each environment per update",
            recommendedRange: [64, 4096],
            isLogarithmic: false,
            unit: "steps"
        },
        batch_size: {
            description: "Minibatch size for gradient updates",
            recommendedRange: [32, 256],
            isLogarithmic: false,
            unit: ""
        },
        n_epochs: {
            description: "Number of epochs when optimizing the surrogate loss",
            recommendedRange: [3, 30],
            isLogarithmic: false,
            unit: "epochs"
        },
        gamma: {
            description: "Discount factor",
            recommendedRange: [0.9, 0.9999],
            isLogarithmic: false,
            unit: ""
        },
        gae_lambda: {
            description: "Factor for trade-off of bias vs variance in GAE",
            recommendedRange: [0.9, 0.99],
            isLogarithmic: false,
            unit: ""
        },
        clip_range: {
            description: "Clipping parameter for PPO",
            recommendedRange: [0.1, 0.3],
            isLogarithmic: false,
            unit: ""
        },
        ent_coef: {
            description: "Entropy coefficient for exploration",
            recommendedRange: [0.0, 0.1],
            isLogarithmic: true,
            unit: ""
        },
        vf_coef: {
            description: "Value function coefficient",
            recommendedRange: [0.1, 1.0],
            isLogarithmic: false,
            unit: ""
        },
        max_grad_norm: {
            description: "Maximum norm for gradient clipping",
            recommendedRange: [0.1, 1.0],
            isLogarithmic: false,
            unit: ""
        }
    },

    /**
     * Initialize the hyperparameters optimizer component
     * @param {string} containerId - ID of the container element
     * @param {Object} hyperparameters - Current hyperparameters
     * @param {Function} onUpdate - Callback function when hyperparameters are updated
     */
    init(containerId, hyperparameters, onUpdate) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container element with ID '${containerId}' not found`);
            return;
        }

        // Store references
        this.container = container;
        this.hyperparameters = hyperparameters || JSON.parse(JSON.stringify(this.defaultHyperparameters.PPO));
        this.onUpdate = onUpdate;
        this.optimizationEnabled = {};
        this.parameterValues = {};

        // Always enable smart optimization by default
        this.smartOptimization = {
            enabled: true,
            maxTrials: 1,
            strategy: 'bayesian'
        };

        // Default optimization depth
        this.optimizationDepth = 'deep';
        console.log('DEBUG - Initialized HyperparametersOptimizer with depth:', this.optimizationDepth);

        // Initialize parameter values with current values and enable optimization for all
        Object.keys(this.hyperparameters).forEach(key => {
            this.optimizationEnabled[key] = true; // Enable optimization for all parameters by default

            // Initialize with common values for each parameter
            switch (key) {
                case 'learning_rate':
                    this.parameterValues[key] = [0.00001, 0.0001, 0.0003, 0.001, 0.003];
                    break;
                case 'n_steps':
                    this.parameterValues[key] = [64, 128, 512, 1024, 2048];
                    break;
                case 'batch_size':
                    this.parameterValues[key] = [32, 64, 128, 256, 512];
                    break;
                case 'n_epochs':
                    this.parameterValues[key] = [3, 5, 10, 15, 20];
                    break;
                case 'gamma':
                    this.parameterValues[key] = [0.9, 0.95, 0.98, 0.99, 0.999];
                    break;
                case 'gae_lambda':
                    this.parameterValues[key] = [0.9, 0.92, 0.95, 0.97, 0.99];
                    break;
                case 'clip_range':
                    this.parameterValues[key] = [0.1, 0.15, 0.2, 0.25, 0.3];
                    break;
                case 'ent_coef':
                    this.parameterValues[key] = [0.0, 0.001, 0.01, 0.05, 0.1];
                    break;
                case 'vf_coef':
                    this.parameterValues[key] = [0.1, 0.3, 0.5, 0.7, 1.0];
                    break;
                case 'max_grad_norm':
                    this.parameterValues[key] = [0.1, 0.3, 0.5, 0.7, 1.0];
                    break;
                default:
                    this.parameterValues[key] = [this.hyperparameters[key]];
                    break;
            }
        });

        // Render the component
        this.render();
    },

    /**
     * Render the hyperparameters optimizer component
     */
    render() {
        // Clear the container
        this.container.innerHTML = '';

        // Create the main container
        const optimizerContainer = document.createElement('div');
        optimizerContainer.className = 'hyperparameters-optimizer bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4';

        // Add header without toggle (optimization is always enabled)
        const header = document.createElement('div');
        header.className = 'flex items-center mb-4';

        const icon = document.createElement('svg');
        icon.className = 'w-5 h-5 mr-2 text-indigo-600 dark:text-indigo-400';
        icon.setAttribute('fill', 'none');
        icon.setAttribute('stroke', 'currentColor');
        icon.setAttribute('viewBox', '0 0 24 24');
        icon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        `;

        const title = document.createElement('h3');
        title.className = 'text-sm font-medium text-gray-700 dark:text-gray-300';
        title.textContent = 'Smart Bayesian Hyperparameter Optimization';

        header.appendChild(icon);
        header.appendChild(title);

        optimizerContainer.appendChild(header);

        // Add the parameters section
        const parametersContainer = document.createElement('div');
        parametersContainer.className = 'space-y-4 mt-2';
        parametersContainer.id = 'parameters-container';

        // Add each parameter
        Object.keys(this.hyperparameters).forEach(key => {
            const paramContainer = this.createParameterControl(key);
            parametersContainer.appendChild(paramContainer);
        });

        optimizerContainer.appendChild(parametersContainer);

        // Add smart optimization info (always enabled)
        const smartOptContainer = document.createElement('div');
        smartOptContainer.className = 'mt-4 p-3 bg-purple-50 dark:bg-purple-900 rounded-md';

        const smartOptTitle = document.createElement('div');
        smartOptTitle.className = 'text-sm font-medium text-purple-700 dark:text-purple-300 mb-2';
        smartOptTitle.textContent = 'Smart Bayesian Optimization';

        smartOptContainer.appendChild(smartOptTitle);

        // Add info about Bayesian optimization
        const bayesianDesc = document.createElement('div');
        bayesianDesc.className = 'text-xs text-purple-600 dark:text-purple-400 mb-3';
        bayesianDesc.innerHTML = `<span class="font-medium">Bayesian Optimization:</span> Intelligently explores the parameter space by focusing on promising regions. Best for finding optimal parameters efficiently.`;

        smartOptContainer.appendChild(bayesianDesc);

        // Add optimization depth selector
        const depthContainer = document.createElement('div');
        depthContainer.className = 'mt-2';

        const depthLabel = document.createElement('div');
        depthLabel.className = 'text-xs font-medium text-purple-700 dark:text-purple-300 mb-1';
        depthLabel.textContent = 'Optimization Depth:';

        depthContainer.appendChild(depthLabel);

        // Create radio buttons for depth options
        const depthOptions = [
            { id: 'depth-deep', value: 'deep', label: 'Deep', description: '5 parameter values per dimension' },
            { id: 'depth-deeper', value: 'deeper', label: 'Deeper', description: '8 parameter values per dimension' },
            { id: 'depth-extra', value: 'extra', label: 'Extra Deep', description: '12 parameter values per dimension' }
        ];

        const depthRadioGroup = document.createElement('div');
        depthRadioGroup.className = 'flex flex-col space-y-2 mt-2';

        depthOptions.forEach((option, index) => {
            const radioContainer = document.createElement('div');
            radioContainer.className = 'flex items-center';

            const radio = document.createElement('input');
            radio.type = 'radio';
            radio.id = option.id;
            radio.name = 'optimization-depth';
            radio.value = option.value;
            radio.className = 'h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300';
            radio.checked = index === 0; // Select the first option by default
            radio.dataset.depth = option.value;

            const radioLabel = document.createElement('label');
            radioLabel.htmlFor = option.id;
            radioLabel.className = 'ml-2 block text-sm text-purple-700 dark:text-purple-300';
            radioLabel.innerHTML = `<span class="font-medium">${option.label}</span> - <span class="text-xs">${option.description}</span>`;

            radioContainer.appendChild(radio);
            radioContainer.appendChild(radioLabel);
            depthRadioGroup.appendChild(radioContainer);

            // Add event listener
            radio.addEventListener('change', () => {
                if (radio.checked) {
                    this.optimizationDepth = option.value;
                    console.log('DEBUG - Changed optimization depth to:', this.optimizationDepth);
                    this.updateHyperparameters();
                }
            });
        });

        depthContainer.appendChild(depthRadioGroup);
        smartOptContainer.appendChild(depthContainer);

        // Add smart optimization container to the main container
        optimizerContainer.appendChild(smartOptContainer);

        // Add smart optimization info
        const smartOptInfo = document.createElement('div');
        smartOptInfo.id = 'smart-optimization-info';
        smartOptInfo.className = 'mt-4 p-3 bg-green-50 dark:bg-green-900 rounded-md';

        const smartOptInfoText = document.createElement('p');
        smartOptInfoText.className = 'text-xs text-green-700 dark:text-green-300';

        // Create info text based on depth
        let infoText = `<span class="font-medium">Smart Bayesian Optimization</span> will intelligently explore the parameter space to find optimal hyperparameters. `;

        switch (this.optimizationDepth) {
            case 'deeper':
                infoText += `With <span class="font-bold">Deeper</span> mode, the system will explore 8 values per parameter, providing a more thorough search.`;
                break;
            case 'extra':
                infoText += `With <span class="font-bold">Extra Deep</span> mode, the system will explore 12 values per parameter, providing the most comprehensive search.`;
                break;
            case 'deep':
            default:
                infoText += `With <span class="font-bold">Deep</span> mode, the system will explore 5 values per parameter, providing a good balance between speed and thoroughness.`;
                break;
        }

        smartOptInfoText.innerHTML = infoText;
        smartOptInfo.appendChild(smartOptInfoText);

        optimizerContainer.appendChild(smartOptInfo);

        // Add the container to the DOM
        this.container.appendChild(optimizerContainer);

        // Add event listeners
        this.addEventListeners();

        // Update parameter value displays based on default depth
        this.updateParameterValueDisplays();

        // Add custom styles
        this.addCustomStyles();
    },

    /**
     * Create a parameter control for a specific hyperparameter
     * @param {string} key - Parameter key
     * @returns {HTMLElement} Parameter control element
     */
    createParameterControl(key) {
        const paramInfo = this.parameterInfo[key] || {
            description: "Parameter description",
            recommendedRange: [0, 1],
            isLogarithmic: false,
            unit: ""
        };

        const container = document.createElement('div');
        container.className = 'parameter-container bg-gray-50 dark:bg-gray-700 p-3 rounded-md border border-gray-200 dark:border-gray-600';

        // Parameter header with name and description
        const header = document.createElement('div');
        header.className = 'flex items-center justify-between';

        const nameContainer = document.createElement('div');

        const paramName = document.createElement('label');
        paramName.className = 'text-sm font-medium text-gray-700 dark:text-gray-300';
        paramName.textContent = key;

        const paramDesc = document.createElement('p');
        paramDesc.className = 'text-xs text-gray-500 dark:text-gray-400 mt-0.5';

        // Add more detailed descriptions for PPO parameters
        let description = paramInfo.description;
        switch (key) {
            case 'learning_rate':
                description = 'Learning rate for the optimizer. Controls how much to update the model in response to errors.';
                break;
            case 'n_steps':
                description = 'Number of steps to run for each environment per update. Larger values = more exploration.';
                break;
            case 'batch_size':
                description = 'Minibatch size for gradient updates. Smaller values = more updates but less stable.';
                break;
            case 'n_epochs':
                description = 'Number of epochs when optimizing the surrogate loss. More epochs = more optimization.';
                break;
            case 'gamma':
                description = 'Discount factor for future rewards. Higher values (closer to 1) = more focus on long-term rewards.';
                break;
            case 'gae_lambda':
                description = 'Factor for trade-off of bias vs variance in Generalized Advantage Estimation. Higher = less bias, more variance.';
                break;
            case 'clip_range':
                description = 'Clipping parameter for PPO. Limits how much the policy can change in a single update.';
                break;
            case 'ent_coef':
                description = 'Entropy coefficient. Higher values encourage more exploration.';
                break;
            case 'vf_coef':
                description = 'Value function coefficient. Controls the importance of value function loss in the total loss.';
                break;
            case 'max_grad_norm':
                description = 'Maximum value for gradient clipping. Prevents exploding gradients.';
                break;
        }

        paramDesc.textContent = description;

        nameContainer.appendChild(paramName);
        nameContainer.appendChild(paramDesc);

        // Hidden checkbox for optimization (always checked)
        const hiddenCheckbox = document.createElement('input');
        hiddenCheckbox.type = 'hidden';
        hiddenCheckbox.className = 'parameter-optimize-toggle';
        hiddenCheckbox.dataset.param = key;
        hiddenCheckbox.value = 'true';

        header.appendChild(nameContainer);
        header.appendChild(hiddenCheckbox);

        container.appendChild(header);

        // Current value display
        const currentValue = document.createElement('div');
        currentValue.className = 'mt-2';

        const valueLabel = document.createElement('span');
        valueLabel.className = 'text-xs font-medium text-gray-700 dark:text-gray-300';
        valueLabel.textContent = 'Default Value: ';

        const valueDisplay = document.createElement('span');
        valueDisplay.className = 'text-xs text-gray-800 dark:text-gray-200';
        valueDisplay.textContent = this.hyperparameters[key] + (paramInfo.unit ? ` ${paramInfo.unit}` : '');

        currentValue.appendChild(valueLabel);
        currentValue.appendChild(valueDisplay);

        container.appendChild(currentValue);

        // Values display (always visible)
        const valuesContainer = document.createElement('div');
        valuesContainer.className = 'mt-2';
        valuesContainer.dataset.param = key;

        const valuesLabel = document.createElement('div');
        valuesLabel.className = 'text-xs font-medium text-gray-700 dark:text-gray-300 mb-1';
        valuesLabel.textContent = 'Optimization Values:';

        // Create a display for the values that will be updated based on depth
        const valuesDisplay = document.createElement('div');
        valuesDisplay.className = 'text-xs text-gray-800 dark:text-gray-200 p-2 bg-gray-100 dark:bg-gray-600 rounded';
        valuesDisplay.id = `values-display-${key}`;

        // Set initial values based on default depth
        const initialValues = this.generateDepthValues(key, 5); // Default to 'deep' (5 values)
        valuesDisplay.textContent = initialValues.join(', ');

        valuesContainer.appendChild(valuesLabel);
        valuesContainer.appendChild(valuesDisplay);

        container.appendChild(valuesContainer);

        return container;
    },

    /**
     * Add event listeners to the component
     */
    addEventListeners() {
        // Set all parameters to be optimized by default
        Object.keys(this.hyperparameters).forEach(param => {
            this.optimizationEnabled[param] = true;
        });

        // Update hyperparameters with smart optimization enabled
        this.updateHyperparameters();

        // Add event listeners to depth radio buttons
        document.querySelectorAll('input[name="optimization-depth"]').forEach(radio => {
            radio.addEventListener('change', () => {
                if (radio.checked) {
                    this.optimizationDepth = radio.dataset.depth;

                    // Update all parameter value displays
                    this.updateParameterValueDisplays();

                    // Update hyperparameters
                    this.updateHyperparameters();
                }
            });
        });
    },

    /**
     * Update all parameter value displays based on the current depth
     */
    updateParameterValueDisplays() {
        let count;
        switch (this.optimizationDepth) {
            case 'deeper':
                count = 8;
                break;
            case 'extra':
                count = 12;
                break;
            case 'deep':
            default:
                count = 5;
                break;
        }

        // Update each parameter's value display
        Object.keys(this.hyperparameters).forEach(param => {
            const valuesDisplay = document.getElementById(`values-display-${param}`);
            if (valuesDisplay) {
                const values = this.generateDepthValues(param, count);
                valuesDisplay.textContent = values.join(', ');
            }
        });

        // Update smart optimization info text
        const smartOptInfoText = document.querySelector('#smart-optimization-info p');
        if (smartOptInfoText) {
            let infoText = `<span class="font-medium">Smart Bayesian Optimization</span> will intelligently explore the parameter space to find optimal hyperparameters. `;

            switch (this.optimizationDepth) {
                case 'deeper':
                    infoText += `With <span class="font-bold">Deeper</span> mode, the system will explore 8 values per parameter, providing a more thorough search.`;
                    break;
                case 'extra':
                    infoText += `With <span class="font-bold">Extra Deep</span> mode, the system will explore 12 values per parameter, providing the most comprehensive search.`;
                    break;
                case 'deep':
                default:
                    infoText += `With <span class="font-bold">Deep</span> mode, the system will explore 5 values per parameter, providing a good balance between speed and thoroughness.`;
                    break;
            }

            smartOptInfoText.innerHTML = infoText;
        }
    },

    /**
     * Add custom styles for the component
     */
    addCustomStyles() {
        // Check if styles already exist
        if (document.getElementById('hyperparameters-optimizer-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'hyperparameters-optimizer-styles';
        style.textContent = `
            /* Toggle switch styles */
            .switch .slider {
                background-color: #ccc;
            }

            .switch input:checked + .slider {
                background-color: #4f46e5;
            }

            .switch input:focus + .slider {
                box-shadow: 0 0 1px #4f46e5;
            }

            .switch .slider:before {
                position: absolute;
                content: "";
                height: 16px;
                width: 16px;
                left: 2px;
                bottom: 2px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
            }

            .switch input:checked + .slider:before {
                transform: translateX(18px);
            }

            .switch .slider.round {
                border-radius: 34px;
            }

            .dark .switch .slider {
                background-color: #4b5563;
            }

            .dark .switch input:checked + .slider {
                background-color: #6366f1;
            }
        `;

        document.head.appendChild(style);
    },

    /**
     * Generate linearly spaced values
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @param {number} count - Number of values to generate
     * @returns {Array} Array of values
     */
    generateLinearRange(min, max, count) {
        const values = [];
        for (let i = 0; i < count; i++) {
            const value = min + (max - min) * (i / (count - 1));
            values.push(this.roundValue(value));
        }
        return values;
    },

    /**
     * Generate logarithmically spaced values
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @param {number} count - Number of values to generate
     * @returns {Array} Array of values
     */
    generateLogRange(min, max, count) {
        const logMin = Math.log(min);
        const logMax = Math.log(max);
        const values = [];

        for (let i = 0; i < count; i++) {
            const logValue = logMin + (logMax - logMin) * (i / (count - 1));
            const value = Math.exp(logValue);
            values.push(this.roundValue(value));
        }

        return values;
    },

    /**
     * Generate common values for a specific parameter
     * @param {string} param - Parameter name
     * @returns {Array} Array of common values
     */
    generateCommonValues(param) {
        switch (param) {
            case 'learning_rate':
                return [0.00001, 0.0001, 0.0003, 0.001, 0.003];
            case 'n_steps':
                return [64, 128, 512, 1024, 2048];
            case 'batch_size':
                return [32, 64, 128, 256, 512];
            case 'n_epochs':
                return [3, 5, 10, 15, 20];
            case 'gamma':
                return [0.9, 0.95, 0.98, 0.99, 0.999];
            case 'gae_lambda':
                return [0.9, 0.92, 0.95, 0.97, 0.99];
            case 'clip_range':
                return [0.1, 0.15, 0.2, 0.25, 0.3];
            case 'ent_coef':
                return [0.0, 0.001, 0.01, 0.05, 0.1];
            case 'vf_coef':
                return [0.1, 0.3, 0.5, 0.7, 1.0];
            case 'max_grad_norm':
                return [0.1, 0.3, 0.5, 0.7, 1.0];
            default:
                return [0.1, 0.3, 0.5, 0.7, 0.9];
        }
    },

    /**
     * Round a value to a reasonable precision
     * @param {number} value - Value to round
     * @returns {number} Rounded value
     */
    roundValue(value) {
        if (value >= 1) {
            return Math.round(value * 100) / 100;
        } else if (value >= 0.01) {
            return Math.round(value * 1000) / 1000;
        } else {
            return Math.round(value * 100000) / 100000;
        }
    },

    /**
     * Update the combinations count display
     */
    updateCombinationsCount() {
        let totalCombinations = 1;

        Object.keys(this.optimizationEnabled).forEach(param => {
            if (this.optimizationEnabled[param]) {
                totalCombinations *= this.parameterValues[param].length;
            }
        });

        const countElement = document.getElementById('combinations-count');
        if (countElement) {
            countElement.textContent = totalCombinations;

            // Add warning class if too many combinations
            if (totalCombinations > 20) {
                countElement.classList.add('text-red-600', 'dark:text-red-400');
            } else {
                countElement.classList.remove('text-red-600', 'dark:text-red-400');
            }
        }
    },

    /**
     * Update hyperparameters and call the onUpdate callback
     */
    updateHyperparameters() {
        if (typeof this.onUpdate === 'function') {
            // Smart optimization is always enabled
            // Check if any parameters are enabled for optimization
            const hasOptimization = Object.values(this.optimizationEnabled).some(enabled => enabled);

            if (!hasOptimization) {
                // No parameters selected for optimization, just use current hyperparameters
                this.onUpdate(this.hyperparameters);
                return;
            }

            // Create parameters object for smart optimization based on depth
            const parameters = {};

            // Get parameter values based on optimization depth
            Object.keys(this.optimizationEnabled).forEach(param => {
                if (this.optimizationEnabled[param]) {
                    // Generate parameter values based on depth
                    let values;
                    switch (this.optimizationDepth) {
                        case 'deeper':
                            values = this.generateDepthValues(param, 8);
                            break;
                        case 'extra':
                            values = this.generateDepthValues(param, 12);
                            break;
                        case 'deep':
                        default:
                            values = this.generateDepthValues(param, 5);
                            break;
                    }
                    parameters[param] = values;
                }
            });

            // Create the smart optimization configuration
            const smartOptimizationConfig = {
                smart_hyperparameters_optimization: {
                    enabled: true,
                    parameters: parameters,
                    max_trials: 1,  // Always use 1 trial
                    strategy: 'bayesian',  // Always use Bayesian optimization
                    single_optimized_job: true,  // Always produce a single optimized job
                    depth: this.optimizationDepth  // Include the depth setting
                }
            };

            console.log('DEBUG - Creating smart optimization config with depth:', this.optimizationDepth);

            // Call the onUpdate callback with the smart optimization configuration
            this.onUpdate(smartOptimizationConfig);
        }
    },

    /**
     * Generate parameter values based on depth
     * @param {string} param - Parameter name
     * @param {number} count - Number of values to generate
     * @returns {Array} Array of parameter values
     */
    generateDepthValues(param, count) {
        // Use parameter-specific ranges based on common values
        switch (param) {
            case 'learning_rate':
                return this.generateLogRange(0.00001, 0.01, count);
            case 'n_steps':
                return [64, 128, 256, 512, 1024, 2048, 4096, 8192].slice(0, count);
            case 'batch_size':
                return [32, 64, 128, 256, 512, 1024].slice(0, Math.min(count, 6));
            case 'n_epochs':
                return this.generateLinearRange(3, 30, count);
            case 'gamma':
                return this.generateLinearRange(0.9, 0.999, count);
            case 'gae_lambda':
                return this.generateLinearRange(0.9, 0.99, count);
            case 'clip_range':
                return this.generateLinearRange(0.1, 0.3, count);
            case 'ent_coef':
                return this.generateLogRange(0.0001, 0.1, count);
            case 'vf_coef':
                return this.generateLinearRange(0.1, 1.0, count);
            case 'max_grad_norm':
                return this.generateLinearRange(0.1, 2.0, count);
            default:
                return this.generateCommonValues(param);
        }
    },

    /**
     * Get all hyperparameter combinations based on the current configuration
     * @returns {Array} Array of hyperparameter combinations
     */
    getCombinations() {
        const combinations = [];
        const params = Object.keys(this.hyperparameters);

        // Helper function to generate combinations recursively
        const generateCombinations = (index, current) => {
            if (index === params.length) {
                combinations.push({...current});
                return;
            }

            const param = params[index];
            const values = this.optimizationEnabled[param] ?
                this.parameterValues[param] : [this.hyperparameters[param]];

            values.forEach(value => {
                current[param] = value;
                generateCombinations(index + 1, current);
            });
        };

        generateCombinations(0, {});
        return combinations;
    }
};

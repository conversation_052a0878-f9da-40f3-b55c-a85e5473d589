/**
 * Main JavaScript for RL Training page
 */
function rlTrainingApp() {
    return {
        // Data
        trainingJobs: [],
        environments: [],
        completedJobs: [], // List of completed jobs for finetuning
        isLoading: true,
        showAddTrainingModal: false,
        showResultsModal: false,
        showEditScheduleModal: false,
        currentEnvironmentSchema: { schema: {} }, // Schema information for the selected environment
        isLoadingSchema: false, // Loading state for environment schema
        currentTrainingJob: {
            environment_id: '',
            algorithm: 'PPO',
            timesteps: 10000,
            training_data_path: 'software/rl/training/data/sample_training.json',
            train_ratio: 0.8,
            finetune: false,
            model_to_finetune: '', // ID of the model to finetune
            recurring_finetune: false, // Enable recurring finetuning
            schedule_time: '03:00', // Default time for daily schedule (3 AM)
            hyperparameters: {},
            description: ''
        },
        selectedJob: null,
        trainingResults: null,
        editScheduleData: {
            job_id: null,
            schedule_time: '12:00',
            save_only_if_improved: true
        },
        notification: {
            show: false,
            message: '',
            type: 'info', // 'success', 'error', 'info'
            timeout: null
        },
        activeTab: 'all', // 'all', 'running', 'completed', 'pending', 'cancelled'
        algorithms: ['PPO'], // Only PPO for now, will add more in the future
        hyperparametersTemplate: {
            PPO: {
                learning_rate: 0.0003,
                n_steps: 2048,
                batch_size: 64,
                n_epochs: 10,
                gamma: 0.99,
                gae_lambda: 0.95,
                clip_range: 0.2,
                ent_coef: 0.0,
                vf_coef: 0.5,
                max_grad_norm: 0.5
            }
            // Other algorithms will be added in the future
        },

        // Lifecycle methods
        async init() {
            try {
                // Load environments first
                await this.loadEnvironments();

                // Then load training jobs
                await this.refreshTrainingJobs();

                // Set up polling for running jobs
                this.setupPolling();

                this.isLoading = false;
            } catch (error) {
                console.error('Initialization error:', error);
                this.showNotification('Failed to initialize: ' + error.message, 'error');
                this.isLoading = false;
            }
        },

        // Load environments from API
        async loadEnvironments() {
            try {
                this.environments = await RLEnvironmentsAPI.getEnvironments();

                // If there are environments, set the default environment for new training jobs
                if (this.environments.length > 0) {
                    // Find an active environment
                    const activeEnv = this.environments.find(env => env.is_active);
                    if (activeEnv) {
                        this.currentTrainingJob.environment_id = activeEnv._id;
                    } else {
                        this.currentTrainingJob.environment_id = this.environments[0]._id;
                    }
                }
            } catch (error) {
                console.error('Error loading environments:', error);
                this.showNotification('Failed to load environments: ' + error.message, 'error');
            }
        },

        // Refresh training jobs list
        async refreshTrainingJobs() {
            try {
                this.isLoading = true;

                // Get status filter based on active tab
                let statusFilter = this.activeTab !== 'all' ? this.activeTab : null;

                // Special handling for pending tab - include both pending and scheduled jobs
                if (this.activeTab === 'pending') {
                    // First get pending jobs
                    const pendingJobs = await fetch('/api/rl-training/?status=pending').then(res => res.json());

                    // Then get scheduled jobs
                    const scheduledJobs = await fetch('/api/rl-training/?status=scheduled').then(res => res.json());

                    // Combine both
                    this.trainingJobs = [...pendingJobs, ...scheduledJobs];

                        // Enrich jobs with environment details
                    this.trainingJobs = this.trainingJobs.map(job => {
                        const environment = this.environments.find(env => env._id === job.environment_id);
                        return {
                            ...job,
                            environment_name: environment ? environment.name : 'Unknown Environment'
                        };
                    });

                    this.isLoading = false;
                    // Skip the regular fetch since we've already fetched the jobs
                    return;
                }

                // Fetch training jobs for other tabs
                const url = statusFilter ? `/api/rl-training/?status=${statusFilter}` : '/api/rl-training/';
                this.trainingJobs = await fetch(url).then(res => res.json());

                // Enrich jobs with environment details
                this.trainingJobs = this.trainingJobs.map(job => {
                    const environment = this.environments.find(env => env._id === job.environment_id);
                    return {
                        ...job,
                        environment_name: environment ? environment.name : 'Unknown Environment'
                    };
                });

                this.isLoading = false;
            } catch (error) {
                console.error('Error refreshing training jobs:', error);
                this.showNotification('Failed to refresh training jobs: ' + error.message, 'error');
                this.isLoading = false;
            }
        },

        // Set up polling for running jobs
        setupPolling() {
            // Poll every 5 seconds for updates on running jobs
            setInterval(async () => {
                // Only poll if there are running jobs
                const runningJobs = this.trainingJobs.filter(job => job.status === 'running');
                if (runningJobs.length > 0) {
                    await this.refreshTrainingJobs();
                }
            }, 5000);
        },

        // Change active tab
        async changeTab(tab) {
            if (this.activeTab !== tab) {
                this.activeTab = tab;
                await this.refreshTrainingJobs();
            }
        },

        // Load completed jobs for finetuning
        async loadCompletedJobs() {
            try {
                // Fetch completed jobs
                const completedJobs = await fetch('/api/rl-training/?status=completed').then(res => res.json());

                // Enrich jobs with environment details
                this.completedJobs = completedJobs.map(job => {
                    const environment = this.environments.find(env => env._id === job.environment_id);
                    return {
                        ...job,
                        environment_name: environment ? environment.name : 'Unknown Environment'
                    };
                });
            } catch (error) {
                console.error('Error loading completed jobs:', error);
                this.showNotification('Failed to load completed jobs: ' + error.message, 'error');
            }
        },

        // Load environment schema - simplified since we don't display it anymore
        async loadEnvironmentSchema(_environmentId) {
            // We don't need to load the schema anymore since we've moved this functionality
            // to the RL Environments page
            this.currentEnvironmentSchema = { schema: {} };
            this.isLoadingSchema = false;
            return; // Early return to avoid trying to use RLEnvironmentsAPI
        },

        // Handle environment change
        async onEnvironmentChange() {
            // We don't need to load the schema anymore since we've moved this functionality
            // to the RL Environments page
            this.currentEnvironmentSchema = { schema: {} };
            this.isLoadingSchema = false;
        },

        // Open add training job modal
        async openAddTrainingModal() {
            // Reset current training job
            this.currentTrainingJob = {
                environment_id: this.environments.length > 0 ? this.environments[0]._id : '',
                algorithm: 'PPO',
                timesteps: 10000,
                training_data_path: 'software/rl/training/data/sample_training.json',
                train_ratio: 0.8,
                finetune: false,
                model_to_finetune: '',
                recurring_finetune: false,
                schedule_time: '03:00', // Default time for daily schedule (3 AM)
                hyperparameters: JSON.parse(JSON.stringify(this.hyperparametersTemplate.PPO)),
                description: ''
            };

            // We don't need to load the schema anymore since we've moved this functionality
            // to the RL Environments page
            this.currentEnvironmentSchema = { schema: {} };
            this.isLoadingSchema = false;

            // Load completed jobs for finetuning
            await this.loadCompletedJobs();

            this.showAddTrainingModal = true;

            // Initialize training data selector and hyperparameters optimizer after modal is shown
            this.$nextTick(() => {
                const trainingDataSelector = document.getElementById('training-data-selector');
                if (trainingDataSelector) {
                    TrainingDataUI.initSelector(trainingDataSelector, (path) => {
                        this.currentTrainingJob.training_data_path = path;
                    });
                }

                // Initialize hyperparameters optimizer with depth options
                const hyperparametersContainer = document.getElementById('add-hyperparameters-optimizer');
                if (hyperparametersContainer) {
                    HyperparametersOptimizer.init(
                        'add-hyperparameters-optimizer',
                        this.currentTrainingJob.hyperparameters,
                        (updatedHyperparameters) => {
                            this.currentTrainingJob.hyperparameters = updatedHyperparameters;
                        }
                    );
                }
            });
        },

        // Close add training job modal
        closeAddTrainingModal() {
            this.showAddTrainingModal = false;
        },

        // Handle algorithm change
        onAlgorithmChange() {
            // Update hyperparameters based on selected algorithm
            this.currentTrainingJob.hyperparameters = JSON.parse(
                JSON.stringify(this.hyperparametersTemplate[this.currentTrainingJob.algorithm])
            );
        },

        // Get the selected model for finetuning
        getSelectedModel() {
            if (!this.currentTrainingJob.model_to_finetune) return null;
            return this.completedJobs.find(job => job._id === this.currentTrainingJob.model_to_finetune);
        },

        // Create a new training job
        async createTrainingJob() {
            try {
                // Validate inputs
                if (!this.currentTrainingJob.environment_id) {
                    this.showNotification('Please select an environment', 'error');
                    return;
                }

                // Validate finetuning
                if (this.currentTrainingJob.finetune && !this.currentTrainingJob.model_to_finetune) {
                    this.showNotification('Please select a model to finetune', 'error');
                    return;
                }

                // Validate recurring finetuning
                if (this.currentTrainingJob.recurring_finetune && !this.currentTrainingJob.schedule_time) {
                    this.showNotification('Please select a time for recurring finetuning', 'error');
                    return;
                }

                // Prepare training job data
                const trainingJobData = { ...this.currentTrainingJob };

                // ALWAYS enable smart Bayesian optimization by default
                console.log('DEBUG - Always using smart Bayesian optimization by default');

                // Check if we already have a smart optimization configuration from the HyperparametersOptimizer component
                let smartOptConfig;

                // If we have a smart_hyperparameters_optimization property in the hyperparameters, use that
                if (this.currentTrainingJob.hyperparameters &&
                    this.currentTrainingJob.hyperparameters.smart_hyperparameters_optimization) {
                    smartOptConfig = this.currentTrainingJob.hyperparameters.smart_hyperparameters_optimization;
                    console.log('DEBUG - Using existing smart optimization config with depth:', smartOptConfig.depth);
                } else {
                    // Create a default smart optimization configuration
                    smartOptConfig = {
                        enabled: true,
                        parameters: {
                            learning_rate: [1e-05, 0.0001, 0.0003, 0.001, 0.003],
                            n_steps: [64, 128, 512, 1024, 2048],
                            batch_size: [32, 64, 128, 256],
                            n_epochs: [5, 10, 15, 20],
                            gamma: [0.95, 0.97, 0.99],
                            gae_lambda: [0.9, 0.95, 0.98],
                            clip_range: [0.1, 0.2, 0.3],
                            ent_coef: [0, 0.01, 0.05],
                            vf_coef: [0.5, 0.7, 1.0],
                            max_grad_norm: [0.5, 1.0, 2.0]
                        },
                        max_trials: 1,  // Only produce one optimized job at the end
                        strategy: 'bayesian',
                        single_optimized_job: true,  // Flag to indicate we want a single optimized job
                        depth: 'deep'  // Default to 'deep' if not specified
                    };
                    console.log('DEBUG - Created default smart optimization config with depth: deep');
                }

                // IMPORTANT: Make sure we're not nesting this inside hyperparameters
                // Remove it from hyperparameters if it's there
                if (trainingJobData.hyperparameters && trainingJobData.hyperparameters.smart_hyperparameters_optimization) {
                    // Extract the depth parameter before deleting
                    const depth = trainingJobData.hyperparameters.smart_hyperparameters_optimization.depth;
                    if (depth) {
                        console.log(`DEBUG - Extracted depth parameter from hyperparameters: ${depth}`);
                        // Make sure the smartOptConfig has the depth parameter
                        smartOptConfig.depth = depth;
                    }

                    delete trainingJobData.hyperparameters.smart_hyperparameters_optimization;
                }

                // Set the smart optimization config as a TOP-LEVEL property (not nested in hyperparameters)
                trainingJobData.smart_hyperparameters_optimization = smartOptConfig;

                // Debug log to confirm the depth parameter is being sent
                console.log(`DEBUG - Final smart optimization config depth: ${trainingJobData.smart_hyperparameters_optimization.depth}`);

                // Set default hyperparameters for the first job (these will be ignored by the server)
                // but we need to have something in the hyperparameters field
                trainingJobData.hyperparameters = {
                    learning_rate: 0.0003,
                    n_steps: 2048,
                    batch_size: 64,
                    n_epochs: 10,
                    gamma: 0.99,
                    gae_lambda: 0.95,
                    clip_range: 0.2,
                    ent_coef: 0.0,
                    vf_coef: 0.5,
                    max_grad_norm: 0.5
                };

                console.log('DEBUG - Smart optimization config:', trainingJobData.smart_hyperparameters_optimization);

                /* We're always using smart optimization, so we don't need this code anymore
                // Handle regular hyperparameters optimization if enabled
                if (trainingJobData.hyperparameters_optimization && trainingJobData.hyperparameters_optimization.enabled) {
                    // Check if we have too many combinations
                    let totalCombinations = 1;
                    const parameters = trainingJobData.hyperparameters_optimization.parameters;

                    Object.keys(parameters).forEach(param => {
                        totalCombinations *= parameters[param].length;
                    });

                    if (totalCombinations > 20) {
                        if (!confirm(`This will create ${totalCombinations} training jobs. Are you sure you want to continue?`)) {
                            return;
                        }
                    }

                    // Set hyperparameters to the default values for the first job (these will be ignored by the server)
                    // but we need to have something in the hyperparameters field
                    trainingJobData.hyperparameters = {};
                    Object.keys(parameters).forEach(param => {
                        trainingJobData.hyperparameters[param] = parameters[param][0];
                    });
                }
                */

                // If finetuning, add the model_id to the request
                if (this.currentTrainingJob.finetune) {
                    // Use the correct property name based on the API
                    trainingJobData.model_to_finetune = this.currentTrainingJob.model_to_finetune;

                    // If recurring finetuning is enabled, add the save_only_if_improved flag
                    if (this.currentTrainingJob.recurring_finetune) {
                        trainingJobData.save_only_if_improved = true;
                        trainingJobData.schedule = {
                            type: 'daily',
                            time: this.currentTrainingJob.schedule_time
                        };
                    }
                }

                // Debug log the training job data
                console.log('DEBUG - Training job data being sent to server:', trainingJobData);

                // Create training job
                await fetch('/api/rl-training/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(trainingJobData)
                });

                // Close modal
                this.showAddTrainingModal = false;

                // Show success notification
                if (this.currentTrainingJob.recurring_finetune) {
                    this.showNotification('Recurring finetuning job scheduled successfully', 'success');
                } else {
                    this.showNotification('Training job created successfully', 'success');
                }

                // Refresh training jobs
                await this.refreshTrainingJobs();

                // Switch to appropriate tab based on job type
                if (this.currentTrainingJob.recurring_finetune) {
                    // For recurring jobs, switch to pending tab
                    this.changeTab('pending');
                } else {
                    // For regular jobs, switch to running tab
                    this.changeTab('running');
                }
            } catch (error) {
                console.error('Error creating training job:', error);
                this.showNotification('Failed to create training job: ' + error.message, 'error');
            }
        },

        // Cancel a training job
        async cancelTrainingJob(job) {
            try {
                if (confirm(`Are you sure you want to cancel the training job for ${job.environment_name}?`)) {
                    await fetch(`/api/rl-training/${job._id}/cancel`, {
                        method: 'POST'
                    });
                    this.showNotification('Training job cancelled successfully', 'success');
                    await this.refreshTrainingJobs();
                }
            } catch (error) {
                console.error('Error cancelling training job:', error);
                this.showNotification('Failed to cancel training job: ' + error.message, 'error');
            }
        },

        // Delete a training job
        async deleteTrainingJob(job) {
            try {
                if (confirm(`Are you sure you want to delete the training job for ${job.environment_name}?`)) {
                    await fetch(`/api/rl-training/${job._id}`, {
                        method: 'DELETE'
                    });
                    this.showNotification('Training job deleted successfully', 'success');
                    await this.refreshTrainingJobs();
                }
            } catch (error) {
                console.error('Error deleting training job:', error);
                this.showNotification('Failed to delete training job: ' + error.message, 'error');
            }
        },

        // Delete all training jobs in the current tab
        async deleteAllJobs() {
            try {
                // Get the status based on the active tab
                const status = this.activeTab !== 'all' ? this.activeTab : null;

                // Confirmation message
                const statusText = status ? `with status "${status}"` : '';
                const confirmMessage = `Are you sure you want to delete ALL training jobs ${statusText}? This action cannot be undone.`;

                if (confirm(confirmMessage)) {
                    // Call the API to delete all jobs
                    const result = await RLTrainingAPI.deleteAllTrainingJobs(status);

                    // Show success notification
                    this.showNotification(result.message || 'All jobs deleted successfully', 'success');

                    // Refresh the jobs list
                    await this.refreshTrainingJobs();
                }
            } catch (error) {
                console.error('Error deleting all training jobs:', error);
                this.showNotification('Failed to delete all training jobs: ' + error.message, 'error');
            }
        },

        // View training results
        async viewTrainingResults(job) {
            try {
                this.selectedJob = job;
                const response = await fetch(`/api/rl-training/${job._id}/results`);
                this.trainingResults = await response.json();

                // Ensure finetuning_history is an array
                if (this.trainingResults && this.trainingResults.finetuning_history === null) {
                    this.trainingResults.finetuning_history = [];
                }

                this.showResultsModal = true;
            } catch (error) {
                console.error('Error fetching training results:', error);
                this.showNotification('Failed to fetch training results: ' + error.message, 'error');
            }
        },

        // Close results modal
        closeResultsModal() {
            this.showResultsModal = false;
            this.selectedJob = null;
            this.trainingResults = null;
        },

        /**
         * Get the best hyperparameters from an optimization group
         * @param {string} groupId - Optimization group ID
         */
        async getBestHyperparameters(groupId) {
            try {
                this.showNotification('Fetching best hyperparameters...', 'info');

                // Get the best hyperparameters
                const result = await RLTrainingAPI.getBestHyperparameters(groupId);

                if (!result.best_hyperparameters) {
                    this.showNotification('No completed jobs found in this optimization group yet.', 'info');
                    return;
                }

                // Create a formatted message
                const message = `
                    <div class="space-y-3">
                        <div class="font-medium text-purple-700 dark:text-purple-300">Best Hyperparameters Found!</div>
                        <div class="space-y-1">
                            <div class="flex justify-between">
                                <span class="font-medium">Mean Reward:</span>
                                <span>${result.best_mean_reward ? result.best_mean_reward.toFixed(4) : 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Std Reward:</span>
                                <span>${result.std_reward ? result.std_reward.toFixed(4) : 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Completed Jobs:</span>
                                <span>${result.completed_jobs}/${result.total_jobs}</span>
                            </div>
                            ${result.is_smart_optimization ?
                                `<div class="flex justify-between">
                                    <span class="font-medium">Optimization Strategy:</span>
                                    <span>${result.optimization_strategy || 'smart'}</span>
                                </div>` : ''}
                        </div>
                        <div class="mt-2">
                            <div class="font-medium mb-1">Hyperparameters:</div>
                            <pre class="bg-white dark:bg-gray-800 p-2 rounded text-xs overflow-auto max-h-40">${JSON.stringify(result.best_hyperparameters, null, 2)}</pre>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                            Job ID: ${result.best_job_id}
                        </div>
                    </div>
                `;

                // Show the result in a modal or alert
                Swal.fire({
                    title: 'Best Hyperparameters',
                    html: message,
                    icon: 'success',
                    confirmButtonText: 'Copy to Clipboard',
                    showCancelButton: true,
                    cancelButtonText: 'Close',
                    customClass: {
                        container: 'swal-wide',
                        popup: 'dark:bg-gray-800 dark:text-white',
                        title: 'dark:text-white',
                        htmlContainer: 'dark:text-gray-300',
                        confirmButton: 'bg-purple-600 hover:bg-purple-700',
                        cancelButton: 'dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Copy hyperparameters to clipboard
                        const hyperparamsStr = JSON.stringify(result.best_hyperparameters, null, 2);
                        navigator.clipboard.writeText(hyperparamsStr).then(() => {
                            this.showNotification('Hyperparameters copied to clipboard!', 'success');
                        }).catch(err => {
                            console.error('Could not copy text: ', err);
                            this.showNotification('Failed to copy to clipboard', 'error');
                        });
                    }
                });

            } catch (error) {
                console.error('Error getting best hyperparameters:', error);
                this.showNotification('Failed to get best hyperparameters: ' + error.message, 'error');
            }
        },

        // Format date
        formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString();
        },

        // Format mean reward
        formatMeanReward(reward) {
            if (reward === undefined || reward === null) return 'N/A';
            return reward.toFixed(4);  // Use 4 decimal places for more precision
        },

        // Format standard deviation
        formatStdReward(reward) {
            if (reward === undefined || reward === null) return 'N/A';
            // Even if the value is 0, we should display it as 0.0000 rather than N/A
            return reward.toFixed(4);  // Use 4 decimal places for more precision
        },

        // Format duration
        formatDuration(seconds) {
            if (seconds === undefined || seconds === null) return 'N/A';

            // If less than a minute, show seconds
            if (seconds < 60) {
                return `${seconds.toFixed(1)}s`;
            }

            // If less than an hour, show minutes and seconds
            if (seconds < 3600) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
            }

            // Otherwise, show hours, minutes, and seconds
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;
            return `${hours}h ${minutes}m ${remainingSeconds.toFixed(0)}s`;
        },

        // Format model ID
        formatModelId(id) {
            if (!id) return 'N/A';
            return id.substring(0, 8) + '...';
        },

        // Run the scheduler to check for scheduled jobs
        async runScheduler() {
            try {
                const response = await fetch('/api/rl-training/scheduler/run');
                const data = await response.json();

                if (data.jobs && data.jobs.length > 0) {
                    this.showNotification(`Started ${data.jobs.length} scheduled jobs`, 'success');
                    // Refresh the jobs list
                    await this.refreshTrainingJobs();
                    // Switch to running tab
                    this.changeTab('running');
                } else {
                    this.showNotification(data.message || 'No scheduled jobs to run at this time', 'info');
                }
            } catch (error) {
                console.error('Error running scheduler:', error);
                this.showNotification('Failed to run scheduler: ' + error.message, 'error');
            }
        },

        // Force run a scheduled job immediately
        async forceRunScheduledJob(job) {
            try {
                if (!confirm(`Are you sure you want to run the scheduled job for ${job.environment_name} now?`)) {
                    return;
                }

                const response = await fetch(`/api/rl-training/jobs/${job._id}/force-run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                this.showNotification(data.message || 'Scheduled job started successfully', 'success');

                // Refresh the jobs list
                await this.refreshTrainingJobs();

                // Switch to running tab
                this.changeTab('running');
            } catch (error) {
                console.error('Error force running scheduled job:', error);
                this.showNotification('Failed to force run scheduled job: ' + error.message, 'error');
            }
        },

        // Edit a scheduled job
        async editScheduledJob(job) {
            try {
                // Load completed jobs for model selection
                await this.loadCompletedJobs();

                // Initialize edit schedule data with all job properties
                this.editScheduleData = {
                    job_id: job._id,
                    environment_id: job.environment_id,
                    algorithm: job.algorithm || 'PPO',
                    timesteps: job.timesteps || 10000,
                    training_data_path: job.training_data_path || 'software/rl/training/data/sample_training.json',
                    train_ratio: job.train_ratio || 0.8,
                    model_to_finetune_id: job.model_to_finetune_id || '',
                    schedule_time: job.schedule?.time || '12:00',
                    save_only_if_improved: job.save_only_if_improved !== false, // Default to true if not specified
                    hyperparameters: job.hyperparameters ? JSON.parse(JSON.stringify(job.hyperparameters)) :
                                    JSON.parse(JSON.stringify(this.hyperparametersTemplate.PPO)),
                    description: job.description || ''
                };

                // We don't need to load the schema anymore since we've moved this functionality
                // to the RL Environments page
                this.currentEnvironmentSchema = { schema: {} };
                this.isLoadingSchema = false;

                // Show edit schedule modal
                this.showEditScheduleModal = true;

                // Initialize training data selector and hyperparameters optimizer after modal is shown
                this.$nextTick(() => {
                    const trainingDataSelector = document.getElementById('edit-training-data-selector');
                    if (trainingDataSelector) {
                        TrainingDataUI.initSelector(trainingDataSelector, (path) => {
                            this.editScheduleData.training_data_path = path;
                        }, this.editScheduleData.training_data_path);
                    }

                    // Initialize hyperparameters optimizer with depth options
                    const hyperparametersContainer = document.getElementById('edit-hyperparameters-optimizer');
                    if (hyperparametersContainer) {
                        HyperparametersOptimizer.init(
                            'edit-hyperparameters-optimizer',
                            this.editScheduleData.hyperparameters,
                            (updatedHyperparameters) => {
                                this.editScheduleData.hyperparameters = updatedHyperparameters;
                            }
                        );
                    }
                });
            } catch (error) {
                console.error('Error preparing edit form:', error);
                this.showNotification('Failed to prepare edit form: ' + error.message, 'error');
            }
        },

        // Handle environment change in edit modal
        async onEditEnvironmentChange() {
            // We don't need to load the schema anymore since we've moved this functionality
            // to the RL Environments page
            this.currentEnvironmentSchema = { schema: {} };
            this.isLoadingSchema = false;
        },

        // Close edit schedule modal
        closeEditScheduleModal() {
            this.showEditScheduleModal = false;
        },

        // Save schedule changes
        async saveScheduleChanges() {
            try {
                // Validate inputs
                if (!this.editScheduleData.model_to_finetune_id) {
                    this.showNotification('Please select a model to finetune', 'error');
                    return;
                }

                if (!this.editScheduleData.schedule_time) {
                    this.showNotification('Please select a time for recurring finetuning', 'error');
                    return;
                }

                // Prepare update data
                const updateData = {
                    // Schedule settings
                    schedule_time: this.editScheduleData.schedule_time,
                    save_only_if_improved: this.editScheduleData.save_only_if_improved,

                    // Basic settings
                    timesteps: this.editScheduleData.timesteps,
                    training_data_path: this.editScheduleData.training_data_path,
                    train_ratio: this.editScheduleData.train_ratio,

                    // Model to finetune
                    model_to_finetune_id: this.editScheduleData.model_to_finetune_id,

                    // Description
                    description: this.editScheduleData.description
                };

                // Handle hyperparameters optimization if enabled
                if (this.editScheduleData.hyperparameters && this.editScheduleData.hyperparameters.enabled) {
                    // Check if we have too many combinations
                    let totalCombinations = 1;
                    const parameters = this.editScheduleData.hyperparameters.parameters;

                    Object.keys(parameters).forEach(param => {
                        totalCombinations *= parameters[param].length;
                    });

                    if (totalCombinations > 20) {
                        if (!confirm(`This will create ${totalCombinations} training jobs. Are you sure you want to continue?`)) {
                            return;
                        }
                    }

                    // Add optimization flag to the request
                    updateData.hyperparameters_optimization = {
                        enabled: true,
                        parameters: this.editScheduleData.hyperparameters.parameters
                    };

                    // Set hyperparameters to the default values for the first job
                    updateData.hyperparameters = {};
                    Object.keys(parameters).forEach(param => {
                        updateData.hyperparameters[param] = parameters[param][0];
                    });
                } else {
                    // Regular hyperparameters
                    updateData.hyperparameters = this.editScheduleData.hyperparameters;
                }

                const response = await fetch(`/api/rl-training/jobs/${this.editScheduleData.job_id}/schedule`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();
                this.showNotification(data.message || 'Job updated successfully', 'success');

                // Close modal
                this.closeEditScheduleModal();

                // Refresh the jobs list
                await this.refreshTrainingJobs();
            } catch (error) {
                console.error('Error updating scheduled job:', error);
                this.showNotification('Failed to update job: ' + error.message, 'error');
            }
        },

        // Format duration
        formatDuration(seconds) {
            if (!seconds) return 'N/A';

            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = Math.floor(seconds % 60);

            let result = '';
            if (hours > 0) result += `${hours}h `;
            if (minutes > 0 || hours > 0) result += `${minutes}m `;
            result += `${remainingSeconds}s`;

            return result;
        },

        // Show notification
        showNotification(message, type = 'info') {
            // Clear any existing timeout
            if (this.notification.timeout) {
                clearTimeout(this.notification.timeout);
            }

            // Set notification
            this.notification = {
                show: true,
                message,
                type,
                timeout: setTimeout(() => {
                    this.notification.show = false;
                }, 5000)
            };
        },

        // Get status badge color
        getStatusColor(status) {
            switch (status) {
                case 'running':
                    return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
                case 'completed':
                    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
                case 'pending':
                    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
                case 'cancelled':
                    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
                case 'failed':
                    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
                default:
                    return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
            }
        }
    };
}

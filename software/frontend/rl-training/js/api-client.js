/**
 * API client for RL Training
 */
const RLTrainingAPI = {
    /**
     * Get all training jobs
     * @param {string} environmentId Optional environment ID to filter by
     * @param {string} status Optional status to filter by
     * @returns {Promise<Array>} List of training jobs
     */
    async getTrainingJobs(environmentId = null, status = null) {
        try {
            let url = '/api/rl-training/';
            const params = new URLSearchParams();

            if (environmentId) {
                params.append('environment_id', environmentId);
            }

            if (status) {
                params.append('status', status);
            }

            if (params.toString()) {
                url += '?' + params.toString();
            }

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Failed to fetch training jobs: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching training jobs:', error);
            throw error;
        }
    },

    /**
     * Get a specific training job by ID
     * @param {string} id Training job ID
     * @returns {Promise<Object>} Training job details
     */
    async getTrainingJob(id) {
        try {
            const response = await fetch(`/api/rl-training/${id}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch training job: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching training job ${id}:`, error);
            throw error;
        }
    },

    /**
     * Create a new training job
     * @param {Object} trainingData Training job data
     * @returns {Promise<Object>} Created training job
     */
    async createTrainingJob(trainingData) {
        try {
            const response = await fetch('/api/rl-training/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trainingData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to create training job: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error creating training job:', error);
            throw error;
        }
    },

    /**
     * Update an existing training job
     * @param {string} id Training job ID
     * @param {Object} updateData Updated training job data
     * @returns {Promise<Object>} Updated training job
     */
    async updateTrainingJob(id, updateData) {
        try {
            const response = await fetch(`/api/rl-training/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to update training job: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error updating training job ${id}:`, error);
            throw error;
        }
    },

    /**
     * Delete a training job
     * @param {string} id Training job ID
     * @returns {Promise<Object>} Response message
     */
    async deleteTrainingJob(id) {
        try {
            const response = await fetch(`/api/rl-training/${id}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to delete training job: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error deleting training job ${id}:`, error);
            throw error;
        }
    },

    /**
     * Cancel a running training job
     * @param {string} id Training job ID
     * @returns {Promise<Object>} Response message
     */
    async cancelTrainingJob(id) {
        try {
            const response = await fetch(`/api/rl-training/${id}/cancel`, {
                method: 'POST'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to cancel training job: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error canceling training job ${id}:`, error);
            throw error;
        }
    },

    /**
     * Get training results for a specific job
     * @param {string} id Training job ID
     * @returns {Promise<Object>} Training results
     */
    async getTrainingResults(id) {
        try {
            const response = await fetch(`/api/rl-training/${id}/results`);
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to fetch training results: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching training results for job ${id}:`, error);
            throw error;
        }
    },

    /**
     * Get list of available training data files
     * @returns {Promise<Array>} List of training data files
     */
    async getTrainingDataFiles() {
        try {
            console.log('Fetching training data files...');
            const response = await fetch('/api/rl-training/data-files');
            if (!response.ok) {
                const errorData = await response.json();
                console.error('Error response:', errorData);
                throw new Error(errorData.detail || `Failed to fetch training data files: ${response.statusText}`);
            }
            const data = await response.json();
            console.log('Training data files:', data);
            return data;
        } catch (error) {
            console.error('Error fetching training data files:', error);
            // Return empty array as fallback
            return [];
        }
    },

    /**
     * Delete all training jobs with the specified status
     * @param {string} status Optional status to filter jobs to delete (all, running, completed, pending, cancelled)
     * @returns {Promise<Object>} Response message
     */
    async deleteAllTrainingJobs(status = null) {
        try {
            let url = '/api/rl-training/jobs/delete-all';
            if (status && status !== 'all') {
                url += `?status=${status}`;
            }

            const response = await fetch(url, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to delete all training jobs: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error deleting all training jobs:`, error);
            throw error;
        }
    },

    /**
     * Get the best hyperparameters from an optimization group
     * @param {string} groupId - Optimization group ID
     * @returns {Promise<Object>} - Best hyperparameters and related information
     */
    async getBestHyperparameters(groupId) {
        try {
            const response = await fetch(`/api/rl-training/optimization-group/${groupId}/best`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to get best hyperparameters');
            }

            return await response.json();
        } catch (error) {
            console.error('Error getting best hyperparameters:', error);
            throw error;
        }
    },

    /**
     * Get list of available MongoDB collections
     * @returns {Promise<Array>} List of MongoDB collections
     */
    async getMongoDBCollections() {
        try {
            console.log('Fetching MongoDB collections...');
            const response = await fetch('/api/rl-training/mongodb-collections');
            if (!response.ok) {
                const errorData = await response.json();
                console.error('Error response:', errorData);
                throw new Error(errorData.detail || `Failed to fetch MongoDB collections: ${response.statusText}`);
            }
            const data = await response.json();
            console.log('MongoDB collections:', data);
            return data;
        } catch (error) {
            console.error('Error fetching MongoDB collections:', error);
            // Return empty array as fallback
            return [];
        }
    },

    /**
     * Get a sample document from a MongoDB collection
     * @param {string} collectionName - Name of the collection
     * @returns {Promise<Object>} Sample document
     */
    async getMongoDBSampleDocument(collectionName) {
        try {
            console.log(`Fetching sample document from ${collectionName}...`);
            const response = await fetch(`/api/rl-training/mongodb-collections/${collectionName}/sample`);
            if (!response.ok) {
                const errorData = await response.json();
                console.error('Error response:', errorData);
                throw new Error(errorData.detail || `Failed to fetch sample document: ${response.statusText}`);
            }
            const data = await response.json();
            console.log('Sample document:', data);
            return data;
        } catch (error) {
            console.error(`Error fetching sample document from ${collectionName}:`, error);
            return null;
        }
    }
};

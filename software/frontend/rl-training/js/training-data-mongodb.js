/**
 * Training Data MongoDB Collection UI Component
 * <PERSON>les selecting a MongoDB collection for training data
 */

const TrainingDataMongoDBUI = {
    /**
     * Fetch available MongoDB collections from the API
     * @returns {Promise<Array>} Array of collection objects
     */
    async fetchCollections() {
        try {
            console.log('TrainingDataMongoDBUI: Fetching MongoDB collections...');
            const collections = await RLTrainingAPI.getMongoDBCollections();
            console.log('TrainingDataMongoDBUI: Received collections:', collections);
            return collections || [];
        } catch (error) {
            console.error('TrainingDataMongoDBUI: Error fetching MongoDB collections:', error);
            return [];
        }
    },

    /**
     * Initialize the MongoDB collection selector
     * @param {HTMLElement} container Container element for the selector
     * @param {function} onSelect Callback function when a collection is selected
     * @param {string} defaultCollection Default collection to select
     */
    async init(container, onSelect, defaultCollection = null) {
        // Clear container
        container.innerHTML = '';

        // Create collection selection container
        const collectionContainer = document.createElement('div');
        collectionContainer.className = 'space-y-3';

        // Create collection dropdown
        const collectionSelectContainer = document.createElement('div');
        collectionSelectContainer.className = 'flex space-x-2';

        const collectionSelect = document.createElement('select');
        collectionSelect.className = 'block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white';
        
        // Add loading option
        const loadingOption = document.createElement('option');
        loadingOption.value = '';
        loadingOption.textContent = 'Loading collections...';
        collectionSelect.appendChild(loadingOption);

        // Add refresh button
        const refreshButton = document.createElement('button');
        refreshButton.type = 'button';
        refreshButton.className = 'inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600';
        refreshButton.innerHTML = `
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
        `;
        
        // Add collection select and refresh button to container
        collectionSelectContainer.appendChild(collectionSelect);
        collectionSelectContainer.appendChild(refreshButton);
        collectionContainer.appendChild(collectionSelectContainer);

        // Create field selection container
        const fieldContainer = document.createElement('div');
        fieldContainer.className = 'space-y-2';
        fieldContainer.style.display = 'none'; // Hide initially

        // Create field selection title
        const fieldTitle = document.createElement('div');
        fieldTitle.className = 'text-sm font-medium text-gray-700 dark:text-gray-300';
        fieldTitle.textContent = 'Select Fields';
        fieldContainer.appendChild(fieldTitle);

        // Create field selection inputs
        const fieldSelectionContainer = document.createElement('div');
        fieldSelectionContainer.className = 'grid grid-cols-1 gap-2';
        fieldContainer.appendChild(fieldSelectionContainer);

        // Add field container to main container
        collectionContainer.appendChild(fieldContainer);

        // Add collection container to main container
        container.appendChild(collectionContainer);

        // Fetch collections
        const collections = await this.fetchCollections();
        
        // Clear select and add collections
        collectionSelect.innerHTML = '';
        
        if (collections.length === 0) {
            const noCollectionsOption = document.createElement('option');
            noCollectionsOption.value = '';
            noCollectionsOption.textContent = 'No collections available';
            collectionSelect.appendChild(noCollectionsOption);
        } else {
            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Select a collection';
            collectionSelect.appendChild(defaultOption);
            
            // Add collection options
            collections.forEach(collection => {
                const option = document.createElement('option');
                option.value = collection.name;
                option.textContent = `${collection.name} (${collection.count} documents)`;
                collectionSelect.appendChild(option);
                
                // Select default collection if provided
                if (defaultCollection && collection.name === defaultCollection) {
                    option.selected = true;
                }
            });
        }

        // Add event listener for collection selection
        collectionSelect.addEventListener('change', async () => {
            const selectedCollection = collectionSelect.value;
            
            if (!selectedCollection) {
                fieldContainer.style.display = 'none';
                return;
            }
            
            // Show field selection
            fieldContainer.style.display = 'block';
            
            // Fetch sample document to get fields
            try {
                const sampleDocument = await RLTrainingAPI.getMongoDBSampleDocument(selectedCollection);
                
                // Create field selection inputs
                fieldSelectionContainer.innerHTML = '';
                
                if (!sampleDocument) {
                    const noFieldsMessage = document.createElement('div');
                    noFieldsMessage.className = 'text-gray-500 dark:text-gray-400 text-sm italic';
                    noFieldsMessage.textContent = 'No sample document available';
                    fieldSelectionContainer.appendChild(noFieldsMessage);
                    return;
                }
                
                // Create field inputs
                const fields = Object.keys(sampleDocument);
                
                // Create vector field input
                const vectorFieldContainer = document.createElement('div');
                vectorFieldContainer.className = 'flex items-center space-x-2';
                
                const vectorFieldLabel = document.createElement('label');
                vectorFieldLabel.className = 'text-sm text-gray-600 dark:text-gray-400 w-24';
                vectorFieldLabel.textContent = 'Vector Field:';
                
                const vectorFieldSelect = document.createElement('select');
                vectorFieldSelect.className = 'block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white';
                vectorFieldSelect.id = 'mongodb-vector-field';
                
                // Add field options
                fields.forEach(field => {
                    const option = document.createElement('option');
                    option.value = field;
                    option.textContent = field;
                    
                    // Auto-select vector field if found
                    if (field === 'vector' || field === 'embedding' || field === 'embeddings') {
                        option.selected = true;
                    }
                    
                    vectorFieldSelect.appendChild(option);
                });
                
                vectorFieldContainer.appendChild(vectorFieldLabel);
                vectorFieldContainer.appendChild(vectorFieldSelect);
                fieldSelectionContainer.appendChild(vectorFieldContainer);
                
                // Create label field input
                const labelFieldContainer = document.createElement('div');
                labelFieldContainer.className = 'flex items-center space-x-2';
                
                const labelFieldLabel = document.createElement('label');
                labelFieldLabel.className = 'text-sm text-gray-600 dark:text-gray-400 w-24';
                labelFieldLabel.textContent = 'Label Field:';
                
                const labelFieldSelect = document.createElement('select');
                labelFieldSelect.className = 'block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white';
                labelFieldSelect.id = 'mongodb-label-field';
                
                // Add field options
                fields.forEach(field => {
                    const option = document.createElement('option');
                    option.value = field;
                    option.textContent = field;
                    
                    // Auto-select label field if found
                    if (field === 'label' || field === 'score' || field === 'quality_score' || field === 'reward') {
                        option.selected = true;
                    }
                    
                    labelFieldSelect.appendChild(option);
                });
                
                labelFieldContainer.appendChild(labelFieldLabel);
                labelFieldContainer.appendChild(labelFieldSelect);
                fieldSelectionContainer.appendChild(labelFieldContainer);
                
                // Update training data path with MongoDB URI
                const mongoDBPath = `mongodb://${selectedCollection}?vector_field=${vectorFieldSelect.value}&label_field=${labelFieldSelect.value}`;
                
                if (typeof onSelect === 'function') {
                    onSelect(mongoDBPath);
                }
                
                // Add event listeners for field selection changes
                vectorFieldSelect.addEventListener('change', () => {
                    const mongoDBPath = `mongodb://${selectedCollection}?vector_field=${vectorFieldSelect.value}&label_field=${labelFieldSelect.value}`;
                    if (typeof onSelect === 'function') {
                        onSelect(mongoDBPath);
                    }
                });
                
                labelFieldSelect.addEventListener('change', () => {
                    const mongoDBPath = `mongodb://${selectedCollection}?vector_field=${vectorFieldSelect.value}&label_field=${labelFieldSelect.value}`;
                    if (typeof onSelect === 'function') {
                        onSelect(mongoDBPath);
                    }
                });
                
            } catch (error) {
                console.error('Error fetching sample document:', error);
                fieldSelectionContainer.innerHTML = '';
                
                const errorMessage = document.createElement('div');
                errorMessage.className = 'text-red-500 dark:text-red-400 text-sm italic';
                errorMessage.textContent = 'Error fetching sample document';
                fieldSelectionContainer.appendChild(errorMessage);
            }
        });
        
        // Add event listener for refresh button
        refreshButton.addEventListener('click', async () => {
            // Show loading state
            collectionSelect.innerHTML = '';
            const loadingOption = document.createElement('option');
            loadingOption.value = '';
            loadingOption.textContent = 'Loading collections...';
            collectionSelect.appendChild(loadingOption);
            
            // Hide field selection
            fieldContainer.style.display = 'none';
            
            // Fetch collections
            const collections = await this.fetchCollections();
            
            // Clear select and add collections
            collectionSelect.innerHTML = '';
            
            if (collections.length === 0) {
                const noCollectionsOption = document.createElement('option');
                noCollectionsOption.value = '';
                noCollectionsOption.textContent = 'No collections available';
                collectionSelect.appendChild(noCollectionsOption);
            } else {
                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Select a collection';
                collectionSelect.appendChild(defaultOption);
                
                // Add collection options
                collections.forEach(collection => {
                    const option = document.createElement('option');
                    option.value = collection.name;
                    option.textContent = `${collection.name} (${collection.count} documents)`;
                    collectionSelect.appendChild(option);
                });
            }
        });
    }
};

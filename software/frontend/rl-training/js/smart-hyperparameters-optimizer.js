/**
 * Smart Hyperparameters Optimizer for RL Training
 *
 * This component provides a simplified interface that always uses
 * Bayesian optimization for hyperparameter tuning and produces a single optimized job.
 */

const SmartHyperparametersOptimizer = {
    // Default hyperparameters for PPO algorithm
    defaultHyperparameters: {
        PPO: {
            learning_rate: 0.0003,
            n_steps: 2048,
            batch_size: 64,
            n_epochs: 10,
            gamma: 0.99,
            gae_lambda: 0.95,
            clip_range: 0.2,
            ent_coef: 0.0,
            vf_coef: 0.5,
            max_grad_norm: 0.5
        }
    },

    /**
     * Initialize the smart hyperparameters optimizer component
     * @param {string} containerId - ID of the container element
     * @param {Object} hyperparameters - Current hyperparameters
     * @param {Function} onUpdate - Callback function when hyperparameters are updated
     */
    init(containerId, hyperparameters, onUpdate) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container element with ID '${containerId}' not found`);
            return;
        }

        // Store references
        this.container = container;
        this.hyperparameters = hyperparameters || JSON.parse(JSON.stringify(this.defaultHyperparameters.PPO));
        this.onUpdate = onUpdate;

        // Render the component
        this.render();

        // Immediately trigger the Bayesian optimization
        this.updateHyperparameters();
    },

    /**
     * Render the smart hyperparameters optimizer component
     */
    render() {
        // Clear the container
        this.container.innerHTML = '';

        // Create the main container
        const optimizerContainer = document.createElement('div');
        optimizerContainer.className = 'smart-hyperparameters-optimizer bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4';

        // Add header with icon
        const header = document.createElement('div');
        header.className = 'flex items-center mb-2';

        const icon = document.createElement('svg');
        icon.className = 'w-5 h-5 mr-2 text-purple-600 dark:text-purple-400';
        icon.setAttribute('fill', 'none');
        icon.setAttribute('stroke', 'currentColor');
        icon.setAttribute('viewBox', '0 0 24 24');
        icon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        `;

        const title = document.createElement('h3');
        title.className = 'text-sm font-medium text-gray-700 dark:text-gray-300';
        title.textContent = 'Smart Bayesian Hyperparameter Optimization';

        header.appendChild(icon);
        header.appendChild(title);
        optimizerContainer.appendChild(header);

        // Add info box
        const infoBox = document.createElement('div');
        infoBox.className = 'p-3 bg-blue-50 dark:bg-blue-900 rounded-md';

        const infoText = document.createElement('p');
        infoText.className = 'text-xs text-blue-700 dark:text-blue-300';
        infoText.innerHTML = `
            <span class="font-medium">Bayesian optimization</span> is automatically enabled for this training job.
            The system will internally test multiple hyperparameter combinations and produce a single optimized model.
            <ul class="mt-2 list-disc list-inside">
                <li>Intelligently explores the parameter space</li>
                <li>Focuses on promising regions</li>
                <li>Produces a single optimized model</li>
                <li>Perfect for recurring finetuning</li>
            </ul>
        `;

        infoBox.appendChild(infoText);
        optimizerContainer.appendChild(infoBox);

        // Add the container to the DOM
        this.container.appendChild(optimizerContainer);
    },

    /**
     * Update hyperparameters and call the onUpdate callback
     */
    updateHyperparameters() {
        if (typeof this.onUpdate === 'function') {
            // Always enable Bayesian optimization with predefined parameter ranges
            const optimizationConfig = {
                enabled: true,
                parameters: {
                    learning_rate: [1e-05, 0.0001, 0.0003, 0.001, 0.003],
                    n_steps: [64, 128, 512, 1024, 2048],
                    batch_size: [32, 64, 128, 256],
                    n_epochs: [5, 10, 15, 20],
                    gamma: [0.95, 0.97, 0.99],
                    gae_lambda: [0.9, 0.95, 0.98],
                    clip_range: [0.1, 0.2, 0.3],
                    ent_coef: [0, 0.01, 0.05],
                    vf_coef: [0.5, 0.7, 1.0],
                    max_grad_norm: [0.5, 1.0, 2.0]
                },
                max_trials: 1,  // Only produce one optimized job at the end
                strategy: "bayesian",
                single_optimized_job: true  // Flag to indicate we want a single optimized job
            };

            console.log('DEBUG - Smart Bayesian optimization enabled by default');
            console.log('DEBUG - Smart hyperparameters optimization config:', optimizationConfig);

            this.onUpdate({
                smart_hyperparameters_optimization: optimizationConfig
            });
        }
    }
};

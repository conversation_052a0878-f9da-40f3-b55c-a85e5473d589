{% extends "base.html" %}

{% block title %}RL Training{% endblock %}

{% block header %}RL Training{% endblock %}

{% block content %}
<div x-data="rlTrainingApp()" x-init="init()">
    <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Reinforcement Learning Training</h2>
            <div class="flex space-x-2">
                <button onclick="window.location.reload(true)"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Hard Refresh
                </button>
                <button @click="runScheduler()"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Run Scheduler
                </button>
                <button @click="openAddTrainingModal()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Add Training Job
                </button>
            </div>
        </div>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
            Train and manage reinforcement learning models using Stable Baselines3.
        </p>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200 dark:border-gray-700 mb-4">
        <div class="flex justify-between items-center">
            <nav class="flex space-x-4" aria-label="Tabs">
                <button @click="changeTab('all')"
                        :class="activeTab === 'all' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                        class="py-2 px-3 text-sm font-medium border-b-2 transition-colors">
                    All Jobs
                </button>
                <button @click="changeTab('running')"
                        :class="activeTab === 'running' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                        class="py-2 px-3 text-sm font-medium border-b-2 transition-colors">
                    Running
                </button>
                <button @click="changeTab('completed')"
                        :class="activeTab === 'completed' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                        class="py-2 px-3 text-sm font-medium border-b-2 transition-colors">
                    Completed
                </button>
                <button @click="changeTab('pending')"
                        :class="activeTab === 'pending' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                        class="py-2 px-3 text-sm font-medium border-b-2 transition-colors">
                    Pending
                </button>
                <button @click="changeTab('cancelled')"
                        :class="activeTab === 'cancelled' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                        class="py-2 px-3 text-sm font-medium border-b-2 transition-colors">
                    Cancelled
                </button>
            </nav>

            <!-- Delete All Button -->
            <button x-show="!isLoading && trainingJobs.length > 0"
                    @click="deleteAllJobs()"
                    class="py-1 px-3 text-sm font-medium text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 border border-red-300 dark:border-red-700 rounded-md hover:bg-red-50 dark:hover:bg-red-900 transition-colors flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete All <span x-text="activeTab !== 'all' ? activeTab : ''"></span>
            </button>
        </div>
    </div>

    <!-- Training Jobs List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <!-- Loading State -->
        <div x-show="isLoading" class="p-8 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Loading training jobs...</p>
        </div>

        <!-- Empty State -->
        <div x-show="!isLoading && trainingJobs.length === 0" class="p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            <p class="mt-2 text-gray-600 dark:text-gray-400">No training jobs found</p>
            <p class="text-sm text-gray-500 dark:text-gray-500">Click "Add Training Job" to start training a model</p>
        </div>

        <!-- Training Jobs Table -->
        <div x-show="!isLoading && trainingJobs.length > 0" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Environment
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Algorithm
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Progress
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Model ID
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Mean Reward
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Training Time
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Created
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                    <template x-for="job in trainingJobs" :key="job._id">
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white" x-text="job.environment_name"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white" x-text="job.algorithm"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                      :class="getStatusColor(job.status)">
                                    <span x-text="job.status"></span>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700" x-show="job.status === 'running'">
                                    <div class="bg-blue-600 h-2.5 rounded-full" :style="`width: ${job.progress || 0}%`"></div>
                                </div>
                                <span x-show="job.status !== 'running'" x-text="`${job.progress || 0}%`" class="text-sm text-gray-900 dark:text-white"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white font-mono" x-text="formatModelId(job._id)" :title="job._id"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div x-show="job.mean_reward !== undefined" class="text-sm font-medium" :class="job.mean_reward > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'" x-text="formatMeanReward(job.mean_reward)"></div>
                                <div x-show="job.mean_reward === undefined" class="text-sm text-gray-500 dark:text-gray-400">N/A</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="formatDuration(job.training_time)"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(job.created_at)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <button @click="viewTrainingResults(job)" x-show="job.status === 'completed' || job.status === 'scheduled'"
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                    Results
                                </button>
                                <button @click="cancelTrainingJob(job)" x-show="job.status === 'running'"
                                        class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300">
                                    Cancel
                                </button>
                                <button @click="forceRunScheduledJob(job)" x-show="job.status === 'scheduled'"
                                        class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                                    Force Run
                                </button>
                                <button @click="editScheduledJob(job)" x-show="job.status === 'scheduled'"
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                    Edit
                                </button>
                                <button @click="deleteTrainingJob(job)"
                                        class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add Training Job Modal -->
    <div x-show="showAddTrainingModal"
         class="fixed inset-0 z-50 overflow-y-auto"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                <div class="bg-gradient-to-r from-indigo-600 to-blue-500 px-4 py-3 sm:px-6">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Add New Training Job
                        </h3>
                        <button type="button" @click="closeAddTrainingModal()" class="text-white hover:text-gray-200 focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-[70vh] overflow-y-auto">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="mt-4 space-y-4">
                                <div>
                                    <label for="environment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Environment</label>
                                    <select id="environment" x-model="currentTrainingJob.environment_id" @change="onEnvironmentChange()"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="" disabled>Select an environment</option>
                                        <template x-for="env in environments" :key="env._id">
                                            <option :value="env._id" x-text="env.name"></option>
                                        </template>
                                    </select>

                                    <!-- Environment information link -->
                                    <div class="mt-3 bg-gray-50 dark:bg-gray-700 p-4 rounded-md border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <p class="text-sm text-gray-600 dark:text-gray-300">
                                                Environment details are available in the RL Environments section
                                            </p>
                                            <a href="/rl-environments" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                                View Environments
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label for="algorithm" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Algorithm</label>
                                    <select id="algorithm" x-model="currentTrainingJob.algorithm" @change="onAlgorithmChange()"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="PPO">PPO</option>
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Only PPO is available now. More algorithms will be added in the future.</p>
                                </div>
                                <div>
                                    <label for="timesteps" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Timesteps</label>
                                    <input type="number" id="timesteps" x-model.number="currentTrainingJob.timesteps" min="1000" step="1000"
                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                </div>
                                <div>
                                    <label for="training-data-selector" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Training Data</label>
                                    <div id="training-data-selector" class="mt-1"></div>
                                </div>
                                <div>
                                    <label for="train-ratio" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Train/Test Split Ratio: <span class="font-bold" x-text="Math.round(currentTrainingJob.train_ratio * 100) + '%'"></span>
                                    </label>
                                    <input type="range" id="train-ratio" x-model.number="currentTrainingJob.train_ratio" min="0.5" max="1" step="0.05"
                                           class="mt-1 block w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <span>50/50</span>
                                        <span>75/25</span>
                                        <span>100/0</span>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400" x-text="Math.round(currentTrainingJob.train_ratio * 100) + '% training / ' + Math.round((1 - currentTrainingJob.train_ratio) * 100) + '% testing'"></p>
                                </div>
                                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                                    <input type="checkbox" id="finetune" x-model="currentTrainingJob.finetune"
                                           class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:bg-gray-600 dark:border-gray-500">
                                    <label for="finetune" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Finetune existing model
                                    </label>
                                </div>

                                <!-- Model selector (only shown when finetune is checked) -->
                                <div x-show="currentTrainingJob.finetune" x-transition:enter="transition ease-out duration-300"
                                     x-transition:enter-start="opacity-0 transform scale-95"
                                     x-transition:enter-end="opacity-100 transform scale-100">
                                    <label for="model-to-finetune" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select Model to Finetune</label>
                                    <select id="model-to-finetune" x-model="currentTrainingJob.model_to_finetune"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="" disabled>Select a model</option>
                                        <template x-for="job in completedJobs" :key="job._id">
                                            <option :value="job._id" x-text="'ID: ' + job._id.substring(0, 8) + '... | ' + job.environment_name + ' | Mean Reward: ' + (job.mean_reward ? job.mean_reward.toFixed(2) : 'N/A')"></option>
                                        </template>
                                    </select>
                                    <div x-show="currentTrainingJob.model_to_finetune" class="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Selected Model Details</h4>
                                        <template x-if="currentTrainingJob.model_to_finetune">
                                            <div class="mt-2 text-xs space-y-1">
                                                <p class="text-gray-600 dark:text-gray-400 flex justify-between">
                                                    <span class="font-medium">Model ID:</span>
                                                    <span class="text-right" x-text="currentTrainingJob.model_to_finetune"></span>
                                                </p>
                                                <p class="text-gray-600 dark:text-gray-400 flex justify-between" x-show="getSelectedModel()">
                                                    <span class="font-medium">Training Data:</span>
                                                    <span class="text-right truncate max-w-[200px]" x-text="getSelectedModel()?.training_data_path || 'N/A'" :title="getSelectedModel()?.training_data_path"></span>
                                                </p>
                                                <p class="text-gray-600 dark:text-gray-400 flex justify-between" x-show="getSelectedModel()">
                                                    <span class="font-medium">Mean Reward:</span>
                                                    <span class="text-right" x-text="getSelectedModel()?.mean_reward ? getSelectedModel().mean_reward.toFixed(2) : 'N/A'"></span>
                                                </p>
                                                <p class="text-gray-600 dark:text-gray-400 flex justify-between" x-show="getSelectedModel()">
                                                    <span class="font-medium">Completed:</span>
                                                    <span class="text-right" x-text="getSelectedModel()?.completed_at ? formatDate(getSelectedModel().completed_at) : 'N/A'"></span>
                                                </p>
                                            </div>
                                        </template>
                                    </div>

                                    <!-- Recurring Finetuning Option -->
                                    <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-md border border-blue-200 dark:border-blue-800">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="recurring-finetune" x-model="currentTrainingJob.recurring_finetune"
                                                       class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-600 dark:border-gray-500">
                                                <label for="recurring-finetune" class="ml-2 block text-sm font-medium text-blue-700 dark:text-blue-300">
                                                    Enable Recurring Finetuning
                                                </label>
                                            </div>
                                            <span class="text-xs text-blue-600 dark:text-blue-400 font-medium">Daily Schedule</span>
                                        </div>

                                        <div x-show="currentTrainingJob.recurring_finetune" class="mt-3 space-y-3"
                                             x-transition:enter="transition ease-out duration-300"
                                             x-transition:enter-start="opacity-0 transform scale-95"
                                             x-transition:enter-end="opacity-100 transform scale-100">

                                            <div>
                                                <label for="schedule-time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Daily Execution Time</label>
                                                <input type="time" id="schedule-time" x-model="currentTrainingJob.schedule_time"
                                                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                            </div>

                                            <div class="bg-white dark:bg-gray-800 p-3 rounded-md border border-gray-200 dark:border-gray-600">
                                                <div class="flex items-center">
                                                    <svg class="h-5 w-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <p class="text-xs text-gray-600 dark:text-gray-400">
                                                        <span class="font-medium">Note:</span> The model will only be updated if the mean reward increases. Otherwise, the original model will be preserved.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Hyperparameters section -->
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Hyperparameters</label>
                                        <span class="text-xs text-indigo-600 dark:text-indigo-400">PPO Algorithm</span>
                                    </div>
                                    <div id="add-hyperparameters-optimizer" class="mt-2"></div>
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (optional)</label>
                                    <textarea id="description" x-model="currentTrainingJob.description" rows="3"
                                              placeholder="Enter a description for this training job..."
                                              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-200 dark:border-gray-600">
                    <button type="button" @click="createTrainingJob()"
                            class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gradient-to-r from-indigo-600 to-blue-500 text-base font-medium text-white hover:from-indigo-700 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Start Training
                    </button>
                    <button type="button" @click="closeAddTrainingModal()"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-700 transition-colors duration-200">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Results Modal -->
    <div x-show="showResultsModal"
         class="fixed inset-0 z-50 overflow-y-auto"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                <div class="bg-gradient-to-r from-green-600 to-teal-500 px-4 py-3 sm:px-6">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Training Results
                        </h3>
                        <button type="button" @click="closeResultsModal()" class="text-white hover:text-gray-200 focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-[70vh] overflow-y-auto">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div x-show="selectedJob && trainingResults" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Left column -->
                                <div class="space-y-4">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                            </svg>
                                            Model Information
                                        </h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Model ID:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="selectedJob?._id || 'N/A'"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Environment:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="selectedJob?.environment_name"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Algorithm:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="selectedJob?.algorithm"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Model Path:</span>
                                                <span class="text-sm text-gray-900 dark:text-white truncate max-w-[200px]" x-text="selectedJob?.model_path || 'N/A'" :title="selectedJob?.model_path"></span>
                                            </div>
                                            <div class="flex justify-between" x-show="trainingResults?.recurring_finetune">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Recurring Finetuning:</span>
                                                <span class="text-sm text-green-600 dark:text-green-400 font-medium">Enabled</span>
                                            </div>
                                            <div class="flex justify-between" x-show="trainingResults?.schedule">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Schedule:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="trainingResults?.schedule?.type + ' at ' + trainingResults?.schedule?.time"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                            Performance Metrics
                                        </h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Mean Reward:</span>
                                                <span class="text-sm text-gray-900 dark:text-white font-bold" x-text="formatMeanReward(trainingResults?.mean_reward)"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Standard Deviation:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="formatStdReward(trainingResults?.std_reward)"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Training Time:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="formatDuration(selectedJob?.training_time)"></span>
                                            </div>
                                            <div class="flex justify-between" x-show="trainingResults?.improved !== undefined">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Improvement:</span>
                                                <span class="text-sm font-medium" :class="trainingResults?.improved ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'" x-text="trainingResults?.improved ? 'Yes' : 'No'"></span>
                                            </div>
                                            <div class="flex justify-between" x-show="trainingResults?.original_mean_reward !== undefined">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Original Mean Reward:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="formatMeanReward(trainingResults?.original_mean_reward)"></span>
                                            </div>
                                            <div class="flex justify-between" x-show="trainingResults?.model_kept !== undefined">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Model Kept:</span>
                                                <span class="text-sm font-medium"
                                                    :class="{
                                                        'text-green-600 dark:text-green-400': trainingResults?.model_kept === 'new',
                                                        'text-yellow-600 dark:text-yellow-400': trainingResults?.model_kept === 'original',
                                                        'text-blue-600 dark:text-blue-400': trainingResults?.model_kept === 'new (original not found)'
                                                    }"
                                                    x-text="trainingResults?.model_kept === 'new' ? 'New Model' :
                                                           trainingResults?.model_kept === 'original' ? 'Original Model' :
                                                           'New Model (Original Not Found)'"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Right column -->
                                <div class="space-y-4">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                                            </svg>
                                            Training Data
                                        </h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Data Path:</span>
                                                <span class="text-sm text-gray-900 dark:text-white truncate max-w-[200px]" x-text="selectedJob?.training_data_path || 'N/A'" :title="selectedJob?.training_data_path"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Train Ratio:</span>
                                                <span class="text-sm text-gray-900 dark:text-white" x-text="selectedJob?.train_ratio ? (selectedJob.train_ratio * 100).toFixed(0) + '%' : 'N/A'"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Hyperparameters
                                        </h4>
                                        <div class="text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-800 p-3 rounded-md overflow-auto max-h-40 border border-gray-200 dark:border-gray-600">
                                            <pre x-text="JSON.stringify(selectedJob?.hyperparameters || {}, null, 2)" class="text-xs"></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Finetuning History Section -->
                            <div x-show="trainingResults?.finetuning_history && Array.isArray(trainingResults.finetuning_history) && trainingResults.finetuning_history.length > 0" class="mt-6">
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                    <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Finetuning History
                                    </h4>
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                            <thead class="bg-gray-100 dark:bg-gray-600">
                                                <tr>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Date
                                                    </th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Timesteps
                                                    </th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Mean Reward
                                                    </th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Std Dev
                                                    </th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Original Reward
                                                    </th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Improved
                                                    </th>
                                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                                        Model Kept
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                                                <template x-for="(attempt, index) in trainingResults.finetuning_history" :key="index">
                                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatDate(attempt.executed_at || attempt.created_at)"></td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="attempt.timesteps ? attempt.timesteps.toLocaleString() : 'N/A'"></td>
                                                        <td class="px-3 py-2 whitespace-nowrap">
                                                            <span class="text-sm font-medium" :class="attempt.mean_reward > (attempt.original_mean_reward || 0) ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'" x-text="formatMeanReward(attempt.mean_reward)"></span>
                                                        </td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="formatStdReward(attempt.std_reward)"></td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="attempt.original_mean_reward ? attempt.original_mean_reward.toFixed(4) : 'N/A'"></td>
                                                        <td class="px-3 py-2 whitespace-nowrap">
                                                            <span x-show="attempt.improved === true" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                                Yes
                                                            </span>
                                                            <span x-show="attempt.improved === false" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                                No
                                                            </span>
                                                            <span x-show="attempt.improved === null || attempt.improved === undefined" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                                N/A
                                                            </span>
                                                        </td>
                                                        <td class="px-3 py-2 whitespace-nowrap">
                                                            <span x-show="attempt.model_kept === 'new'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                                New
                                                            </span>
                                                            <span x-show="attempt.model_kept === 'original'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                                Original
                                                            </span>
                                                            <span x-show="attempt.model_kept === 'new (original not found)'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                                New (Original Not Found)
                                                            </span>
                                                            <span x-show="!attempt.model_kept" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                                N/A
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-200 dark:border-gray-600">
                    <!-- Show Get Best Hyperparameters button for optimization groups -->
                    <button type="button"
                            x-show="selectedJob && selectedJob.optimization_group === true"
                            @click="getBestHyperparameters(selectedJob._id)"
                            class="inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-500 text-base font-medium text-white hover:from-purple-700 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-200 mr-2">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        Get Best Hyperparameters
                    </button>

                    <button type="button" @click="closeResultsModal()"
                            class="inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Scheduled Job Modal -->
    <div x-show="showEditScheduleModal"
         class="fixed inset-0 z-50 overflow-y-auto"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                <div class="bg-gradient-to-r from-blue-600 to-indigo-500 px-4 py-3 sm:px-6">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Edit Scheduled Job
                        </h3>
                        <button type="button" @click="closeEditScheduleModal()" class="text-white hover:text-gray-200 focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-[70vh] overflow-y-auto">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="mt-4 space-y-4">
                                <!-- Schedule Settings Section -->
                                <div class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
                                    <h4 class="text-md font-medium text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Schedule Settings
                                    </h4>
                                    <div class="space-y-3">
                                        <div>
                                            <label for="edit-schedule-time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Daily Execution Time</label>
                                            <input type="time" id="edit-schedule-time" x-model="editScheduleData.schedule_time"
                                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        </div>

                                        <div class="flex items-center p-3 bg-white dark:bg-gray-700 rounded-md">
                                            <input type="checkbox" id="edit-save-only-if-improved" x-model="editScheduleData.save_only_if_improved"
                                                   class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:bg-gray-600 dark:border-gray-500">
                                            <label for="edit-save-only-if-improved" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Only save if mean reward improves
                                            </label>
                                        </div>

                                        <div class="text-sm text-blue-700 dark:text-blue-300 p-2">
                                            Job will run automatically at the scheduled time each day.
                                        </div>
                                    </div>
                                </div>

                                <!-- Basic Settings Section -->
                                <div>
                                    <label for="edit-environment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Environment</label>
                                    <select id="edit-environment" x-model="editScheduleData.environment_id" @change="onEditEnvironmentChange()"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="" disabled>Select an environment</option>
                                        <template x-for="env in environments" :key="env._id">
                                            <option :value="env._id" x-text="env.name"></option>
                                        </template>
                                    </select>

                                    <!-- Environment Schema Information -->
                                    <div x-show="isLoadingSchema" class="mt-3 flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
                                        <svg class="animate-spin h-5 w-5 text-indigo-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600 dark:text-gray-300">Loading environment schema...</span>
                                    </div>

                                    <div x-show="currentEnvironmentSchema && !isLoadingSchema" class="mt-3 bg-gray-50 dark:bg-gray-700 p-4 rounded-md border border-gray-200 dark:border-gray-600">
                                        <div class="space-y-3">
                                            <!-- Environment Name and Description -->
                                            <div x-show="currentEnvironmentSchema && currentEnvironmentSchema.schema && currentEnvironmentSchema.schema.name">
                                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                                    <svg class="w-4 h-4 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <span x-text="currentEnvironmentSchema.schema.name || 'Environment'"></span>
                                                </h4>
                                                <p x-show="currentEnvironmentSchema && currentEnvironmentSchema.schema && currentEnvironmentSchema.schema.description"
                                                   class="text-xs text-gray-600 dark:text-gray-400 mt-1"
                                                   x-text="currentEnvironmentSchema.schema.description || ''"></p>
                                            </div>

                                            <!-- Observation and Action Spaces -->
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                <div x-show="currentEnvironmentSchema && currentEnvironmentSchema.schema && currentEnvironmentSchema.schema.observation_space"
                                                     class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                                                    <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Observation Space:</h5>
                                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 font-mono"
                                                       x-text="currentEnvironmentSchema.schema.observation_space || 'Not specified'"></p>
                                                </div>
                                                <div x-show="currentEnvironmentSchema && currentEnvironmentSchema.schema && currentEnvironmentSchema.schema.action_space"
                                                     class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                                                    <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Action Space:</h5>
                                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 font-mono"
                                                       x-text="currentEnvironmentSchema.schema.action_space || 'Not specified'"></p>
                                                </div>
                                            </div>

                                            <!-- Reward Mechanism -->
                                            <div x-show="currentEnvironmentSchema && currentEnvironmentSchema.schema && currentEnvironmentSchema.schema.reward_mechanism"
                                                 class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                                                <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Reward Mechanism:</h5>
                                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1"
                                                   x-text="currentEnvironmentSchema.schema.reward_mechanism || 'Not specified'"></p>
                                            </div>

                                            <!-- Fallback message when no schema data is available -->
                                            <div x-show="!currentEnvironmentSchema || !currentEnvironmentSchema.schema || Object.keys(currentEnvironmentSchema.schema).length === 0"
                                                 class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
                                                <div class="flex items-center">
                                                    <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <p class="text-sm text-yellow-700 dark:text-yellow-300">
                                                        Doesn't exist
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label for="edit-timesteps" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Timesteps</label>
                                    <input type="number" id="edit-timesteps" x-model.number="editScheduleData.timesteps" min="1000" step="1000"
                                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                </div>

                                <div>
                                    <label for="edit-training-data-selector" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Training Data</label>
                                    <div id="edit-training-data-selector" class="mt-1"></div>
                                </div>

                                <div>
                                    <label for="edit-train-ratio" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Train/Test Split Ratio: <span class="font-bold" x-text="Math.round(editScheduleData.train_ratio * 100) + '%'"></span>
                                    </label>
                                    <input type="range" id="edit-train-ratio" x-model.number="editScheduleData.train_ratio" min="0.5" max="1" step="0.05"
                                           class="mt-1 block w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <span>50/50</span>
                                        <span>75/25</span>
                                        <span>100/0</span>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400" x-text="Math.round(editScheduleData.train_ratio * 100) + '% training / ' + Math.round((1 - editScheduleData.train_ratio) * 100) + '% testing'"></p>
                                </div>

                                <!-- Model to Finetune Section -->
                                <div>
                                    <label for="edit-model-to-finetune" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Model to Finetune</label>
                                    <select id="edit-model-to-finetune" x-model="editScheduleData.model_to_finetune_id"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="" disabled>Select a model</option>
                                        <template x-for="job in completedJobs" :key="job._id">
                                            <option :value="job._id" x-text="'ID: ' + job._id.substring(0, 8) + '... | ' + job.environment_name + ' | Mean Reward: ' + (job.mean_reward ? job.mean_reward.toFixed(2) : 'N/A')"></option>
                                        </template>
                                    </select>
                                </div>

                                <!-- Hyperparameters Section -->
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Hyperparameters</label>
                                        <span class="text-xs text-indigo-600 dark:text-indigo-400">PPO Algorithm</span>
                                    </div>
                                    <div id="edit-hyperparameters-optimizer" class="mt-2"></div>
                                </div>

                                <div>
                                    <label for="edit-description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (optional)</label>
                                    <textarea id="edit-description" x-model="editScheduleData.description" rows="3"
                                              placeholder="Enter a description for this training job..."
                                              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-200 dark:border-gray-600">
                    <button type="button" @click="saveScheduleChanges()"
                            class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-500 text-base font-medium text-white hover:from-blue-700 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Save Changes
                    </button>
                    <button type="button" @click="closeEditScheduleModal()"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-700 transition-colors duration-200">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div x-show="notification.show"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         @click="notification.show = false"
         class="fixed bottom-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm"
         :class="{
            'bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-100': notification.type === 'success',
            'bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-100': notification.type === 'error',
            'bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-100': notification.type === 'info'
         }">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg x-show="notification.type === 'success'" class="h-5 w-5 text-green-400 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <svg x-show="notification.type === 'error'" class="h-5 w-5 text-red-400 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                <svg x-show="notification.type === 'info'" class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium" x-text="notification.message"></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/frontend/rl-training/js/polyfills.js?v=1"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
    .swal-wide {
        width: 600px !important;
    }

    /* Dark mode styles for SweetAlert */
    .dark .swal2-popup {
        background-color: #1f2937;
        color: #f3f4f6;
    }

    .dark .swal2-title {
        color: #f3f4f6;
    }

    .dark .swal2-html-container {
        color: #d1d5db;
    }

    .dark .swal2-confirm {
        background-color: #7c3aed !important;
    }

    .dark .swal2-confirm:hover {
        background-color: #6d28d9 !important;
    }

    .dark .swal2-cancel {
        background-color: #4b5563 !important;
        color: #f3f4f6 !important;
    }

    .dark .swal2-cancel:hover {
        background-color: #374151 !important;
    }
</style>
<script src="/frontend/rl-environments/js/api-client.js?v=1"></script>
<script src="/frontend/rl-training/js/api-client.js?v=1"></script>
<script src="/frontend/rl-training/js/training-data-files.js?v=1"></script>
<script src="/frontend/rl-training/js/training-data-custom.js?v=1"></script>
<script src="/frontend/rl-training/js/training-data-mongodb.js?v=1"></script>
<script src="/frontend/rl-training/js/training-data.js?v=1"></script>
<script src="/frontend/rl-training/js/hyperparameters-optimizer.js?v=1"></script>
<script src="/frontend/rl-training/js/smart-hyperparameters-optimizer.js?v=1"></script>
<script src="/frontend/rl-training/js/main.js?v=1"></script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Vero AI Trading Platform{% endblock %}

{% block header %}Welcome to Vero{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Hero Section with Logo -->
    <div class="bg-gradient-to-r from-indigo-900 to-blue-900 dark:from-gray-900 dark:to-indigo-900 rounded-lg shadow-xl p-8">
        <div class="flex flex-col items-center text-center">
            <!-- Clean Animated Logo -->
            <svg class="size-32 mb-6" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <!-- Elegant background glow -->
              <defs>
                <filter id="glow">
                  <feGaussianBlur stdDeviation="1" result="blur"/>
                  <feComposite in="blur" operator="over" in2="SourceGraphic"/>
                </filter>

                <!-- Complex gradients -->
                <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%">
                    <animate attributeName="stop-color"
                             values="#4F46E5;#06B6D4;#6366F1;#4F46E5"
                             dur="8s"
                             repeatCount="indefinite"/>
                  </stop>
                  <stop offset="50%">
                    <animate attributeName="stop-color"
                             values="#06B6D4;#6366F1;#4F46E5;#06B6D4"
                             dur="8s"
                             repeatCount="indefinite"/>
                  </stop>
                  <stop offset="100%">
                    <animate attributeName="stop-color"
                             values="#6366F1;#4F46E5;#06B6D4;#6366F1"
                             dur="8s"
                             repeatCount="indefinite"/>
                  </stop>
                </linearGradient>

                <linearGradient id="orbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%">
                    <animate attributeName="stop-color"
                             values="#4F46E5;#06B6D4;#4F46E5"
                             dur="6s"
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-opacity"
                             values="0.6;0.8;0.6"
                             dur="4s"
                             repeatCount="indefinite"/>
                  </stop>
                  <stop offset="100%">
                    <animate attributeName="stop-color"
                             values="#06B6D4;#4F46E5;#06B6D4"
                             dur="6s"
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-opacity"
                             values="0.8;0.6;0.8"
                             dur="4s"
                             repeatCount="indefinite"/>
                  </stop>
                </linearGradient>

                <!-- Particle system -->
                <radialGradient id="particleGradient">
                  <stop offset="0%" stop-color="#ffffff" stop-opacity="0.8"/>
                  <stop offset="100%" stop-color="#ffffff" stop-opacity="0"/>
                </radialGradient>
              </defs>

              <!-- Dynamic background rings -->
              <g class="background-rings">
                <circle cx="50" cy="50" r="48" fill="none" stroke="url(#orbGradient)" stroke-width="0.25" opacity="0.3">
                  <animateTransform attributeName="transform"
                                    type="rotate"
                                    from="0 50 50"
                                    to="360 50 50"
                                    dur="30s"
                                    repeatCount="indefinite"/>
                </circle>
                <circle cx="50" cy="50" r="46" fill="none" stroke="url(#orbGradient)" stroke-width="0.25" opacity="0.3">
                  <animateTransform attributeName="transform"
                                    type="rotate"
                                    from="360 50 50"
                                    to="0 50 50"
                                    dur="25s"
                                    repeatCount="indefinite"/>
                </circle>
              </g>

              <!-- Main outer ring with complex animation -->
              <circle cx="50" cy="50" r="47" fill="none" stroke="url(#orbGradient)" stroke-width="2.5" filter="url(#glow)">
                <animate attributeName="stroke-dasharray"
                         from="0,300" to="300,0"
                         dur="3s"
                         fill="freeze"/>
                <animate attributeName="stroke-opacity"
                         values="0.8;1;0.8"
                         dur="4s"
                         repeatCount="indefinite"/>
              </circle>

              <!-- Inner geometric patterns -->
              <g class="inner-geometry" opacity="0.4">
                <path d="M50 50 L20 25 L80 25 Z" fill="none" stroke="url(#orbGradient)" stroke-width="0.25">
                  <animateTransform attributeName="transform"
                                    type="rotate"
                                    from="0 50 50"
                                    to="360 50 50"
                                    dur="20s"
                                    repeatCount="indefinite"/>
                </path>
                <path d="M50 50 L20 75 L80 75 Z" fill="none" stroke="url(#orbGradient)" stroke-width="0.25">
                  <animateTransform attributeName="transform"
                                    type="rotate"
                                    from="360 50 50"
                                    to="0 50 50"
                                    dur="20s"
                                    repeatCount="indefinite"/>
                </path>
              </g>

              <!-- Enhanced V shape with dynamic effects -->
              <g filter="url(#glow)">
                <path class="v-shape"
                      d="M50 80 L20 25 L50 65 L80 25 L50 80"
                      fill="none"
                      stroke="url(#mainGradient)"
                      stroke-width="3"
                      stroke-linejoin="round"
                      stroke-linecap="round">
                  <animate attributeName="stroke-dasharray"
                           from="0,200" to="200,0"
                           dur="2s"
                           fill="freeze"/>
                  <animate attributeName="stroke-opacity"
                           values="0.9;1;0.9"
                           dur="3s"
                           repeatCount="indefinite"/>
                </path>
              </g>

              <!-- Subtle accent rings -->
              <circle cx="50" cy="50" r="44" fill="none" stroke="url(#orbGradient)" stroke-width="0.5" opacity="0.3">
                <animate attributeName="r"
                         values="44;46;44"
                         dur="4s"
                         repeatCount="indefinite"/>
                <animate attributeName="stroke-opacity"
                         values="0.3;0.5;0.3"
                         dur="4s"
                         repeatCount="indefinite"/>
              </circle>

              <!-- Particle effects -->
              <g class="particles">
                <circle cx="50" cy="25" r="0.5" fill="url(#particleGradient)">
                  <animate attributeName="opacity"
                           values="0;1;0"
                           dur="3s"
                           repeatCount="indefinite"/>
                  <animateMotion
                    path="M0 0 Q10 10 0 20 Q-10 30 0 40"
                    dur="3s"
                    repeatCount="indefinite"/>
                </circle>
                <circle cx="20" cy="25" r="0.5" fill="url(#particleGradient)">
                  <animate attributeName="opacity"
                           values="0;1;0"
                           dur="4s"
                           repeatCount="indefinite"/>
                  <animateMotion
                    path="M0 0 Q-10 10 0 20 Q10 30 0 40"
                    dur="4s"
                    repeatCount="indefinite"/>
                </circle>
                <circle cx="80" cy="25" r="0.5" fill="url(#particleGradient)">
                  <animate attributeName="opacity"
                           values="0;1;0"
                           dur="5s"
                           repeatCount="indefinite"/>
                  <animateMotion
                    path="M0 0 Q10 10 0 20 Q-10 30 0 40"
                    dur="5s"
                    repeatCount="indefinite"/>
                </circle>
              </g>
            </svg>


            <h1 class="text-4xl font-bold text-white mb-4">Vero AI Trading Platform</h1>
            <p class="text-xl text-blue-100 max-w-2xl">
                AI-Powered Hedge Fund Intellect – Human Experience, Machine Precision
            </p>
        </div>
    </div>

    <!-- To-Do List Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">To-Do List</h2>
        <div class="space-y-4">
            <form id="todo-form" class="flex space-x-2">
                <input type="text" id="todo-title" placeholder="Title" class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md">Add</button>
            </form>
            <ul id="todo-list" class="space-y-2">
                <!-- To-Do items will be dynamically inserted here -->
            </ul>
        </div>
    </div>

    <!-- Description Modal -->
    <div id="description-modal" class="hidden fixed top-0 left-0 right-0 bottom-0 w-screen h-screen bg-black/50 backdrop-blur-sm z-100">
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-8 w-full max-w-3xl mx-4">
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white" id="modal-title"></h3>
                </div>
                <textarea id="todo-description" rows="8" class="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none" placeholder="Enter task description..."></textarea>
                <div class="mt-6 flex justify-end space-x-3">
                    <button onclick="copyTaskContent()" class="px-6 py-2.5 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200">Copy</button>
                    <button onclick="closeDescriptionModal()" class="px-6 py-2.5 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200">Close</button>
                    <button onclick="saveDescription()" class="px-6 py-2.5 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Company Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- About Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">About Vero</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
                Vero revolutionizes market analysis through AI agents that embody the analytical prowess of legendary hedge fund managers. Our platform specializes in identifying overlooked market opportunities through deep-dive analysis of unconventional data patterns and market irregularities.
            </p>
            <p class="text-gray-600 dark:text-gray-300">
                Each AI agent conducts exhaustive research, challenges market assumptions, and generates comprehensive reports that uncover hidden market opportunities. By combining contrarian thinking with massive data analysis capabilities, we spot market inefficiencies before they become mainstream knowledge.
            </p>
        </div>

        <!-- Key Features -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Platform Features</h2>
            <ul class="space-y-3">
                <li class="flex items-center text-gray-600 dark:text-gray-300">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Deep-Dive Market Analysis & Pattern Recognition
                </li>
                <li class="flex items-center text-gray-600 dark:text-gray-300">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Custom Data Loader Creation & Management
                </li>
                <li class="flex items-center text-gray-600 dark:text-gray-300">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Intelligent Feature Engineering & Model Development
                </li>
                <li class="flex items-center text-gray-600 dark:text-gray-300">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Comprehensive Research Reports Generation
                </li>
                <li class="flex items-center text-gray-600 dark:text-gray-300">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Contrarian Market Hypothesis Testing
                </li>
            </ul>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Platform Capabilities</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 bg-indigo-50 dark:bg-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-indigo-600 dark:text-indigo-400">Data Sources Analyzed</h3>
                <p class="text-3xl font-bold text-indigo-800 dark:text-indigo-300">500+</p>
            </div>
            <div class="p-4 bg-blue-50 dark:bg-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-600 dark:text-blue-400">Research Depth</h3>
                <p class="text-3xl font-bold text-blue-800 dark:text-blue-300">10K hrs/day</p>
            </div>
            <div class="p-4 bg-cyan-50 dark:bg-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-cyan-600 dark:text-cyan-400">Market Patterns</h3>
                <p class="text-3xl font-bold text-cyan-800 dark:text-cyan-300">∞</p>
            </div>
        </div>
    </div>

    <!-- Additional Research Approach Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Our Research Approach</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Data Collection</h3>
                <p class="text-gray-600 dark:text-gray-300">Comprehensive gathering of market data, SEC filings, economic indicators, and alternative data sources.</p>
            </div>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Pattern Analysis</h3>
                <p class="text-gray-600 dark:text-gray-300">Deep examination of market inefficiencies and hidden correlations across multiple sectors.</p>
            </div>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Hypothesis Testing</h3>
                <p class="text-gray-600 dark:text-gray-300">Rigorous validation of market theories through advanced statistical methods and historical data.</p>
            </div>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Report Generation</h3>
                <p class="text-gray-600 dark:text-gray-300">Detailed analysis reports with actionable insights and supporting evidence.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
<script>
async function addTodoItem(event) {
    event.preventDefault();
    const titleInput = document.getElementById('todo-title');
    const descriptionInput = document.getElementById('todo-description');
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();

    if (!title) {
        alert('Please enter a task title');
        return;
    }

    try {
        // Get current todos to calculate the highest order value
        const response = await fetch('/api/todos');
        const todos = await response.json();

        // Find the highest current order value
        const maxOrder = todos.reduce((max, todo) => Math.max(max, todo.order || 0), 0);

        // Add new todo with order higher than the highest current order
        const createResponse = await fetch('/api/todos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: title,
                description: description,
                order: maxOrder + 1000 // Use a large increment to allow for future insertions
            })
        });

        if (!createResponse.ok) {
            throw new Error('Failed to add todo');
        }

        titleInput.value = '';
        descriptionInput.value = '';
        closeDescriptionModal();
        await fetchTodos();
    } catch (error) {
        console.error('Error adding todo:', error);
        alert('Failed to add todo item');
    }
}

function formatTime(createdAt, completedAt) {
    const created = new Date(createdAt);
    const now = new Date();
    const completed = completedAt ? new Date(completedAt) : null;

    // Ensure we're comparing in UTC
    const diff = (completed || now).getTime() - created.getTime();
    const seconds = Math.floor(diff / 1000);

    if (seconds < 60) {
        return 'just now';
    }

    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
        return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
        return `${minutes}m`;
    } else {
        return 'just now';
    }
}

function formatId(mongoId) {
    // Extract only numbers from the string
    const numbers = mongoId.replace(/\D/g, '');
    // Get first 3 and last 3 digits
    const first3 = numbers.slice(0, 3);
    const last3 = numbers.slice(-3);
    return `${first3}${last3}`;
}

function copyTodoTitle(element) {
    const title = element.getAttribute('data-title');
    const description = element.getAttribute('data-description');
    if (!title) return;
    
    const content = description ? `${title}\n\n${description}` : title;
    
    // Create temporary textarea
    const textarea = document.createElement('textarea');
    textarea.value = content;
    textarea.style.position = 'fixed';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    
    try {
        // Select and copy text
        textarea.select();
        document.execCommand('copy');
        
        // Show feedback notification
        const notification = document.createElement('div');
        notification.textContent = 'Copied!';
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.padding = '10px 20px';
        notification.style.backgroundColor = '#4CAF50';
        notification.style.color = 'white';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '1000';
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 2000);
    } catch (err) {
        console.error('Failed to copy text: ', err);
    } finally {
        // Clean up
        document.body.removeChild(textarea);
    }
}

async function toggleTodoCompletion(todoId, isChecked) {
    if (!todoId) {
        console.error('Invalid todo ID');
        return;
    }

    try {
        const response = await fetch(`/api/todos/${todoId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ completed: isChecked })
        });

        if (!response.ok) {
            throw new Error('Failed to update todo');
        }
        await fetchTodos();
    } catch (error) {
        console.error('Error toggling todo completion:', error);
        alert('Failed to update todo status');
    }
}

async function deleteTodoItem(todoId) {
    if (!todoId) {
        console.error('Invalid todo ID');
        return;
    }

    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
        const response = await fetch(`/api/todos/${todoId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error('Failed to delete todo');
        }
        await fetchTodos();
    } catch (error) {
        console.error('Error deleting todo:', error);
        alert('Failed to delete todo item');
    }
}

async function updateTaskOrder() {
    const listElement = document.getElementById('todo-list');
    const todoItems = Array.from(listElement.children);
    const order = todoItems.map((item, index) => ({
        id: item.dataset.id,
        // Reverse the order values to maintain descending order
        order: todoItems.length - 1 - index
    })).filter(item => item.id);

    if (order.length === 0) return;

    try {
        const response = await fetch('/api/todos/order', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(order)
        });

        if (!response.ok) {
            throw new Error('Failed to update order');
        }
    } catch (error) {
        console.error('Error updating task order:', error);
        alert('Failed to update task order');
    }
}

async function fetchTodos() {
    try {
        const response = await fetch('/api/todos');
        if (!response.ok) {
            throw new Error('Failed to fetch todos');
        }

        const todos = await response.json();

        const listElement = document.getElementById('todo-list');
        listElement.innerHTML = todos
            .sort((a, b) => (b.order || 0) - (a.order || 0))
            .map(todo => {
                if (!todo._id) return '';
                return `
                    <li class="flex items-center justify-between p-4 bg-gray-100 dark:bg-gray-700 rounded-md shadow-sm"
                        data-id="${todo._id}">
                        <div class="flex items-center space-x-4">
                            <input type="checkbox"
                                class="form-checkbox h-5 w-5 text-blue-600"
                                ${todo.completed_at ? 'checked' : ''}
                                onchange="toggleTodoCompletion('${todo._id}', this.checked)">
                            <div class="flex flex-col">
                                <span class="text-gray-700 dark:text-gray-300 ${todo.completed_at ? 'line-through' : ''}">
                                    #${formatId(todo._id)} ${todo.title}
                                </span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                ${formatTime(todo.created_at, todo.completed_at)}
                            </span>
                            <button class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 edit-description-btn"
                                data-todo-id="${todo._id}"
                                data-title="${todo.title.replace(/"/g, '&quot;')}"
                                data-description="${(todo.description || '').replace(/"/g, '&quot;')}"
                                title="Add/Edit Details">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                            </button>
                            <button onclick="copyTodoTitle(this)"
                                data-title="${todo.title.replace(/"/g, '&quot;')}"
                                data-description="${todo.description ? todo.description.replace(/"/g, '&quot;') : ''}"
                                class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
                                title="Copy title">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                            </button>
                            <button onclick="deleteTodoItem('${todo._id}')"
                                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                    </li>
                `;
            })
            .join('');

        // Initialize Sortable
        if (listElement.children.length > 0) {
            new Sortable(listElement, {
                animation: 150,
                onEnd: updateTaskOrder
            });
        }
    } catch (error) {
        console.error('Error fetching todos:', error);
        alert('Failed to load todo items');
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', () => {
    document.getElementById('todo-form').addEventListener('submit', addTodoItem);
    
    // Add delegation for edit description buttons
    document.addEventListener('click', (e) => {
        if (e.target.closest('.edit-description-btn')) {
            const btn = e.target.closest('.edit-description-btn');
            const todoId = btn.dataset.todoId;
            const title = btn.dataset.title;
            const description = btn.dataset.description;
            openDescriptionModal(todoId, title, description);
        }
    });
    
    fetchTodos();
});

// Modal functions
function openDescriptionModal(todoId, title, description) {
    const modal = document.getElementById('description-modal');
    const modalTitle = document.getElementById('modal-title');
    const descriptionTextarea = document.getElementById('todo-description');
    
    modalTitle.textContent = title || 'Add Task Details';
    modalTitle.dataset.todoId = todoId;
    descriptionTextarea.value = description || '';
    
    modal.classList.remove('hidden');
}

function closeDescriptionModal() {
    const modal = document.getElementById('description-modal');
    modal.classList.add('hidden');
}

function copyTaskContent() {
    const modalTitle = document.getElementById('modal-title');
    const descriptionTextarea = document.getElementById('todo-description');
    
    // Get the actual title (remove the task ID if present)
    let title = modalTitle.textContent;
    if (title.includes('#')) {
        // Extract just the title part after the ID
        title = title.split(/\s+/).slice(2).join(' ');
    }
    
    const description = descriptionTextarea.value;
    const content = `${title}\n\n${description}`;
    
    // Create temporary textarea
    const textarea = document.createElement('textarea');
    textarea.value = content;
    textarea.style.position = 'fixed';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    
    try {
        // Select and copy text
        textarea.select();
        document.execCommand('copy');
        
        // Show feedback notification
        const notification = document.createElement('div');
        notification.textContent = 'Copied!';
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.padding = '10px 20px';
        notification.style.backgroundColor = '#4CAF50';
        notification.style.color = 'white';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '1000';
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 2000);
    } catch (err) {
        console.error('Failed to copy text: ', err);
    } finally {
        // Clean up
        document.body.removeChild(textarea);
    }
}

function saveDescription() {
    const modalTitle = document.getElementById('modal-title');
    const description = document.getElementById('todo-description').value;
    const todoId = modalTitle.dataset.todoId;
    
    if (todoId) {
        // Update existing todo
        fetch(`/api/todos/${todoId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ description })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to update description');
            }
            return fetchTodos();
        })
        .catch(error => {
            console.error('Error updating todo:', error);
            alert('Failed to update description');
        });
    }
    
    closeDescriptionModal();
}
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Add Data Loader{% endblock %}

{% block head %}
{{ super() }}
<style>
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }

    .animate-slide-in {
        animation: slideIn 0.3s ease-out;
    }

    .animate-fade-out {
        animation: fadeOut 0.3s ease-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="px-4 py-6 sm:px-0">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
            <div class="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 px-6 py-4">
                <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">Create Data Loader</h2>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Add a new data loader to your system</p>
            </div>

            <form id="dataLoaderForm" class="p-6 space-y-6">
                <div class="space-y-2">
                    <label for="description" class="block text-sm font-medium text-gray-900 dark:text-gray-100">Description</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <textarea id="description" name="description" rows="3"
                            class="block w-full rounded-md border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400 sm:text-sm transition-colors duration-200"
                            placeholder="Enter a detailed description of what your data loader does"></textarea>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Provide a clear description of the data loader's purpose and functionality.</p>
                </div>

                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">Code</label>
                    <div id="code-editor" class="mt-1 h-[500px] border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus-within:ring-1 focus-within:ring-gray-500 dark:focus-within:ring-gray-400 focus-within:border-gray-500 dark:focus-within:border-gray-400"></div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Write your Python code here. Make sure it follows PEP 8 style guidelines.</p>
                </div>

                <div class="pt-4 flex justify-end border-t border-gray-200 dark:border-gray-700">
                    <button type="submit"
                        class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400 transition-colors duration-200 shadow-sm">
                        Create Data Loader
                    </button>
                </div>
            </form>
        </div>

        <!-- Notification -->
        <div id="notification" class="fixed bottom-4 right-4 flex items-center p-4 mb-4 max-w-md rounded-lg shadow-lg transition-all duration-300 hidden dark:bg-gray-800" role="alert">
            <div class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 rounded-lg mr-3">
                <i id="notification-icon" class="fas text-lg"></i>
            </div>
            <div id="notification-message" class="text-sm font-medium text-gray-900 dark:text-gray-100"></div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex h-8 w-8 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700" onclick="this.parentElement.classList.add('hidden')">
                <span class="sr-only">Close</span>
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/frontend/add-data-loader/js/editor.js"></script>
<script src="/frontend/add-data-loader/js/form_handler.js"></script>
<script src="/frontend/add-data-loader/js/api_keys.js"></script>
<!-- Load the data loader builder script -->
<script src="/frontend/add-data-loader/js/data_loader_builder.js"></script>
{% endblock %}
// Configure Monaco Editor

// Add Monaco Editor CSS
const monacoCSS = document.createElement('link');
monacoCSS.rel = 'stylesheet';
monacoCSS.setAttribute('data-name', 'vs/editor/editor.main');
monacoCSS.href = 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs/editor/editor.main.min.css';
document.head.appendChild(monacoCSS);

// Configure AMD loader for Monaco
var require = { paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs' } };

// Load Monaco Editor scripts
const loaderScript = document.createElement('script');
loaderScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs/loader.js';
loaderScript.onload = () => {
    require(['vs/editor/editor.main'], function() {
        const editorContainer = document.getElementById('code-editor');
        if (!editorContainer) return;

        const sampleCode = `from data.base_data import BaseDataLoader
import pandas as pd
from typing import Dict, Any

class NewDataLoader(BaseDataLoader):
    """
    A custom data loader for handling time series data.
    
    This loader [describe your loader's purpose here]
    """
    
    def __init__(self):
        super().__init__()
        
        self.additional_columns = {
            'value': float,
            # Add more columns as needed
        }
        
    def load_historical_data(self) -> pd.DataFrame:
        """
        Load historical time series data.
        
        Returns:
            pandas.DataFrame: Historical time series data with datetime index
            
        Raises:
            ValueError: If data validation fails
        """
        
        try:
            # Implement your data loading logic here
            df = pd.DataFrame()
            
            # Ensure proper datetime index
            df = self._ensure_timezone_naive(df)
            
            return df
            
        except Exception as e:
            raise ValueError(f"Failed to load data: {str(e)}")
`;

        try {
            window.editor = monaco.editor.create(editorContainer, {
                value: sampleCode,
                language: 'python',
                theme: 'vs-dark',
                automaticLayout: true,
                minimap: { enabled: true },
                fontSize: 14,
                lineNumbers: 'on',
                scrollBeyondLastLine: false,
                renderWhitespace: 'selection',
                formatOnPaste: true,
                formatOnType: true,
            });

            // Add resize listener only after editor is created
            if (window.editor) {
                window.addEventListener('resize', () => {
                    if (window.editor) {
                        window.editor.layout();
                    }
                });
            }
        } catch (error) {
            console.error('Failed to initialize editor:', error);
        }
    });
};
document.head.appendChild(loaderScript);

// Export editor instance and functions for form handler
window.getEditorContent = () => {
    if (!window.editor) return '';
    return window.editor.getValue();
};

window.extractClassName = (code) => {
    const classMatch = code.match(/class\s+(\w+)\s*\(\s*BaseDataLoader\s*\)/);
    return classMatch ? classMatch[1] : null;
};

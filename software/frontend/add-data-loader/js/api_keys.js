// Function to create API key form HTML
function createApiKeyForm() {
    return `
    <div class="space-y-4">
        <div class="flex justify-between items-center">
            <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">API Keys</label>
            <button type="button" id="add-api-key"
                class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400 transition-colors duration-200 shadow-sm">
                <i class="fas fa-plus mr-2"></i>
                Add API Key
            </button>
        </div>
        <div id="api-keys-container" class="space-y-4">
            <!-- API key entries will be added here -->
        </div>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Add any API keys required by your data loader. Keys will be stored securely.</p>
    </div>
    `;
}

// Function to create a new API key entry
function createApiKeyEntry() {
    const entryId = Date.now();
    const entry = document.createElement('div');
    entry.className = 'api-key-entry bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-4 border border-gray-200 dark:border-gray-700';
    entry.innerHTML = `
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
                <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">Key Name</label>
                <input type="text" name="api-key-name-${entryId}" 
                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400 sm:text-sm transition-colors duration-200" 
                    placeholder="e.g., OPENAI_API_KEY" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">API Key</label>
                <input type="password" name="api-key-value-${entryId}" 
                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400 sm:text-sm transition-colors duration-200" 
                    placeholder="Enter API key" required>
            </div>
            <div class="sm:col-span-2">
                <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">Description</label>
                <input type="text" name="api-key-description-${entryId}" 
                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 focus:border-gray-500 dark:focus:border-gray-400 sm:text-sm transition-colors duration-200" 
                    placeholder="Optional description">
            </div>
            <div class="sm:col-span-2 flex justify-end">
                <button type="button" class="delete-api-key text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200">
                    <i class="fas fa-trash-alt mr-1"></i>
                    Remove
                </button>
            </div>
        </div>
    `;

    document.querySelector('#api-keys-container').appendChild(entry);

    // Add delete handler
    entry.querySelector('.delete-api-key').addEventListener('click', () => {
        entry.remove();
    });
}

// Initialize API key functionality
document.addEventListener('DOMContentLoaded', () => {
    // Insert API key form before the submit button
    const submitButton = document.querySelector('button[type="submit"]').parentElement;
    submitButton.insertAdjacentHTML('beforebegin', createApiKeyForm());

    // Add event listener for the "Add API Key" button
    document.querySelector('#add-api-key').addEventListener('click', createApiKeyEntry);
});

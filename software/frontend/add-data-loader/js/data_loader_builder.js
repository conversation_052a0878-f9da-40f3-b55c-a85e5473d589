// Data Loader Builder - AI Code Generation
// Minimal entry point to the AI code generation workflow

// Function to collect API keys from the form
function collectApiKeys() {
    const apiKeys = [];
    const entries = document.querySelectorAll('.api-key-entry');

    entries.forEach(entry => {
        const nameInput = entry.querySelector('input[name^="api-key-name-"]');
        if (!nameInput) return;

        const entryId = nameInput.name.split('-').pop();
        const keyName = entry.querySelector(`input[name="api-key-name-${entryId}"]`).value.trim();
        const keyValue = entry.querySelector(`input[name="api-key-value-${entryId}"]`).value.trim();
        const description = entry.querySelector(`input[name="api-key-description-${entryId}"]`).value.trim();

        if (keyName) {
            apiKeys.push({
                name: keyName,
                value: keyValue,
                description: description
            });
        }
    });

    return apiKeys;
}

// Function to generate data loader code using AI
async function generateDataLoaderCode() {
    try {
        // Get description from the form
        const description = document.getElementById('description').value.trim();
        if (!description) {
            showNotification('Please provide a description for the data loader', 'warning');
            return;
        }

        // Get API keys
        const apiKeys = collectApiKeys();

        // Show loading state
        const aiButton = document.getElementById('generate-code');
        const originalText = aiButton.innerHTML;
        aiButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Generating...';
        aiButton.disabled = true;

        // Make API call to the backend
        const response = await fetch('/api/add-data-loader/generate-code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                description: description,
                api_keys: apiKeys
            })
        });

        // Process response
        if (response.ok) {
            const contentType = response.headers.get("content-type");
            
            if (contentType && contentType.includes("application/json")) {
                const data = await response.json();
                
                if (data.status === 'waiting_for_input') {
                    // Handle case when API keys are needed
                    showNotification(data.message || 'API keys required to continue', 'warning');
                    showApiKeyInputForm(data.graph_state_id, data.api_key_fields || []);
                    return;
                }
                
                if (data.status === 'error') {
                    showNotification(`${data.error}: ${data.details}`, 'error');
                    return;
                }
                
                if (data.status === 'success') {
                    // Set the generated code in the editor
                    if (data.code) {
                        if (window.editor) {
                            window.editor.setValue(data.code);
                        } else {
                            showNotification('Editor not found. Please refresh the page and try again.', 'error');
                        }
                        
                        // Also update other fields if available
                        if (data.class_name) {
                            // If there's a class name field, update it
                            const classNameField = document.getElementById('className');
                            if (classNameField) {
                                classNameField.value = data.class_name;
                            }
                        }
                        
                        if (data.description) {
                            // Update enhanced description if available
                            const descriptionField = document.getElementById('description');
                            if (descriptionField && descriptionField.value.trim() !== data.description) {
                                descriptionField.value = data.description;
                            }
                        }
                        
                        showNotification('Code generated successfully!', 'success');
                    } else {
                        showNotification('Server returned success but no code was provided', 'error');
                    }
                } else {
                    showNotification('Received response with unrecognized status', 'warning');
                }
            } else {
                const textResponse = await response.text();
                showNotification('Server returned an unexpected response format', 'error');
            }
        } else {
            let errorMessage = `Failed to generate code (${response.status}: ${response.statusText})`;
            try {
                const errorData = await response.json();
                errorMessage = errorData.detail || errorMessage;
            } catch (parseError) {
                try {
                    const textResponse = await response.text();
                } catch (textError) {
                    // Unable to read response as text
                }
            }
            showNotification(errorMessage, 'error');
        }
    } catch (error) {
        showNotification(`An error occurred: ${error.message}`, 'error');
    } finally {
        // Reset button state
        const aiButton = document.getElementById('generate-code');
        aiButton.innerHTML = '<i class="fas fa-robot mr-2"></i> Generate with AI';
        aiButton.disabled = false;
    }
}

// Function to show API key input form when needed
function showApiKeyInputForm(graphStateId, apiKeyFields) {
    // Implementation will be added in a future update
    
    // For now, just show a notification
    showNotification('This data loader requires API keys. Please add them in the API Keys section below.', 'info');
}

// Function to show notifications
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const notificationMessage = document.getElementById('notification-message');
    const notificationIcon = document.getElementById('notification-icon');
    
    if (!notification || !notificationMessage || !notificationIcon) {
        return;
    }
    
    // Set notification type
    notification.className = 'fixed bottom-4 right-4 flex items-center p-4 mb-4 max-w-md rounded-lg shadow-lg transition-all duration-300 animate-slide-in';
    
    // Set icon and colors based on type
    switch (type) {
        case 'success':
            notification.classList.add('bg-green-100', 'dark:bg-green-800', 'text-green-700', 'dark:text-green-100');
            notificationIcon.className = 'fas fa-check-circle text-green-500 dark:text-green-300';
            break;
        case 'error':
            notification.classList.add('bg-red-100', 'dark:bg-red-800', 'text-red-700', 'dark:text-red-100');
            notificationIcon.className = 'fas fa-exclamation-circle text-red-500 dark:text-red-300';
            break;
        case 'warning':
            notification.classList.add('bg-yellow-100', 'dark:bg-yellow-800', 'text-yellow-700', 'dark:text-yellow-100');
            notificationIcon.className = 'fas fa-exclamation-triangle text-yellow-500 dark:text-yellow-300';
            break;
        default:
            notification.classList.add('bg-blue-100', 'dark:bg-blue-800', 'text-blue-700', 'dark:text-blue-100');
            notificationIcon.className = 'fas fa-info-circle text-blue-500 dark:text-blue-300';
    }
    
    // Set message
    notificationMessage.textContent = message;
    
    // Show notification
    notification.classList.remove('hidden');
    
    // Hide after 5 seconds
    setTimeout(() => {
        notification.classList.add('animate-fade-out');
        setTimeout(() => {
            notification.classList.add('hidden');
            notification.classList.remove('animate-fade-out');
        }, 300);
    }, 5000);
}

// Initialize the AI code generation button
document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('dataLoaderForm');
    if (form) {
        const submitButtonContainer = form.querySelector('button[type="submit"]')?.parentElement;
        if (submitButtonContainer) {
            const aiButton = document.createElement('button');
            aiButton.id = 'generate-code';
            aiButton.type = 'button';
            aiButton.className = 'inline-flex items-center mr-4 px-4 py-2 text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 shadow-sm';
            aiButton.innerHTML = `<i class="fas fa-robot mr-2"></i> Generate with AI`;
            
            // Add click event listener
            aiButton.addEventListener('click', generateDataLoaderCode);

            submitButtonContainer.insertBefore(aiButton, submitButtonContainer.firstChild);
        }
    }
});
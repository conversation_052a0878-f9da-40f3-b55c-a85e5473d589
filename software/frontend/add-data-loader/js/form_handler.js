document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('dataLoaderForm');
    const notification = document.getElementById('notification');
    const notificationMessage = document.getElementById('notification-message');
    const notificationIcon = document.getElementById('notification-icon');
    const descriptionInput = document.getElementById('description');

    if (!form || !notification || !notificationMessage || !notificationIcon || !descriptionInput) {
        console.error('Required form elements not found');
        return;
    }

    function showNotification(message, type = 'success') {
        notificationMessage.textContent = message;
        notification.classList.remove('hidden', 'bg-green-100', 'bg-red-100', 'bg-yellow-100');
        notificationIcon.classList.remove(
            'fa-check-circle', 'fa-times-circle', 'fa-exclamation-circle',
            'text-green-500', 'text-red-500', 'text-yellow-500'
        );
        
        switch (type) {
            case 'success':
                notification.classList.add('bg-green-100');
                notificationIcon.classList.add('fa-check-circle', 'text-green-500');
                break;
            case 'error':
                notification.classList.add('bg-red-100');
                notificationIcon.classList.add('fa-times-circle', 'text-red-500');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-100');
                notificationIcon.classList.add('fa-exclamation-circle', 'text-yellow-500');
                break;
        }
        
        notification.classList.remove('hidden');
        notification.classList.add('animate-slide-in');
        
        setTimeout(() => {
            notification.classList.remove('animate-slide-in');
            notification.classList.add('animate-fade-out');
            setTimeout(() => {
                notification.classList.add('hidden');
                notification.classList.remove('animate-fade-out');
            }, 300);
        }, 5000);
    }

    function validateInputs() {
        // Validate description
        const description = descriptionInput.value.trim();
        if (!description) {
            showNotification('Please provide a description for your data loader', 'error');
            descriptionInput.focus();
            return null;
        }

        // Validate code and extract class name
        const code = window.editor ? window.editor.getValue().trim() : '';
        if (!code) {
            showNotification('Please provide the code for your data loader', 'error');
            return null;
        }

        // Basic Python class validation
        const classMatch = code.match(/class\s+(\w+)\s*[:(]/);
        if (!classMatch) {
            showNotification('No valid Python class found in the code', 'error');
            return null;
        }

        const className = classMatch[1];
        if (!className) {
            showNotification('Could not extract class name from the code', 'error');
            return null;
        }

        // Collect API keys
        const apiKeys = {};
        document.querySelectorAll('.api-key-entry').forEach(entry => {
            const entryId = entry.querySelector('input[name^="api-key-name-"]').name.split('-').pop();
            const keyName = entry.querySelector(`input[name="api-key-name-${entryId}"]`).value.trim();
            const keyValue = entry.querySelector(`input[name="api-key-value-${entryId}"]`).value.trim();
            const description = entry.querySelector(`input[name="api-key-description-${entryId}"]`).value.trim();

            if (keyName && keyValue) {
                if (!/^[A-Za-z0-9_]+$/.test(keyName)) {
                    showNotification('API key names can only contain letters, numbers, and underscores', 'error');
                    return null;
                }
                apiKeys[keyName] = {
                    key: keyValue,
                    description: description,
                    is_active: true,
                    last_updated: new Date().toISOString()
                };
            } else if (keyName || keyValue) {
                showNotification('Please provide both name and value for all API keys', 'error');
                return null;
            }
        });

        return { className, description, code, api_keys: apiKeys };
    }

    form.onsubmit = async (e) => {
        e.preventDefault();

        const submitButton = form.querySelector('button[type="submit"]');
        if (!submitButton) return false;

        const formData = validateInputs();
        if (!formData) return false;

        // Get any enhanced description details from the detail notification if available
        const detailsElement = document.querySelector('.data-loader-details');
        if (detailsElement) {
            formData.inputs = detailsElement.getAttribute('data-inputs') || null;
            formData.outputs = detailsElement.getAttribute('data-outputs') || null;
            formData.requirements = detailsElement.getAttribute('data-requirements') || null;
        }

        submitButton.disabled = true;
        submitButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
        `;

        try {
            const response = await fetch('/api/add-data-loader/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            let data;
            try {
                data = await response.json();
            } catch (e) {
                showNotification('Invalid server response', 'error');
                submitButton.disabled = false;
                submitButton.innerHTML = 'Create Data Loader';
                return false;
            }

            if (response.status === 500) {
                showNotification(data.detail || 'An unexpected error occurred', 'error');
                submitButton.disabled = false;
                submitButton.innerHTML = 'Create Data Loader';
                return false;
            }

            // Handle response based on status field
            if (data.status === 'success') {
                showNotification('Data loader created successfully! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = '/show-data-loaders';
                }, 2000);
            } else if (data.status === 'validation_error') {
                let errorMessage = data.detail;
                
                // Clean up Pydantic validation messages
                if (typeof errorMessage === 'string') {
                    // Remove common Pydantic prefixes
                    errorMessage = errorMessage
                        .replace(/validation error(?:\s+for\s+\w+)?\s*[:]/i, '')
                        .replace(/value_error\s*[:]/i, '')
                        .trim();
                    
                    // Format specific error messages
                    if (errorMessage.includes('Invalid Python syntax:')) {
                        const syntaxError = errorMessage.split('Invalid Python syntax:')[1].trim();
                        errorMessage = `Python syntax error: ${syntaxError}`;
                    } else if (errorMessage.includes('already exists')) {
                        errorMessage = `A data loader with this name already exists. Please choose a different name.`;
                    } else if (errorMessage.includes('Code formatting error')) {
                        errorMessage = `Code formatting error: Your code doesn't meet the formatting standards. Please ensure it follows Python style guidelines.`;
                    } else if (errorMessage.includes('API key')) {
                        errorMessage = `API Key Error: ${errorMessage}`;
                    }
                }
                
                showNotification(errorMessage, 'warning');
                submitButton.disabled = false;
                submitButton.innerHTML = 'Create Data Loader';
            }
        } catch (error) {
            showNotification('Network error occurred. Please try again.', 'error');
            submitButton.disabled = false;
            submitButton.innerHTML = 'Create Data Loader';
        }
        return false;
    };
});

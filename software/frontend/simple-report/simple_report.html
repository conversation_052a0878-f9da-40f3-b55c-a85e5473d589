{% extends "base.html" %}

{% block title %}{% if report %}Report Details{% else %}Reports{% endif %}{% endblock %}

{% block header %}{% if report %}Report Details{% else %}Reports{% endif %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-100 dark:bg-gray-800 py-8">
    {% if error %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <strong class="font-bold">Error: </strong>
        <span class="block sm:inline">{{ error }}</span>
    </div>
    {% endif %}

    {% if report %}
    <!-- Report Details View -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">Report Details</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    {% if report.type == 'simple' %}bg-blue-100 text-blue-800
                    {% elif report.type == 'join' %}bg-purple-100 text-purple-800
                    {% else %}bg-green-100 text-green-800{% endif %}">
                    {{ report.type|title }}
                </span>
            </div>

            <!-- Chart Section -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Data Visualization</h3>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div id="chart" class="w-full" style="height: 400px;"></div>
                    <div id="checkbox-container" class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4"></div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Info -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
                        <p><span class="font-medium">Ticker:</span> {{ report.ticker }}</p>
                        <p><span class="font-medium">Status:</span> {{ report.status }}</p>
                        <p><span class="font-medium">Created:</span> {{ report.created_at }}</p>
                    </div>
                </div>

                <!-- Workflow Info -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Workflow Details</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
                        {% if report.type == 'simple' or report.type == 'extract' %}
                            <p><span class="font-medium">Data Loader:</span> {{ report.workflow.data_loader }}</p>
                        {% endif %}
                        {% if report.type == 'join' %}
                            <p><span class="font-medium">Left Loader:</span> {{ report.workflow.left_loader }}</p>
                            <p><span class="font-medium">Right Loader:</span> {{ report.workflow.right_loader }}</p>
                            <p><span class="font-medium">Join Type:</span> {{ report.workflow.join_type }}</p>
                        {% endif %}
                        {% if report.workflow.features %}
                            <p><span class="font-medium">Features:</span> {{ report.workflow.features|join(', ') }}</p>
                        {% endif %}
                    </div>
                </div>

                {% if report.workflow.model %}
                <!-- Model Info -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Model Information</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
                        <p><span class="font-medium">Model:</span> {{ report.workflow.model.name }}</p>
                        <p><span class="font-medium">Target:</span> {{ report.workflow.model.prediction_column }}</p>
                        <p><span class="font-medium">Horizon:</span> {{ report.workflow.model.forecast_horizon }} days</p>
                    </div>
                </div>

                <!-- Performance -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Metrics</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
                        {% for key, value in report.performance.items() %}
                            <p><span class="font-medium">{{ key }}:</span> {{ value }}</p>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            {% if report.workflow.model %}
            <!-- Chart -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Time Series Data</h3>
                <div id="chart" class="w-full h-96 bg-gray-50 dark:bg-gray-700 rounded-lg"></div>
                <div id="checkbox-container" class="flex flex-wrap gap-4"></div>
            </div>
            {% endif %}

            <!-- Raw Data -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Raw Report Data</h3>
                <pre class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-sm overflow-auto">{{ report | tojson(indent=2) }}</pre>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Reports List View -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-6 flex justify-between items-center">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Reports</h1>
            <div class="flex items-center gap-4">
                <select id="type-filter" class="block w-40 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="">All Types</option>
                    <option value="simple">Simple</option>
                    <option value="join">Join</option>
                    <option value="extract">Extract</option>
                </select>
                <button id="delete-selected" class="hidden px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium transition-colors duration-200">
                    Delete Selected
                </button>
            </div>
        </div>
        <div id="reports-list" class="space-y-4">
            <!-- Loading state -->
            <div class="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg animate-pulse">
                <div class="mx-auto h-12 w-12 text-gray-400">
                    <svg class="animate-spin h-12 w-12" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Loading reports...</h3>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
{% if report %}
<!-- Chart Scripts -->
<script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Load chart data from backend
        const chartData = {{ report.data.timeseries | tojson }};
        
        const chartElement = document.getElementById('chart');
        const checkboxContainer = document.getElementById('checkbox-container');

        if (!chartElement || !checkboxContainer) {
            return;
        }

        const { createChart } = window.LightweightCharts;

        const chart = createChart(chartElement, {
            width: chartElement.clientWidth,
            height: 400,
            layout: { 
                background: { type: 'solid', color: 'rgb(249, 250, 251)' },
                textColor: 'rgb(55, 65, 81)',
                fontSize: 12,
                fontFamily: 'Inter, sans-serif'
            },
            grid: {
                vertLines: { color: 'rgb(229, 231, 235)' },
                horzLines: { color: 'rgb(229, 231, 235)' }
            },
            timeScale: { 
                timeVisible: true,
                borderColor: 'rgb(229, 231, 235)',
                timeFormat: 'yyyy-MM-dd HH:mm'
            }
        });

        // Convert ISO date string to timestamp
        function dateToTimestamp(dateStr) {
            return Math.floor(new Date(dateStr).getTime() / 1000);
        }

        const seriesMap = {};

        {% if report.type == 'simple' and report.workflow.model %}
        // Handle prediction data for simple reports with models
        const predictionColumn = '{{ report.workflow.model.prediction_column }}';
        if (predictionColumn) {
            // Add actual values
            seriesMap[predictionColumn] = chart.addLineSeries({
                title: predictionColumn,
                color: '#2563eb',
                lineWidth: 2,
                // Allow gaps in data
                lastPriceAnimation: 0
            });

            const actualData = chartData
                .map((item, index) => {
                    // Skip if no date or value doesn't exist
                    if (!item.Date) {
                        return null;
                    }
                    
                    // Get numeric value - can be null for gaps
                    const rawValue = item[predictionColumn];
                    const numValue = rawValue !== undefined && rawValue !== null ? 
                        parseFloat(rawValue) : null;
                    
                    // Gaps are fine, but NaN is not
                    if (numValue !== null && isNaN(numValue)) {
                        return null;
                    }
                    
                    // Convert ISO string to timestamp (numeric) for the chart
                    let timeValue;
                    try {
                        timeValue = dateToTimestamp(item.Date);
                    } catch (e) {
                        return null;
                    }
                    
                    return {
                        time: timeValue,
                        value: numValue
                    };
                })
                .filter(item => item !== null);
            
            try {
                seriesMap[predictionColumn].setData(actualData);
            } catch (error) {
                // Silently handle errors
            }

            // Add predicted values
            const predictedColumn = predictionColumn + '_Predicted';
            if (chartData.some(item => predictedColumn in item)) {
                seriesMap[predictedColumn] = chart.addLineSeries({
                    title: 'Predicted',
                    color: '#dc2626',
                    lineWidth: 2,
                    lineStyle: 2,
                    // Allow gaps in data
                    lastPriceAnimation: 0
                });

                const predictedData = chartData
                    .map((item, index) => {
                        if (!item.Date) {
                            return null;
                        }
                        
                        const rawValue = item[predictedColumn];
                        const numValue = rawValue !== undefined && rawValue !== null ? 
                            parseFloat(rawValue) : null;
                        
                        if (numValue !== null && isNaN(numValue)) {
                            return null;
                        }
                        
                        // Convert ISO string to timestamp (numeric) for the chart
                        let timeValue;
                        try {
                            timeValue = dateToTimestamp(item.Date);
                        } catch (e) {
                            return null;
                        }
                        
                        return {
                            time: timeValue,
                            value: numValue
                        };
                    })
                    .filter(item => item !== null);
                
                try {
                    seriesMap[predictedColumn].setData(predictedData);
                } catch (error) {
                    // Silently handle errors
                }
            }
        }
        {% endif %}

        function addSeries(column, color) {
            if (column in seriesMap) {
                return;
            }
            
            seriesMap[column] = chart.addLineSeries({
                title: column,
                color: color,
                lineWidth: 1,
                // Allow gaps in data
                lastPriceAnimation: 0
            });

            const seriesData = chartData
                .map((item, index) => {
                    if (!item.Date) {
                        return null;
                    }
                    
                    const rawValue = item[column];
                    const numValue = rawValue !== undefined && rawValue !== null ? 
                        parseFloat(rawValue) : null;
                    
                    if (numValue !== null && isNaN(numValue)) {
                        return null;
                    }
                    
                    // Convert ISO string to timestamp (numeric) for the chart
                    let timeValue;
                    try {
                        timeValue = dateToTimestamp(item.Date);
                    } catch (e) {
                        return null;
                    }
                    
                    return {
                        time: timeValue,
                        value: numValue
                    };
                })
                .filter(item => item !== null);
            
            try {
                seriesMap[column].setData(seriesData);
            } catch (error) {
                // Silently handle errors
            }
        }

        function removeSeries(column) {
            if (!(column in seriesMap)) {
                return;
            }
            
            try {
                chart.removeSeries(seriesMap[column]);
                delete seriesMap[column];
            } catch (error) {
                // Silently handle error
            }
        }

        // Add column toggles for valid numeric columns
        const validColumns = Object.keys(chartData[0] || {}).filter(col => {
            {% if report.type == 'simple' and report.workflow.model %}
            if (['Date', '_id', '{{ report.workflow.model.prediction_column }}', '{{ report.workflow.model.prediction_column }}_Predicted'].includes(col)) {
                return false;
            }
            {% else %}
            if (['Date', '_id'].includes(col)) {
                return false;
            }
            {% endif %}
            const values = chartData.map(item => parseFloat(item[col])).filter(v => v !== null && !isNaN(v));
            return values.length > 0;
        });

        validColumns.forEach(col => {
            const div = document.createElement('div');
            div.className = 'flex items-center space-x-2';
            div.innerHTML = `
                <input type="checkbox" id="${col}" class="w-4 h-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500">
                <label for="${col}" class="text-sm text-gray-700">${col}</label>
            `;
            checkboxContainer.appendChild(div);
            
            const checkbox = div.querySelector('input');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    addSeries(col, '#' + Math.floor(Math.random()*16777215).toString(16));
                } else {
                    removeSeries(col);
                }
            });
        });

        const resizeObserver = new ResizeObserver(() => {
            chart.applyOptions({ width: chartElement.clientWidth });
        });
        resizeObserver.observe(chartElement);
    });
</script>
{% else %}
<!-- List View Scripts -->
<script>
    let selectedReports = new Set();
    const deleteButton = document.getElementById('delete-selected');

    function showEmptyState(message, type = null) {
        const listElement = document.getElementById("reports-list");
        if (!listElement) {
            return;
        }
        listElement.innerHTML = `
            <div class="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="${type === 'error' 
                            ? 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
                            : 'M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'}"
                    />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">${message}</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    ${type === 'error' 
                        ? 'Please try again or contact support if the problem persists.'
                        : 'Create a new report to get started.'}
                </p>
                <div class="mt-6">
                    <a href="/extract-workflow" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Create Report
                    </a>
                </div>
            </div>`;
    }

    async function fetchReports(type = '') {
        try {
            const response = await fetch(`/api/reports${type ? `?type=${type}` : ''}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const reports = await response.json();
            const listElement = document.getElementById("reports-list");
            
            if (!reports || reports.length === 0) {
                showEmptyState(
                    type 
                        ? `No ${type} reports found` 
                        : 'No reports found'
                );
                return;
            }

            selectedReports.clear();
            if (deleteButton) {
                deleteButton.classList.add('hidden');
            }
            
            listElement.innerHTML = reports
                .map(report => {
                    const reportId = report.id;
                    return `
                        <div class="bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg shadow p-4 flex items-center gap-4 transition-colors duration-200">
                            <input type="checkbox" class="report-checkbox w-4 h-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500" data-id="${reportId || ''}">
                            <div class="flex-grow space-y-2">
                                <div class="flex items-center gap-2">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">${report.ticker}</h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        ${report.type === 'simple' ? 'bg-blue-100 text-blue-800' :
                                          report.type === 'join' ? 'bg-purple-100 text-purple-800' :
                                          'bg-green-100 text-green-800'}">
                                        ${report.type}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        ${report.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                        ${report.status}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                                    ${report.type === 'extract' ? `
                                        <p>Data Loader: ${report.workflow.data_loader}</p>
                                    ` : report.type === 'join' ? `
                                        <p>Left: ${report.workflow.left_loader} (${report.workflow.left_ticker})</p>
                                        <p>Right: ${report.workflow.right_loader} (${report.workflow.right_ticker})</p>
                                    ` : `
                                        <p>Model: ${report.workflow.model?.name || 'N/A'}</p>
                                        <p>Target: ${report.workflow.model?.prediction_column || 'N/A'}</p>
                                    `}
                                    <p>Created: ${new Date(report.created_at).toLocaleDateString()}</p>
                                </div>
                            </div>
                            ${reportId ? `
                                <a href="/simple-report/${reportId}" 
                                   class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm font-medium transition-colors duration-200">
                                    View
                                </a>
                            ` : '<span class="text-red-500">No ID available</span>'}
                        </div>`;
                })
                .join("");

            document.querySelectorAll('.report-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', handleCheckboxChange);
            });
        } catch (error) {
            showEmptyState('Failed to load reports', 'error');
        }
    }

    function handleCheckboxChange(event) {
        const reportId = event.target.dataset.id;
        if (event.target.checked) {
            selectedReports.add(reportId);
        } else {
            selectedReports.delete(reportId);
        }
        
        if (selectedReports.size > 0) {
            deleteButton.classList.remove('hidden');
        } else {
            deleteButton.classList.add('hidden');
        }
    }

    async function deleteSelectedReports() {
        try {
            const promises = Array.from(selectedReports).map(reportId =>
                fetch(`/api/reports/${reportId}`, { method: 'DELETE' })
            );
            await Promise.all(promises);
            await fetchReports(document.getElementById('type-filter').value);
        } catch (error) {
            showEmptyState('Failed to delete reports', 'error');
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        fetchReports();
        
        const typeFilter = document.getElementById('type-filter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => fetchReports(e.target.value));
        }
        
        if (deleteButton) {
            deleteButton.addEventListener('click', deleteSelectedReports);
        }
    });
</script>
{% endif %}
{% endblock %}

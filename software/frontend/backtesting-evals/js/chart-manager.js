/**
 * Chart Manager for Backtesting Evaluations
 * Handles Highcharts timeseries visualization
 */

class ChartManager {
    constructor() {
        this.chart = null;
        this.chartContainer = 'timeseries-chart';
    }

    /**
     * Initialize and render the timeseries chart
     * @param {Object} data - Timeseries data from API
     */
    renderTimeseriesChart(data) {
        try {
            console.log('Rendering timeseries chart with data:', data);

            if (!data.categories || data.categories.length === 0) {
                console.warn('No data available for chart');
                return;
            }

            // Convert ISO timestamps to JavaScript Date objects for better formatting
            const categories = data.categories.map(timestamp => new Date(timestamp).getTime());

            // Prepare series data with timestamps
            const series = data.series.map(seriesData => ({
                ...seriesData,
                data: seriesData.data.map((value, index) => [categories[index], value])
            }));

            const chartOptions = {
                chart: {
                    type: 'line',
                    backgroundColor: 'transparent',
                    style: {
                        fontFamily: 'Inter, sans-serif'
                    }
                },
                title: {
                    text: null
                },
                xAxis: {
                    type: 'datetime',
                    title: {
                        text: 'Date',
                        style: {
                            color: '#6B7280'
                        }
                    },
                    labels: {
                        style: {
                            color: '#6B7280'
                        }
                    },
                    gridLineColor: '#E5E7EB'
                },
                yAxis: {
                    title: {
                        text: 'Score (0-100)',
                        style: {
                            color: '#6B7280'
                        }
                    },
                    labels: {
                        style: {
                            color: '#6B7280'
                        }
                    },
                    gridLineColor: '#E5E7EB',
                    min: 0,
                    max: 100
                },
                tooltip: {
                    shared: true,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#E5E7EB',
                    borderRadius: 8,
                    shadow: true,
                    useHTML: true,
                    formatter: function() {
                        let tooltip = `<div style="font-size: 12px; padding: 8px;">`;
                        tooltip += `<strong>${Highcharts.dateFormat('%Y-%m-%d %H:%M', this.x)}</strong><br/>`;
                        
                        this.points.forEach(point => {
                            tooltip += `<span style="color: ${point.color};">●</span> `;
                            tooltip += `${point.series.name}: <strong>${point.y}/100</strong><br/>`;
                        });
                        
                        tooltip += `</div>`;
                        return tooltip;
                    }
                },
                legend: {
                    align: 'center',
                    verticalAlign: 'bottom',
                    itemStyle: {
                        color: '#374151',
                        fontWeight: '500'
                    }
                },
                plotOptions: {
                    line: {
                        lineWidth: 2,
                        marker: {
                            radius: 4,
                            symbol: 'circle'
                        },
                        states: {
                            hover: {
                                lineWidth: 3
                            }
                        }
                    }
                },
                series: series,
                credits: {
                    enabled: false
                },
                exporting: {
                    enabled: true,
                    buttons: {
                        contextButton: {
                            menuItems: [
                                'viewFullscreen',
                                'separator',
                                'downloadPNG',
                                'downloadJPEG',
                                'downloadPDF',
                                'downloadSVG',
                                'separator',
                                'downloadCSV',
                                'downloadXLS'
                            ]
                        }
                    }
                },
                responsive: {
                    rules: [{
                        condition: {
                            maxWidth: 768
                        },
                        chartOptions: {
                            legend: {
                                layout: 'horizontal',
                                align: 'center',
                                verticalAlign: 'bottom'
                            }
                        }
                    }]
                }
            };

            // Create the chart
            this.chart = Highcharts.chart(this.chartContainer, chartOptions);
            
            console.log('Timeseries chart rendered successfully');
        } catch (error) {
            console.error('Error rendering timeseries chart:', error);
            throw error;
        }
    }

    /**
     * Update chart with new data
     * @param {Object} data - New timeseries data
     */
    updateChart(data) {
        if (this.chart) {
            this.chart.destroy();
        }
        this.renderTimeseriesChart(data);
    }

    /**
     * Destroy the chart
     */
    destroy() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }

    /**
     * Resize the chart (useful for responsive design)
     */
    resize() {
        if (this.chart) {
            this.chart.reflow();
        }
    }
}

// Create global instance
window.chartManager = new ChartManager();

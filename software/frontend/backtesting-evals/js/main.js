/**
 * Main Application Controller for Backtesting Evaluations
 * Coordinates all components and manages application state
 */

class BacktestingEvalsApp {
    constructor() {
        this.isLoading = false;
        this.hasData = false;

        // DOM elements
        this.loadingContainer = document.getElementById('loading-container');
        this.errorContainer = document.getElementById('error-container');
        this.errorMessage = document.getElementById('error-message');
        this.chartContainer = document.getElementById('chart-container');
        this.noDataContainer = document.getElementById('no-data-container');
        this.refreshBtn = document.getElementById('refresh-btn');

        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        console.log('Initializing Backtesting Evaluations App...');

        // Set up event listeners
        this.setupEventListeners();

        // Load initial data
        await this.loadData();

        console.log('App initialized successfully');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Refresh button
        if (this.refreshBtn) {
            this.refreshBtn.addEventListener('click', () => {
                this.loadData();
            });
        }

        // Window resize handler for chart responsiveness
        window.addEventListener('resize', () => {
            if (window.chartManager && this.hasData) {
                window.chartManager.resize();
            }
        });

        // Dark mode changes (if applicable)
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                if (this.hasData) {
                    this.refreshChart();
                }
            });
        }
    }

    /**
     * Load all data and update UI
     */
    async loadData() {
        if (this.isLoading) {
            console.log('Already loading data, skipping...');
            return;
        }

        try {
            this.setLoadingState(true);
            this.hideAllContainers();
            this.showLoading();

            console.log('Loading evaluation data...');

            // Fetch all data
            const data = await window.backtestingEvalsAPI.fetchAllData();

            console.log('Data loaded successfully:', data);

            this.hasData = data.hasData;

            if (this.hasData) {
                // Render components with data
                await this.renderWithData(data);
            } else {
                // Show no data state
                this.showNoData();
            }

        } catch (error) {
            console.error('Error loading data:', error);
            this.showError(error.message || 'Failed to load evaluation data');
        } finally {
            this.setLoadingState(false);
            this.hideLoading();
        }
    }

    /**
     * Render all components with data
     * @param {Object} data - Combined data object
     */
    async renderWithData(data) {
        try {
            console.log('Rendering components with data...');

            // Render statistics
            if (window.statsDisplay && data.stats) {
                window.statsDisplay.renderStats(data.stats);
            }

            // Render chart
            if (window.chartManager && data.timeseries) {
                window.chartManager.renderTimeseriesChart(data.timeseries);
                this.showChart();
            }

            // Render table
            if (window.tableManager && data.tableData) {
                window.tableManager.renderTable(data.tableData);
            }

            console.log('All components rendered successfully');

        } catch (error) {
            console.error('Error rendering components:', error);
            throw error;
        }
    }

    /**
     * Refresh chart with current data
     */
    async refreshChart() {
        if (!this.hasData) return;

        try {
            const data = await window.backtestingEvalsAPI.getTimeseriesData();
            if (window.chartManager) {
                window.chartManager.updateChart(data);
            }
        } catch (error) {
            console.error('Error refreshing chart:', error);
        }
    }

    /**
     * Set loading state
     * @param {boolean} loading - Loading state
     */
    setLoadingState(loading) {
        this.isLoading = loading;

        if (this.refreshBtn) {
            this.refreshBtn.disabled = loading;

            if (loading) {
                this.refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Loading...
                `;
            } else {
                this.refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh Data
                `;
            }
        }
    }

    /**
     * Show loading state
     */
    showLoading() {
        if (this.loadingContainer) {
            this.loadingContainer.style.display = 'flex';
        }
        if (window.statsDisplay) {
            window.statsDisplay.showLoading();
        }
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        if (this.loadingContainer) {
            this.loadingContainer.style.display = 'none';
        }
    }

    /**
     * Show error state
     * @param {string} message - Error message
     */
    showError(message) {
        if (this.errorContainer && this.errorMessage) {
            this.errorMessage.textContent = message;
            this.errorContainer.style.display = 'block';
        }
        if (window.statsDisplay) {
            window.statsDisplay.showError(message);
        }
    }

    /**
     * Hide error state
     */
    hideError() {
        if (this.errorContainer) {
            this.errorContainer.style.display = 'none';
        }
    }

    /**
     * Show chart container
     */
    showChart() {
        if (this.chartContainer) {
            this.chartContainer.style.display = 'block';
        }
    }

    /**
     * Show no data state
     */
    showNoData() {
        if (this.noDataContainer) {
            this.noDataContainer.style.display = 'block';
        }
        if (window.tableManager) {
            window.tableManager.hide();
        }
    }

    /**
     * Hide all main containers
     */
    hideAllContainers() {
        this.hideError();

        if (this.chartContainer) {
            this.chartContainer.style.display = 'none';
        }
        if (this.noDataContainer) {
            this.noDataContainer.style.display = 'none';
        }
        if (window.tableManager) {
            window.tableManager.hide();
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing Backtesting Evaluations App...');
    window.backtestingEvalsApp = new BacktestingEvalsApp();
});

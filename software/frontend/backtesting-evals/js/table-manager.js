/**
 * Table Manager for Backtesting Evaluations
 * Handles the display of recent evaluations in table format
 */

class TableManager {
    constructor() {
        this.tableBody = document.getElementById('evaluations-table-body');
        this.tableContainer = document.getElementById('recent-evaluations-container');
        this.selectAllCheckbox = document.getElementById('select-all-checkbox');
        this.bulkActionsContainer = document.getElementById('bulk-actions-container');
        this.deleteSelectedBtn = document.getElementById('delete-selected-btn');
        this.selectedEvaluations = new Set();

        // Hover overlay elements
        this.overlay = document.getElementById('business-question-overlay');
        this.overlayContent = document.getElementById('overlay-content');

        this.setupEventListeners();
    }

    /**
     * Set up event listeners for checkbox interactions and bulk actions
     */
    setupEventListeners() {
        // Select all checkbox handler
        if (this.selectAllCheckbox) {
            this.selectAllCheckbox.addEventListener('change', (e) => {
                this.handleSelectAll(e.target.checked);
            });
        }

        // Delete selected button handler
        if (this.deleteSelectedBtn) {
            this.deleteSelectedBtn.addEventListener('click', () => {
                this.handleDeleteSelected();
            });
        }
    }

    /**
     * Render evaluations table from table data
     * @param {Array} tableData - Array of evaluation objects with LLM names resolved
     */
    renderTable(tableData) {
        try {
            console.log('Rendering evaluations table with data:', tableData);

            if (!this.tableBody || !this.tableContainer) {
                console.error('Table elements not found');
                return;
            }

            if (!tableData || tableData.length === 0) {
                console.warn('No data available for table');
                this.tableContainer.style.display = 'none';
                return;
            }

            // Clear existing content and selections
            this.tableBody.innerHTML = '';
            this.selectedEvaluations.clear();
            this.updateBulkActionsVisibility();
            this.hideOverlay(); // Hide any visible overlay

            // Reset select all checkbox
            if (this.selectAllCheckbox) {
                this.selectAllCheckbox.checked = false;
                this.selectAllCheckbox.indeterminate = false;
            }

            // Take the most recent 20 evaluations (data is already sorted by API)
            const recentEvaluations = tableData.slice(0, 20);

            // Render each evaluation row
            recentEvaluations.forEach(evaluation => {
                const row = this.createTableRow(evaluation);
                this.tableBody.appendChild(row);
            });

            // Show the table container
            this.tableContainer.style.display = 'block';

            console.log('Table rendered successfully');
        } catch (error) {
            console.error('Error rendering table:', error);
            throw error;
        }
    }

    /**
     * Handle select all checkbox change
     * @param {boolean} checked - Whether the select all checkbox is checked
     */
    handleSelectAll(checked) {
        const checkboxes = this.tableBody.querySelectorAll('input[type="checkbox"][data-evaluation-id]');

        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const evaluationId = checkbox.getAttribute('data-evaluation-id');

            if (checked) {
                this.selectedEvaluations.add(evaluationId);
            } else {
                this.selectedEvaluations.delete(evaluationId);
            }
        });

        this.updateBulkActionsVisibility();
    }

    /**
     * Handle individual checkbox change
     * @param {string} evaluationId - The evaluation ID
     * @param {boolean} checked - Whether the checkbox is checked
     */
    handleCheckboxChange(evaluationId, checked) {
        if (checked) {
            this.selectedEvaluations.add(evaluationId);
        } else {
            this.selectedEvaluations.delete(evaluationId);
        }

        // Update select all checkbox state
        const totalCheckboxes = this.tableBody.querySelectorAll('input[type="checkbox"][data-evaluation-id]').length;
        const checkedCheckboxes = this.selectedEvaluations.size;

        if (this.selectAllCheckbox) {
            this.selectAllCheckbox.checked = checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0;
            this.selectAllCheckbox.indeterminate = checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes;
        }

        this.updateBulkActionsVisibility();
    }

    /**
     * Update visibility of bulk actions based on selection
     */
    updateBulkActionsVisibility() {
        if (this.bulkActionsContainer) {
            if (this.selectedEvaluations.size > 0) {
                this.bulkActionsContainer.classList.remove('hidden');

                // Update button text with count
                if (this.deleteSelectedBtn) {
                    const count = this.selectedEvaluations.size;
                    this.deleteSelectedBtn.innerHTML = `
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v1M4 7h16"></path>
                        </svg>
                        Delete Selected (${count})
                    `;
                }
            } else {
                this.bulkActionsContainer.classList.add('hidden');
            }
        }
    }

    /**
     * Handle delete selected button click
     */
    async handleDeleteSelected() {
        const selectedIds = Array.from(this.selectedEvaluations);

        if (selectedIds.length === 0) {
            return;
        }

        // Show confirmation dialog
        const confirmed = confirm(`Are you sure you want to delete ${selectedIds.length} selected evaluation${selectedIds.length > 1 ? 's' : ''}? This action cannot be undone.`);

        if (!confirmed) {
            return;
        }

        try {
            // Show loading state
            this.setDeleteButtonLoading(true);

            // Call the delete API
            const result = await window.backtestingEvalsAPI.deleteEvaluations(selectedIds);

            if (result.success) {
                // Show success message
                this.showMessage(`Successfully deleted ${result.deleted_count} evaluation${result.deleted_count > 1 ? 's' : ''}`, 'success');

                // Clear selection
                this.selectedEvaluations.clear();
                this.updateBulkActionsVisibility();

                // Refresh table data
                if (window.backtestingEvalsApp) {
                    await window.backtestingEvalsApp.loadData();
                }
            } else {
                this.showMessage(`Failed to delete evaluations: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error deleting evaluations:', error);
            this.showMessage('Failed to delete evaluations. Please try again.', 'error');
        } finally {
            this.setDeleteButtonLoading(false);
        }
    }

    /**
     * Set loading state for delete button
     * @param {boolean} loading - Whether to show loading state
     */
    setDeleteButtonLoading(loading) {
        if (!this.deleteSelectedBtn) return;

        if (loading) {
            this.deleteSelectedBtn.disabled = true;
            this.deleteSelectedBtn.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Deleting...
            `;
        } else {
            this.deleteSelectedBtn.disabled = false;
            const count = this.selectedEvaluations.size;
            this.deleteSelectedBtn.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v1M4 7h16"></path>
                </svg>
                Delete Selected (${count})
            `;
        }
    }

    /**
     * Show a message to the user
     * @param {string} message - The message to show
     * @param {string} type - The type of message ('success' or 'error')
     */
    showMessage(message, type) {
        // Create a temporary message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white font-medium ${
            type === 'success' ? 'bg-green-600' : 'bg-red-600'
        }`;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // Remove after 3 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    /**
     * Show hover overlay with business question
     * @param {string} businessQuestion - The business question text
     * @param {MouseEvent} event - The mouse event for positioning
     */
    showOverlay(businessQuestion, event) {
        if (!this.overlay || !this.overlayContent) return;

        // Truncate very long business questions
        const maxLength = 300;
        const displayText = businessQuestion.length > maxLength
            ? businessQuestion.substring(0, maxLength) + '...'
            : businessQuestion;

        this.overlayContent.textContent = displayText;

        // Position the overlay near the cursor
        const x = event.clientX;
        const y = event.clientY;

        // Get viewport dimensions
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Show overlay temporarily to get its dimensions
        this.overlay.style.visibility = 'hidden';
        this.overlay.classList.remove('hidden');

        const overlayRect = this.overlay.getBoundingClientRect();
        const overlayWidth = overlayRect.width;
        const overlayHeight = overlayRect.height;

        // Calculate position with viewport boundary checks
        let left = x + 10; // 10px offset from cursor
        let top = y + 10;

        // Adjust if overlay would go off the right edge
        if (left + overlayWidth > viewportWidth) {
            left = x - overlayWidth - 10;
        }

        // Adjust if overlay would go off the bottom edge
        if (top + overlayHeight > viewportHeight) {
            top = y - overlayHeight - 10;
        }

        // Ensure overlay doesn't go off the left or top edges
        left = Math.max(10, left);
        top = Math.max(10, top);

        // Apply position and show overlay
        this.overlay.style.left = `${left}px`;
        this.overlay.style.top = `${top}px`;
        this.overlay.style.visibility = 'visible';
    }

    /**
     * Hide hover overlay
     */
    hideOverlay() {
        if (this.overlay) {
            this.overlay.classList.add('hidden');
        }
    }

    /**
     * Set up hover event listeners for a table row
     * @param {HTMLElement} row - The table row element
     * @param {string} businessQuestion - The business question text
     */
    setupRowHoverListeners(row, businessQuestion) {
        let hoverTimeout;

        row.addEventListener('mouseenter', (event) => {
            // Don't show overlay if hovering over checkbox or other interactive elements
            if (event.target.tagName === 'INPUT' || event.target.closest('input')) {
                return;
            }

            // Clear any existing timeout
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
            }

            // Show overlay with slight delay to prevent flickering
            hoverTimeout = setTimeout(() => {
                this.showOverlay(businessQuestion, event);
            }, 300);
        });

        row.addEventListener('mouseleave', () => {
            // Clear timeout if mouse leaves before delay
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
            }

            this.hideOverlay();
        });

        // Update overlay position on mouse move within the row
        row.addEventListener('mousemove', (event) => {
            // Don't update overlay position if hovering over interactive elements
            if (event.target.tagName === 'INPUT' || event.target.closest('input')) {
                this.hideOverlay();
                return;
            }

            if (!this.overlay.classList.contains('hidden')) {
                this.showOverlay(businessQuestion, event);
            }
        });

        // Prevent overlay from showing when clicking on checkboxes
        const checkbox = row.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.addEventListener('mouseenter', () => {
                if (hoverTimeout) {
                    clearTimeout(hoverTimeout);
                }
                this.hideOverlay();
            });
        }
    }

    /**
     * Create a table row for an evaluation
     * @param {Object} evaluation - Evaluation data
     * @returns {HTMLElement} Table row element
     */
    createTableRow(evaluation) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer';

        // Add business question as data attribute for hover overlay
        row.setAttribute('data-business-question', evaluation.business_question || 'No business question available');

        const formatDate = (dateString) => {
            try {
                return new Date(dateString).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch {
                return 'Invalid Date';
            }
        };

        const getScoreColor = (score) => {
            if (score >= 90) return 'text-green-600 dark:text-green-400';
            if (score >= 80) return 'text-blue-600 dark:text-blue-400';
            if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
            if (score >= 60) return 'text-orange-600 dark:text-orange-400';
            return 'text-red-600 dark:text-red-400';
        };

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox"
                       data-evaluation-id="${evaluation.id}"
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                ${formatDate(evaluation.created_at)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400">
                    ${evaluation.ticker}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                    ${evaluation.llm_name || 'Model Not Available'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold ${getScoreColor(evaluation.overall_score)}">
                ${Math.round(evaluation.overall_score)}/100
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm ${getScoreColor(evaluation.report_readability_score)}">
                ${Math.round(evaluation.report_readability_score)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm ${getScoreColor(evaluation.code_strategy_alignment_score)}">
                ${Math.round(evaluation.code_strategy_alignment_score)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm ${getScoreColor(evaluation.optimization_ranges_realism_score)}">
                ${Math.round(evaluation.optimization_ranges_realism_score)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm ${getScoreColor(evaluation.analysis_quality_score)}">
                ${Math.round(evaluation.analysis_quality_score)}
            </td>
        `;

        // Add event listener to the checkbox
        const checkbox = row.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.addEventListener('change', (e) => {
                this.handleCheckboxChange(evaluation.id, e.target.checked);
            });
        }

        // Set up hover listeners for business question overlay
        this.setupRowHoverListeners(row, evaluation.business_question || 'No business question available');

        return row;
    }

    /**
     * Hide the table container
     */
    hide() {
        if (this.tableContainer) {
            this.tableContainer.style.display = 'none';
        }

        // Clear selections when hiding
        this.selectedEvaluations.clear();
        this.updateBulkActionsVisibility();
        this.hideOverlay(); // Hide any visible overlay

        if (this.selectAllCheckbox) {
            this.selectAllCheckbox.checked = false;
            this.selectAllCheckbox.indeterminate = false;
        }
    }

    /**
     * Show the table container
     */
    show() {
        if (this.tableContainer) {
            this.tableContainer.style.display = 'block';
        }
    }

    /**
     * Clear table content
     */
    clear() {
        if (this.tableBody) {
            this.tableBody.innerHTML = '';
        }
        this.hideOverlay(); // Hide any visible overlay
        this.selectedEvaluations.clear();
        this.updateBulkActionsVisibility();
    }
}

// Create global instance
window.tableManager = new TableManager();

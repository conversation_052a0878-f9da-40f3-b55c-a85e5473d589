/**
 * Stats Display Manager for Backtesting Evaluations
 * Handles the display of evaluation statistics cards
 */

class StatsDisplay {
    constructor() {
        this.container = document.getElementById('stats-container');
    }

    /**
     * Render statistics cards
     * @param {Object} stats - Statistics data from API
     */
    renderStats(stats) {
        try {
            console.log('Rendering stats with data:', stats);

            if (!this.container) {
                console.error('Stats container not found');
                return;
            }

            // Clear existing content
            this.container.innerHTML = '';

            // Create stats cards
            const statsCards = [
                {
                    title: 'Total Evaluations',
                    value: stats.total_count || 0,
                    icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
                    color: 'indigo',
                    suffix: ''
                },
                {
                    title: 'Average Overall Score',
                    value: this.formatScore(stats.avg_overall_score),
                    icon: 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z',
                    color: 'green',
                    suffix: '/100'
                },
                {
                    title: 'Score Range',
                    value: `${this.formatScore(stats.min_overall_score)} - ${this.formatScore(stats.max_overall_score)}`,
                    icon: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z',
                    color: 'blue',
                    suffix: ''
                },
                {
                    title: 'Avg Code Alignment',
                    value: this.formatScore(stats.avg_code_alignment_score),
                    icon: 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4',
                    color: 'purple',
                    suffix: '/100'
                }
            ];

            // Render each card
            statsCards.forEach(card => {
                const cardElement = this.createStatsCard(card);
                this.container.appendChild(cardElement);
            });

            console.log('Stats rendered successfully');
        } catch (error) {
            console.error('Error rendering stats:', error);
            throw error;
        }
    }

    /**
     * Create a statistics card element
     * @param {Object} card - Card configuration
     * @returns {HTMLElement} Card element
     */
    createStatsCard(card) {
        const cardDiv = document.createElement('div');
        cardDiv.className = 'bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700';

        cardDiv.innerHTML = `
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-${card.color}-100 dark:bg-${card.color}-900/20 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-${card.color}-600 dark:text-${card.color}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${card.icon}"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                ${card.title}
                            </dt>
                            <dd class="text-2xl font-semibold text-gray-900 dark:text-white">
                                ${card.value}${card.suffix}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        `;

        return cardDiv;
    }

    /**
     * Format score value for display
     * @param {number} score - Raw score value
     * @returns {string} Formatted score
     */
    formatScore(score) {
        if (score === null || score === undefined || isNaN(score)) {
            return '0';
        }
        return Math.round(score * 10) / 10; // Round to 1 decimal place
    }

    /**
     * Show loading state for stats
     */
    showLoading() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="col-span-full flex items-center justify-center py-8">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                    <span class="text-gray-600 dark:text-gray-400">Loading statistics...</span>
                </div>
            </div>
        `;
    }

    /**
     * Show error state for stats
     * @param {string} message - Error message
     */
    showError(message) {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="col-span-full bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error Loading Statistics</h3>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">${message}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Clear stats display
     */
    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Create global instance
window.statsDisplay = new StatsDisplay();

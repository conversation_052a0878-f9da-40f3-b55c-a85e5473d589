/**
 * API Client for Backtesting Evaluations
 * Handles all API communication for evaluation data
 */

class BacktestingEvalsAPIClient {
    constructor() {
        this.baseURL = '/api/backtest-strategy-eval';
    }

    /**
     * Fetch evaluation timeseries data for charts
     * @param {number} limit - Maximum number of evaluations to fetch
     * @returns {Promise<Object>} Timeseries data formatted for Highcharts
     */
    async getTimeseriesData(limit = 100) {
        try {
            const response = await fetch(`${this.baseURL}/timeseries?limit=${limit}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Timeseries data received:', data);
            return data;
        } catch (error) {
            console.error('Error fetching timeseries data:', error);
            throw error;
        }
    }

    /**
     * Fetch evaluation statistics
     * @returns {Promise<Object>} Statistics about evaluations
     */
    async getStats() {
        try {
            const response = await fetch(`${this.baseURL}/stats`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Stats data received:', data);
            return data;
        } catch (error) {
            console.error('Error fetching stats:', error);
            throw error;
        }
    }

    /**
     * Fetch evaluations for a specific ticker
     * @param {string} ticker - Stock ticker symbol
     * @param {number} limit - Maximum number of evaluations to fetch
     * @returns {Promise<Array>} List of evaluations for the ticker
     */
    async getEvaluationsByTicker(ticker, limit = 50) {
        try {
            const response = await fetch(`${this.baseURL}/ticker/${ticker}?limit=${limit}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log(`Evaluations for ${ticker} received:`, data);
            return data;
        } catch (error) {
            console.error(`Error fetching evaluations for ticker ${ticker}:`, error);
            throw error;
        }
    }

    /**
     * Delete multiple evaluations by their IDs
     * @param {Array<string>} evaluationIds - Array of evaluation IDs to delete
     * @returns {Promise<Object>} Deletion results
     */
    async deleteEvaluations(evaluationIds) {
        try {
            console.log('Deleting evaluations:', evaluationIds);

            const response = await fetch(`${this.baseURL}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    evaluation_ids: evaluationIds
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Delete response received:', data);
            return data;
        } catch (error) {
            console.error('Error deleting evaluations:', error);
            throw error;
        }
    }

    /**
     * Check API health
     * @returns {Promise<Object>} API info and status
     */
    async getInfo() {
        try {
            const response = await fetch(`${this.baseURL}/info`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API info received:', data);
            return data;
        } catch (error) {
            console.error('Error fetching API info:', error);
            throw error;
        }
    }

    /**
     * Fetch evaluation data for table display with LLM names resolved
     * @param {number} limit - Maximum number of evaluations to fetch
     * @returns {Promise<Array>} List of evaluations with LLM names
     */
    async getTableData(limit = 50) {
        try {
            const response = await fetch(`${this.baseURL}/table-data?limit=${limit}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Table data received:', data);
            return data;
        } catch (error) {
            console.error('Error fetching table data:', error);
            throw error;
        }
    }

    /**
     * Fetch all evaluation data needed for the page
     * @returns {Promise<Object>} Combined data object
     */
    async fetchAllData() {
        try {
            console.log('Fetching all evaluation data...');

            const [timeseriesData, stats, tableData] = await Promise.all([
                this.getTimeseriesData(),
                this.getStats(),
                this.getTableData()
            ]);

            return {
                timeseries: timeseriesData,
                stats: stats,
                tableData: tableData,
                hasData: timeseriesData.categories && timeseriesData.categories.length > 0
            };
        } catch (error) {
            console.error('Error fetching all data:', error);
            throw error;
        }
    }
}

// Create global instance
window.backtestingEvalsAPI = new BacktestingEvalsAPIClient();

async function fetchReports() {
  const response = await fetch("/api/reports");
  const reports = await response.json();
  const listElement = document.getElementById("reports-list");
  listElement.innerHTML = reports
    .map(
      (report) => `
        <div>
            <h2>${report.ticker} - ${report.model_name}</h2>
            <p>Created: ${new Date(report.created_at).toLocaleString()}</p>
            <button onclick="showReportDetails('${report.id}')">View Details</button>
        </div>
    `,
    )
    .join("");
}

async function showReportDetails(reportId) {
  const response = await fetch(`/api/reports/${reportId}`);
  const report = await response.json();
  const detailsElement = document.getElementById("report-details");
  detailsElement.innerHTML = `
        <h2>Report Details for ${report.ticker}</h2>
        <p>Model: ${report.model_name}</p>
        <p>Performance: ${JSON.stringify(report.performance)}</p>
        <!-- Add more details as needed -->
    `;
  // Here you could add code to create charts using Chart.js
}

fetchReports();

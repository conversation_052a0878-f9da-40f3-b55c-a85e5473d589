{% extends "base.html" %}

{% block title %}Database Backup{% endblock %}
{% block meta_description %}Manage MongoDB database backups{% endblock %}
{% block og_title %}Database Backup - Vero{% endblock %}
{% block og_description %}Create and restore database backups{% endblock %}

{% block header %}Database Backup{% endblock %}

{% block extra_head %}
<style>
  .backup-action {
    transition: all 0.3s ease;
  }
  .backup-action:hover {
    transform: translateY(-2px);
  }
  .backup-list {
    max-height: 500px;
    overflow-y: auto;
  }

  /* Timeline Component Styles */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes draw-checkmark {
    0% { stroke-dashoffset: 30; }
    100% { stroke-dashoffset: 0; }
  }

  @keyframes draw-circle {
     0% { stroke-dashoffset: 157; }
     100% { stroke-dashoffset: 0; }
  }

  @keyframes fade-in-scale {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
  }

  @keyframes fade-out {
    0% { opacity: 1; }
    100% { opacity: 0; }
  }

  @keyframes pulse-ring {
    0% { box-shadow: 0 0 0 2px var(--bg-card), 0 0 0 4px currentColor; }
    50% { box-shadow: 0 0 0 2px var(--bg-card), 0 0 0 6px currentColor; }
    100% { box-shadow: 0 0 0 2px var(--bg-card), 0 0 0 4px currentColor; }
  }

  /* Variables for backup component */
  :root {
    --bg-card: #fff;
    --manual-color: 201, 94%, 51%;     /* Bright blue */
    --scheduled-color: 142, 76%, 45%;  /* Green */
    --missed-color: 0, 84%, 60%;       /* Red */
    --node-size: 10px;
    --node-size-active: 14px;
    --timeline-height: 48px;
    --transition-curve-elastic: cubic-bezier(0.68, -0.55, 0.27, 1.55);
    --transition-curve-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --transition-snap-duration: 0.35s;
  }

  /* Dark mode support */
  .dark {
    --bg-card: #1f2937;
  }

  .backup-recovery-component {
    width: 100%;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    background-color: var(--bg-card);
    box-shadow: 0 8px 13px -3px rgba(0, 0, 0, 0.05), 0 3px 5px -2px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.25rem;
    position: relative;
    overflow: hidden;
  }

  .dark .backup-recovery-component {
    border-color: #374151;
  }

  .controls {
    width: auto;
  }

  #dateTimeInput {
    font-family: inherit;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    background-color: #f9fafb;
    color: #111827;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    min-width: 240px;
  }

  .dark #dateTimeInput {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  #dateTimeInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2.5px rgba(59, 130, 246, 0.4);
  }

  .timeline-navigation {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    gap: 0.75rem;
  }

  .nav-arrow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    background-color: var(--bg-card);
    color: #6b7280;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out, border-color 0.15s ease-in-out, transform 0.15s ease-in-out;
    flex-shrink: 0;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  }

  .dark .nav-arrow {
    border-color: #4b5563;
    color: #9ca3af;
  }

  .nav-arrow:hover:not(:disabled) {
    background-color: #f3f4f6;
    color: #111827;
    transform: translateY(-1px);
  }

  .dark .nav-arrow:hover:not(:disabled) {
    background-color: #374151;
    color: #f9fafb;
  }

  .nav-arrow:active:not(:disabled) {
    transform: translateY(0px);
  }

  .nav-arrow:focus {
    outline: none;
    box-shadow: 0 0 0 2.5px rgba(59, 130, 246, 0.4);
    border-color: #3b82f6;
  }

  .nav-arrow:disabled {
    color: #9ca3af;
    background-color: var(--bg-card);
    border-color: #e5e7eb;
    cursor: not-allowed;
    opacity: 0.5;
    box-shadow: none;
  }

  .dark .nav-arrow:disabled {
    border-color: #4b5563;
  }

  .timeline-container {
    flex-grow: 1;
    height: var(--timeline-height);
    overflow: hidden;
    position: relative;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    cursor: grab;
  }

  .dark .timeline-container {
    border-color: #4b5563;
  }

  .timeline-container:active {
    cursor: grabbing;
  }

  .timeline-track {
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    will-change: transform;
  }

  .timeline-track::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #f3f4f6 0%, #d1d5db 50%, #f3f4f6 100%);
    transform: translateY(-50%);
  }

  .dark .timeline-track::before {
    background: linear-gradient(90deg, #374151 0%, #6b7280 50%, #374151 100%);
  }

  .timeline-label {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;
    z-index: 1;
  }

  .timeline-label::before {
    content: '';
    width: 1px;
    height: 14px;
    background-color: #9ca3af;
    margin-top: 8px;
  }

  .timeline-label-text {
    font-size: 0.7rem;
    color: #6b7280;
    padding: 1px 4px;
    background-color: var(--bg-card);
    border-radius: 3px;
    white-space: nowrap;
    margin-bottom: 2px;
  }

  .dark .timeline-label-text {
    color: #9ca3af;
  }

  .backup-node {
    position: absolute;
    width: var(--node-size);
    height: var(--node-size);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    transition: all 0.25s var(--transition-curve-elastic);
    z-index: 2;
    box-shadow: 0 0 0 2px var(--bg-card), 0 0 0 3px currentColor;
  }

  .backup-node:hover {
    transform: translateY(-50%) scale(1.25);
    box-shadow: 0 0 0 2px var(--bg-card), 0 0 0 4px currentColor;
  }

  .backup-node.selected {
    width: var(--node-size-active);
    height: var(--node-size-active);
    transform: translateY(-50%);
    box-shadow: 0 0 0 2px var(--bg-card), 0 0 0 4px currentColor;
    animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
  }

  .backup-node.manual { background-color: hsl(var(--manual-color)); color: hsl(var(--manual-color)); }
  .backup-node.scheduled { background-color: hsl(var(--scheduled-color)); color: hsl(var(--scheduled-color)); }
  .backup-node.missed { background-color: hsl(var(--missed-color)); color: hsl(var(--missed-color)); }

  .backup-details {
    width: 100%;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--bg-card);
  }

  .dark .backup-details {
    border-color: #4b5563;
  }

  .backup-details .details-placeholder {
    font-size: 0.9rem;
    color: #6b7280;
    text-align: center;
  }

  .dark .backup-details .details-placeholder {
    color: #9ca3af;
  }

  .backup-details .details-content {
    width: 100%;
    display: none;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  .backup-details.has-data .details-placeholder {
    display: none;
  }

  .backup-details.has-data .details-content {
    display: block;
  }

  .details-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
  }

  .backup-details h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    color: #111827;
    padding: 0.2rem 0;
  }

  .dark .backup-details h3 {
    color: #f9fafb;
  }

  .backup-details p {
    font-size: 0.875rem;
    color: #374151;
    margin: 0.35rem 0;
  }

  .dark .backup-details p {
    color: #d1d5db;
  }

  .backup-details p:last-child {
    margin-bottom: 0;
  }

  .backup-details p strong {
    font-weight: 600;
    color: #111827;
    margin-right: 0.25rem;
  }

  .dark .backup-details p strong {
    color: #f9fafb;
  }

  .restore-button {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 0.4rem 0.9rem;
    border-radius: 0.375rem;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .restore-button:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  .restore-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .restore-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4);
  }

  .restore-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  }

  .restore-overlay.visible {
    opacity: 1;
    visibility: visible;
  }

  .overlay-content {
    background-color: white;
    padding: 2.5rem;
    border-radius: 1rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1.2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
  }

  .dark .overlay-content {
    background-color: #1f2937;
  }

  .restore-overlay.visible .overlay-content {
    transform: scale(1);
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(59, 130, 246, 0.1);
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
  }

  .checkmark {
    width: 50px;
    height: 50px;
    stroke: #10b981;
    stroke-width: 3;
    stroke-linecap: round;
    display: none;
  }

  .checkmark-circle {
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
  }

  .checkmark-check {
    stroke-dasharray: 30;
    stroke-dashoffset: 30;
  }

  .restore-overlay.processing .spinner {
    display: block;
  }

  .restore-overlay.processing .checkmark {
    display: none;
  }

  .restore-overlay.processing #restoreStatusText {
    color: #111827;
  }

  .dark .restore-overlay.processing #restoreStatusText {
    color: #f9fafb;
  }

  .restore-overlay.success .overlay-content {
    animation: flash-bg 0.6s ease-in-out;
  }

  .restore-overlay.success .spinner {
    display: none;
  }

  .restore-overlay.success .checkmark {
    display: block;
  }

  .restore-overlay.success .checkmark-circle {
    animation: draw-circle 0.5s ease-in-out forwards;
  }

  .restore-overlay.success .checkmark-check {
    animation: draw-checkmark 0.2s 0.5s ease-in-out forwards;
  }

  #restoreStatusText {
    font-size: 1rem;
    margin: 0;
    color: #111827;
  }

  .dark #restoreStatusText {
    color: #f9fafb;
  }

  .restore-overlay.success #restoreStatusText {
    color: #10b981;
    font-weight: 600;
  }

  @media (max-width: 768px) {
    body {
      padding: 1rem;
    }
    .backup-recovery-component {
      padding: 1rem;
      gap: 1rem;
    }
    .timeline-navigation {
      gap: 0.5rem;
    }
    .backup-details {
      padding: 0.75rem;
    }
    .details-header {
      flex-direction: column;
    }
  }

  @media (max-width: 480px) {
    :root { --timeline-height: 40px; --node-size: 8px; --node-size-active: 12px; }
    .backup-recovery-component { padding: 0.75rem; gap: 0.75rem; }
    #dateTimeInput { font-size: 0.8rem; padding: 0.4rem 0.6rem; min-width: 180px;}
    .nav-arrow { width: 1.8rem; height: 1.8rem; }
    .nav-arrow svg { width: 0.9rem; height: 0.9rem; }
    .backup-details { padding: 0.75rem; min-height: 80px;}
    .details-header { flex-direction: row; align-items: center; gap: 0.5rem; margin-bottom: 0.6rem;}
    .restore-button { align-self: center; }
    .overlay-content { padding: 1.5rem; }
    #restoreStatusText { font-size: 0.9rem;}
    .spinner { width: 30px; height: 30px; border-width: 3px;}
    .checkmark { width: 40px; height: 40px; stroke-width: 2.5;}
  }

  .timeline-labels-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: center;
    max-width: 100%;
    position: relative;
  }
  
  .timeline-labels-container::before,
  .timeline-labels-container::after {
    content: "•••";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 12px;
    letter-spacing: 1px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    background-color: var(--bg-card);
    padding: 2px 4px;
    border-radius: 4px;
    z-index: 5;
    margin-top: 0;
  }
  
  .timeline-labels-container::before {
    left: -36px;
  }
  
  .timeline-labels-container::after {
    right: -36px;
  }
  
  .timeline-labels-container.has-hidden-start::before,
  .timeline-labels-container.has-hidden-end::after {
    opacity: 1;
  }
  
  .dark .timeline-labels-container::before,
  .dark .timeline-labels-container::after {
    color: #9ca3af;
  }
  
  .timeline-label-button {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
    background-color: #f9fafb;
    color: #374151;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
  }
  
  .dark .timeline-label-button {
    border-color: #4b5563;
    background-color: #374151;
    color: #d1d5db;
  }
  
  .timeline-label-button:hover {
    background-color: #f3f4f6;
    transform: translateY(-1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .dark .timeline-label-button:hover {
    background-color: #4b5563;
  }
  
  .timeline-label-button.active {
    background-color: #171717;
    border-color: #171717;
    color: #ffffff;
    font-weight: 500;
  }
  
  .dark .timeline-label-button.active {
    background-color: #000000;
    border-color: #262626;
    color: #ffffff;
  }
</style>
{% endblock %}

{% block content %}
<div class="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <!-- Create Backup Section -->
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Create New Backup</h2>
      <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
        Create a new backup of your entire database. This process runs in the background and may take a few minutes depending on database size.
      </p>
      
      <div class="flex items-center space-x-4">
        <input type="text" id="backup-name" placeholder="Backup name (optional)" 
               class="flex-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
        
        <button id="create-backup-btn" 
                class="backup-action inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Create Backup
        </button>
      </div>
    </div>
    
    <!-- Schedule Daily Backups -->
    <div class="mb-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-900">
      <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Daily Automatic Backups</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
        Configure automatic daily backups at a specific time.
      </p>
      
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <label for="backup-time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Time (24h format)</label>
          <input type="time" id="backup-time" 
                 class="mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
        </div>
        
        <button id="schedule-backup-btn" 
                class="backup-action inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 mt-6">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Schedule
        </button>
      </div>
      
      <div id="scheduler-status" class="mt-4 text-sm text-gray-600 dark:text-gray-400 hidden">
        <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded">
          <div class="flex items-center mb-1">
            <svg class="h-4 w-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="font-medium">Scheduler Active</span>
          </div>
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span class="text-gray-500 dark:text-gray-500">Next backup:</span>
              <span id="next-backup-time">-</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-500">Last backup:</span>
              <span id="last-backup-time">-</span>
            </div>
          </div>
          <div class="mt-2 flex justify-end">
            <button id="check-missed-backups-btn" class="text-xs text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300">
              Check for missed backups
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Backup List Section -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Existing Backups</h2>
        <button id="refresh-backups-btn"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-700 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh
        </button>
      </div>
      
      <!-- Advanced Backup Recovery Component -->
      <div class="backup-recovery-component" id="backupComponent">
        <div class="controls">
          <input type="datetime-local" id="dateTimeInput" step="1">
        </div>

        <div class="timeline-navigation">
           <button class="nav-arrow left" id="navLeft" aria-label="Previous Backup">
               <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
           </button>
           <div class="timeline-container" id="timelineContainer">
               <div class="timeline-track" id="timelineTrack">
                   <!-- Backup nodes will be injected here by JS -->
               </div>
           </div>
           <button class="nav-arrow right" id="navRight" aria-label="Next Backup">
               <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
           </button>
        </div>
        
        <div class="timeline-labels-container" id="timelineLabelsContainer">
            <!-- Time period labels will be inserted here -->
        </div>

        <div class="backup-details" id="backupDetails">
            <p class="details-placeholder">Select a backup point</p>
            <div class="details-content">
                <div class="details-header">
                    <h3 id="detailsType"></h3>
                    <button id="restoreButton" class="restore-button">Restore</button>
                </div>
                <p><strong>File:</strong> <span id="detailsId"></span></p>
                <p><strong>Date:</strong> <time id="detailsDate"></time></p>
                <p><strong>Size:</strong> <span id="detailsSize"></span></p>
                <p style="display: none;"><strong>Details:</strong> <span id="detailsInfo"></span></p>
            </div>
        </div>
      </div>

      <!-- Restore Action Overlay -->
      <div class="restore-overlay" id="restoreOverlay">
          <div class="overlay-content">
              <div class="spinner" id="restoreSpinner"></div>
              <svg class="checkmark" id="restoreCheckmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                  <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>
                  <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
              </svg>
              <p id="restoreStatusText"></p>
          </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal for backup operations -->
<div id="backup-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>
    
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    
    <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div id="modal-icon" class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10">
            <!-- Icon will be dynamically inserted here -->
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
              <!-- Title will be dynamically inserted here -->
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500 dark:text-gray-400" id="modal-message">
                <!-- Message will be dynamically inserted here -->
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <button type="button" id="modal-confirm-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">
          Confirm
        </button>
        <button type="button" id="modal-cancel-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Toast notifications -->
<div id="toast-container" class="fixed bottom-0 right-0 p-4 z-50 space-y-4">
  <!-- Toast notifications will be dynamically inserted here -->
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Debug mode only when explicitly enabled
    const DEBUG = false;
    
    // Simple debug logger
    const debugLog = DEBUG ? (message, data) => {
      console.log(`[DEBUG] ${message}`, data || '');
    } : () => {};
    
    // DOM elements
    const createBackupBtn = document.getElementById('create-backup-btn');
    const backupNameInput = document.getElementById('backup-name');
    const refreshBackupsBtn = document.getElementById('refresh-backups-btn');
    const backupTimeInput = document.getElementById('backup-time');
    const scheduleBackupBtn = document.getElementById('schedule-backup-btn');
    const schedulerStatus = document.getElementById('scheduler-status');
    const nextBackupTime = document.getElementById('next-backup-time');
    const lastBackupTime = document.getElementById('last-backup-time');
    const checkMissedBackupsBtn = document.getElementById('check-missed-backups-btn');
    
    // Toast notifications elements
    const toastContainer = document.getElementById('toast-container');
    
    // Modal elements
    const backupModal = document.getElementById('backup-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const modalIcon = document.getElementById('modal-icon');
    const modalConfirmBtn = document.getElementById('modal-confirm-btn');
    const modalCancelBtn = document.getElementById('modal-cancel-btn');
    
    // Timeline component elements
    const backupComponent = document.getElementById('backupComponent');
    const timelineContainer = document.getElementById('timelineContainer');
    const timelineTrack = document.getElementById('timelineTrack');
    const dateTimeInput = document.getElementById('dateTimeInput');
    const backupDetailsEl = document.getElementById('backupDetails');
    const detailsContent = backupDetailsEl.querySelector('.details-content');
    const detailsType = document.getElementById('detailsType');
    const detailsId = document.getElementById('detailsId');
    const detailsDate = document.getElementById('detailsDate');
    const detailsSize = document.getElementById('detailsSize');
    const detailsInfo = document.getElementById('detailsInfo');
    const restoreButton = document.getElementById('restoreButton');
    const navLeft = document.getElementById('navLeft');
    const navRight = document.getElementById('navRight');
    const restoreOverlay = document.getElementById('restoreOverlay');
    const restoreStatusText = document.getElementById('restoreStatusText');
    const restoreSpinner = document.getElementById('restoreSpinner');
    const restoreCheckmark = document.getElementById('restoreCheckmark');
    const timelineLabelsContainer = document.getElementById('timelineLabelsContainer');

    // Timeline constants
    const nodeSpacing = 90;
    const restoreDuration = 1500; // Processing time in ms
    const successDuration = 1200; // Time success message stays visible
    
    // Timeline vars
    let backupData = [];
    let trackWidth = 0;
    let isDragging = false;
    let startX, currentTranslateX = 0;
    let timelineRect;
    let nodes = [];
    let selectedIndex = -1;
    let isRestoring = false; // Prevent multiple restore clicks
    let activeBackupFile = null;
    
    // Simplified toast notification system
    const showToast = (message, type = 'info') => {
      const toast = document.createElement('div');
      const colorClass = type === 'success' ? 'bg-green-500' : 
                         type === 'error' ? 'bg-red-500' : 
                         type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';
                         
      toast.className = `${colorClass} text-white px-4 py-3 rounded shadow-lg flex items-center justify-between transition-all duration-300 ease-in-out`;
      toast.innerHTML = `
        <div class="mr-2">${message}</div>
        <button class="text-white hover:text-gray-100 focus:outline-none">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      `;
      
      toastContainer.appendChild(toast);
      
      // Fade in animation
      setTimeout(() => {
        toast.style.opacity = '1';
      }, 10);
      
      // Add click handler to close button
      toast.querySelector('button').addEventListener('click', () => {
        toast.style.opacity = '0';
        setTimeout(() => {
          toast.remove();
        }, 300);
      });
      
      // Auto remove after 5 seconds
      setTimeout(() => {
        if (toast.parentNode) {
          toast.style.opacity = '0';
          setTimeout(() => {
            if (toast.parentNode) {
              toast.remove();
            }
          }, 300);
        }
      }, 5000);
    };
    
    // Fetch all backups and initialize the timeline
    const fetchBackups = async () => {
      try {
        debugLog('Fetching backups');
        const response = await fetch('/api/database-backup/list');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch backups: ${response.status} ${response.statusText}`);
        }
        
        const backups = await response.json();
        debugLog('Fetched backups', backups);
        
        // Convert API responses to timeline data format
        backupData = backups.map((backup, index) => {
          // Determine backup type based on filename or tags
          let type = 'manual';
          if (backup.filename.includes('scheduled')) {
            type = 'scheduled';
          } 
          if (backup.filename.includes('missed')) {
            type = 'missed';
          }
          
          // Format date and details
          const date = new Date(backup.created_at);
          const details = backup.description || 'MongoDB backup';
          
          return {
            id: backup.filename,
            timestamp: backup.created_at,
            date,
            type,
            details,
            size: backup.size,
            originalIndex: index
          };
        })
        .sort((a, b) => a.date - b.date)
        .map((b, index) => ({ ...b, sortedIndex: index }));
        
        // Initialize the timeline
        initializeTimeline();
      } catch (error) {
        console.error('Error fetching backups:', error);
        showToast('Failed to load backups. Please try again.', 'error');
      }
    };
    
    function initializeTimeline() {
      if (!backupData || backupData.length === 0) {
        timelineTrack.innerHTML = '';
        backupDetailsEl.classList.remove('has-data');
        navLeft.disabled = true;
        navRight.disabled = true;
        timelineLabelsContainer.innerHTML = '';
        return;
      };

      timelineRect = timelineContainer.getBoundingClientRect();
      const padding = timelineRect.width / 2;
      trackWidth = (backupData.length - 1) * nodeSpacing + padding * 2;
      timelineTrack.style.width = `${trackWidth}px`;
      timelineTrack.innerHTML = '';
      timelineLabelsContainer.innerHTML = '';
      nodes = [];

      // Group backups by year-month for timeline labels
      const timeLabels = {};
      backupData.forEach((backup) => {
        const date = backup.date;
        const year = date.getFullYear();
        const month = date.getMonth();
        const yearMonth = `${year}-${month}`;
        
        if (!timeLabels[yearMonth]) {
          timeLabels[yearMonth] = {
            date: new Date(year, month, 1),
            count: 0,
            index: backup.sortedIndex
          };
        }
        timeLabels[yearMonth].count++;
      });

      // Add time labels to the track
      Object.values(timeLabels).forEach((labelInfo) => {
        const labelEl = document.createElement('div');
        labelEl.classList.add('timeline-label');
        
        const labelText = document.createElement('div');
        labelText.classList.add('timeline-label-text');
        
        // Format date based on whether all backups are in the same year
        const allSameYear = Object.values(timeLabels).every(
          label => label.date.getFullYear() === labelInfo.date.getFullYear()
        );
        
        if (allSameYear) {
          // If all in same year, just show month name
          labelText.textContent = labelInfo.date.toLocaleDateString(undefined, { month: 'short' });
        } else {
          // Show month and year
          labelText.textContent = labelInfo.date.toLocaleDateString(undefined, { 
            month: 'short', 
            year: '2-digit' 
          });
        }
        
        labelEl.appendChild(labelText);
        
        // Position the label
        const positionPx = padding + labelInfo.index * nodeSpacing;
        labelEl.style.left = `${positionPx}px`;
        
        timelineTrack.appendChild(labelEl);
        
        // Create clickable label button
        const labelButton = document.createElement('button');
        labelButton.classList.add('timeline-label-button');
        labelButton.dataset.index = labelInfo.index;
        labelButton.textContent = labelText.textContent + (labelInfo.count > 1 ? ` (${labelInfo.count})` : '');
        
        console.log(`Creating label button: "${labelButton.textContent}" with index ${labelInfo.index}`);
        
        labelButton.addEventListener('click', () => {
          console.log(`Label button clicked: ${labelButton.textContent}`);
          selectNodeByIndex(labelInfo.index);
          updateActiveLabelButton(labelInfo.index);
        });
        
        timelineLabelsContainer.appendChild(labelButton);
      });

      // Add backup nodes to the timeline
      backupData.forEach((backup) => {
        const nodeEl = document.createElement('div');
        nodeEl.classList.add('backup-node', backup.type);
        nodeEl.dataset.id = backup.id;
        nodeEl.dataset.index = backup.sortedIndex;
        const positionPx = padding + backup.sortedIndex * nodeSpacing;
        nodeEl.style.left = `${positionPx}px`;
        nodeEl.addEventListener('click', () => handleNodeClick(backup.sortedIndex));
        timelineTrack.appendChild(nodeEl);
        nodes.push({ element: nodeEl, data: backup });
      });

      addEventListeners();
      selectNodeByIndex(backupData.length - 1, true);
      
      console.log('Timeline initialized, disabling default label activation');
      
      // Apply a small delay to force proper active label calculation
      setTimeout(() => {
        const currentIndex = selectedIndex;
        // Clear active class from all buttons first
        const buttons = timelineLabelsContainer.querySelectorAll('.timeline-label-button');
        buttons.forEach(button => button.classList.remove('active'));
        
        // Remove hidden indicators initially
        timelineLabelsContainer.classList.remove('has-hidden-start', 'has-hidden-end');
        
        // Initially hide all buttons except the last few (most recent)
        if (buttons.length > 5) {
          const buttonIndices = Array.from(buttons).map(button => parseInt(button.dataset.index)).sort((a, b) => a - b);
          buttons.forEach(button => {
            const buttonIndex = parseInt(button.dataset.index);
            const buttonPosition = buttonIndices.indexOf(buttonIndex);
            
            // Only show the last 5 buttons initially (most recent periods)
            if (buttonPosition >= buttonIndices.length - 5) {
              button.style.display = '';
            } else {
              button.style.display = 'none';
            }
          });
          
          // Add hidden start indicator if we're hiding some at the beginning
          if (buttonIndices.length > 5) {
            timelineLabelsContainer.classList.add('has-hidden-start');
          }
        }
        
        // Then update correctly
        if (currentIndex >= 0) {
          updateActiveLabelButton(currentIndex);
        }
      }, 100);
    }
    
    // Update the active state of label buttons
    function updateActiveLabelButton(nodeIndex) {
      const buttons = timelineLabelsContainer.querySelectorAll('.timeline-label-button');
      
      console.log('Updating active label for nodeIndex:', nodeIndex);
      
      // First remove active class from all buttons
      buttons.forEach(button => {
        button.classList.remove('active');
      });
      
      // Remove hidden indicators
      timelineLabelsContainer.classList.remove('has-hidden-start', 'has-hidden-end');
      
      // Only set active class if nodeIndex is valid and not -1 (initial state)
      if (nodeIndex >= 0 && nodeIndex < nodes.length) {
        // Create an array of button indices sorted
        const buttonIndices = Array.from(buttons).map(button => parseInt(button.dataset.index)).sort((a, b) => a - b);
        
        // Find which range the current nodeIndex falls into
        let foundButtonIndex = buttonIndices[0]; // Default to first button
        let foundButtonPosition = 0; // Position in the array
        
        for (let i = 0; i < buttonIndices.length; i++) {
          const currentButtonIndex = buttonIndices[i];
          const nextButtonIndex = i < buttonIndices.length - 1 ? buttonIndices[i + 1] : Infinity;
          
          console.log(`Checking range: ${currentButtonIndex} <= ${nodeIndex} < ${nextButtonIndex}`);
          
          if (nodeIndex >= currentButtonIndex && nodeIndex < nextButtonIndex) {
            foundButtonIndex = currentButtonIndex;
            foundButtonPosition = i;
            break;
          }
        }
        
        console.log('Found button index for range:', foundButtonIndex, 'at position:', foundButtonPosition);
        
        // Calculate visible range (current ±2)
        const minVisible = Math.max(0, foundButtonPosition - 2);
        const maxVisible = Math.min(buttonIndices.length - 1, foundButtonPosition + 2);
        
        console.log(`Visible range: ${minVisible} to ${maxVisible}`);
        
        // Show/hide and activate buttons based on proximity
        buttons.forEach((button, idx) => {
          const buttonIndex = parseInt(button.dataset.index);
          const buttonPosition = buttonIndices.indexOf(buttonIndex);
          
          // Hide or show based on proximity to active button
          if (buttonPosition >= minVisible && buttonPosition <= maxVisible) {
            button.style.display = '';
          } else {
            button.style.display = 'none';
          }
          
          // Activate the found button
          if (buttonIndex === foundButtonIndex) {
            console.log('Activating button:', button.textContent);
            button.classList.add('active');
          }
        });
        
        // Add hidden indicators if needed
        if (minVisible > 0) {
          timelineLabelsContainer.classList.add('has-hidden-start');
        }
        
        if (maxVisible < buttonIndices.length - 1) {
          timelineLabelsContainer.classList.add('has-hidden-end');
        }
      }
    }
    
    function selectNodeByIndex(index, immediate = false) {
      if (index < 0 || index >= nodes.length) {
        if (selectedIndex !== -1) setTimelinePosition(calculateCenteringTranslateX(selectedIndex), immediate);
        return;
      }
      
      if (index === selectedIndex && !immediate) {
        setTimelinePosition(calculateCenteringTranslateX(selectedIndex), immediate);
        return;
      }
      
      const previousIndex = selectedIndex;
      if (previousIndex !== -1 && nodes[previousIndex]) {
        nodes[previousIndex].element.classList.remove('selected');
      }
      
      selectedIndex = index;
      const selectedNode = nodes[selectedIndex];
      selectedNode.element.classList.add('selected');
      const backup = selectedNode.data;
      
      if (previousIndex !== selectedIndex || immediate) {
        displayBackupDetails(backup);
        updateDateTimeInput(backup.date.getTime());
      }
      
      const targetTranslateX = calculateCenteringTranslateX(selectedIndex);
      setTimelinePosition(targetTranslateX, immediate);
      navLeft.disabled = selectedIndex === 0;
      navRight.disabled = selectedIndex === nodes.length - 1;
      
      // Update active label button
      updateActiveLabelButton(selectedIndex);
    }

    function calculateCenteringTranslateX(targetIndex) {
      const containerWidth = timelineRect.width;
      const padding = containerWidth / 2;
      const nodeCenterPx = padding + targetIndex * nodeSpacing;
      let targetTranslateX = containerWidth / 2 - nodeCenterPx;
      const maxTranslateX = 0;
      const minTranslateX = Math.min(0, containerWidth - trackWidth);
      targetTranslateX = Math.max(minTranslateX, Math.min(maxTranslateX, targetTranslateX));
      return targetTranslateX;
    }

    function setTimelinePosition(targetTranslateX, immediate = false) {
      currentTranslateX = targetTranslateX;
      timelineTrack.style.transition = immediate ? 'none' : `transform var(--transition-snap-duration) var(--transition-curve-smooth)`;
      timelineTrack.style.transform = `translateX(${currentTranslateX}px)`;
    }

    function updateDateTimeInput(timestamp) {
      if (!timestamp) {
        dateTimeInput.value = ''; return;
      }
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      dateTimeInput.value = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    }

    // Format file size to human-readable format
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      
      return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function displayBackupDetails(backup) {
      if (!backup) {
        backupDetailsEl.classList.remove('has-data');
        detailsContent.style.opacity = 0;
        setTimeout(() => {
          if (!backupDetailsEl.classList.contains('has-data')) {
            detailsContent.style.display = 'none';
          }
        }, 200);
        return;
      }
      
      activeBackupFile = backup.id; // Store for restore operation
      detailsType.textContent = backup.type.charAt(0).toUpperCase() + backup.type.slice(1) + ' Backup';
      detailsId.textContent = backup.id;
      detailsDate.textContent = backup.date.toLocaleString();
      detailsDate.setAttribute('datetime', backup.timestamp);
      detailsSize.textContent = formatFileSize(backup.size || 0);
      detailsInfo.textContent = backup.details;
      detailsContent.style.display = 'block';
      backupDetailsEl.classList.add('has-data');
      requestAnimationFrame(() => { detailsContent.style.opacity = 1; });
    }

    function findClosestNodeByTime(targetTimestamp) {
      if (!backupData || backupData.length === 0) return null;
      let closestBackup = null;
      let minDiff = Infinity;
      backupData.forEach((backup) => {
        const diff = Math.abs(backup.date.getTime() - targetTimestamp);
        if (diff < minDiff) { minDiff = diff; closestBackup = backup; }
      });
      return closestBackup;
    }

    function findClosestNodeByPosition(targetTranslateX) {
      if (!nodes || nodes.length === 0) return -1;
      const containerCenterPx = timelineRect.width / 2;
      const trackCenterPx = containerCenterPx - targetTranslateX;
      let closestIndex = -1;
      let minDiff = Infinity;
      const padding = timelineRect.width / 2;
      nodes.forEach((node, index) => {
        const nodeCenterPx = padding + index * nodeSpacing;
        const diff = Math.abs(nodeCenterPx - trackCenterPx);
        if (diff < minDiff) { minDiff = diff; closestIndex = index; }
      });
      return closestIndex;
    }

    function handleNodeClick(index) {
      if (isRestoring) return; // Prevent interaction during restore
      selectNodeByIndex(index);
    }

    function startDrag(e) {
      if (nodes.length <= 1 || isRestoring) return;
      isDragging = true;
      startX = (e.touches ? e.touches[0].clientX : e.clientX) - currentTranslateX;
      timelineContainer.style.cursor = 'grabbing';
      timelineTrack.style.transition = 'none';
      // Always return undefined, not true
      return false;
    }

    function drag(e) {
      if (!isDragging || isRestoring) return;
      e.preventDefault();
      const currentX = e.touches ? e.touches[0].clientX : e.clientX;
      let newTranslateX = currentX - startX;
      const minValidTranslateX = calculateCenteringTranslateX(nodes.length - 1);
      const maxValidTranslateX = calculateCenteringTranslateX(0);
      const buffer = timelineRect.width * 0.3;
      const minClamp = minValidTranslateX - buffer;
      const maxClamp = maxValidTranslateX + buffer;
      newTranslateX = Math.max(minClamp, Math.min(maxClamp, newTranslateX));
      currentTranslateX = newTranslateX;
      timelineTrack.style.transform = `translateX(${currentTranslateX}px)`;
      // Always return undefined, not true
      return false;
    }

    function endDrag() {
      if (!isDragging || isRestoring) return;
      isDragging = false;
      timelineContainer.style.cursor = 'grab';
      const closestIndex = findClosestNodeByPosition(currentTranslateX);
      if (closestIndex !== -1) {
        selectNodeByIndex(closestIndex);
      } else if (nodes.length > 0) {
        selectNodeByIndex(nodes.length - 1);
      }
      // Always return undefined, not true
      return false;
    }

    function navigate(direction) {
      if (nodes.length === 0 || isRestoring) return;
      let newIndex = selectedIndex + direction;
      newIndex = Math.max(0, Math.min(nodes.length - 1, newIndex));
      selectNodeByIndex(newIndex);
    }

    async function handleRestoreClick() {
      if (selectedIndex === -1 || !nodes[selectedIndex] || isRestoring || !activeBackupFile) {
        return; // No node selected or already restoring
      }

      const backupToRestore = nodes[selectedIndex].data;
      isRestoring = true;
      restoreButton.disabled = true; // Disable button during restore

      // Show Overlay - Processing State
      restoreOverlay.classList.remove('success'); // Ensure success state is removed
      restoreOverlay.classList.add('processing', 'visible');
      restoreStatusText.textContent = `Restoring backup from ${backupToRestore.date.toLocaleTimeString()}`;

      // Call API to restore backup
      try {
        const response = await fetch(`/api/database-backup/restore/${activeBackupFile}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to restore backup');
        }

        const result = await response.json();
        debugLog('Restore response:', result);

        // Transition to Success State
        restoreOverlay.classList.remove('processing');
        restoreOverlay.classList.add('success');
        restoreStatusText.textContent = 'Restore Successful!';

        // Hide overlay after a delay
        setTimeout(() => {
          restoreOverlay.classList.remove('visible');
          isRestoring = false;
          restoreButton.disabled = false;
          // Reset overlay classes fully after fade out
          setTimeout(() => {
            restoreOverlay.classList.remove('success');
          }, 300); // Match overlay fade-out time
        }, successDuration);

      } catch (error) {
        console.error('Error restoring backup:', error);
        restoreOverlay.classList.remove('processing');
        restoreOverlay.classList.add('visible');
        restoreStatusText.textContent = 'Restore Failed. Please try again.';
        restoreStatusText.style.color = '#ef4444'; // Red color for error
        
        // Hide overlay after a delay
        setTimeout(() => {
          restoreOverlay.classList.remove('visible');
          isRestoring = false;
          restoreButton.disabled = false;
          // Reset text color
          restoreStatusText.style.color = '';
        }, successDuration);
      }
    }

    function handleKeyDown(e) {
      if (isRestoring) { // Block all keyboard interaction during restore
        e.preventDefault();
        return;
      }

      if (document.activeElement === dateTimeInput || document.activeElement === restoreButton) {
        if(document.activeElement === dateTimeInput && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) { return; }
        if (document.activeElement !== dateTimeInput || (e.key !== 'ArrowLeft' && e.key !== 'ArrowRight')) { return; }
      }

      switch (e.key) {
        case 'ArrowLeft': e.preventDefault(); navigate(-1); break;
        case 'ArrowRight': e.preventDefault(); navigate(1); break;
        case ' ': case 'Enter':
          if (backupDetailsEl.classList.contains('has-data') && document.activeElement !== restoreButton) {
            e.preventDefault();
            handleRestoreClick();
          }
          break;
      }
    }

    function handleDateTimeBlur() {
      if (isRestoring) return;
      try {
        const inputVal = dateTimeInput.value;
        if (!inputVal) {
          if(selectedIndex !== -1) updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
          return;
        };
        const inputDate = new Date(inputVal);
        if (isNaN(inputDate.getTime())) {
          if(selectedIndex !== -1) updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
          return;
        };
        const closestBackup = findClosestNodeByTime(inputDate.getTime());
        if (closestBackup) {
          selectNodeByIndex(closestBackup.sortedIndex);
        } else if (selectedIndex !== -1) {
          updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
        }
      } catch (e) {
        console.error("Error parsing date on blur:", e);
        if(selectedIndex !== -1) updateDateTimeInput(nodes[selectedIndex].data.date.getTime());
      }
    }

    function handleResize() {
      if (!backupData || backupData.length === 0 || isRestoring) return;
      timelineRect = timelineContainer.getBoundingClientRect();
      const padding = timelineRect.width / 2;
      trackWidth = (backupData.length - 1) * nodeSpacing + padding * 2;
      timelineTrack.style.width = `${trackWidth}px`;
      nodes.forEach((node, index) => {
        const positionPx = padding + index * nodeSpacing;
        node.element.style.left = `${positionPx}px`;
      });
      if (selectedIndex !== -1) {
        const targetTranslateX = calculateCenteringTranslateX(selectedIndex);
        setTimelinePosition(targetTranslateX, true);
      }
    }

    function addEventListeners() {
      // Timeline component event listeners with explicit false returns
      timelineContainer.addEventListener('mousedown', function(e) {
        startDrag(e);
        return false; // Ensure we don't return true
      });
      
      document.addEventListener('mousemove', function(e) {
        drag(e);
        return false; // Ensure we don't return true
      });
      
      document.addEventListener('mouseup', function(e) {
        endDrag();
        return false; // Ensure we don't return true
      });
      
      timelineContainer.addEventListener('mouseleave', function(e) {
        endDrag();
        return false; // Ensure we don't return true
      });
      
      timelineContainer.addEventListener('touchstart', function(e) {
        startDrag(e);
        if (isDragging) e.preventDefault(); // Only prevent default if we're dragging
        return false; // Ensure we don't return true
      }, { passive: false });
      
      document.addEventListener('touchmove', function(e) {
        drag(e);
        return false; // Ensure we don't return true
      }, { passive: false });
      
      document.addEventListener('touchend', function(e) {
        endDrag();
        return false; // Ensure we don't return true
      });
      
      document.addEventListener('touchcancel', function(e) {
        endDrag();
        return false; // Ensure we don't return true
      });
      
      navLeft.addEventListener('click', () => {
        navigate(-1);
        return false;
      });
      
      navRight.addEventListener('click', () => {
        navigate(1);
        return false;
      });
      
      restoreButton.addEventListener('click', () => {
        handleRestoreClick();
        return false;
      });
      
      dateTimeInput.addEventListener('blur', () => {
        handleDateTimeBlur();
        return false;
      });
      
      dateTimeInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') { 
          handleDateTimeBlur(); 
          dateTimeInput.blur(); 
        }
        return false;
      });
      
      document.addEventListener('keydown', (e) => {
        handleKeyDown(e);
        return false;
      });
      
      window.addEventListener('resize', debounce(handleResize, 200));
    }

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => { clearTimeout(timeout); func(...args); };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
    
    // Create a new backup
    const createBackup = async () => {
      const backupName = backupNameInput.value.trim();
      
      try {
        const response = await fetch('/api/database-backup/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ backup_name: backupName || undefined })
        });
        
        if (!response.ok) {
          throw new Error(`Failed to create backup: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        debugLog('Create backup response', result);
        
        showToast('Backup started in background. It will be available once completed.', 'success');
        
        // Clear the input field
        backupNameInput.value = '';
        
        // Schedule a refresh after a delay
        setTimeout(() => {
          fetchBackups();
        }, 5000);
      } catch (error) {
        console.error('Error creating backup:', error);
        showToast('Failed to create backup. Please try again.', 'error');
      }
    };
    
    // Schedule a daily backup
    const scheduleBackup = async () => {
      const timeValue = backupTimeInput.value;
      
      if (!timeValue) {
        showToast('Please select a time for the backup.', 'warning');
        return;
      }
      
      // Extract hour and minute
      const [hours, minutes] = timeValue.split(':').map(Number);
      
      try {
        const response = await fetch('/api/database-backup/schedule', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ hour: hours, minute: minutes })
        });
        
        if (!response.ok) {
          throw new Error(`Failed to schedule backup: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        debugLog('Schedule backup response', result);
        
        showToast(`Daily backup scheduled for ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`, 'success');
        
        // Update scheduler status display
        checkSchedulerStatus();
      } catch (error) {
        console.error('Error scheduling backup:', error);
        showToast('Failed to schedule backup. Please try again.', 'error');
      }
    };
    
    // Check missed backups
    const checkMissedBackups = async () => {
      try {
        const response = await fetch('/api/database-backup/run-missed-backup-check', {
          method: 'POST'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to check missed backups: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        debugLog('Missed backup check response', result);
        
        showToast('Missed backup check completed successfully.', 'success');
        
        // Refresh after a delay
        setTimeout(() => {
          fetchBackups();
        }, 3000);
      } catch (error) {
        console.error('Error checking missed backups:', error);
        showToast('Failed to check missed backups. Please try again.', 'error');
      }
    };
    
    // Check scheduler status
    const checkSchedulerStatus = async () => {
      try {
        const response = await fetch('/api/database-backup/debug-scheduler');
        
        if (!response.ok) {
          throw new Error(`Failed to check scheduler status: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        debugLog('Scheduler status', result);
        
        if (result.scheduler_info.config && result.scheduler_info.config.daily_backup && result.scheduler_info.config.daily_backup.enabled) {
          schedulerStatus.classList.remove('hidden');
          
          // Update next backup time
          if (result.scheduler_info.formatted_time) {
            nextBackupTime.textContent = result.scheduler_info.formatted_time;
          }
          
          // Update last backup time if available (you may need to fetch this from your last backup)
          if (backupData.length > 0) {
            const lastScheduledBackup = [...backupData]
              .filter(b => b.type === 'scheduled')
              .sort((a, b) => b.date - a.date)[0];
              
            if (lastScheduledBackup) {
              lastBackupTime.textContent = lastScheduledBackup.date.toLocaleString();
            } else {
              lastBackupTime.textContent = 'None yet';
            }
          } else {
            lastBackupTime.textContent = 'None yet';
          }
          
          // Update time input to match current schedule
          if (result.scheduler_info.formatted_time) {
            backupTimeInput.value = result.scheduler_info.formatted_time;
          }
        } else {
          schedulerStatus.classList.add('hidden');
        }
      } catch (error) {
        console.error('Error checking scheduler status:', error);
        schedulerStatus.classList.add('hidden');
      }
    };
    
    // Modal handling
    const showModal = (title, message, iconHtml, confirmText, confirmClass, cancelText, onConfirm) => {
      modalTitle.textContent = title;
      modalMessage.textContent = message;
      modalIcon.innerHTML = iconHtml;
      modalConfirmBtn.textContent = confirmText;
      modalConfirmBtn.className = `w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm ${confirmClass}`;
      modalCancelBtn.textContent = cancelText;
      
      // Set confirm action
      const confirmHandler = () => {
        onConfirm();
        backupModal.classList.add('hidden');
        modalConfirmBtn.removeEventListener('click', confirmHandler);
      };
      
      modalConfirmBtn.addEventListener('click', confirmHandler);
      
      // Set cancel action
      const cancelHandler = () => {
        backupModal.classList.add('hidden');
        modalCancelBtn.removeEventListener('click', cancelHandler);
      };
      
      modalCancelBtn.addEventListener('click', cancelHandler);
      
      // Show modal
      backupModal.classList.remove('hidden');
    };
    
    // Event listeners
    createBackupBtn.addEventListener('click', createBackup);
    scheduleBackupBtn.addEventListener('click', scheduleBackup);
    refreshBackupsBtn.addEventListener('click', fetchBackups);
    checkMissedBackupsBtn.addEventListener('click', checkMissedBackups);
    
    // Initialization
    fetchBackups();
    checkSchedulerStatus();
  });
</script>
{% endblock %} 
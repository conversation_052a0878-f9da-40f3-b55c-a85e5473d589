// Function to show notifications
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const notificationMessage = document.getElementById('notification-message');
    const notificationIcon = document.getElementById('notification-icon');
    
    // Set message
    notificationMessage.textContent = message;
    
    // Set icon and colors based on type
    if (type === 'success') {
        notification.classList.add('bg-green-100', 'dark:bg-green-800');
        notificationIcon.classList.add('fa-check-circle', 'text-green-500', 'dark:text-green-400');
    } else {
        notification.classList.add('bg-red-100', 'dark:bg-red-800');
        notificationIcon.classList.add('fa-exclamation-circle', 'text-red-500', 'dark:text-red-400');
    }
    
    // Show notification
    notification.classList.remove('hidden');
    notification.classList.add('animate-slide-in');
    
    // Hide after 5 seconds
    setTimeout(() => {
        notification.classList.add('animate-fade-out');
        setTimeout(() => {
            notification.classList.add('hidden');
            notification.classList.remove('animate-slide-in', 'animate-fade-out', 'bg-green-100', 'bg-red-100', 'dark:bg-green-800', 'dark:bg-red-800');
            notificationIcon.classList.remove('fa-check-circle', 'fa-exclamation-circle', 'text-green-500', 'text-red-500', 'dark:text-green-400', 'dark:text-red-400');
        }, 300);
    }, 5000);
}

// Function to validate the tool code
function validateToolCode(code) {
    const errors = [];
    
    // Check if code defines a class
    if (!code.includes('class') || !code.includes('BaseTool')) {
        errors.push("Code must define a class that inherits from BaseTool");
    }
    
    // Check for required fields
    if (!code.includes('name: str = Field(') || !code.includes('description: str = Field(')) {
        errors.push("Tool code must include 'name' and 'description' fields");
    }
    
    // Check for _arun method
    if (!code.includes('async def _arun(')) {
        errors.push("Tool must implement async _arun method");
    }
    
    // Check for director_state imports
    if (!code.includes('from software.ai.graph.director_state import')) {
        errors.push("Tool should import utilities from software.ai.graph.director_state");
    }
    
    return { 
        valid: errors.length === 0,
        errors
    };
}

// Function to extract tool data from code
function extractToolData(code, toolName) {
    const validation = validateToolCode(code);
    
    if (!validation.valid) {
        return { valid: false, errors: validation.errors };
    }
    
    // Validate tool name
    if (!toolName || !/^[A-Z][A-Za-z0-9]*Tool$/.test(toolName)) {
        return { 
            valid: false, 
            errors: ["Invalid tool name. Must be PascalCase and end with 'Tool'"]
        };
    }
    
    return {
        valid: true,
        name: toolName,
        code: code
    };
}

// Initialize handler
document.addEventListener('DOMContentLoaded', () => {
    const saveButton = document.getElementById('saveToolBtn');
    const toolNameInput = document.getElementById('tool-name-input');
    
    if (saveButton) {
        saveButton.addEventListener('click', async () => {
            // Get the code from editor
            const code = window.toolEditor.getCode();
            
            // Get the tool name from input
            const toolName = toolNameInput ? toolNameInput.value.trim() : '';
            
            // Validate and extract data
            const result = extractToolData(code, toolName);
            
            if (!result.valid) {
                showNotification(result.errors.join('\n'), 'error');
                return;
            }
            
            try {
                // Send to API
                const response = await fetch('/api/toolbox/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(result)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || 'Failed to create tool');
                }
                
                const data = await response.json();
                showNotification(`Tool ${result.name} created successfully!`, 'success');
                
                // Reset editor with default template after successful creation
                setTimeout(() => {
                    window.toolEditor.setCode(window.toolEditor.defaultToolTemplate);
                    if (toolNameInput) toolNameInput.value = 'MyNewTool';
                }, 2000);
                
            } catch (error) {
                showNotification(error.message, 'error');
            }
        });
    }
}); 
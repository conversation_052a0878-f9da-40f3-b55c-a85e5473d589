// Function to show notifications
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const notificationMessage = document.getElementById('notification-message');
    const notificationIcon = document.getElementById('notification-icon');
    
    // Set message
    notificationMessage.textContent = message;
    
    // Set icon and colors based on type
    if (type === 'success') {
        notification.classList.add('bg-green-100', 'dark:bg-green-800');
        notificationIcon.classList.add('fa-check-circle', 'text-green-500', 'dark:text-green-400');
    } else {
        notification.classList.add('bg-red-100', 'dark:bg-red-800');
        notificationIcon.classList.add('fa-exclamation-circle', 'text-red-500', 'dark:text-red-400');
    }
    
    // Show notification
    notification.classList.remove('hidden');
    notification.classList.add('animate-slide-in');
    
    // Hide after 5 seconds
    setTimeout(() => {
        notification.classList.add('animate-fade-out');
        setTimeout(() => {
            notification.classList.add('hidden');
            notification.classList.remove('animate-slide-in', 'animate-fade-out', 'bg-green-100', 'bg-red-100', 'dark:bg-green-800', 'dark:bg-red-800');
            notificationIcon.classList.remove('fa-check-circle', 'fa-exclamation-circle', 'text-green-500', 'text-red-500', 'dark:text-green-400', 'dark:text-red-400');
        }, 300);
    }, 5000);
}

// Function to validate form data
function validateForm(formData) {
    const errors = [];
    
    if (!formData.name) {
        errors.push("Tool name is required");
    } else if (!/^[A-Z][A-Za-z0-9]*Tool$/.test(formData.name)) {
        errors.push("Tool name must be in PascalCase and end with 'Tool'");
    }
    
    if (!formData.code) {
        errors.push("Code is required");
    } else {
        // Check for required fields in the code
        if (!formData.code.includes('name: str = Field(') || !formData.code.includes('description: str = Field(')) {
            errors.push("Tool code must include 'name' and 'description' fields");
        }
        
        // Check for BaseTool inheritance
        if (!formData.code.includes('class') || !formData.code.includes('BaseTool')) {
            errors.push("Code must define a class that inherits from BaseTool");
        }
        
        // Check for _run method
        if (!formData.code.includes('def _run(')) {
            errors.push("Tool must implement _run method");
        }
    }
    
    return errors;
}

// Initialize form handler
document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('toolForm');
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const categoryInput = document.getElementById('category');
    const versionInput = document.getElementById('version');

    // Update template when tool name changes
    if (nameInput) {
        nameInput.addEventListener('input', (e) => {
            const toolName = e.target.value || 'NewTool';
            if (window.toolEditor) {
                window.toolEditor.updateTemplate(toolName);
            }
        });
    }

    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const toolName = nameInput.value;
            const toolDescription = descriptionInput.value;
            const toolCategory = categoryInput.value;
            const toolVersion = versionInput.value;
            
            // Get the code from editor
            let code = window.toolEditor.getCode();
            
            // Update the code with form values using proper Field format
            code = code.replace(
                /name: str = Field\([^)]+\)/,
                `name: str = Field("${toolName}", description="The name of the tool")`
            );
            code = code.replace(
                /description: str = Field\([^)]+\)/,
                `description: str = Field("${toolDescription}", description="A detailed description of the tool's functionality")`
            );
            code = code.replace(
                /category: str = Field\([^)]+\)/,
                `category: str = Field(default="${toolCategory}", description="Tool category")`
            );
            code = code.replace(
                /version: str = Field\([^)]+\)/,
                `version: str = Field(default="${toolVersion}", description="Tool version")`
            );
            
            const formData = {
                name: toolName,
                description: toolDescription,
                category: toolCategory,
                version: toolVersion,
                code: code
            };
            
            // Validate form data
            const errors = validateForm(formData);
            if (errors.length > 0) {
                showNotification(errors.join('\n'), 'error');
                return;
            }
            
            try {
                // Create the tool file
                const createResponse = await fetch('/api/toolbox/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (!createResponse.ok) {
                    const error = await createResponse.json();
                    throw new Error(error.detail || 'Failed to create tool');
                }
                
                showNotification('Tool created successfully!', 'success');
                
                // Reset form after successful creation
                form.reset();
                window.toolEditor.setCode(window.toolEditor.defaultToolTemplate);
                
            } catch (error) {
                showNotification(error.message, 'error');
            }
        });
    }
}); 
// Configure Monaco Editor

// Add Monaco Editor CSS
const monacoCSS = document.createElement('link');
monacoCSS.rel = 'stylesheet';
monacoCSS.setAttribute('data-name', 'vs/editor/editor.main');
monacoCSS.href = 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs/editor/editor.main.min.css';
document.head.appendChild(monacoCSS);

// Configure AMD loader for Monaco
var require = { paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs' } };

// Default template for tool code
const DEFAULT_TOOL_TEMPLATE = `from typing import Dict, Any, Optional, Type
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty

class MyNewToolSchema(BaseModel):
    """Schema for MyNewTool input parameters"""
    param1: str = Field(
        description="Description of parameter 1",
    )

class MyNewTool(BaseTool):
    """Tool description - replace this with a meaningful description"""
    
    name: str = Field("MyNewTool", description="The name of the tool")
    description: str = Field("Description of what this tool does", description="A detailed description")
    category: str = Field(default="general", description="Tool category")
    version: str = Field(default="1.0.0", description="Tool version")
    args_schema: Type[BaseModel] = MyNewToolSchema
    
    async def _arun(self, param1: str) -> Dict[str, Any]:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with parameter: {param1}", "Tool Execution")
        
        # Your code here
        # Use print_step for showing progress steps
        print_step("Processing input parameters", f"{self.name}: Step 1", "blue")
        
        # Process data and create result
        result = {
            "message": f"Tool executed with parameter: {param1}",
            "status": "success"
        }
        
        # Show pretty result summary
        print_pretty(result, f"{self.name} Result")
        
        return result
`;

let toolEditor;

// Custom event for when editor is fully loaded
const editorReadyEvent = new CustomEvent('editorReady');

// Load Monaco Editor scripts
const loaderScript = document.createElement('script');
loaderScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.30.1/min/vs/loader.js';
loaderScript.onload = () => {
    require(['vs/editor/editor.main'], function() {
        const editorContainer = document.getElementById('code-editor');
        if (!editorContainer) {
            return;
        }

        try {
            // Define Python language features
            monaco.languages.registerCompletionItemProvider('python', {
                provideCompletionItems: (model, position) => {
                    const suggestions = [
                        {
                            label: 'BaseTool',
                            kind: monaco.languages.CompletionItemKind.Class,
                            insertText: 'BaseTool',
                            detail: 'Base class for tools',
                        },
                        {
                            label: 'Field',
                            kind: monaco.languages.CompletionItemKind.Function,
                            insertText: 'Field(${1:default})',
                            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                            detail: 'Field definition',
                        },
                        {
                            label: '_arun',
                            kind: monaco.languages.CompletionItemKind.Method,
                            insertText: [
                                'async def _arun(self, ${1:param}) -> ${2:Dict[str, Any]}:',
                                '    """Execute the tool\'s functionality asynchronously."""',
                                '    print_debug(f"Running {self.name} with parameter: {${1:param}}", "Tool Execution")',
                                '    ${3:# Your code here}',
                                '    ${4:return {}}',
                            ].join('\n    '),
                            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                            detail: 'Async implementation',
                        },
                        {
                            label: 'print_debug',
                            kind: monaco.languages.CompletionItemKind.Function,
                            insertText: 'print_debug(${1:message}, ${2:title})',
                            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                            detail: 'Print debug message',
                        },
                        {
                            label: 'print_step',
                            kind: monaco.languages.CompletionItemKind.Function,
                            insertText: 'print_step(${1:message}, ${2:title}, ${3:color})',
                            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                            detail: 'Print workflow step',
                        },
                        {
                            label: 'print_pretty',
                            kind: monaco.languages.CompletionItemKind.Function,
                            insertText: 'print_pretty(${1:data}, ${2:title})',
                            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                            detail: 'Print formatted data',
                        }
                    ];
                    
                    return { suggestions };
                }
            });

            // Create editor
            toolEditor = monaco.editor.create(editorContainer, {
                value: DEFAULT_TOOL_TEMPLATE,
                language: 'python',
                theme: document.documentElement.classList.contains('dark') ? 'vs-dark' : 'vs',
                automaticLayout: true,
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                fontSize: 14,
                lineHeight: 22,
                fontFamily: 'Menlo, Monaco, "Courier New", monospace',
                tabSize: 4,
                insertSpaces: true,
                formatOnPaste: true,
                wordWrap: 'on'
            });
            
            // Export editor instance
            window.toolEditor = {
                getCode: () => toolEditor.getValue(),
                setCode: (code) => toolEditor.setValue(code),
                defaultToolTemplate: DEFAULT_TOOL_TEMPLATE
            };
            
            // Handle dark mode changes
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class') {
                        const isDarkMode = document.documentElement.classList.contains('dark');
                        monaco.editor.setTheme(isDarkMode ? 'vs-dark' : 'vs');
                    }
                });
            });
            
            observer.observe(document.documentElement, { attributes: true });
            
            // Dispatch editor ready event
            document.dispatchEvent(editorReadyEvent);
        } catch (error) {
            // Silently fail
        }
    });
};
document.head.appendChild(loaderScript); 
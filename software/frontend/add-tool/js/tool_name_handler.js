// Tool name handling and dynamic code update
document.addEventListener('DOMContentLoaded', () => {
    // Check if elements are available immediately
    const toolNameInputImmediate = document.getElementById('tool-name-input');
    
    // Function to initialize tool name handler
    function initToolNameHandler() {
        const toolNameInput = document.getElementById('tool-name-input');
        
        if (!toolNameInput) {
            return false;
        }
        
        if (!window.toolEditor || typeof window.toolEditor.getCode !== 'function') {
            return false;
        }
        
        // Ensure default value is set
        toolNameInput.value = 'MyNewTool';
        // Also update the placeholder to match
        toolNameInput.placeholder = 'MyNewTool';
        
        // Function to validate PascalCase tool name
        function isValidToolName(name) {
            return /^[A-Z][A-Za-z0-9]*Tool$/.test(name);
        }
        
        // Initialize styling based on default value
        if (isValidToolName(toolNameInput.value)) {
            toolNameInput.classList.remove('border-red-500');
            toolNameInput.classList.add('border-green-500');
        }
        
        // Function to update name in code
        function updateToolNameInCode(newName) {
            try {
                // Get current code
                const currentCode = window.toolEditor.getCode();
                
                // First find the BaseTool class (main tool class)
                const mainClassRegex = /class\s+(\w+Tool)\s*\(\s*BaseTool\s*\)/;
                const mainClassMatch = currentCode.match(mainClassRegex);
                
                if (!mainClassMatch || !mainClassMatch[1]) {
                    return;
                }
                
                const currentClassName = mainClassMatch[1];
                
                // Find the schema class
                const schemaClassRegex = /class\s+(\w+Schema)\s*\(\s*BaseModel\s*\)/;
                const schemaClassMatch = currentCode.match(schemaClassRegex);
                
                let currentSchemaName = null;
                if (schemaClassMatch && schemaClassMatch[1]) {
                    currentSchemaName = schemaClassMatch[1];
                }
                
                // If already the same, no need to update
                if (currentClassName === newName) {
                    return;
                }
                
                // Create new code
                let newCode = currentCode;
                
                // Generate new schema name
                const newSchemaName = `${newName}Schema`;
                
                // Replace main class name occurrences
                newCode = newCode.replace(
                    new RegExp(`class\\s+${currentClassName}\\b`, 'g'), 
                    `class ${newName}`
                );
                
                // Replace schema class name if it exists
                if (currentSchemaName) {
                    newCode = newCode.replace(
                        new RegExp(`class\\s+${currentSchemaName}\\b`, 'g'),
                        `class ${newSchemaName}`
                    );
                    
                    // Also update args_schema reference
                    newCode = newCode.replace(
                        new RegExp(`args_schema:\\s*Type\\[BaseModel\\]\\s*=\\s*${currentSchemaName}\\b`, 'g'),
                        `args_schema: Type[BaseModel] = ${newSchemaName}`
                    );
                }
                
                // Replace name field string value
                newCode = newCode.replace(
                    new RegExp(`name:\\s*str\\s*=\\s*Field\\(\\s*["']${currentClassName}["']`, 'g'),
                    `name: str = Field("${newName}"`
                );
                
                // Set updated code
                window.toolEditor.setCode(newCode);
            } catch (error) {
                // Silently fail on errors
            }
        }
        
        // Listen for input changes
        toolNameInput.addEventListener('input', (e) => {
            const newName = e.target.value.trim();
            
            // Validate name format
            if (isValidToolName(newName)) {
                toolNameInput.classList.remove('border-red-500');
                toolNameInput.classList.add('border-green-500');
                updateToolNameInCode(newName);
            } else {
                toolNameInput.classList.remove('border-green-500');
                toolNameInput.classList.add('border-red-500');
            }
        });
        
        // Force code update with current input value
        updateToolNameInCode(toolNameInput.value);
        
        return true;
    }
    
    // Try to initialize immediately if editor is already available
    let initialized = false;
    
    if (window.toolEditor && typeof window.toolEditor.getCode === 'function') {
        initialized = initToolNameHandler();
    }
    
    // If not successful or editor not ready, wait for editor ready event
    if (!initialized) {
        // Listen for editor ready event
        document.addEventListener('editorReady', () => {
            // Small delay to ensure editor is fully setup
            setTimeout(() => {
                initialized = initToolNameHandler();
                
                // Final fallback with long timeout if initialization still fails
                if (!initialized) {
                    setTimeout(() => {
                        if (!initialized) {
                            initialized = initToolNameHandler();
                        }
                    }, 1000);
                }
            }, 100);
        });
        
        // Fallback mechanism with timeout in case the event doesn't fire
        setTimeout(() => {
            if (!initialized) {
                initialized = initToolNameHandler();
            }
        }, 3000);
    }
}); 
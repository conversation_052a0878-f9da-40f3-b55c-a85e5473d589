/**
 * Code Display Module for Backtest Strategy
 *
 * This module handles displaying different code blocks:
 * - AI-generated strategy methods
 * - Universal template code blocks
 * - Complete strategy implementation
 */

/**
 * Display the strategy code blocks in a structured format
 * @param {Object} data - The response data containing strategy_methods
 * @param {string} containerId - The ID of the container to display the code in
 */
function displayStrategyCodeBlocks(data, containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container with ID ${containerId} not found`);
        return;
    }

    // Clear existing content
    container.innerHTML = '';

    if (!data.strategy_methods) {
        container.innerHTML = `
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                <p>No strategy methods available to display.</p>
            </div>
        `;
        return;
    }

    const strategyMethods = data.strategy_methods;

    // Create the main container
    const codeContainer = document.createElement('div');
    codeContainer.className = 'space-y-6';

    // Add strategy overview
    codeContainer.appendChild(createStrategyOverviewSection(strategyMethods));

    // Add AI-generated code blocks (main section)
    codeContainer.appendChild(createAIGeneratedCodeSection(strategyMethods));

    // Add AI-generated Pydantic schemas
    codeContainer.appendChild(createAIGeneratedSchemasSection());

    // Add optimization configuration if available
    if (strategyMethods.optimization_config) {
        codeContainer.appendChild(createOptimizationConfigSection(strategyMethods.optimization_config));
    }

    // Add hardcoded helper code sections
    codeContainer.appendChild(createHardcodedHelpersSection());

    // Add chart generation & visualization section
    codeContainer.appendChild(createChartGenerationSection());

    container.appendChild(codeContainer);

    // Apply syntax highlighting if Prism.js is available
    if (typeof Prism !== 'undefined') {
        Prism.highlightAllUnder(container);
    }
}

/**
 * Create strategy overview section
 * @param {Object} strategyMethods - The strategy methods object
 * @returns {HTMLElement} - The strategy overview section
 */
function createStrategyOverviewSection(strategyMethods) {
    const section = document.createElement('div');
    section.className = 'bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4';

    section.innerHTML = `
        <h4 class="text-lg font-medium text-blue-800 dark:text-blue-300 mb-2">📊 Strategy Overview</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400">Indicators Used:</span>
                <div class="mt-1">
                    ${strategyMethods.indicators_for_chart.map(indicator =>
                        `<span class="inline-block text-xs font-mono bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded mr-1 mb-1">${indicator}</span>`
                    ).join('')}
                </div>
            </div>
            <div>
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400">Code Generation:</span>
                <div class="text-sm text-blue-700 dark:text-blue-400 mt-1">
                    ✅ AI-Generated Strategy Methods<br>
                    ✅ AI-Generated Optimization Config<br>
                    ⚙️ Hardcoded Template & Helpers
                </div>
            </div>
        </div>
    `;

    return section;
}

/**
 * Create AI-generated code section
 * @param {Object} strategyMethods - The strategy methods object
 * @returns {HTMLElement} - The AI-generated code section
 */
function createAIGeneratedCodeSection(strategyMethods) {
    const section = document.createElement('div');
    section.className = 'bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4';

    const header = document.createElement('h4');
    header.className = 'text-lg font-medium text-green-800 dark:text-green-300 mb-4';
    header.textContent = '🤖 AI-Generated Strategy Methods';
    section.appendChild(header);

    const description = document.createElement('p');
    description.className = 'text-sm text-green-700 dark:text-green-400 mb-4';
    description.textContent = 'These methods are dynamically generated by AI based on the business question using structured LLM output.';
    section.appendChild(description);

    // Calculate indicators code block
    section.appendChild(createCodeBlock(
        'Calculate Indicators Method',
        strategyMethods.calculate_indicators_code,
        'AI-generated method that calculates technical indicators (RSI, MACD, SMA, etc.) needed for the strategy.',
        'ai-calculate-indicators'
    ));

    // Should long code block
    section.appendChild(createCodeBlock(
        'Entry Logic (Long Position)',
        strategyMethods.should_long_code,
        'AI-generated method that determines when to enter a long position based on strategy conditions.',
        'ai-should-long'
    ));

    // Should exit long code block
    section.appendChild(createCodeBlock(
        'Exit Logic (Long Position)',
        strategyMethods.should_exit_long_code,
        'AI-generated method that determines when to exit a long position based on strategy conditions.',
        'ai-should-exit-long'
    ));

    // Optional short methods
    if (strategyMethods.should_short_code) {
        section.appendChild(createCodeBlock(
            'Entry Logic (Short Position)',
            strategyMethods.should_short_code,
            'AI-generated method that determines when to enter a short position.',
            'ai-should-short'
        ));
    }

    if (strategyMethods.should_exit_short_code) {
        section.appendChild(createCodeBlock(
            'Exit Logic (Short Position)',
            strategyMethods.should_exit_short_code,
            'AI-generated method that determines when to exit a short position.',
            'ai-should-exit-short'
        ));
    }

    return section;
}

/**
 * Create AI-generated Pydantic schemas section
 * @returns {HTMLElement} - The AI-generated schemas section
 */
function createAIGeneratedSchemasSection() {
    const section = document.createElement('div');
    section.className = 'bg-emerald-50 dark:bg-emerald-900/30 border border-emerald-200 dark:border-emerald-700 rounded-lg p-4';

    const header = document.createElement('h4');
    header.className = 'text-lg font-medium text-emerald-800 dark:text-emerald-300 mb-4';
    header.textContent = '🤖 AI-Generated Pydantic Schemas';
    section.appendChild(header);

    const description = document.createElement('p');
    description.className = 'text-sm text-emerald-700 dark:text-emerald-400 mb-4';
    description.textContent = 'These Pydantic schemas define the structure for AI-generated strategy methods and optimization configuration.';
    section.appendChild(description);

    // Strategy Methods Schema
    const strategyMethodsSchema = `class StrategyMethods(BaseModel):
    """Schema for AI-generated strategy methods"""

    calculate_indicators_code: str = Field(
        ...,
        description="Python code for calculate_indicators() method body. Should calculate all technical indicators needed for the strategy (RSI, MACD, Bollinger Bands, etc.). Include proper error handling and data validation.",
        examples=[
            "if len(self.df) < 50:\\n    return\\nself.df['rsi'] = ta.rsi(self.df['Close'], length=14)\\nself.df['sma_20'] = self.df['Close'].rolling(20).mean()\\nself.df['sma_50'] = self.df['Close'].rolling(50).mean()"
        ]
    )

    should_long_code: str = Field(
        ...,
        description="Python code for should_long() method body. Return True when entering long position. Include data validation.",
        examples=[
            "if len(self.df) < 50 or pd.isna(self.df['rsi'].iloc[-1]):\\n    return False\\nreturn self.df['rsi'].iloc[-1] < 30 and self.df['Close'].iloc[-1] > self.df['sma_20'].iloc[-1]"
        ]
    )

    should_exit_long_code: str = Field(
        ...,
        description="Python code for should_exit_long() method body. Return True when exiting long position. Include data validation.",
        examples=[
            "if len(self.df) < 50 or pd.isna(self.df['rsi'].iloc[-1]):\\n    return False\\nreturn self.df['rsi'].iloc[-1] > 70"
        ]
    )

    should_short_code: Optional[str] = Field(
        None,
        description="Python code for should_short() method body. Return True when entering short position. Include data validation."
    )

    should_exit_short_code: Optional[str] = Field(
        None,
        description="Python code for should_exit_short() method body. Return True when exiting short position. Include data validation."
    )

    indicators_for_chart: List[str] = Field(
        ...,
        description="List of indicator column names to display in Panel 2 of the chart",
        examples=[["rsi", "macd", "sma_20", "bollinger_upper", "bollinger_lower"]]
    )

    strategy_description: str = Field(
        ...,
        description="Brief description of the strategy for chart title",
        examples=["RSI Oversold/Overbought Strategy", "Moving Average Crossover Strategy", "Bollinger Bands Mean Reversion"]
    )`;

    section.appendChild(createCodeBlock(
        'StrategyMethods Schema',
        strategyMethodsSchema,
        'Pydantic schema that structures the AI-generated strategy methods with validation and examples.',
        'schema-strategy-methods'
    ));

    // Optimization Config Schemas
    const optimizationSchemas = `class IndicatorRange(BaseModel):
    """Range configuration for indicator parameters"""
    name: str = Field(..., description="Parameter name (e.g., 'rsi_period', 'sma_period')")
    base_value: int = Field(..., description="Default/base value for the parameter")
    min_value: int = Field(..., description="Minimum value for optimization")
    max_value: int = Field(..., description="Maximum value for optimization")
    step: int = Field(..., description="Step size for parameter sweep")

class ThresholdRange(BaseModel):
    """Range configuration for strategy thresholds"""
    name: str = Field(..., description="Threshold name (e.g., 'oversold_threshold', 'overbought_threshold')")
    base_value: float = Field(..., description="Default/base value for the threshold")
    min_value: float = Field(..., description="Minimum value for optimization")
    max_value: float = Field(..., description="Maximum value for optimization")
    step: float = Field(..., description="Step size for parameter sweep")

class OptimizationConfig(BaseModel):
    """Configuration for hyperparameter optimization"""
    indicators: List[IndicatorRange] = Field(default=[], description="Indicator parameter ranges")
    thresholds: List[ThresholdRange] = Field(default=[], description="Strategy threshold ranges")
    max_combinations: int = Field(default=100, description="Maximum parameter combinations to test")

class OptimizedStrategyMethods(StrategyMethods):
    """Enhanced strategy methods with optimization configuration"""
    optimization_config: OptimizationConfig = Field(default_factory=OptimizationConfig)`;

    section.appendChild(createCodeBlock(
        'Optimization Configuration Schemas',
        optimizationSchemas,
        'Pydantic schemas for hyperparameter optimization configuration and parameter ranges.',
        'schema-optimization-config'
    ));

    return section;
}

/**
 * Create optimization configuration section
 * @param {Object} optimizationConfig - The optimization configuration object
 * @returns {HTMLElement} - The optimization configuration section
 */
function createOptimizationConfigSection(optimizationConfig) {
    const section = document.createElement('div');
    section.className = 'bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4';

    const header = document.createElement('h4');
    header.className = 'text-lg font-medium text-green-800 dark:text-green-300 mb-4';
    header.textContent = '🤖 AI-Generated Optimization Configuration';
    section.appendChild(header);

    const description = document.createElement('p');
    description.className = 'text-sm text-green-700 dark:text-green-400 mb-4';
    description.textContent = 'AI-generated parameter ranges and optimization settings for hyperparameter tuning.';
    section.appendChild(description);

    // Create optimization parameters display
    const configContainer = document.createElement('div');
    configContainer.className = 'space-y-4';

    // Display indicator parameters
    if (optimizationConfig.indicators && optimizationConfig.indicators.length > 0) {
        const indicatorsSection = document.createElement('div');
        indicatorsSection.innerHTML = `
            <h5 class="text-md font-medium text-green-700 dark:text-green-400 mb-2">Indicator Parameters</h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                ${optimizationConfig.indicators.map(indicator => `
                    <div class="bg-white dark:bg-gray-800 border border-green-200 dark:border-green-600 rounded p-3">
                        <div class="font-mono text-sm font-medium text-green-800 dark:text-green-300">${indicator.name}</div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                            Range: [${indicator.min_value} - ${indicator.max_value}] | Step: ${indicator.step} | Default: ${indicator.base_value}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        configContainer.appendChild(indicatorsSection);
    }

    // Display threshold parameters
    if (optimizationConfig.thresholds && optimizationConfig.thresholds.length > 0) {
        const thresholdsSection = document.createElement('div');
        thresholdsSection.innerHTML = `
            <h5 class="text-md font-medium text-green-700 dark:text-green-400 mb-2">Threshold Parameters</h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                ${optimizationConfig.thresholds.map(threshold => `
                    <div class="bg-white dark:bg-gray-800 border border-green-200 dark:border-green-600 rounded p-3">
                        <div class="font-mono text-sm font-medium text-green-800 dark:text-green-300">${threshold.name}</div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                            Range: [${threshold.min_value} - ${threshold.max_value}] | Step: ${threshold.step} | Default: ${threshold.base_value}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        configContainer.appendChild(thresholdsSection);
    }

    // Display optimization settings
    const settingsSection = document.createElement('div');
    settingsSection.innerHTML = `
        <h5 class="text-md font-medium text-green-700 dark:text-green-400 mb-2">Optimization Settings</h5>
        <div class="bg-white dark:bg-gray-800 border border-green-200 dark:border-green-600 rounded p-3">
            <div class="text-sm text-green-700 dark:text-green-400">
                <span class="font-medium">Max Combinations:</span> ${optimizationConfig.max_combinations || 100}
            </div>
            <div class="text-xs text-green-600 dark:text-green-500 mt-1">
                The AI will test up to this many parameter combinations to find the optimal strategy.
            </div>
        </div>
    `;
    configContainer.appendChild(settingsSection);

    // Add AI-generated replacement patterns code
    const replacementPatternsCode = `async def generate_replacement_patterns(strategy_methods: OptimizedStrategyMethods) -> Dict[str, Dict[str, str]]:
    """Generate replacement patterns for all parameters using LLM once."""

    try:
        from software.ai.llm.llm_connect import get_llm_connect
        from langchain_core.messages import SystemMessage, HumanMessage

        # Use AI to generate replacement patterns
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")

        # Collect all parameter names from optimization config
        param_names = []
        for indicator in strategy_methods.optimization_config.indicators:
            param_names.append(indicator.name)
        for threshold in strategy_methods.optimization_config.thresholds:
            param_names.append(threshold.name)

        system_prompt = f"""You are a code parameter replacement pattern generator.
Analyze the given trading strategy code and generate regex replacement patterns for each parameter.

PARAMETERS TO ANALYZE: {', '.join(param_names)}

RULES:
1. For each parameter, find ALL patterns that need replacement
2. Return a JSON object with parameter names as keys
3. Each parameter should have an array of [old_pattern, new_pattern_template] pairs
4. Use {{value}} as placeholder for the new parameter value
5. Include regex patterns for complex replacements

Return only valid JSON without any explanations."""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content="Generate replacement patterns for the parameters.")
        ]

        response = await model.ainvoke(messages)
        patterns_text = response.content.strip()

        # Remove any markdown formatting
        if patterns_text.startswith('\`\`\`json'):
            patterns_text = patterns_text.replace('\`\`\`json', '').replace('\`\`\`', '').strip()
        elif patterns_text.startswith('\`\`\`'):
            patterns_text = patterns_text.replace('\`\`\`', '').strip()

        # Parse JSON response
        import json
        patterns = json.loads(patterns_text)
        return patterns

    except Exception as e:
        # Fallback to empty patterns
        return {}

def apply_replacement_patterns(code: str, param_name: str, param_value: Any, patterns: Dict[str, Dict[str, str]]) -> str:
    """Apply pre-generated replacement patterns efficiently."""
    import re

    if param_name not in patterns:
        return code

    modified_code = code
    for old_pattern, new_pattern_template in patterns[param_name]:
        new_pattern = new_pattern_template.replace('{value}', str(param_value))
        modified_code = re.sub(old_pattern, new_pattern, modified_code)

    return modified_code`;

    section.appendChild(createCodeBlock(
        'AI-Generated Parameter Replacement',
        replacementPatternsCode,
        'LLM generates intelligent regex patterns to replace parameters in strategy code (1 LLM call for all parameters).',
        'optimization-replacement-patterns'
    ));

    section.appendChild(configContainer);
    return section;
}

/**
 * Create hardcoded helpers section
 * @returns {HTMLElement} - The hardcoded helpers section
 */
function createHardcodedHelpersSection() {
    const section = document.createElement('div');
    section.className = 'bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-700 rounded-lg p-4';

    const header = document.createElement('h4');
    header.className = 'text-lg font-medium text-gray-800 dark:text-gray-300 mb-4';
    header.textContent = '⚙️ Hardcoded Helper Code';
    section.appendChild(header);

    const description = document.createElement('p');
    description.className = 'text-sm text-gray-700 dark:text-gray-400 mb-4';
    description.textContent = 'These are pre-written template components and helper functions that provide the infrastructure for executing AI-generated strategies.';
    section.appendChild(description);

    // BacktestTemplate class
    const backtestTemplateCode = `class BacktestTemplate:
    """Template class for backtesting strategies with AI-generated methods."""

    def __init__(self, data: pd.DataFrame, strategy_methods: StrategyMethods, ticker: str, unique_id: str):
        self.data = data.copy()
        self.strategy_methods = strategy_methods
        self.ticker = ticker
        self.unique_id = unique_id
        self.strategy = None

    def create_chart(self) -> str:
        """Create the backtest chart using the template."""
        try:
            # Create strategy instance with AI-generated methods
            self.strategy = self._create_strategy_instance()

            # Generate the 5-panel chart
            chart_path = self._generate_chart()
            return chart_path
        except Exception as e:
            return self._create_error_chart(str(e))

    def _create_strategy_instance(self):
        """Create a strategy instance with AI-generated methods."""
        # Create a dynamic strategy class with AI methods
        class DynamicStrategy:
            def __init__(self, df, strategy_methods):
                self.df = df.copy()
                self.strategy_methods = strategy_methods
                self.signals = pd.DataFrame(index=df.index)
                self.signals['position'] = 0

                # Execute AI-generated methods
                self._execute_calculate_indicators()
                self._generate_signals()
                self._calculate_returns()`;

    section.appendChild(createCodeBlock(
        'BacktestTemplate Class',
        backtestTemplateCode,
        'Main template class that orchestrates the execution of AI-generated strategy methods.',
        'helper-backtest-template'
    ));

    // Signal generation engine
    const signalGenerationCode = `def _generate_signals(self):
    """Generate trading signals using AI methods."""
    try:
        # Initialize position column
        self.signals['position'] = 0

        # Ensure we have enough data
        if len(self.df) < 50:
            return

        # Loop through data to generate signals
        for i in range(1, len(self.df)):
            # Update current index for AI methods
            self.current_idx = i

            # Default to previous position (hold)
            self.signals.loc[self.signals.index[i], 'position'] = self.signals.loc[self.signals.index[i-1], 'position']

            # Check for long entry
            should_long_result = self._should_long()

            if self.signals.loc[self.signals.index[i-1], 'position'] == 0 and should_long_result:
                self.signals.loc[self.signals.index[i], 'position'] = 1

            # Check for long exit
            elif self.signals.loc[self.signals.index[i-1], 'position'] == 1:
                should_exit_result = self._should_exit_long()
                if should_exit_result:
                    self.signals.loc[self.signals.index[i], 'position'] = 0

    except Exception as e:
        self.signals['position'] = 0`;

    section.appendChild(createCodeBlock(
        'Signal Generation Engine',
        signalGenerationCode,
        'Hardcoded signal generation logic that calls AI-generated entry/exit methods.',
        'helper-signal-generation'
    ));

    // AI method execution wrapper
    const aiExecutionCode = `def _execute_calculate_indicators(self):
    """Execute AI-generated calculate_indicators code."""
    try:
        # Create namespace for execution
        namespace = {
            'self': self,
            'pd': pd,
            'np': np,
            'ta': None  # Will try to import pandas_ta
        }

        # Try to import pandas_ta
        try:
            import pandas_ta as ta
            namespace['ta'] = ta
        except ImportError:
            pass

        # Wrap the code in a function to avoid 'return' outside function error
        wrapped_code = f"""
def calculate_indicators_impl():
{chr(10).join('    ' + line for line in self.strategy_methods.calculate_indicators_code.split(chr(10)))}

calculate_indicators_impl()
"""

        # Execute the AI-generated code
        exec(wrapped_code, namespace)

    except Exception as e:
        pass

    # Always ensure fallback indicators are created
    self._create_fallback_indicators()

def _should_long(self):
    """Execute AI-generated should_long code."""
    try:
        namespace = {
            'self': self,
            'pd': pd,
            'np': np
        }

        # Modify the AI-generated code to use current index instead of -1
        modified_code = self.strategy_methods.should_long_code
        if hasattr(self, 'current_idx'):
            # Replace all instances of .iloc[-1] with .iloc[current_idx]
            import re
            modified_code = re.sub(r'\\.iloc\\[-1\\]', f'.iloc[{self.current_idx}]', modified_code)

        # Wrap the code in a function to handle returns properly
        wrapped_code = f"""
def should_long_impl():
{chr(10).join('    ' + line for line in modified_code.split(chr(10)))}

result = should_long_impl()
"""
        # Execute the AI-generated code and return result
        exec(wrapped_code, namespace)
        result = namespace.get('result', False)
        return result

    except Exception as e:
        return False`;

    section.appendChild(createCodeBlock(
        'AI Method Execution Wrapper',
        aiExecutionCode,
        'Hardcoded wrapper functions that safely execute AI-generated strategy methods.',
        'helper-ai-execution'
    ));

    // Fallback indicators
    const fallbackIndicatorsCode = `def _create_fallback_indicators(self):
    """Create fallback indicators when pandas_ta is not available."""
    try:
        # Ensure we have enough data
        if len(self.df) < 14:
            return

        # RSI calculation (14-period)
        if 'rsi' not in self.df.columns or self.df['rsi'].isna().all():
            delta = self.df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.df['rsi'] = 100 - (100 / (1 + rs))

        # Simple Moving Averages
        if 'sma_20' not in self.df.columns and len(self.df) >= 20:
            self.df['sma_20'] = self.df['Close'].rolling(20).mean()

        if 'sma_50' not in self.df.columns and len(self.df) >= 50:
            self.df['sma_50'] = self.df['Close'].rolling(50).mean()

        # MACD (12, 26, 9)
        if 'macd' not in self.df.columns and len(self.df) >= 26:
            exp1 = self.df['Close'].ewm(span=12).mean()
            exp2 = self.df['Close'].ewm(span=26).mean()
            self.df['macd'] = exp1 - exp2
            self.df['macd_signal'] = self.df['macd'].ewm(span=9).mean()
            self.df['macd_histogram'] = self.df['macd'] - self.df['macd_signal']

        # Bollinger Bands (20-period, 2 std)
        if 'bb_upper' not in self.df.columns and len(self.df) >= 20:
            sma_20 = self.df['Close'].rolling(20).mean()
            std_20 = self.df['Close'].rolling(20).std()
            self.df['bb_upper'] = sma_20 + (std_20 * 2)
            self.df['bb_lower'] = sma_20 - (std_20 * 2)
            self.df['bb_middle'] = sma_20

    except Exception as e:
        pass`;

    section.appendChild(createCodeBlock(
        'Fallback Indicator System',
        fallbackIndicatorsCode,
        'Hardcoded fallback calculations when pandas_ta is not available or AI-generated indicators fail.',
        'helper-fallback-indicators'
    ));

    // Returns calculation
    const returnsCalculationCode = `def _calculate_returns(self):
    """Calculate strategy returns and performance metrics."""
    try:
        # Calculate daily returns
        self.signals['returns'] = self.df['Close'].pct_change()

        # Calculate strategy returns
        self.signals['strategy_returns'] = self.signals['position'].shift(1) * self.signals['returns']

        # Calculate cumulative returns
        self.signals['cumulative_returns'] = (1 + self.signals['returns']).cumprod()
        self.signals['strategy_cumulative_returns'] = (1 + self.signals['strategy_returns']).cumprod()

        # Fill NaN values
        self.signals.fillna(0, inplace=True)

    except Exception as e:
        # Create empty returns columns
        self.signals['returns'] = 0.0
        self.signals['strategy_returns'] = 0.0
        self.signals['cumulative_returns'] = 1.0
        self.signals['strategy_cumulative_returns'] = 1.0`;

    section.appendChild(createCodeBlock(
        'Returns Calculation Engine',
        returnsCalculationCode,
        'Hardcoded returns calculation for performance analysis and metrics.',
        'helper-returns-calculation'
    ));

    return section;
}

/**
 * Create chart generation & visualization section
 * @returns {HTMLElement} - The chart generation section
 */
function createChartGenerationSection() {
    const section = document.createElement('div');
    section.className = 'bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-lg p-4';

    const header = document.createElement('h4');
    header.className = 'text-lg font-medium text-orange-800 dark:text-orange-300 mb-4';
    header.textContent = '🟠 Chart Generation & Visualization';
    section.appendChild(header);

    const chartGenerationCode = `def _generate_chart(self) -> str:
    """Generate the 5-panel backtest chart with comprehensive analysis."""
    import matplotlib.pyplot as plt
    from matplotlib.gridspec import GridSpec
    import os

    # Create figure and subplots
    fig = plt.figure(figsize=(14, 12))
    gs = GridSpec(5, 1, height_ratios=[2, 1, 1, 1, 1], figure=fig)

    # Panel 1: Price chart with signals and indicators
    ax1 = fig.add_subplot(gs[0])
    ax1.plot(self.df.index, self.df['Close'], label='Price', color='black', linewidth=1)

    # Add buy/sell signals
    if 'position' in self.signals.columns:
        buy_signals = self.signals['position'].diff() > 0
        sell_signals = self.signals['position'].diff() < 0

        if buy_signals.any():
            buy_dates = self.signals.index[buy_signals]
            buy_prices = self.df.loc[buy_dates, 'Close']
            ax1.scatter(buy_dates, buy_prices, marker='^', color='green',
                       s=100, label='Buy Signal', zorder=5)

        if sell_signals.any():
            sell_dates = self.signals.index[sell_signals]
            sell_prices = self.df.loc[sell_dates, 'Close']
            ax1.scatter(sell_dates, sell_prices, marker='v', color='red',
                       s=100, label='Sell Signal', zorder=5)

    # Add moving averages if available
    for indicator in self.strategy_methods.indicators_for_chart:
        if indicator in self.df.columns:
            ax1.plot(self.df.index, self.df[indicator], label=indicator, alpha=0.7)

    ax1.set_title(f'{self.ticker} - {self.strategy_methods.strategy_description}')
    ax1.set_ylabel('Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Panel 2: Technical indicators
    ax2 = fig.add_subplot(gs[1], sharex=ax1)
    for indicator in self.strategy_methods.indicators_for_chart:
        if indicator in self.df.columns and indicator not in ['Close', 'sma_20', 'sma_50']:
            ax2.plot(self.df.index, self.df[indicator], label=indicator)
    ax2.set_ylabel('Indicators')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Panel 3: Returns comparison
    ax3 = fig.add_subplot(gs[2], sharex=ax1)
    if 'returns' in self.signals.columns:
        ax3.plot(self.signals.index, self.signals['returns'].cumsum() * 100,
                label='Buy & Hold Returns (%)', color='blue')
        ax3.plot(self.signals.index, self.signals['strategy_returns'].cumsum() * 100,
                label='Strategy Returns (%)', color='green')
    ax3.set_ylabel('Returns (%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Panel 4: Portfolio value
    ax4 = fig.add_subplot(gs[3], sharex=ax1)
    if 'cumulative_returns' in self.signals.columns:
        initial_investment = 10000
        bh_values = self.signals['cumulative_returns'] * initial_investment
        strategy_values = self.signals['strategy_cumulative_returns'] * initial_investment

        ax4.plot(self.signals.index, bh_values,
                label='Buy & Hold Value ($)', color='blue')
        ax4.plot(self.signals.index, strategy_values,
                label='Strategy Value ($)', color='green')

        # Set y-axis to start from a reasonable minimum (not 0)
        min_value = min(bh_values.min(), strategy_values.min())
        max_value = max(bh_values.max(), strategy_values.max())
        padding = (max_value - min_value) * 0.05  # 5% padding
        ax4.set_ylim(min_value - padding, max_value + padding)

    ax4.set_ylabel('Portfolio Value ($)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # Panel 5: Drawdown analysis
    ax5 = fig.add_subplot(gs[4], sharex=ax1)
    if 'cumulative_returns' in self.signals.columns:
        # Calculate drawdowns
        strategy_peak = self.signals['strategy_cumulative_returns'].cummax()
        strategy_drawdown = (self.signals['strategy_cumulative_returns'] - strategy_peak) / strategy_peak * 100

        bh_peak = self.signals['cumulative_returns'].cummax()
        bh_drawdown = (self.signals['cumulative_returns'] - bh_peak) / bh_peak * 100

        ax5.plot(self.signals.index, bh_drawdown, label='Buy & Hold Drawdown', color='blue')
        ax5.plot(self.signals.index, strategy_drawdown, label='Strategy Drawdown', color='green')

    ax5.set_ylabel('Drawdown (%)')
    ax5.set_xlabel('Date')
    ax5.legend()
    ax5.grid(True, alpha=0.3)

    # Add performance metrics
    self._add_performance_metrics(fig)

    # Save chart
    plt.tight_layout()
    file_path = os.path.join('software/library/images',
                           f'backtest_strategy_{self.ticker.lower()}_{self.unique_id}.png')
    plt.savefig(file_path, dpi=100, bbox_inches='tight')
    plt.close(fig)

    return file_path`;

    section.appendChild(createCodeBlock(
        'Complete Chart Generation',
        chartGenerationCode,
        '5-panel visualization with price, indicators, returns, portfolio value, and drawdown analysis.',
        'chart-generation-complete'
    ));

    const performanceMetricsCode = `def _add_performance_metrics(self, fig):
    """Add performance metrics text box to the chart."""

    try:
        # Calculate key metrics
        total_return = (self.signals['strategy_cumulative_returns'].iloc[-1] - 1) * 100
        bh_return = (self.signals['cumulative_returns'].iloc[-1] - 1) * 100

        # Calculate Sharpe ratios
        strategy_sharpe = (self.signals['strategy_returns'].mean() /
                          self.signals['strategy_returns'].std()) * (252 ** 0.5) \\
                          if self.signals['strategy_returns'].std() > 0 else 0

        # Calculate maximum drawdown
        strategy_peak = self.signals['strategy_cumulative_returns'].cummax()
        strategy_drawdown = (self.signals['strategy_cumulative_returns'] - strategy_peak) / strategy_peak
        max_drawdown = strategy_drawdown.min() * 100

        # Calculate win rate
        winning_trades = (self.signals['strategy_returns'] > 0).sum()
        total_trades = (self.signals['position'].diff() != 0).sum() // 2
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        # Format metrics text
        metrics_text = (
            f"Performance Metrics:\\n"
            f"Strategy Return: {total_return:.2f}%\\n"
            f"Buy & Hold Return: {bh_return:.2f}%\\n"
            f"Sharpe Ratio: {strategy_sharpe:.2f}\\n"
            f"Max Drawdown: {max_drawdown:.2f}%\\n"
            f"Win Rate: {win_rate:.1f}%\\n"
            f"Total Trades: {total_trades}"
        )

        # Add text box to chart
        props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
        fig.text(0.02, 0.02, metrics_text, fontsize=9,
                verticalalignment='bottom', bbox=props)

    except Exception as e:
        pass`;

    section.appendChild(createCodeBlock(
        'Performance Metrics Display',
        performanceMetricsCode,
        'Calculate and display comprehensive performance metrics on the chart.',
        'chart-performance-metrics'
    ));

    return section;
}

/**
 * Create complete implementation section
 * @param {Object} data - The response data
 * @returns {HTMLElement} - The complete implementation section
 */
function createCompleteImplementationSection(data) {
    const section = document.createElement('div');
    section.className = 'bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4';

    const header = document.createElement('h4');
    header.className = 'text-lg font-medium text-gray-700 dark:text-gray-300 mb-4';
    header.textContent = '⚙️ Complete Strategy Implementation';
    section.appendChild(header);

    const description = document.createElement('p');
    description.className = 'text-sm text-gray-600 dark:text-gray-400 mb-4';
    description.textContent = 'The complete backtesting strategy combines all AI-generated methods with universal template components to create a fully functional trading strategy.';
    section.appendChild(description);

    return section;
}

/**
 * Create a minimalistic code block with syntax highlighting
 * @param {string} title - The title of the code block
 * @param {string} code - The code content
 * @param {string} description - Description of what the code does
 * @param {string} id - Unique ID for the code block
 * @returns {HTMLElement} - The code block element
 */
function createCodeBlock(title, code, description, id) {
    const blockContainer = document.createElement('div');
    blockContainer.className = 'mb-4 last:mb-0';

    blockContainer.innerHTML = `
        <div class="mb-2">
            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">${title}</h5>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${description}</p>
        </div>
        <div class="relative">
            <pre id="${id}" class="bg-gray-900 text-gray-100 rounded-md p-3 overflow-x-auto text-xs" style="max-height: 250px;"><code class="language-python">${escapeHtml(code)}</code></pre>
        </div>
    `;

    return blockContainer;
}

/**
 * Escape HTML special characters to prevent XSS
 * @param {string} html - The HTML string to escape
 * @returns {string} - The escaped HTML string
 */
function escapeHtml(html) {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
}







// Expose functions to global scope
window.displayStrategyCodeBlocks = displayStrategyCodeBlocks;
window.createStrategyOverviewSection = createStrategyOverviewSection;
window.createAIGeneratedCodeSection = createAIGeneratedCodeSection;
window.createAIGeneratedSchemasSection = createAIGeneratedSchemasSection;
window.createOptimizationConfigSection = createOptimizationConfigSection;
window.createHardcodedHelpersSection = createHardcodedHelpersSection;
window.createChartGenerationSection = createChartGenerationSection;

window.createCodeBlock = createCodeBlock;
window.escapeHtml = escapeHtml;

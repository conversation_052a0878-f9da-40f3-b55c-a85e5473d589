/**
 * Backtest Strategy JavaScript Module
 *
 * This module handles the backtest strategy functionality including:
 * - Loading available data sources
 * - Handling form submission
 * - Displaying example questions
 * - Tracking backtest progress
 * - Displaying results
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Backtest Strategy module loaded');

// Polyfill for crypto.randomUUID if not available
if (!crypto.randomUUID) {
    crypto.randomUUID = function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    };
}

    // Initialize the backtest strategy functionality
    initializeBacktestStrategy();
});

/**
 * Initialize the backtest strategy functionality
 */
function initializeBacktestStrategy() {
    // Load data sources
    loadDataSources();

    // Set up form submission handler
    setup<PERSON><PERSON><PERSON><PERSON><PERSON>();

    // Set up ticker extraction from business question
    setupTickerExtraction();

    // Load example business questions
    loadExampleQuestions();
}

/**
 * Load available data sources from the API
 */
async function loadDataSources() {
    try {
        // Fetch data sources from the API
        const response = await fetch('/api/options/data-loaders');
        const data = await response.json();

        // Get the data source select element
        const dataSourceSelect = document.getElementById('data_source');

        // Clear existing options except the first one
        while (dataSourceSelect.options.length > 1) {
            dataSourceSelect.remove(1);
        }

        // Add data sources to the select element
        if (data && data.data_loaders && Array.isArray(data.data_loaders)) {
            data.data_loaders.forEach(source => {
                const option = document.createElement('option');
                option.value = source;
                option.textContent = source;
                dataSourceSelect.appendChild(option);
            });

            // Load saved data source from localStorage
            const savedDataSource = localStorage.getItem('backtest-data-source');

            // Set the data source value (either saved or first available)
            if (savedDataSource && data.data_loaders.includes(savedDataSource)) {
                dataSourceSelect.value = savedDataSource;
            } else if (data.data_loaders.length > 0) {
                dataSourceSelect.value = data.data_loaders[0];
                // Save the default selection
                localStorage.setItem('backtest-data-source', data.data_loaders[0]);
            }
        }

        // Add event listener to save data source selection
        dataSourceSelect.addEventListener('change', function() {
            localStorage.setItem('backtest-data-source', this.value);
        });

    } catch (error) {
        console.error('Error loading data sources:', error);
    }
}

/**
 * Set up the form submission handler
 */
function setupFormHandler() {
    const form = document.getElementById('backtest-form');

    form.addEventListener('submit', async function(event) {
        event.preventDefault();

        // Get form data
        const dataSource = document.getElementById('data_source').value;
        const ticker = document.getElementById('ticker').value;
        const businessQuestion = document.getElementById('business_question').value;

        // Validate form data
        if (!dataSource) {
            alert('Please select a data source');
            return;
        }

        if (!ticker) {
            alert('Please enter a ticker symbol');
            return;
        }

        if (!businessQuestion) {
            alert('Please enter a business question');
            return;
        }

        // Show progress indicator
        showProgress();

        // Disable submit button
        document.getElementById('submit-button').disabled = true;

        try {
            // Submit the form data to the API
            const response = await fetch('/api/backtest-strategy/run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data_source: dataSource,
                    ticker: ticker,
                    business_question: businessQuestion
                })
            });

            const data = await response.json();

            if (data.task_id) {
                // Start polling for task status
                pollTaskStatus(data.task_id);
            } else {
                // Show error
                showError('No task ID returned from the server');
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            showError('Error submitting form: ' + error.message);
        }
    });
}

/**
 * Poll the task status from the API
 * @param {string} taskId - The ID of the task to poll
 */
async function pollTaskStatus(taskId) {
    try {
        // Update progress status
        updateProgressStatus('Checking task status...');

        // Fetch task status from the API
        const response = await fetch(`/api/backtest-strategy/status/${taskId}`);
        const data = await response.json();

        if (data.status === 'completed') {
            // Task completed successfully
            updateProgressStatus('Backtest completed successfully!');
            updateProgressBar(100);

            // Show results
            showResults(data);

            // Hide progress indicator after a delay
            setTimeout(() => {
                hideProgress();
                // Enable submit button
                document.getElementById('submit-button').disabled = false;
            }, 1000);
        } else if (data.status === 'failed') {
            // Task failed
            showError('Backtest failed: ' + (data.error || 'Unknown error'));

            // Hide progress indicator
            hideProgress();

            // Enable submit button
            document.getElementById('submit-button').disabled = false;
        } else {
            // Task still in progress
            const progress = data.progress || 0;
            updateProgressBar(progress);
            updateProgressStatus(data.message || 'Processing...');

            // Poll again after a delay
            setTimeout(() => pollTaskStatus(taskId), 2000);
        }
    } catch (error) {
        console.error('Error polling task status:', error);
        showError('Error polling task status: ' + error.message);

        // Hide progress indicator
        hideProgress();

        // Enable submit button
        document.getElementById('submit-button').disabled = false;
    }
}

/**
 * Show the progress indicator
 */
function showProgress() {
    const progressContainer = document.getElementById('progress-container');
    progressContainer.classList.remove('hidden');

    // Hide results container if visible
    const resultsContainer = document.getElementById('results-container');
    resultsContainer.classList.add('hidden');

    // Reset progress bar
    updateProgressBar(0);
    updateProgressStatus('Initializing backtest...');
}

/**
 * Hide the progress indicator
 */
function hideProgress() {
    const progressContainer = document.getElementById('progress-container');
    progressContainer.classList.add('hidden');
}

/**
 * Update the progress bar
 * @param {number} progress - The progress percentage (0-100)
 */
function updateProgressBar(progress) {
    const progressBar = document.getElementById('progress-bar');
    progressBar.style.width = `${progress}%`;
}

/**
 * Update the progress status message
 * @param {string} message - The status message to display
 */
function updateProgressStatus(message) {
    const progressStatus = document.getElementById('progress-status');
    progressStatus.textContent = message;
}

/**
 * Show an error message
 * @param {string} message - The error message to display
 */
function showError(message) {
    // Hide progress indicator
    hideProgress();

    // Use markdown display for errors if available
    if (window.markdownDisplay && window.markdownDisplay.displayMarkdownError) {
        window.markdownDisplay.displayMarkdownError(message);
        return;
    }

    // Fallback to original error display
    // Show results container with error message
    const resultsContainer = document.getElementById('results-container');
    resultsContainer.classList.remove('hidden');

    const resultsContent = document.getElementById('results-content');
    resultsContent.innerHTML = `
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">${message}</span>
        </div>
    `;
}

/**
 * Show the backtest results
 * @param {Object} data - The results data from the API
 */
function showResults(data) {
    console.log('showResults called with data:', data);
    console.log('Data keys:', Object.keys(data));
    console.log('Has supervisor_evaluation:', !!data.supervisor_evaluation);
    console.log('Supervisor evaluation data:', data.supervisor_evaluation);

    // Check if markdown display is available
    if (window.markdownDisplay && data.analysis) {
        console.log('Using markdown display path');

        // Use markdown display for analysis
        window.markdownDisplay.displayMarkdownAnalysis(data.analysis, data.chart_path);

        // Add strategy implementation section if code is available
        if (data.code && data.strategy_methods) {
            const resultsContent = document.getElementById('results-content');
            const strategyCodeSection = `
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm mt-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Strategy Implementation</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div id="strategy-code-blocks">
                            <!-- Strategy code blocks will be populated here -->
                        </div>
                    </div>
                </div>
            `;
            resultsContent.innerHTML += strategyCodeSection;

            // Populate strategy code blocks
            if (typeof window.displayStrategyCodeBlocks === 'function') {
                window.displayStrategyCodeBlocks(data, 'strategy-code-blocks');
            }
        }

        // Display supervisor evaluation if available (ADDED FOR MARKDOWN PATH)
        console.log('Checking supervisor evaluation in markdown path:', {
            hasSupervisorEvaluation: !!data.supervisor_evaluation,
            hasDisplayFunction: !!window.supervisorEvaluationDisplay,
            supervisorData: data.supervisor_evaluation
        });

        if (data.supervisor_evaluation && window.supervisorEvaluationDisplay) {
            console.log('Displaying supervisor evaluation in markdown path:', data.supervisor_evaluation);
            window.supervisorEvaluationDisplay.displayEvaluation(data.supervisor_evaluation);
        } else {
            if (!data.supervisor_evaluation) {
                console.warn('No supervisor evaluation data found in markdown path');
            }
            if (!window.supervisorEvaluationDisplay) {
                console.warn('Supervisor evaluation display function not available in markdown path');
            }
        }

        return;
    }

    // Fallback to original display method
    // Show results container
    const resultsContainer = document.getElementById('results-container');
    resultsContainer.classList.remove('hidden');

    const resultsContent = document.getElementById('results-content');

    // Check if there's a chart path and generated code
    if (data.chart_path && data.code) {
        resultsContent.innerHTML = `
            <div class="space-y-6">
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">Backtest strategy executed successfully.</span>
                </div>

                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Strategy Description</h4>
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        ${data.analysis || 'No analysis available.'}
                    </div>
                </div>

                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Strategy Performance Chart</h4>
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <img src="${data.chart_path}" alt="Backtest Strategy Chart" class="w-full h-auto rounded-lg shadow-lg">
                    </div>
                </div>

                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Strategy Implementation</h4>
                    <div id="strategy-code-blocks">
                        <!-- Strategy code blocks will be populated here -->
                    </div>
                </div>
            </div>
        `;

        // Display strategy code blocks using the code display module
        if (typeof window.displayStrategyCodeBlocks === 'function') {
            console.log('Calling displayStrategyCodeBlocks with data:', data);
            window.displayStrategyCodeBlocks(data, 'strategy-code-blocks');
        } else {
            console.error('displayStrategyCodeBlocks function not available');
            console.log('Available functions:', Object.getOwnPropertyNames(window).filter(name => typeof window[name] === 'function'));
        }

        // Display supervisor evaluation if available
        console.log('Checking supervisor evaluation:', {
            hasSupervisorEvaluation: !!data.supervisor_evaluation,
            hasDisplayFunction: !!window.supervisorEvaluationDisplay,
            supervisorData: data.supervisor_evaluation
        });

        if (data.supervisor_evaluation && window.supervisorEvaluationDisplay) {
            console.log('Displaying supervisor evaluation for successful case:', data.supervisor_evaluation);
            window.supervisorEvaluationDisplay.displayEvaluation(data.supervisor_evaluation);
        } else {
            if (!data.supervisor_evaluation) {
                console.warn('No supervisor evaluation data found in response');
            }
            if (!window.supervisorEvaluationDisplay) {
                console.warn('Supervisor evaluation display function not available');
            }
        }
    }
    // Only code was generated but execution failed
    else if (data.code) {
        resultsContent.innerHTML = `
            <div class="space-y-6">
                <div class="${data.error ? 'bg-yellow-100 border border-yellow-400 text-yellow-700' : 'bg-green-100 border border-green-400 text-green-700'} px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">${data.error ? 'Warning:' : 'Success!'}</strong>
                    <span class="block sm:inline">${data.error ? 'Code generated but execution failed.' : 'Backtest code generated successfully.'}</span>
                </div>

                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Strategy Description</h4>
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        ${data.analysis || 'No analysis available.'}
                    </div>
                </div>

                ${data.error ? `
                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Execution Error</h4>
                    <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4 text-red-700 dark:text-red-400">
                        ${data.error}
                    </div>
                </div>
                ` : ''}

                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Strategy Implementation</h4>
                    <div id="strategy-code-blocks-failed">
                        <!-- Strategy code blocks will be populated here -->
                    </div>
                </div>

                <div class="mt-4 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <h4 class="text-lg font-medium text-yellow-800 dark:text-yellow-300 mb-2">Note</h4>
                    <p class="text-yellow-700 dark:text-yellow-400">
                        ${data.error ? 'Execution failed, but you can still use this code in your own environment:' : 'This code can be used in your own environment:'}
                        <ol class="list-decimal list-inside mt-2 space-y-1">
                            <li>Copy the code to a Python environment</li>
                            <li>Install required dependencies (pandas, numpy, matplotlib, mplfinance)</li>
                            <li>Load data for the specified ticker</li>
                            <li>Call the create_chart function with your data</li>
                        </ol>
                    </p>
                </div>
            </div>
        `;

        // Display strategy code blocks using the code display module
        if (typeof window.displayStrategyCodeBlocks === 'function') {
            console.log('Calling displayStrategyCodeBlocks for failed case with data:', data);
            window.displayStrategyCodeBlocks(data, 'strategy-code-blocks-failed');
        } else {
            console.error('displayStrategyCodeBlocks function not available');
            console.log('Available functions:', Object.getOwnPropertyNames(window).filter(name => typeof window[name] === 'function'));
        }

        // Display supervisor evaluation if available (even for failed cases)
        if (data.supervisor_evaluation && window.supervisorEvaluationDisplay) {
            console.log('Displaying supervisor evaluation for failed case:', data.supervisor_evaluation);
            window.supervisorEvaluationDisplay.displayEvaluation(data.supervisor_evaluation);
        }
    } else {
        // No code, just show the analysis or error
        resultsContent.innerHTML = `
            <div class="space-y-4">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error:</strong>
                    <span class="block sm:inline">${data.error || 'An unknown error occurred.'}</span>
                </div>

                <div class="mt-4">
                    <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Analysis</h4>
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        ${data.analysis || 'No analysis available.'}
                    </div>
                </div>
            </div>
        `;
    }
}

/**
 * Escape HTML special characters to prevent XSS
 * @param {string} html - The HTML string to escape
 * @returns {string} - The escaped HTML string
 */
function escapeHtml(html) {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
}



/**
 * Fill the business question input with an example question
 * @param {HTMLElement} element - The element containing the example question
 */
function fillExampleQuestion(element) {
    const question = element.textContent.trim();
    const businessQuestionInput = document.getElementById('business_question');
    businessQuestionInput.value = question;

    // Extract ticker from the question
    extractTickerFromQuestion(question);
}

/**
 * Set up ticker extraction from business question
 */
function setupTickerExtraction() {
    const businessQuestionInput = document.getElementById('business_question');

    businessQuestionInput.addEventListener('blur', function() {
        const question = businessQuestionInput.value.trim();
        if (question) {
            extractTickerFromQuestion(question);
        }
    });
}

/**
 * Extract ticker from business question and update the ticker input
 * @param {string} question - The business question to extract ticker from
 */
function extractTickerFromQuestion(question) {
    // Look for ticker in parentheses, e.g., (AAPL)
    const tickerMatch = question.match(/\(([A-Z]+)\)/);

    if (tickerMatch && tickerMatch[1]) {
        const ticker = tickerMatch[1];
        document.getElementById('ticker').value = ticker;
    }
}

/**
 * Load example business questions from the example_questions.js file
 * and populate the example questions container with checkboxes
 */
function loadExampleQuestions() {
    // Get the example questions container
    const exampleQuestionsContainer = document.getElementById('example-questions-container');

    if (!exampleQuestionsContainer) {
        console.error('Example questions container not found');
        return;
    }

    // Clear existing example questions
    exampleQuestionsContainer.innerHTML = '';

    // Add each example question to the container
    if (typeof exampleBusinessQuestions !== 'undefined' && Array.isArray(exampleBusinessQuestions)) {
        exampleBusinessQuestions.forEach((question, index) => {
            const li = document.createElement('li');
            li.className = 'flex items-start space-x-3 p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600';

            // Create checkbox
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `question-checkbox-${index}`;
            checkbox.className = 'mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded';

            // Load checkbox state from localStorage
            const checkboxKey = `backtest-question-${index}`;
            const isChecked = localStorage.getItem(checkboxKey) === 'true';
            checkbox.checked = isChecked;

            // Save checkbox state on change
            checkbox.addEventListener('change', function() {
                localStorage.setItem(checkboxKey, this.checked);
            });

            // Create question text
            const questionText = document.createElement('span');
            questionText.className = 'cursor-pointer hover:text-indigo-600 dark:hover:text-indigo-400 flex-1';
            questionText.textContent = question;
            questionText.onclick = function() {
                fillExampleQuestion(this);
            };

            // Assemble the list item
            li.appendChild(checkbox);
            li.appendChild(questionText);
            exampleQuestionsContainer.appendChild(li);
        });
    } else {
        console.error('Example business questions not found or not an array');
        // Add a fallback message
        const li = document.createElement('li');
        li.textContent = 'Example questions could not be loaded';
        exampleQuestionsContainer.appendChild(li);
    }
}

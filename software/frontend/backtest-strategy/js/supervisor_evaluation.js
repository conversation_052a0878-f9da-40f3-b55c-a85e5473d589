/**
 * Supervisor Evaluation Display Module
 * Handles the display and visualization of supervisor evaluation scores
 */

class SupervisorEvaluationDisplay {
    constructor() {
        console.log('SupervisorEvaluationDisplay constructor called');
        this.supervisorContainer = null;
        this.initializeContainer();
    }

    /**
     * Initialize the supervisor evaluation container
     */
    initializeContainer() {
        console.log('Initializing supervisor evaluation container...');

        // Find or create the supervisor container
        this.supervisorContainer = document.getElementById('supervisor-evaluation-container');

        if (!this.supervisorContainer) {
            console.log('Creating new supervisor evaluation container');

            // Create container if it doesn't exist
            this.supervisorContainer = document.createElement('div');
            this.supervisorContainer.id = 'supervisor-evaluation-container';
            this.supervisorContainer.className = 'mt-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700';
            this.supervisorContainer.style.display = 'none'; // Hidden by default

            // Insert before the results container content, but after the results container itself
            const resultsContent = document.getElementById('results-content');
            if (resultsContent && resultsContent.parentNode) {
                // Insert at the beginning of results-content
                resultsContent.parentNode.insertBefore(this.supervisorContainer, resultsContent);
                console.log('Supervisor evaluation container inserted before results content');
            } else {
                // Fallback: try results container
                const resultsContainer = document.getElementById('results-container');
                if (resultsContainer && resultsContainer.parentNode) {
                    resultsContainer.parentNode.insertBefore(this.supervisorContainer, resultsContainer.nextSibling);
                    console.log('Supervisor evaluation container inserted after results container (fallback)');
                } else {
                    // Last resort: append to body
                    document.body.appendChild(this.supervisorContainer);
                    console.log('Supervisor evaluation container appended to body as last resort');
                }
            }
        } else {
            console.log('Found existing supervisor evaluation container');
        }
    }

    /**
     * Display supervisor evaluation results
     * @param {Object} evaluation - Supervisor evaluation data
     */
    displayEvaluation(evaluation) {
        console.log('SupervisorEvaluationDisplay.displayEvaluation called with:', evaluation);

        if (!evaluation) {
            console.warn('No evaluation data provided, hiding supervisor evaluation');
            this.hideSupervisorEvaluation();
            return;
        }

        // Reinitialize container positioning since DOM may have changed
        this.repositionContainer();

        console.log('Creating supervisor evaluation HTML...');

        // Create the supervisor evaluation HTML
        const evaluationHTML = this.createEvaluationHTML(evaluation);

        // Update container content
        this.supervisorContainer.innerHTML = evaluationHTML;
        this.supervisorContainer.style.display = 'block';

        console.log('Supervisor evaluation displayed successfully');

        // Add progress bar animations
        this.animateProgressBars();
    }

    /**
     * Reposition the container to ensure it's in the right place
     */
    repositionContainer() {
        if (!this.supervisorContainer) return;

        console.log('Repositioning supervisor evaluation container...');

        // Remove from current position
        if (this.supervisorContainer.parentNode) {
            this.supervisorContainer.parentNode.removeChild(this.supervisorContainer);
        }

        // Find the best position - before results-content
        const resultsContent = document.getElementById('results-content');
        if (resultsContent && resultsContent.parentNode) {
            resultsContent.parentNode.insertBefore(this.supervisorContainer, resultsContent);
            console.log('Supervisor evaluation container repositioned before results content');
        } else {
            // Fallback: after results container
            const resultsContainer = document.getElementById('results-container');
            if (resultsContainer && resultsContainer.parentNode) {
                resultsContainer.parentNode.insertBefore(this.supervisorContainer, resultsContainer.nextSibling);
                console.log('Supervisor evaluation container repositioned after results container');
            }
        }
    }

    /**
     * Create HTML for supervisor evaluation display
     * @param {Object} evaluation - Supervisor evaluation data
     * @returns {string} HTML string
     */
    createEvaluationHTML(evaluation) {
        const overallScoreColor = this.getScoreColor(evaluation.overall_score);

        return `
            <div class="supervisor-evaluation">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                        <svg class="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Supervisor Evaluation
                    </h3>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mr-2">Overall Score:</span>
                        <span class="text-2xl font-bold ${overallScoreColor}">${evaluation.overall_score}/100</span>
                    </div>
                </div>

                <!-- Score Breakdown -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    ${this.createScoreCard('Report Readability', evaluation.report_readability_score, 'How clear and well-structured is the report?')}
                    ${this.createScoreCard('Code-Strategy Alignment', evaluation.code_strategy_alignment_score, 'Does the code match the business question?')}
                    ${this.createScoreCard('Optimization Ranges', evaluation.optimization_ranges_realism_score, 'Are parameter ranges realistic and appropriate?')}
                    ${this.createScoreCard('Analysis Quality', evaluation.analysis_quality_score, 'Quality of analysis and recommendations')}
                </div>

                <!-- Detailed Feedback -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Detailed Feedback</h4>
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">${evaluation.detailed_feedback}</p>
                    </div>
                </div>

                <!-- Strengths and Improvements -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-green-700 dark:text-green-400 mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Key Strengths
                        </h4>
                        <ul class="space-y-2">
                            ${evaluation.key_strengths.map(strength =>
                                `<li class="flex items-start">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    <span class="text-gray-700 dark:text-gray-300">${strength}</span>
                                </li>`
                            ).join('')}
                        </ul>
                    </div>

                    <div>
                        <h4 class="text-lg font-semibold text-orange-700 dark:text-orange-400 mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            Areas for Improvement
                        </h4>
                        <ul class="space-y-2">
                            ${evaluation.improvement_areas.map(area =>
                                `<li class="flex items-start">
                                    <span class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    <span class="text-gray-700 dark:text-gray-300">${area}</span>
                                </li>`
                            ).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create a score card for individual metrics
     * @param {string} title - Score title
     * @param {number} score - Score value (0-100)
     * @param {string} description - Score description
     * @returns {string} HTML string
     */
    createScoreCard(title, score, description) {
        const scoreColor = this.getScoreColor(score);
        const progressWidth = Math.max(score, 5); // Minimum 5% for visibility

        return `
            <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                <div class="flex justify-between items-center mb-2">
                    <h5 class="font-semibold text-gray-900 dark:text-white">${title}</h5>
                    <span class="text-lg font-bold ${scoreColor}">${score}/100</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">${description}</p>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="progress-bar h-2 rounded-full transition-all duration-1000 ease-out ${this.getProgressBarColor(score)}"
                         style="width: 0%" data-target-width="${progressWidth}%"></div>
                </div>
            </div>
        `;
    }

    /**
     * Get color class based on score
     * @param {number} score - Score value (0-100)
     * @returns {string} CSS color class
     */
    getScoreColor(score) {
        if (score >= 90) return 'text-green-600 dark:text-green-400';
        if (score >= 80) return 'text-blue-600 dark:text-blue-400';
        if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
        if (score >= 60) return 'text-orange-600 dark:text-orange-400';
        return 'text-red-600 dark:text-red-400';
    }

    /**
     * Get progress bar color class based on score
     * @param {number} score - Score value (0-100)
     * @returns {string} CSS background color class
     */
    getProgressBarColor(score) {
        if (score >= 90) return 'bg-green-500';
        if (score >= 80) return 'bg-blue-500';
        if (score >= 70) return 'bg-yellow-500';
        if (score >= 60) return 'bg-orange-500';
        return 'bg-red-500';
    }

    /**
     * Animate progress bars
     */
    animateProgressBars() {
        setTimeout(() => {
            const progressBars = this.supervisorContainer.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const targetWidth = bar.getAttribute('data-target-width');
                bar.style.width = targetWidth;
            });
        }, 100);
    }

    /**
     * Hide supervisor evaluation section
     */
    hideSupervisorEvaluation() {
        if (this.supervisorContainer) {
            this.supervisorContainer.style.display = 'none';
        }
    }

    /**
     * Show supervisor evaluation section
     */
    showSupervisorEvaluation() {
        if (this.supervisorContainer) {
            this.supervisorContainer.style.display = 'block';
        }
    }
}

// Create global instance
console.log('Creating global SupervisorEvaluationDisplay instance...');
window.supervisorEvaluationDisplay = new SupervisorEvaluationDisplay();
console.log('Global SupervisorEvaluationDisplay instance created:', window.supervisorEvaluationDisplay);

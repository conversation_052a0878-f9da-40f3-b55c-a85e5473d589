/**
 * Markdown Display Module for Backtest Strategy Results
 * Handles rendering of markdown analysis reports from LLM vision analysis
 */

/**
 * Convert markdown text to HTML using marked.js library
 * @param {string} markdown - The markdown text to convert
 * @returns {string} HTML string
 */
function markdownToHtml(markdown) {
    if (!markdown) return '';

    // Check if marked library is available
    if (typeof marked === 'undefined') {
        console.error('Marked.js library not loaded');
        return `<p class="text-red-500">Error: Markdown parser not available</p>`;
    }

    try {
        // Configure marked with custom renderer for styling
        const renderer = new marked.Renderer();

        // Custom heading renderer with Tailwind classes
        renderer.heading = function(text, level) {
            const classes = {
                1: 'text-3xl font-bold text-gray-900 dark:text-gray-100 mt-8 mb-6 border-b border-gray-200 dark:border-gray-700 pb-2',
                2: 'text-2xl font-bold text-gray-900 dark:text-gray-100 mt-6 mb-4',
                3: 'text-xl font-semibold text-gray-800 dark:text-gray-200 mt-5 mb-3',
                4: 'text-lg font-semibold text-gray-800 dark:text-gray-200 mt-4 mb-2',
                5: 'text-base font-semibold text-gray-800 dark:text-gray-200 mt-3 mb-2',
                6: 'text-sm font-semibold text-gray-800 dark:text-gray-200 mt-2 mb-1'
            };
            return `<h${level} class="${classes[level] || classes[6]}">${text}</h${level}>`;
        };

        // Custom paragraph renderer
        renderer.paragraph = function(text) {
            return `<p class="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">${text}</p>`;
        };

        // Custom list renderer
        renderer.list = function(body, ordered) {
            const tag = ordered ? 'ol' : 'ul';
            const classes = ordered ? 'list-decimal list-inside space-y-1 mb-4 ml-4' : 'list-disc list-inside space-y-1 mb-4 ml-4';
            return `<${tag} class="${classes}">${body}</${tag}>`;
        };

        renderer.listitem = function(text) {
            return `<li class="text-gray-700 dark:text-gray-300">${text}</li>`;
        };

        // Custom strong/bold renderer
        renderer.strong = function(text) {
            return `<strong class="font-bold text-gray-900 dark:text-gray-100">${text}</strong>`;
        };

        // Custom emphasis/italic renderer
        renderer.em = function(text) {
            return `<em class="italic text-gray-700 dark:text-gray-300">${text}</em>`;
        };

        // Custom code renderer
        renderer.code = function(code, language) {
            return `<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-4 border border-gray-200 dark:border-gray-700"><code class="text-sm text-gray-800 dark:text-gray-200 font-mono">${code}</code></pre>`;
        };

        renderer.codespan = function(code) {
            return `<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm text-gray-800 dark:text-gray-200 font-mono border border-gray-200 dark:border-gray-600">${code}</code>`;
        };

        // Custom blockquote renderer
        renderer.blockquote = function(quote) {
            return `<blockquote class="border-l-4 border-blue-500 pl-4 py-2 mb-4 bg-blue-50 dark:bg-blue-900/20 text-gray-700 dark:text-gray-300 italic">${quote}</blockquote>`;
        };

        // Configure marked options
        marked.setOptions({
            renderer: renderer,
            gfm: true,
            breaks: true,
            sanitize: false
        });

        // Parse markdown to HTML
        let html = marked.parse(markdown);

        // Post-process for emoji and special indicators
        html = html.replace(/❌/g, '<span class="text-red-500 text-lg">❌</span>');
        html = html.replace(/✅/g, '<span class="text-green-500 text-lg">✅</span>');
        html = html.replace(/⚠️/g, '<span class="text-yellow-500 text-lg">⚠️</span>');
        html = html.replace(/🎉/g, '<span class="text-blue-500 text-lg">🎉</span>');
        html = html.replace(/📊/g, '<span class="text-purple-500 text-lg">📊</span>');
        html = html.replace(/📈/g, '<span class="text-green-500 text-lg">📈</span>');
        html = html.replace(/📉/g, '<span class="text-red-500 text-lg">📉</span>');

        return html;

    } catch (error) {
        console.error('Error parsing markdown:', error);
        return `<p class="text-red-500">Error parsing markdown: ${error.message}</p>`;
    }
}

/**
 * Display markdown analysis in the results container
 * @param {string} markdownContent - The markdown content to display
 * @param {string} chartPath - Optional chart image path
 */
function displayMarkdownAnalysis(markdownContent, chartPath = null) {
    const resultsContainer = document.getElementById('results-container');
    const resultsContent = document.getElementById('results-content');

    if (!resultsContainer || !resultsContent) {
        console.error('Results container elements not found');
        return;
    }

    // Show results container
    resultsContainer.classList.remove('hidden');

    // Convert markdown to HTML
    const htmlContent = markdownToHtml(markdownContent);

    // Create the results HTML structure
    let resultsHtml = `
        <div class="space-y-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                            Analysis Complete
                        </h3>
                        <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                            <p>Backtest strategy analysis has been completed with LLM vision analysis.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Strategy Analysis Report</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="max-w-none">
                        ${htmlContent}
                    </div>
                </div>
            </div>
    `;

    // Add chart section if chart path is provided
    if (chartPath) {
        resultsHtml += `
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Strategy Performance Chart</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="flex justify-center">
                        <img src="${chartPath}" alt="Backtest Strategy Chart" class="max-w-full h-auto rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    </div>
                </div>
            </div>
        `;
    }

    resultsHtml += '</div>';

    // Set the content
    resultsContent.innerHTML = resultsHtml;
}

/**
 * Display error message in markdown format
 * @param {string} errorMessage - The error message to display
 * @param {string} details - Optional additional details
 */
function displayMarkdownError(errorMessage, details = '') {
    const markdownError = `
# ❌ Strategy Execution Error

## Error Details
${errorMessage}

${details ? `## Additional Information\n${details}` : ''}

## Recommendations
- Check the business question for clarity and logical consistency
- Ensure the strategy conditions are realistic and achievable
- Consider revising entry and exit criteria
- Verify that the ticker symbol is valid and has sufficient data
`;

    displayMarkdownAnalysis(markdownError);
}

/**
 * Display loading state for markdown analysis
 */
function displayMarkdownLoading() {
    const resultsContainer = document.getElementById('results-container');
    const resultsContent = document.getElementById('results-content');

    if (!resultsContainer || !resultsContent) {
        console.error('Results container elements not found');
        return;
    }

    // Show results container
    resultsContainer.classList.remove('hidden');

    resultsContent.innerHTML = `
        <div class="space-y-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="animate-spin h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                            Analyzing Results
                        </h3>
                        <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                            <p>Generating comprehensive analysis with LLM vision capabilities...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Export functions for use in other modules
window.markdownDisplay = {
    markdownToHtml,
    displayMarkdownAnalysis,
    displayMarkdownError,
    displayMarkdownLoading
};

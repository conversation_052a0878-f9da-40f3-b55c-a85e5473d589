{% extends "base.html" %}

{% block title %}Backtest Strategy{% endblock %}
{% block header %}Backtest Strategy{% endblock %}

{% block content %}
<div class="container mx-auto p-6 space-y-6">
    <!-- Main Backtest Strategy Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-semibold mb-6 dark:text-white">Backtest Strategy Configuration</h2>

        <form id="backtest-form" class="space-y-6">
            <!-- Data Source Selection -->
            <div>
                <label for="data_source" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Data Source</label>
                <select id="data_source" name="data_source" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="">Select a data source</option>
                </select>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Choose the data source for historical market data</p>
            </div>

            <!-- Ticker Symbol Input -->
            <div>
                <label for="ticker" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ticker Symbol</label>
                <input type="text" id="ticker" name="ticker" value="AAPL" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Enter ticker symbol (e.g., AAPL, MSFT, GOOGL)">
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Enter the stock ticker symbol to analyze</p>
            </div>

            <!-- Business Question Input -->
            <div>
                <label for="business_question" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Business Question</label>
                <textarea id="business_question" name="business_question" rows="4" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Describe the backtesting strategy you want to evaluate..."></textarea>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Describe the trading strategy you want to backtest (e.g., "Backtest a strategy that buys when RSI is below 30 and sells when it's above 70")</p>
            </div>

            <!-- Example Questions -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Example Business Questions:</h3>
                <ul id="example-questions-container" class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <!-- Example questions will be loaded from JavaScript -->
                </ul>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" id="submit-button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Run Backtest
                </button>
            </div>
        </form>

        <!-- Progress Indicator (Hidden by default) -->
        <div id="progress-container" class="mt-6 hidden">
            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Backtesting in Progress</h3>
            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                <div id="progress-bar" class="bg-indigo-600 h-2.5 rounded-full" style="width: 0%"></div>
            </div>
            <p id="progress-status" class="mt-2 text-sm text-gray-500 dark:text-gray-400">Initializing backtest...</p>
        </div>

        <!-- Results Container (Hidden by default) -->
        <div id="results-container" class="mt-6 hidden">
            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Backtest Results</h3>
            <div id="results-content" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Markdown Parser -->
<script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>

<!-- Syntax Highlighting -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>

<!-- Custom Scripts -->
<script src="/frontend/backtest-strategy/js/markdown_display.js"></script>
<script src="/frontend/backtest-strategy/js/example_questions.js"></script>
<script src="/frontend/backtest-strategy/js/code_display.js"></script>
<script src="/frontend/backtest-strategy/js/supervisor_evaluation.js"></script>
<script src="/frontend/backtest-strategy/js/backtest_strategy.js"></script>
{% endblock %}

// UI Handler for Research Director page
class UIHandler {
    static formatDate(dateString) {
        return new Date(dateString).toLocaleString();
    }

    /**
     * Truncate text to a specified length and add ellipsis if needed
     * @param {string} text - The text to truncate
     * @param {number} maxLength - Maximum length before truncation
     * @returns {string} - Truncated text with ellipsis if needed
     */
    static truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    static createDirectorCard(director) {
        const directorId = director.id || director._id;
        const card = document.createElement('div');
        card.className = 'director-card bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 mb-3';
        card.dataset.directorId = directorId;

        // Create the header section (always visible)
        const headerSection = `
            <!-- Header Section -->
            <div class="flex justify-between items-center py-3 px-4 cursor-pointer director-header" data-director-id="${directorId}">
                <div class="flex-shrink-0 min-w-0 mr-2">
                    <div class="flex items-center">
                        <h2 class="text-lg sm:text-xl font-semibold tracking-tight text-gray-900 dark:text-gray-100 truncate">${director.name}</h2>
                        ${director.total_reports > 0 ?
                            `<span class="ml-2 inline-flex items-center justify-center h-5 min-w-5 px-1.5 text-xs font-medium leading-none rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/70 dark:text-indigo-200">${director.total_reports}</span>`
                            : ''}
                    </div>
                    <p class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">${director.title}</p>
                </div>
                <div class="flex items-center space-x-1 sm:space-x-2">
                    <!-- Graph Selection Dropdown -->
                    <div class="relative">
                        <select id="graph-select-${directorId}"
                                class="block w-24 sm:w-32 md:w-40 pl-2 pr-6 py-1 text-xs md:text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-gray-200">
                            <option value="">Graph Type</option>
                            <!-- Graph options will be populated dynamically -->
                        </select>
                    </div>
                    <button class="deploy-button inline-flex items-center px-2 sm:px-3 py-1 border border-transparent text-xs sm:text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-indigo-500 transition-colors duration-200" data-director-id="${directorId}">
                        <svg class="w-4 h-4 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span class="hidden md:inline">Deploy</span>
                    </button>
                    <button class="auto-deploy-button inline-flex items-center px-2 sm:px-3 py-1 border border-transparent text-xs sm:text-sm font-medium rounded-md shadow-sm text-white ${director.auto_deploy ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-indigo-500 transition-colors duration-200" data-director-id="${directorId}" data-auto-deploy="${director.auto_deploy ? 'true' : 'false'}">
                        ${director.auto_deploy ?
                            `<svg class="w-4 h-4 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                            </svg>
                            <span class="hidden md:inline">Stop</span>` :
                            `<svg class="w-4 h-4 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="hidden md:inline">Auto</span>`
                        }
                    </button>
                    <button class="clean-memory-button inline-flex items-center px-2 sm:px-3 py-1 border border-transparent text-xs sm:text-sm font-medium rounded-md shadow-sm text-white bg-red-500 hover:bg-red-600 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-red-500 transition-colors duration-200" data-director-id="${directorId}">
                        <svg class="w-4 h-4 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        <span class="hidden md:inline">Clean</span>
                    </button>
                    <button class="connect-memory-button inline-flex items-center px-2 sm:px-3 py-1 border border-transparent text-xs sm:text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-indigo-500 transition-colors duration-200" data-director-id="${directorId}">
                        <svg class="w-4 h-4 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span class="hidden md:inline">Connect</span>
                    </button>
                    <button class="expand-toggle ml-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" data-director-id="${directorId}">
                        <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                </div>
            </div>

        `;

        // Create the content section (only visible when expanded)
        const contentSection = `
            <div class="director-content hidden border-t border-gray-200 dark:border-gray-700">
                <!-- Info Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                    <!-- Expertise Section -->
                    <div class="space-y-2">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                            Expertise
                        </h3>
                        <div class="flex flex-wrap gap-2">
                            ${director.expertise.map(exp => `
                                <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-indigo-50 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-200 border border-indigo-200 dark:border-indigo-800">
                                    ${exp}
                                </span>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Stats Section -->
                    <div class="space-y-2">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            Stats
                        </h3>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-2 border border-gray-200 dark:border-gray-600">
                                <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Total Reports</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">${director.total_reports}</p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-2 border border-gray-200 dark:border-gray-600">
                                <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Experience</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">${director.experience_years} years</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Style, Background & Personality Tabs -->
                <div class="px-4 mb-4">
                    <!-- Tab Navigation -->
                    <div class="flex border-b border-gray-200 dark:border-gray-700">
                        <button class="tab-button py-2 px-4 text-sm font-medium border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400 dark:border-indigo-400 -mb-px"
                                data-tab="analysis-style-${directorId}" data-color="indigo">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                                Analysis Style
                            </div>
                        </button>
                        <button class="tab-button py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600"
                                data-tab="background-${directorId}" data-color="blue">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-blue-500 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Background
                            </div>
                        </button>
                        <button class="tab-button py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600"
                                data-tab="personality-${directorId}" data-color="purple">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-purple-500 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Personality
                            </div>
                        </button>
                    </div>

                    <!-- Tab Content -->
                    <div class="bg-white dark:bg-gray-800 rounded-b-lg shadow-sm border border-t-0 border-gray-200 dark:border-gray-700">
                        <!-- Analysis Style Tab -->
                        <div id="analysis-style-${directorId}" class="tab-content p-4 block">
                            <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">${director.analysis_style}</p>
                        </div>

                        <!-- Background Tab -->
                        <div id="background-${directorId}" class="tab-content p-4 hidden">
                            <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">${director.background}</p>
                        </div>

                        <!-- Personality Tab -->
                        <div id="personality-${directorId}" class="tab-content p-4 hidden">
                            <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">${director.personality}</p>
                        </div>
                    </div>
                </div>

                <!-- Research Reports Section -->
                <div class="px-4 pb-4">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center mb-3">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Research Reports
                    </h3>
                    <div id="reports-${directorId}" class="space-y-3">
                        <!-- Reports will be dynamically inserted here -->
                    </div>
                </div>
            </div>
        `;

        // Combine header and content sections
        card.innerHTML = headerSection + contentSection;

        // Populate graph options
        this.populateGraphOptions(directorId);

        // Add graph selection event listener
        const graphSelect = card.querySelector(`#graph-select-${directorId}`);
        graphSelect.addEventListener('change', async (event) => {
            const graphId = event.target.value;
            if (graphId) {
                try {
                    await ResearchDirectorAPI.updateDirectorGraph(directorId, graphId);
                    this.showToast('Graph type updated successfully', 'success');
                } catch (error) {
                    this.showToast('Failed to update graph type', 'error');
                }
            }
        });

        // Add event listener for header click to toggle expansion
        const headerElement = card.querySelector('.director-header');
        if (headerElement) {
            headerElement.addEventListener('click', (event) => {
                // Don't toggle if clicking on a button or select
                if (event.target.closest('button') || event.target.closest('select')) {
                    return;
                }

                // Toggle expansion through the director router
                window.directorRouter.toggleDirector(directorId);
            });
        }

        // Add event listener for expand toggle button
        const expandToggle = card.querySelector('.expand-toggle');
        if (expandToggle) {
            expandToggle.addEventListener('click', () => {
                window.directorRouter.toggleDirector(directorId);
            });
        }

        // Add event listeners for tab buttons
        const tabButtons = card.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const tabId = button.dataset.tab;
                const tabColor = button.dataset.color;

                // Hide all tab content
                const tabContents = card.querySelectorAll('.tab-content');
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });

                // Show the selected tab content
                const selectedContent = document.getElementById(tabId);
                if (selectedContent) {
                    selectedContent.classList.remove('hidden');
                }

                // Reset all tab buttons
                tabButtons.forEach(btn => {
                    btn.classList.remove(
                        'border-indigo-500', 'border-blue-500', 'border-purple-500',
                        'text-indigo-600', 'text-blue-600', 'text-purple-600',
                        'dark:text-indigo-400', 'dark:text-blue-400', 'dark:text-purple-400',
                        'dark:border-indigo-400', 'dark:border-blue-400', 'dark:border-purple-400'
                    );
                    btn.classList.add('text-gray-500', 'border-transparent');
                });

                // Highlight the active tab button
                button.classList.remove('text-gray-500', 'border-transparent');
                button.classList.add(
                    `border-${tabColor}-500`,
                    `text-${tabColor}-600`,
                    `dark:text-${tabColor}-400`,
                    `dark:border-${tabColor}-400`
                );

                // Prevent event from bubbling up
                event.stopPropagation();
            });
        });

        return card;
    }

    static async populateGraphOptions(directorId) {
        try {
            // Get the director data to know the current graph type
            const director = await ResearchDirectorAPI.getDirector(directorId);
            const { graphs } = await ResearchDirectorAPI.getAvailableGraphs();
            const select = document.getElementById(`graph-select-${directorId}`);

            if (select) {
                // Clear existing options
                select.innerHTML = '';

                graphs.forEach((graph, index) => {
                    const option = document.createElement('option');
                    option.value = graph.id;
                    option.textContent = graph.name;
                    // Set selected if this is the current graph type or first item if none selected
                    if (director.graph_type === graph.id || (!director.graph_type && index === 0)) {
                        option.selected = true;
                        // If no graph type is set, update it to the first one
                        if (!director.graph_type && index === 0) {
                            ResearchDirectorAPI.updateDirectorGraph(directorId, graph.id)
                                .then(() => this.showToast('Default graph type set', 'success'))
                                .catch(() => this.showToast('Failed to set default graph type', 'error'));
                        }
                    }
                    select.appendChild(option);
                });
            }
        } catch (error) {
            this.showToast('Failed to load graph types', 'error');
        }
    }

    static showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            'bg-blue-500'
        } text-white`;
        toast.textContent = message;
        document.body.appendChild(toast);

        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    static async handleDeployClick(event) {
        const button = event.target.closest('.deploy-button'); // Use closest to handle clicks on SVG
        if (!button) {
            this.showToast('Deploy button not found', 'error');
            return;
        }
        const directorId = button.dataset.directorId;

        try {
            // Check if a graph type is selected
            const graphSelect = document.getElementById(`graph-select-${directorId}`);
            if (!graphSelect || !graphSelect.value) {
                this.showToast('Please select a graph type before deploying', 'error');
                return;
            }

            button.disabled = true;
            button.textContent = 'Deploying...';

            // Deploy the director
            const response = await ResearchDirectorAPI.deployDirector(directorId);
            const taskId = response.task_id;

            // Create initial report card with progress bar
            const reportsContainer = document.getElementById(`reports-${directorId}`);

            if (reportsContainer) {
                // Clear the container if it only has the "No reports available" message
                if (reportsContainer.querySelector('p') && reportsContainer.querySelector('p').textContent === 'No reports available yet') {
                    reportsContainer.innerHTML = '';
                }

                const initialCard = document.createElement('div');
                initialCard.id = `report-${taskId}`;
                initialCard.className = 'bg-white rounded-lg shadow-lg p-6 mb-4 border-l-4 border-blue-500';
                initialCard.innerHTML = `
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <div class="text-sm text-gray-500">${this.formatDate(new Date())}</div>
                            <div class="text-xl font-semibold mt-2">New Analysis</div>
                        </div>
                        <div class="px-3 py-1 rounded-full bg-yellow-100 text-yellow-800">
                            In Progress
                        </div>
                    </div>
                `;

                // Add progress bar
                const progressBar = await ProgressHandler.createProgressBar(taskId);

                if (progressBar) {
                    initialCard.appendChild(progressBar);
                    // Add to container
                    reportsContainer.insertBefore(initialCard, reportsContainer.firstChild);

                    // Start progress tracking
                    await ReportHandler.startProgressTracking(taskId);
                } else {
                    this.showToast('Error creating progress bar', 'error');
                }
            } else {
                this.showToast('Error: Reports container not found', 'error');
            }

            this.showToast('Director deployed successfully!', 'success');
        } catch (error) {
            this.showToast('Failed to deploy director', 'error');
        } finally {
            button.disabled = false;
            button.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Deploy
            `;
        }
    }

    static async handleAutoDeployClick(event) {
        const button = event.target.closest('.auto-deploy-button');
        if (!button) {
            this.showToast('Auto-deploy button not found', 'error');
            return;
        }

        const directorId = button.dataset.directorId;
        const currentState = button.dataset.autoDeploy === 'true';
        const newState = !currentState;

        try {
            button.disabled = true;

            if (!newState) {
                // If turning off auto-deploy, get current stats to check in-progress tasks
                const response = await ResearchDirectorAPI.getAutoDeployStats(directorId);
                const inProgressTasks = response.stats.in_progress_tasks || 0;

                if (inProgressTasks > 0) {
                    this.showToast(`Auto-deploy will stop after ${inProgressTasks} in-progress task${inProgressTasks > 1 ? 's' : ''} complete`, 'info');
                }
            }

            // Toggle auto-deploy status in the backend
            await ResearchDirectorAPI.toggleAutoDeployStatus(directorId, newState);

            // Update button appearance
            button.dataset.autoDeploy = newState.toString();

            if (newState) {
                button.classList.remove('bg-green-600', 'hover:bg-green-700');
                button.classList.add('bg-red-600', 'hover:bg-red-700');
                button.innerHTML = `
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                    </svg>
                    Stop
                `;

                // Create or update stats display
                await this.createAutoDeployStats(directorId);

                // Auto-deploy is now enabled, so we auto-deploy if not already running
                // Check if graph type is selected
                const graphSelect = document.getElementById(`graph-select-${directorId}`);
                if (!graphSelect || !graphSelect.value) {
                    this.showToast('Please select a graph type before enabling auto-deploy', 'error');
                    // Reset button to previous state
                    await this.handleAutoDeployClick(event);
                    return;
                }

                // Deploy an initial task if none is currently running
                // This is a simplified check - in a real app you might want to check if there's any in-progress task
                const response = await ResearchDirectorAPI.getAutoDeployStats(directorId);
                const inProgressTasks = response.stats.in_progress_tasks || 0;

                if (inProgressTasks === 0) {
                    const deployButton = document.querySelector(`.deploy-button[data-director-id="${directorId}"]`);
                    if (deployButton && !deployButton.disabled) {
                        deployButton.click();
                    }
                } else {
                    this.showToast(`Auto-deploy enabled with ${inProgressTasks} task${inProgressTasks > 1 ? 's' : ''} already in progress`, 'info');
                }

                // Start stats update polling
                this.startAutoDeployStatsPolling(directorId);
            } else {
                button.classList.remove('bg-red-600', 'hover:bg-red-700');
                button.classList.add('bg-green-600', 'hover:bg-green-700');
                button.innerHTML = `
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Auto-Deploy
                `;

                // Continue updating stats for any in-progress tasks
                const response = await ResearchDirectorAPI.getAutoDeployStats(directorId);
                const inProgressTasks = response.stats.in_progress_tasks || 0;

                if (inProgressTasks > 0) {
                    // Keep polling stats until in-progress tasks complete
                    this.showToast(`Auto-deploy disabled, but still tracking ${inProgressTasks} in-progress task${inProgressTasks > 1 ? 's' : ''}`, 'info');
                    // Keep polling stats until all tasks complete
                    this.startAutoDeployStatsPolling(directorId);
                } else {
                    // Stop stats update polling
                    this.stopAutoDeployStatsPolling(directorId);
                }
            }

            this.showToast(`Auto-deploy ${newState ? 'enabled' : 'disabled'}`, 'success');
        } catch (error) {
            this.showToast('Failed to toggle auto-deploy', 'error');
        } finally {
            button.disabled = false;
        }
    }

    static async createAutoDeployStats(directorId) {
        try {
            // Get the reports container to place the stats above it
            const reportsSection = document.querySelector(`#reports-${directorId}`).closest('.mt-6');
            if (!reportsSection) {
                return;
            }

            // Check if stats container already exists
            let statsContainer = document.getElementById(`auto-deploy-stats-${directorId}`);

            if (!statsContainer) {
                // Create the stats container if it doesn't exist
                statsContainer = document.createElement('div');
                statsContainer.id = `auto-deploy-stats-${directorId}`;
                statsContainer.className = 'bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700 mb-6';

                // Insert before the reports section
                reportsSection.insertBefore(statsContainer, reportsSection.firstChild);
            }

            // Get the stats data
            const response = await ResearchDirectorAPI.getAutoDeployStats(directorId);
            const stats = response.stats;

            // Update the stats display
            this.updateAutoDeployStats(directorId, stats);
        } catch (error) {
            console.error('Error creating auto-deploy stats:', error);
        }
    }

    static updateAutoDeployStats(directorId, stats) {
        const statsContainer = document.getElementById(`auto-deploy-stats-${directorId}`);
        if (!statsContainer) return;

        const startedAt = stats.started_at ? new Date(stats.started_at).toLocaleString() : 'N/A';
        const lastCompletedAt = stats.last_completed_at ? new Date(stats.last_completed_at).toLocaleString() : 'N/A';
        const lastFailedAt = stats.last_failed_at ? new Date(stats.last_failed_at).toLocaleString() : 'N/A';

        statsContainer.innerHTML = `
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Auto-Deploy Statistics
                </h3>
                <button id="reset-in-progress-${directorId}" class="text-xs text-gray-500 dark:text-gray-400 underline hover:text-blue-500 transition-colors">
                    Reset Counter
                </button>
            </div>
            <div class="grid grid-cols-4 gap-3">
                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 border border-gray-200 dark:border-gray-600 text-center">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total</p>
                    <p class="mt-1 text-xl font-bold text-gray-900 dark:text-gray-100">${stats.total_tasks}</p>
                </div>
                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 border border-green-200 dark:border-green-800 text-center">
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">Completed</p>
                    <p class="mt-1 text-xl font-bold text-green-600 dark:text-green-400">${stats.completed_tasks}</p>
                </div>
                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 border border-red-200 dark:border-red-800 text-center">
                    <p class="text-sm font-medium text-red-600 dark:text-red-400">Failed</p>
                    <p class="mt-1 text-xl font-bold text-red-600 dark:text-red-400">${stats.failed_tasks}</p>
                </div>
                <div class="bg-white dark:bg-gray-700/50 rounded-lg p-3 border border-blue-200 dark:border-blue-800 text-center">
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400">In Progress</p>
                    <p class="mt-1 text-xl font-bold text-blue-600 dark:text-blue-400">${stats.in_progress_tasks}</p>
                </div>
            </div>
            <div class="mt-3 text-xs text-gray-500 dark:text-gray-400 grid grid-cols-2 gap-2">
                <div>Started: ${startedAt}</div>
                <div>Last Completed: ${lastCompletedAt}</div>
                <div>Last Failed: ${lastFailedAt}</div>
                <div>Success Rate: ${stats.total_tasks > 0 ? Math.round((stats.completed_tasks / stats.total_tasks) * 100) : 0}%</div>
            </div>
        `;

        // Add event listener for reset button
        const resetButton = document.getElementById(`reset-in-progress-${directorId}`);
        if (resetButton) {
            resetButton.addEventListener('click', async () => {
                try {
                    resetButton.disabled = true;
                    resetButton.textContent = 'Resetting...';

                    // Call the reset endpoint
                    const result = await ResearchDirectorAPI.resetInProgressCounter(directorId);

                    // Show toast with result
                    UIHandler.showToast(`Counter reset: ${result.in_progress_tasks} in-progress task(s)`, 'success');

                    // Update stats
                    const response = await ResearchDirectorAPI.getAutoDeployStats(directorId);
                    UIHandler.updateAutoDeployStats(directorId, response.stats);
                } catch (error) {
                    UIHandler.showToast('Failed to reset counter', 'error');
                } finally {
                    resetButton.disabled = false;
                    resetButton.textContent = 'Reset Counter';
                }
            });
        }
    }

    // Store interval IDs for polling stats
    static autoDeployStatsIntervals = {};

    static startAutoDeployStatsPolling(directorId) {
        // Stop any existing polling
        this.stopAutoDeployStatsPolling(directorId);

        // Start polling for stats updates
        const POLL_INTERVAL = 5000; // Update every 5 seconds
        this.autoDeployStatsIntervals[directorId] = setInterval(async () => {
            try {
                const response = await ResearchDirectorAPI.getAutoDeployStats(directorId);
                this.updateAutoDeployStats(directorId, response.stats);

                // Check if we should stop polling
                const inProgressTasks = response.stats.in_progress_tasks || 0;
                const autoDeployEnabled = response.auto_deploy;

                // Stop polling if:
                // 1. Auto-deploy is disabled AND there are no in-progress tasks
                if (!autoDeployEnabled && inProgressTasks === 0) {
                    this.stopAutoDeployStatsPolling(directorId);
                }
            } catch (error) {
                console.error('Error updating auto-deploy stats:', error);
            }
        }, POLL_INTERVAL);
    }

    static stopAutoDeployStatsPolling(directorId) {
        if (this.autoDeployStatsIntervals[directorId]) {
            clearInterval(this.autoDeployStatsIntervals[directorId]);
            delete this.autoDeployStatsIntervals[directorId];
        }
    }

    static async handleCleanMemoryClick(event) {
        const button = event.target.closest('.clean-memory-button');
        if (!button) return;

        const directorId = button.dataset.directorId;

        // Confirm before cleaning memory
        if (!confirm('Are you sure you want to clean all memory for this director? This action cannot be undone.')) {
            return;
        }

        try {
            button.disabled = true;
            button.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Cleaning...
            `;

            const result = await ResearchDirectorAPI.cleanDirectorMemory(directorId);

            // Show success message
            this.showToast(`Memory cleaned successfully: ${result.cleaned_reports} reports unlinked`, 'success');

            // Refresh the director data to update UI
            await this.loadDirectors();

        } catch (error) {
            this.showToast(`Failed to clean memory: ${error.message}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Clean Memory
            `;
        }
    }

    /**
     * Expand a director card to show full content
     * @param {HTMLElement} card - The director card element to expand
     */
    static expandDirectorCard(card) {
        const directorId = card.dataset.directorId;
        const contentSection = card.querySelector('.director-content');
        const expandToggle = card.querySelector('.expand-toggle svg');
        const header = card.querySelector('.director-header');

        if (contentSection) {
            contentSection.classList.remove('hidden');
        }

        if (expandToggle) {
            expandToggle.classList.add('rotate-180');
        }

        if (header) {
            header.classList.add('bg-indigo-50', 'dark:bg-indigo-900/20');
        }

        card.classList.add('border-indigo-500', 'shadow-md');
        card.classList.remove('border-gray-200', 'dark:border-gray-700', 'shadow-sm');
    }

    /**
     * Minimize a director card to show only the header
     * @param {HTMLElement} card - The director card element to minimize
     */
    static minimizeDirectorCard(card) {
        const directorId = card.dataset.directorId;
        const contentSection = card.querySelector('.director-content');
        const expandToggle = card.querySelector('.expand-toggle svg');
        const header = card.querySelector('.director-header');

        if (contentSection) {
            contentSection.classList.add('hidden');
        }

        if (expandToggle) {
            expandToggle.classList.remove('rotate-180');
        }

        if (header) {
            header.classList.remove('bg-indigo-50', 'dark:bg-indigo-900/20');
        }

        card.classList.remove('border-indigo-500', 'shadow-md');
        card.classList.add('border-gray-200', 'dark:border-gray-700', 'shadow-sm');
    }

    static async loadDirectors() {
        try {
            const directors = await ResearchDirectorAPI.loadDirectors();
            const container = document.getElementById('directors-container');

            if (container) {
                container.innerHTML = '';
                for (const director of directors) {
                    const card = this.createDirectorCard(director);
                    container.appendChild(card);

                    try {
                        // Load existing reports for this director
                        const reports = await ResearchDirectorAPI.getReports(director.id || director._id);
                        const reportsContainer = document.getElementById(`reports-${director.id || director._id}`);
                        if (reportsContainer) {
                            if (reports && reports.length > 0) {
                                // Sort reports by started_at in descending order
                                reports.sort((a, b) => new Date(b.started_at) - new Date(a.started_at));
                                reports.forEach(report => {
                                    const reportCard = ReportHandler.createReportCard(report);
                                    reportsContainer.appendChild(reportCard);
                                });
                            } else {
                                // Add a message when there are no reports
                                const noReportsMessage = document.createElement('div');
                                noReportsMessage.className = 'text-center py-6 bg-gray-50 dark:bg-gray-700/30 rounded-lg border border-gray-200 dark:border-gray-700';
                                noReportsMessage.innerHTML = `
                                    <svg class="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <p class="text-gray-500 dark:text-gray-400">No reports available yet</p>
                                    <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">Click "Deploy" to generate a new report</p>
                                `;
                                reportsContainer.appendChild(noReportsMessage);
                            }
                        }

                        // If auto-deploy is enabled, create and start polling for stats
                        if (director.auto_deploy) {
                            await this.createAutoDeployStats(director.id || director._id);
                            this.startAutoDeployStatsPolling(director.id || director._id);
                        }
                    } catch (reportError) {
                        this.showToast(`Failed to load reports for director ${director.id || director._id}`, 'error');
                    }
                }

                // Initialize the UI based on the current URL
                if (window.directorRouter) {
                    window.directorRouter.updateUI();
                }
            }
        } catch (error) {
            this.showToast('Failed to load directors', 'error');
            throw error;
        }
    }
}

// Make showToast globally available
window.showToast = UIHandler.showToast;
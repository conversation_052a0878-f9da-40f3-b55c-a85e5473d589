// API Client for Research Director operations
class ResearchDirectorAPI {
    static async loadDirectors() {
        try {
            const response = await fetch('/api/research-agents/directors');
            if (!response.ok) {
                throw new Error(`Failed to load directors: ${response.statusText}`);
            }
            const data = await response.json();
            return data.directors;
        } catch (error) {
            console.error('Error loading directors:', error);
            throw error;
        }
    }

    static async deployDirector(directorId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/deploy`, {
                method: 'POST'
            });
            if (!response.ok) {
                throw new Error(`Failed to deploy director: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error deploying director:', error);
            throw error;
        }
    }

    static async getReport(taskId) {
        try {
            const response = await fetch(`/api/research-agents/reports/${taskId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch report: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching report:', error);
            throw error;
        }
    }

    static async getReports(directorId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/reports`);

            if (!response.ok) {
                throw new Error(`Failed to fetch reports: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error fetching reports for director ${directorId}:`, error);
            throw error;
        }
    }

    static async getTaskStatus(taskId) {
        try {
            const response = await fetch(`/api/research-agents/tasks/${taskId}/status`);
            if (!response.ok) {
                throw new Error(`Failed to fetch task status: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching task status:', error);
            throw error;
        }
    }

    static async updateDirectorLLMSettings(directorId, settings) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/llm-settings`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    llm_connection: {
                        director_stages: {
                            formulate_question: settings.formulate_question,
                            assign_analysts: settings.assign_analysts,
                            collect_conclusions: settings.collect_conclusions,
                            summarize_report: settings.summarize_report
                        },
                        analyst_stages: {
                            generate_hypothesis: settings.generate_hypothesis,
                            agent_execution: settings.agent_execution,
                            verdict_generation: settings.verdict_generation
                        }
                    }
                })
            });
            return await response.json();
        } catch (error) {
            console.error('Error updating LLM settings:', error);
            throw error;
        }
    }

    static async getLLMModels() {
        try {
            const response = await fetch('/api/llm/llm');
            return await response.json();
        } catch (error) {
            console.error('Error fetching LLM models:', error);
            throw error;
        }
    }

    static async getAvailableGraphs() {
        try {
            const response = await fetch('/api/research-agents/graphs');
            if (!response.ok) {
                throw new Error(`Failed to load graphs: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error loading graphs:', error);
            throw error;
        }
    }

    static async updateDirectorGraph(directorId, graphId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/graph`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ graph_id: graphId })
            });
            if (!response.ok) {
                throw new Error(`Failed to update graph: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error updating graph:', error);
            throw error;
        }
    }

    static async getDirector(directorId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}`);
            if (!response.ok) {
                throw new Error(`Failed to get director: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error getting director:', error);
            throw error;
        }
    }

    static async toggleAutoDeployStatus(directorId, autoDeployStatus) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/auto-deploy`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ auto_deploy: autoDeployStatus })
            });
            if (!response.ok) {
                throw new Error(`Failed to toggle auto-deploy status: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error toggling auto-deploy status:', error);
            throw error;
        }
    }

    static async getAutoDeployStats(directorId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/auto-deploy-stats`);
            if (!response.ok) {
                throw new Error(`Failed to get auto-deploy stats: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error getting auto-deploy stats:', error);
            throw error;
        }
    }

    static async resetInProgressCounter(directorId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/reset-in-progress`, {
                method: 'POST'
            });
            if (!response.ok) {
                throw new Error(`Failed to reset in-progress counter: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error resetting in-progress counter:', error);
            throw error;
        }
    }

    static async cleanDirectorMemory(directorId) {
        try {
            const response = await fetch(`/api/research-agents/directors/${directorId}/clean-memory`, {
                method: 'POST'
            });
            if (!response.ok) {
                throw new Error(`Failed to clean director memory: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error cleaning director memory:', error);
            throw error;
        }
    }

    static async connectToLettaMemory(directorId) {
        try {

            const response = await fetch(`/api/research-agents/directors/${directorId}/connect-memory`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });


            if (!response.ok) {
                throw new Error('Failed to connect to Letta memory');
            }

            const result = await response.json();

            return result;
        } catch (error) {

            throw error;
        }
    }

    static async getMemoryConnectionStatus(directorId) {
        try {

            const response = await fetch(`/api/research-agents/directors/${directorId}/memory-connection`);


            if (!response.ok) {

                throw new Error(`Failed to get memory connection status: ${response.statusText}`);
            }

            const result = await response.json();

            return result;
        } catch (error) {

            throw error;
        }
    }
}
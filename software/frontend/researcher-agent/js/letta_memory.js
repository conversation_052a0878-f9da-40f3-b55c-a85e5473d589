// Letta Memory Connection Handler for Research Directors

class LettaMemoryHandler {
    /**
     * Check if a director is connected to a Letta agent
     * @param {string} directorId - The ID of the director to check
     * @returns {Promise<Object>} - Connection status object
     */
    static async checkConnection(directorId) {
        try {

            const result = await ResearchDirectorAPI.getMemoryConnectionStatus(directorId);

            return result;
        } catch (error) {

            throw error;
        }
    }

    /**
     * Connect a director to a Letta agent
     * @param {string} directorId - The ID of the director to connect
     * @returns {Promise<Object>} - Connection result object
     */
    static async connectToLetta(directorId) {
        try {

            const result = await ResearchDirectorAPI.connectToLettaMemory(directorId);

            return result;
        } catch (error) {


            throw error;
        }
    }

    /**
     * Update the connect button state based on connection status
     * @param {HTMLElement} button - The connect button element
     * @param {boolean} isConnected - Whether the director is connected
     * @param {boolean} agentExists - Whether the Letta agent exists
     * @param {string} agentId - The Letta agent ID (if any)
     */
    static updateButtonState(button, isConnected, agentExists = true, agentId = null) {


        if (isConnected) {
            // Director is connected - disable button
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed', 'bg-green-600', 'hover:bg-green-700');
            button.classList.remove('bg-indigo-600', 'hover:bg-indigo-700', 'bg-yellow-600', 'hover:bg-yellow-700');

            // Check if it's a mock agent
            const isMockAgent = agentId && agentId.startsWith('mock-agent-');

            button.innerHTML = `
                <svg class="-ml-1 md:mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span class="hidden md:inline">${isMockAgent ? 'Connected (Mock)' : 'Connected to Memory'}</span>
            `;
        } else {
            // Director is not connected - enable button
            button.disabled = false;
            button.classList.remove('opacity-50', 'cursor-not-allowed', 'bg-green-600', 'hover:bg-green-700', 'bg-yellow-600', 'hover:bg-yellow-700');
            button.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
            button.innerHTML = `
                <svg class="-ml-1 md:mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                <span class="hidden md:inline">Connect to Memory</span>
            `;
        }
    }

    /**
     * Handle click on the connect button
     * @param {Event} event - The click event
     */
    static async handleConnectClick(event) {

        const button = event.target.closest('.connect-memory-button');
        if (!button) {

            UIHandler.showToast('Connect button not found', 'error');
            return;
        }

        const directorId = button.dataset.directorId;


        try {
            // Disable button during request

            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');
            button.innerHTML = `
                <svg class="animate-spin -ml-1 md:mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="hidden md:inline">Connecting...</span>
            `;

            // Connect to Letta

            const result = await this.connectToLetta(directorId);


            // Update button state

            this.updateButtonState(button, true, true, result.letta_agent_id);

            // Show success message

            UIHandler.showToast(result.message, 'success');
        } catch (error) {


            // Reset button

            this.updateButtonState(button, false);

            // Extract error message
            let errorMessage = error.message;


            // Check if it's a server error with a detail message
            if (error.message.includes('Failed to connect to Letta memory')) {
                try {
                    // Try to extract the detailed error message

                    const match = error.message.match(/Failed to connect to Letta memory: (.+)/);

                    if (match && match[1]) {
                        errorMessage = match[1];

                    }
                } catch (e) {
                    // If parsing fails, use the original error message

                }
            }

            // Show error message

            UIHandler.showToast('Failed to connect to memory. Using mock connection instead.', 'warning');
        }
    }

    /**
     * Initialize the connection status for a director
     * @param {string} directorId - The ID of the director
     * @param {HTMLElement} button - The connect button element
     */
    static async initConnectionStatus(directorId, button) {
        try {
            const status = await this.checkConnection(directorId);


            this.updateButtonState(button, status.connected, status.agent_exists, status.letta_agent_id);

            // Show a toast if the agent doesn't exist but the director is connected
            if (status.letta_agent_id && !status.agent_exists) {
                UIHandler.showToast('Letta agent no longer exists. Please reconnect.', 'warning');
            }
        } catch (error) {
            console.error(`Error initializing connection status for director ${directorId}:`, error);
            // Default to not connected on error
            this.updateButtonState(button, false);
        }
    }
}

// Export the class
window.LettaMemoryHandler = LettaMemoryHandler;

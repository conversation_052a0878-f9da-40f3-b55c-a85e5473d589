// Handler for Progress Bar
class ProgressHandler {
    static formatStageName(stage) {
        // Remove underscores and capitalize
        if (stage === '__start__') return 'Start';
        if (stage === 'END') return 'Complete';
        return stage.replace(/_/g, ' ')
                   .replace(/([A-Z])/g, ' $1') // Add space before capital letters
                   .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
    }

    static formatDuration(ms) {
        if (!ms) return '';

        // For completed stages, show the exact time
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    static formatElapsedTime(startTime) {
        if (!startTime) return '';

        const start = new Date(startTime);
        const now = new Date();
        const elapsed = now - start;

        return this.formatDuration(elapsed);
    }

    static async createProgressBar(taskId) {
        const progressBar = document.createElement('div');
        progressBar.className = 'mt-6';

        // Get workflow stages from the API
        try {
            const response = await fetch(`/api/research-agents/tasks/${taskId}/workflow`);
            const data = await response.json();

            // Check the structure of the response
            if (data.workflow_stages) {
                var stages = Object.keys(data.workflow_stages.stages || {});
            } else if (data.stages) {
                var stages = Object.keys(data.stages || {});
            } else {
                return progressBar;
            }

            if (!stages.length) {
                return progressBar;
            }

            // Create status text
            const statusText = document.createElement('div');
            statusText.id = `status-${taskId}`;
            statusText.className = 'text-sm text-gray-600 mb-4';
            statusText.textContent = 'Initializing...';
            progressBar.appendChild(statusText);

            // Create progress dots container
            const dotsContainer = document.createElement('div');
            dotsContainer.className = 'flex items-center justify-between';

            // Create dots and connecting lines
            stages.forEach((stage, index) => {
                // Create dot
                const dot = document.createElement('div');
                dot.id = `dot-${taskId}-${stage}`;
                dot.className = 'w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-white text-sm';
                dot.textContent = (index + 1).toString();

                // Add dot to container
                dotsContainer.appendChild(dot);

                // Add connecting line if not last dot
                if (index < stages.length - 1) {
                    const line = document.createElement('div');
                    line.className = 'flex-1 h-0.5 bg-gray-200 mx-2';
                    dotsContainer.appendChild(line);
                }
            });

            progressBar.appendChild(dotsContainer);

            // Create stage labels
            const labelsContainer = document.createElement('div');
            labelsContainer.className = 'flex items-center justify-between mt-2';
            stages.forEach((stage) => {
                const label = document.createElement('div');
                label.className = 'text-xs text-gray-500 w-20 text-center';
                label.textContent = this.formatStageName(stage);
                labelsContainer.appendChild(label);
            });
            progressBar.appendChild(labelsContainer);

        } catch (error) {
            progressBar.innerHTML = '<div class="text-red-500">Error loading progress bar</div>';
        }

        return progressBar;
    }

    static getOverallStatus(workflowStage) {
        if (!workflowStage || !workflowStage.stages) return { status: 'pending', style: 'bg-yellow-100 text-yellow-800' };

        // Check for any failed stages
        const hasFailedStage = Object.values(workflowStage.stages)
            .some(stage => stage.status === 'failed');
        if (hasFailedStage) {
            return {
                status: 'Failed',
                style: 'bg-red-100 text-red-800'
            };
        }

        // Check if all stages are completed
        const allCompleted = Object.values(workflowStage.stages)
            .every(stage => stage.status === 'completed');
        if (allCompleted) {
            return {
                status: 'Completed',
                style: 'bg-green-100 text-green-800'
            };
        }

        // Check if any stage is in progress
        const hasInProgress = Object.values(workflowStage.stages)
            .some(stage => stage.status === 'in_progress');
        if (hasInProgress) {
            return {
                status: 'In Progress',
                style: 'bg-yellow-100 text-yellow-800'
            };
        }

        return {
            status: 'Pending',
            style: 'bg-gray-100 text-gray-800'
        };
    }

    static updateProgressBar(taskId, workflowStage) {
        if (!workflowStage || !workflowStage.stages) {
            return;
        }

        const stages = Object.keys(workflowStage.stages);

        // Special handling for __start__ stage - it should be in_progress by default
        if (workflowStage.stages['__start__'] && workflowStage.stages['__start__'].status === 'pending') {
            workflowStage.stages['__start__'].status = 'in_progress';
        }

        const statusColors = {
            'in_progress': 'bg-blue-500',
            'completed': 'bg-green-500',
            'failed': 'bg-red-500'
        };

        // Update status text and card styling
        const statusElement = document.getElementById(`status-${taskId}`);
        const reportCard = document.getElementById(`report-${taskId}`);

        if (statusElement) {
            const currentStage = workflowStage.current;
            const currentStatus = workflowStage.stages[currentStage]?.status || 'pending';
            statusElement.textContent = `${this.formatStageName(currentStage)} - ${currentStatus}`;

            // Update card border color and status badge based on overall status
            if (reportCard) {
                const statusBadge = reportCard.querySelector('.rounded-full');
                if (statusBadge) {
                    const overallStatus = this.getOverallStatus(workflowStage);
                    statusBadge.className = `px-3 py-1 rounded-full ${overallStatus.style}`;
                    statusBadge.textContent = overallStatus.status;

                    // Update card border color
                    if (overallStatus.status === 'Failed') {
                        reportCard.className = reportCard.className.replace(/border-(blue|green|red)-500/, 'border-red-500');
                    } else if (overallStatus.status === 'Completed') {
                        reportCard.className = reportCard.className.replace(/border-(blue|green|red)-500/, 'border-green-500');
                    }
                }
            }
        }

        // Update timer for the current stage
        if (typeof TimerHandler !== 'undefined') {
            TimerHandler.updateTimerFromWorkflowStage(taskId, workflowStage);
        }

        // Update dots and lines
        let reachedCurrent = false;
        let hasFailed = false;

        stages.forEach((stage, index) => {
            const dot = document.getElementById(`dot-${taskId}-${stage}`);
            if (!dot) return;

            const stageInfo = workflowStage.stages[stage];
            if (!stageInfo) {
                dot.className = `w-6 h-6 rounded-full ${hasFailed ? 'bg-gray-300' : 'bg-gray-200'} flex items-center justify-center text-white text-sm`;
                return;
            }

            // Special handling for __start__ stage visualization
            const status = stage === '__start__' && stageInfo.status === 'pending' ? 'in_progress' : stageInfo.status;

            if (status === 'failed') {
                hasFailed = true;
                dot.className = `w-6 h-6 rounded-full bg-red-500 flex items-center justify-center text-white text-sm`;
                dot.innerHTML = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>`;
            } else if (status === 'completed') {
                dot.className = `w-6 h-6 rounded-full bg-green-500 flex items-center justify-center text-white text-sm`;
                dot.innerHTML = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>`;
            } else if (status === 'in_progress' && !hasFailed) {
                dot.className = `w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm animate-pulse`;
                reachedCurrent = true;
            } else {
                dot.className = `w-6 h-6 rounded-full ${hasFailed ? 'bg-gray-300' : 'bg-gray-200'} flex items-center justify-center text-white text-sm`;
                dot.textContent = (index + 1).toString();
            }

            // Update connecting lines
            if (index < stages.length - 1) {
                const nextLine = dot.nextElementSibling;
                if (nextLine) {
                    if (status === 'completed') {
                        nextLine.className = 'flex-1 h-0.5 bg-green-500 mx-2';
                    } else if (hasFailed) {
                        nextLine.className = 'flex-1 h-0.5 bg-gray-300 mx-2';
                    } else {
                        nextLine.className = 'flex-1 h-0.5 bg-gray-200 mx-2';
                    }
                }
            }
        });
    }
}

// Export the class
window.ProgressHandler = ProgressHandler;
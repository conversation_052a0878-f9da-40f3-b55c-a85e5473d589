// Handler for workflow timer
class TimerHandler {
    static timers = {};

    // Initialize a timer for a specific task
    static initTimer(taskId) {
        // Clear any existing timer for this task
        this.clearTimer(taskId);

        // Create timer element if it doesn't exist
        const timerId = `timer-${taskId}`;
        if (!document.getElementById(timerId)) {
            const timerElement = document.createElement('div');
            timerElement.id = timerId;
            timerElement.className = 'text-sm text-blue-600 dark:text-blue-400 font-medium mt-2';
            timerElement.style.display = 'none';

            // Find the status element to insert timer after
            const statusElement = document.getElementById(`status-${taskId}`);
            if (statusElement) {
                statusElement.parentNode.insertBefore(timerElement, statusElement.nextSibling);
            }
        }
    }

    // Start the workflow timer for a specific task
    static startWorkflowTimer(taskId, startTime) {
        this.initTimer(taskId);

        // If timer is already running, don't start a new one
        if (this.timers[taskId]) return;

        const timerElement = document.getElementById(`timer-${taskId}`);
        if (!timerElement) return;

        // Show the timer element
        timerElement.style.display = 'block';

        // Use the provided start time or current time
        const startDate = startTime ? new Date(startTime) : new Date();

        // Store start time for this task
        this.timers[taskId] = {
            startDate: startDate,
            intervalId: null
        };

        // Update timer immediately
        this.updateTimer(taskId);

        // Set interval to update timer every second
        this.timers[taskId].intervalId = setInterval(() => {
            this.updateTimer(taskId);
        }, 1000);
    }

    // Update the timer display
    static updateTimer(taskId) {
        if (!this.timers[taskId]) return;

        const timerElement = document.getElementById(`timer-${taskId}`);
        if (!timerElement) return;

        const { startDate } = this.timers[taskId];
        const elapsed = new Date() - startDate;

        // Format elapsed time
        const seconds = Math.floor(elapsed / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        let timeString;
        if (hours > 0) {
            timeString = `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            timeString = `${minutes}m ${seconds % 60}s`;
        } else {
            timeString = `${seconds}s`;
        }

        // Update timer text
        timerElement.textContent = `Workflow in progress: ${timeString}`;
    }

    // Stop the timer
    static stopTimer(taskId) {
        if (this.timers[taskId] && this.timers[taskId].intervalId) {
            clearInterval(this.timers[taskId].intervalId);

            // Hide the timer element
            const timerElement = document.getElementById(`timer-${taskId}`);
            if (timerElement) {
                timerElement.style.display = 'none';
            }

            // Remove timer data
            delete this.timers[taskId];
        }
    }

    // Clear all timers
    static clearTimer(taskId) {
        this.stopTimer(taskId);
    }

    // Update timer based on workflow stage
    static updateTimerFromWorkflowStage(taskId, workflowStage) {
        if (!workflowStage || !workflowStage.stages) {
            return;
        }

        // Check if workflow is completed or failed
        const overallStatus = ProgressHandler.getOverallStatus(workflowStage);
        if (overallStatus.status === 'Completed' || overallStatus.status === 'Failed') {
            this.stopTimer(taskId);
            return;
        }

        // Check if workflow has started
        const startStage = workflowStage.stages['__start__'];
        if (startStage && startStage.started_at && !this.timers[taskId]) {
            // Start the workflow timer if it hasn't been started yet
            this.startWorkflowTimer(taskId, startStage.started_at);
        }
    }
}

// Export the class
window.TimerHandler = TimerHandler;

// Initialize the Research Director page
document.addEventListener('DOMContentLoaded', async () => {
    // Load initial directors
    await UIHandler.loadDirectors();

    // Add event listeners for deploy buttons
    document.addEventListener('click', async (event) => {
        if (event.target.closest('.deploy-button')) {
            event.preventDefault();
            await UIHandler.handleDeployClick(event);
        }
        else if (event.target.closest('.auto-deploy-button')) {
            event.preventDefault();
            await UIHandler.handleAutoDeployClick(event);
        }
        else if (event.target.closest('.clean-memory-button')) {
            event.preventDefault();
            await UIHandler.handleCleanMemoryClick(event);
        }
        else if (event.target.closest('.connect-memory-button')) {
            event.preventDefault();
            await LettaMemoryHandler.handleConnectClick(event);
        }
    });

    // Initialize Letta memory connection status for all directors
    const connectButtons = document.querySelectorAll('.connect-memory-button');
    connectButtons.forEach(button => {
        const directorId = button.dataset.directorId;
        if (directorId) {
            LettaMemoryHandler.initConnectionStatus(directorId, button);
        }
    });
});
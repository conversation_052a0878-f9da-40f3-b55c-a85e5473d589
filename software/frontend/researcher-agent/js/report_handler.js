// Handler for Research Reports
class ReportHandler {
    static formatDate(dateString) {
        return new Date(dateString).toLocaleString();
    }

    static createReportCard(report) {
        // Get task ID from various possible locations
        const taskId = report.task_id || (report.state && report.state.task_id) || report._id || 'unknown';

        const card = document.createElement('details');
        card.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-4 border-l-4 border-blue-500 transition-all duration-300';
        card.open = false;

        // Safely extract started_at with fallback
        const startedAt = report.started_at || (report.metadata && report.metadata.created_at) || new Date().toISOString();

        // Create summary section
        const summary = document.createElement('summary');
        summary.className = 'flex justify-between items-center cursor-pointer';
        summary.innerHTML = `
            <div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${this.formatDate(startedAt)}</div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white mt-2">Research Report</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    ${report.metadata?.analysis_duration ? `Completed in ${report.metadata.analysis_duration.toFixed(1)}s` : 'In Progress'}
                </div>
            </div>
            <div class="px-4 py-2 rounded-full ${
                report.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                report.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300' :
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300'
            } font-medium">
                ${report.status || 'In Progress'}
            </div>
        `;

        // Create content container
        const content = document.createElement('div');
        content.className = 'mt-6';

        content.innerHTML = `
            ${report.final_report ? `
                <!-- Final Report Section -->
                <div class="mt-8">
                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Final Report</div>
                    <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                        ${typeof marked !== 'undefined' ? marked.parse(report.final_report) : report.final_report.replace(/\n/g, '<br>')}
                    </div>
                </div>
            ` : report.state?.final_report ? `
                <!-- Final Report Section from State -->
                <div class="mt-8">
                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Final Report</div>
                    <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                        ${typeof marked !== 'undefined' ? marked.parse(report.state.final_report) : report.state.final_report.replace(/\n/g, '<br>')}
                    </div>
                </div>
            ` : ''}

            ${report.state?.business_question ? `
                <!-- Business Question Section -->
                <div class="mt-8">
                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Business Question</div>
                    <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                        ${report.state.business_question}
                    </div>
                </div>
            ` : ''}

            ${report.state?.plan ? `
                <!-- Plan Section -->
                <div class="mt-8">
                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Research Plan</div>
                    <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                        ${(() => {
                            // Handle plan as either object or string
                            if (typeof report.state.plan === 'object') {
                                // If it's an object with tasks
                                if (report.state.plan.tasks && Array.isArray(report.state.plan.tasks)) {
                                    return `<ul>${report.state.plan.tasks.map(task => `<li>${task}</li>`).join('')}</ul>`;
                                }
                                // Just stringify the object
                                return JSON.stringify(report.state.plan, null, 2);
                            }
                            // Handle as string (original behavior)
                            return typeof marked !== 'undefined' ? marked.parse(report.state.plan) : report.state.plan.replace(/\n/g, '<br>');
                        })()}
                    </div>
                </div>
            ` : ''}

            ${report.state?.analysts ? `
                <!-- Analysts Section -->
                <div class="mt-8">
                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Research Analysts</div>
                    <div class="space-y-4">
                        ${(() => {
                            // Handle different formats of analysts data
                            let analystsList = [];
                            if (Array.isArray(report.state.analysts)) {
                                analystsList = report.state.analysts;
                            } else if (report.state.analysts.analysts && Array.isArray(report.state.analysts.analysts)) {
                                analystsList = report.state.analysts.analysts;
                            }

                            return analystsList.map(analyst => `
                                <details class="bg-white dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                    <summary class="flex justify-between items-center cursor-pointer">
                                        <div>
                                            <div class="font-semibold text-gray-900 dark:text-white">${analyst.name_analyst || 'Unknown Analyst'}</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">${analyst.title_analyst || ''}</div>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">${analyst.age_analyst ? `Age: ${analyst.age_analyst}` : ''}</div>
                                    </summary>
                                    <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                                        ${analyst.background_analyst ? `
                                            <div class="mt-3">
                                                <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Background</div>
                                                <div class="mt-1 text-sm text-gray-800 dark:text-gray-200">${analyst.background_analyst}</div>
                                            </div>
                                        ` : ''}
                                        ${analyst.business_question ? `
                                            <div class="mt-3">
                                                <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Research Question</div>
                                                <div class="mt-1 text-sm text-gray-800 dark:text-gray-200">${analyst.business_question}</div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </details>
                            `).join('');
                        })()}
                    </div>
                </div>
            ` : ''}

            ${report.state?.completed_reports && report.state.completed_reports.length > 0 ? `
                <!-- Completed Reports Section -->
                <div class="mt-8">
                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Completed Reports (${report.state.completed_reports.length})</div>
                    <div class="space-y-4">
                        ${report.state.completed_reports.map((completedReport, index) => {
                            // Extract a preview (first 100 characters)
                            const reportStr = typeof completedReport === 'string' ? completedReport : JSON.stringify(completedReport);
                            const preview = reportStr.length > 100 ? reportStr.substring(0, 100) + '...' : reportStr;

                            // Prepare the content
                            let content;
                            if (typeof completedReport === 'string') {
                                if (typeof marked !== 'undefined') {
                                    content = marked.parse(completedReport);
                                } else {
                                    // Basic markdown to HTML conversion if marked library is not available
                                    content = completedReport
                                        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>') // Bold
                                        .replace(/\*(.+?)\*/g, '<em>$1</em>') // Italic
                                        .replace(/\n\n/g, '</p><p>') // Paragraphs
                                        .replace(/\n/g, '<br>') // Line breaks
                                        .replace(/#{3}\s(.+?)\n/g, '<h3>$1</h3>') // H3
                                        .replace(/#{2}\s(.+?)\n/g, '<h2>$1</h2>') // H2
                                        .replace(/#{1}\s(.+?)\n/g, '<h1>$1</h1>'); // H1
                                    content = `<p>${content}</p>`;
                                }
                            } else {
                                content = `<pre class="bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto">${JSON.stringify(completedReport, null, 2)}</pre>`;
                            }

                            return `
                                <details class="bg-white dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                                    <summary class="px-4 py-3 cursor-pointer font-medium">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <span class="font-semibold text-gray-900 dark:text-white">Report ${index + 1}</span>
                                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-1">${preview}</p>
                                            </div>
                                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200 flex-shrink-0"
                                                style="transform: rotate(-180deg)"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </summary>
                                    <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700 prose dark:prose-invert max-w-none">
                                        ${content}
                                    </div>
                                </details>
                            `;
                        }).join('')}
                    </div>
                </div>
            ` : ''}

            <!-- State Section -->
            <div class="mt-8">
                <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">State Information</div>
                <div id="state-section-${taskId || 'default'}">
                    ${StateHandler.createStateCard(report.state || {})}
                </div>
            </div>

            <!-- Workflow Progress Section -->
            <div class="mt-8">
                <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Workflow Progress</div>
                <div class="space-y-4">
                    ${Object.entries(report.workflow_stage?.stages || {}).map(([stage, info]) => `
                        <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex justify-between items-center">
                                <div class="font-medium text-gray-900 dark:text-white">
                                    ${stage === '__start__' ? 'Start' :
                                      stage === 'END' ? 'Complete' :
                                      stage.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').trim()}
                                </div>
                                <div class="px-3 py-1 rounded-full text-sm ${
                                    info.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                                    info.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300' :
                                    info.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300' :
                                    'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300'
                                }">
                                    ${info.status || 'Pending'}
                                </div>
                            </div>
                            ${info.started_at ? `
                                <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                    Started: ${this.formatDate(info.started_at)}
                                </div>
                            ` : ''}
                            ${info.completed_at ? `
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    Completed: ${this.formatDate(info.completed_at)}
                                </div>
                                ${info.duration_ms ? `
                                <div class="text-sm text-green-600 dark:text-green-400 font-medium">
                                    Duration: ${(info.duration_ms / 1000).toFixed(1)}s
                                </div>
                                ` : ''}
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>

            <!-- Metadata Section -->
            <div class="mt-8 grid grid-cols-2 md:grid-cols-3 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Started At</div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        ${this.formatDate(startedAt)}
                    </div>
                </div>
                ${report.completed_at ? `
                    <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed At</div>
                        <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            ${this.formatDate(report.completed_at)}
                        </div>
                    </div>
                ` : ''}
                ${report.metadata?.analysis_duration ? `
                    <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Analysis Duration</div>
                        <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                            ${report.metadata.analysis_duration.toFixed(1)}s
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        card.appendChild(summary);
        card.appendChild(content);

        // Render state information if available
        if (report.state) {
            const stateContainer = card.querySelector(`#state-section-${taskId || 'default'}`);
            if (stateContainer) {
                StateHandler.renderState(stateContainer.id, report);
            }
        }

        // Initialize workflow timer if the report is in progress
        if (typeof TimerHandler !== 'undefined' && report.status !== 'completed' && report.status !== 'failed') {
            const taskId = report.task_id || (report.state && report.state.task_id) || report._id || 'unknown';
            const startStage = report.workflow_stage?.stages?.['__start__'];

            if (startStage && startStage.started_at) {
                TimerHandler.startWorkflowTimer(taskId, startStage.started_at);
            }
        }

        // Add chevron rotation for completed reports accordions
        const completedReportDetails = card.querySelectorAll('.space-y-4 details');
        completedReportDetails.forEach(details => {
            const chevron = details.querySelector('svg');
            if (chevron) {
                details.addEventListener('toggle', () => {
                    chevron.style.transform = details.open ? 'rotate(0deg)' : 'rotate(-180deg)';
                });
            }
        });

        return card;
    }

    static createVerdictSection(verdicts) {
        if (!verdicts || verdicts.length === 0) return '';
        return `
            <div class="mt-8">
                <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Analysis Verdicts</div>
                <div class="space-y-4">
                    ${verdicts.map(verdict => `
                        <div class="bg-white dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow">
                            <div class="flex justify-between items-start">
                                <div class="font-semibold text-gray-900 dark:text-white">${verdict.analyst}</div>
                                <div class="px-3 py-1 rounded-full text-sm font-medium ${
                                    verdict?.verdict?.decision === 'REJECT' ? 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300' :
                                    verdict?.verdict?.decision === 'NOT REJECT' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300'
                                }">
                                    ${verdict?.verdict?.decision || 'PENDING'}
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="text-sm text-gray-600 dark:text-gray-400">Confidence Level</div>
                                <div class="mt-1 flex items-center gap-2">
                                    <div class="flex-1 h-2 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                                        <div class="h-full ${
                                            verdict?.verdict?.confidence_level === 'HIGH' ? 'bg-green-500 dark:bg-green-400 w-full' :
                                            verdict?.verdict?.confidence_level === 'MEDIUM' ? 'bg-yellow-500 dark:bg-yellow-400 w-2/3' :
                                            'bg-red-500 dark:bg-red-400 w-1/3'
                                        }"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">${verdict?.verdict?.confidence_level || 'LOW'}</span>
                                </div>
                            </div>
                            ${verdict?.verdict?.supporting_evidence?.length ? `
                                <div class="mt-3">
                                    <div class="text-sm text-gray-600 dark:text-gray-400">Supporting Evidence</div>
                                    <ul class="mt-2 space-y-1">
                                        ${verdict.verdict.supporting_evidence.map(evidence =>
                                            `<li class="text-sm text-gray-700 dark:text-gray-300 flex items-start">
                                                <svg class="w-4 h-4 mr-2 mt-1 text-green-500 dark:text-green-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                ${evidence}
                                            </li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    static createAnalysisSection(finalReport) {
        if (!finalReport) return '';
        const finalReportContent = typeof marked !== 'undefined' ?
            marked.parse(finalReport) :
            finalReport.replace(/\*\*/g, '').replace(/\n/g, '<br>');
        return `
            <div class="mt-8">
                <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Final Report</div>
                <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                    ${finalReportContent}
                </div>
            </div>
        `;
    }

    static createConclusionsSection(conclusions) {
        if (!conclusions) return '';
        const conclusionsContent = typeof marked !== 'undefined' ?
            marked.parse(conclusions) :
            conclusions.replace(/\*\*/g, '').replace(/\n/g, '<br>');
        return `
            <div class="mt-8">
                <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Detailed Conclusions</div>
                <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                    ${conclusionsContent}
                </div>
            </div>
        `;
    }

    static createMetadataSection(report) {
        return `
            <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Analysts</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">${report.metadata?.total_analysts || 0}</div>
                        <div class="ml-2 text-sm text-gray-500 dark:text-gray-400">experts</div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Analysis Duration</div>
                    <div class="mt-1 flex items-baseline">
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">${(report.metadata?.analysis_duration || 0).toFixed(1)}</div>
                        <div class="ml-2 text-sm text-gray-500 dark:text-gray-400">seconds</div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Started At</div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">${this.formatDate(report.started_at)}</div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed At</div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">${this.formatDate(report.completed_at)}</div>
                </div>
            </div>
        `;
    }

    static renderReports(containerId, reports) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
                Research Reports (${reports.length})
            </div>
            <div class="space-y-4">
                ${reports.map(report => {
                    const taskId = report.task_id || (report.state && report.state.task_id) || report._id || 'default';
                    const startedAt = report.started_at || (report.metadata && report.metadata.created_at) || new Date().toISOString();
                    return `
                        <details class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-l-4 border-blue-500 transition-all duration-300">
                            <summary class="flex justify-between items-center cursor-pointer">
                                <div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">${this.formatDate(startedAt)}</div>
                                    <div class="text-2xl font-bold text-gray-900 dark:text-white mt-2">Research Report</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                        ${report.metadata?.analysis_duration ? `Completed in ${report.metadata.analysis_duration.toFixed(1)}s` : 'In Progress'}
                                    </div>
                                </div>
                                <div class="px-4 py-2 rounded-full ${
                                    report.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                                    report.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300' :
                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300'
                                } font-medium">
                                    ${report.status || 'In Progress'}
                                </div>
                            </summary>

                            <div class="content mt-6">
                                ${report.final_report ? `
                                    <!-- Final Report Section -->
                                    <div class="mt-8">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Final Report</div>
                                        <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                                            ${typeof marked !== 'undefined' ? marked.parse(report.final_report) : report.final_report.replace(/\n/g, '<br>')}
                                        </div>
                                    </div>
                                ` : report.state?.final_report ? `
                                    <!-- Final Report Section from State -->
                                    <div class="mt-8">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Final Report</div>
                                        <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                                            ${typeof marked !== 'undefined' ? marked.parse(report.state.final_report) : report.state.final_report.replace(/\n/g, '<br>')}
                                        </div>
                                    </div>
                                ` : ''}

                                ${report.state?.business_question ? `
                                    <!-- Business Question Section -->
                                    <div class="mt-8">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Business Question</div>
                                        <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                                            ${report.state.business_question}
                                        </div>
                                    </div>
                                ` : ''}

                                ${report.state?.plan ? `
                                    <!-- Plan Section -->
                                    <div class="mt-8">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Research Plan</div>
                                        <div class="prose dark:prose-invert max-w-none p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                                            ${(() => {
                                                // Handle plan as either object or string
                                                if (typeof report.state.plan === 'object') {
                                                    // If it's an object with tasks
                                                    if (report.state.plan.tasks && Array.isArray(report.state.plan.tasks)) {
                                                        return `<ul>${report.state.plan.tasks.map(task => `<li>${task}</li>`).join('')}</ul>`;
                                                    }
                                                    // Just stringify the object
                                                    return JSON.stringify(report.state.plan, null, 2);
                                                }
                                                // Handle as string (original behavior)
                                                return typeof marked !== 'undefined' ? marked.parse(report.state.plan) : report.state.plan.replace(/\n/g, '<br>');
                                            })()}
                                        </div>
                                    </div>
                                ` : ''}

                                ${report.state?.analysts ? `
                                    <!-- Analysts Section -->
                                    <div class="mt-8">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Research Analysts</div>
                                        <div class="space-y-4">
                                            ${(() => {
                                                // Handle different formats of analysts data
                                                let analystsList = [];
                                                if (Array.isArray(report.state.analysts)) {
                                                    analystsList = report.state.analysts;
                                                } else if (report.state.analysts.analysts && Array.isArray(report.state.analysts.analysts)) {
                                                    analystsList = report.state.analysts.analysts;
                                                }

                                                return analystsList.map(analyst => `
                                                    <details class="bg-white dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                                        <summary class="flex justify-between items-center cursor-pointer">
                                                            <div>
                                                                <div class="font-semibold text-gray-900 dark:text-white">${analyst.name_analyst || 'Unknown Analyst'}</div>
                                                                <div class="text-sm text-gray-600 dark:text-gray-400">${analyst.title_analyst || ''}</div>
                                                            </div>
                                                            <div class="text-sm text-gray-500 dark:text-gray-400">${analyst.age_analyst ? `Age: ${analyst.age_analyst}` : ''}</div>
                                                        </summary>
                                                        <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                                                            ${analyst.background_analyst ? `
                                                                <div class="mt-3">
                                                                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Background</div>
                                                                    <div class="mt-1 text-sm text-gray-800 dark:text-gray-200">${analyst.background_analyst}</div>
                                                                </div>
                                                            ` : ''}
                                                            ${analyst.business_question ? `
                                                                <div class="mt-3">
                                                                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Research Question</div>
                                                                    <div class="mt-1 text-sm text-gray-800 dark:text-gray-200">${analyst.business_question}</div>
                                                                </div>
                                                            ` : ''}
                                                        </div>
                                                    </details>
                                                `).join('');
                                            })()}
                                        </div>
                                    </div>
                                ` : ''}

                                ${report.state?.completed_reports && report.state.completed_reports.length > 0 ? `
                                    <!-- Completed Reports Section -->
                                    <div class="mt-8">
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Completed Reports (${report.state.completed_reports.length})</div>
                                        <div class="space-y-4">
                                            ${report.state.completed_reports.map((completedReport, index) => {
                                                // Extract a preview (first 100 characters)
                                                const reportStr = typeof completedReport === 'string' ? completedReport : JSON.stringify(completedReport);
                                                const preview = reportStr.length > 100 ? reportStr.substring(0, 100) + '...' : reportStr;

                                                // Prepare the content
                                                let content;
                                                if (typeof completedReport === 'string') {
                                                    if (typeof marked !== 'undefined') {
                                                        content = marked.parse(completedReport);
                                                    } else {
                                                        // Basic markdown to HTML conversion if marked library is not available
                                                        content = completedReport
                                                            .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>') // Bold
                                                            .replace(/\*(.+?)\*/g, '<em>$1</em>') // Italic
                                                            .replace(/\n\n/g, '</p><p>') // Paragraphs
                                                            .replace(/\n/g, '<br>') // Line breaks
                                                            .replace(/#{3}\s(.+?)\n/g, '<h3>$1</h3>') // H3
                                                            .replace(/#{2}\s(.+?)\n/g, '<h2>$1</h2>') // H2
                                                            .replace(/#{1}\s(.+?)\n/g, '<h1>$1</h1>'); // H1
                                                        content = `<p>${content}</p>`;
                                                    }
                                                } else {
                                                    content = `<pre class="bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto">${JSON.stringify(completedReport, null, 2)}</pre>`;
                                                }

                                                return `
                                                    <details class="bg-white dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                                                        <summary class="px-4 py-3 cursor-pointer font-medium">
                                                            <div class="flex justify-between items-center">
                                                                <div>
                                                                    <span class="font-semibold text-gray-900 dark:text-white">Report ${index + 1}</span>
                                                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-1">${preview}</p>
                                                                </div>
                                                                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200 flex-shrink-0"
                                                                    style="transform: rotate(-180deg)"
                                                                    fill="none"
                                                                    stroke="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        stroke-width="2"
                                                                        d="M19 9l-7 7-7-7" />
                                                                </svg>
                                                            </div>
                                                        </summary>
                                                        <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700 prose dark:prose-invert max-w-none">
                                                            ${content}
                                                        </div>
                                                    </details>
                                                `;
                                            }).join('')}
                                        </div>
                                    </div>
                                ` : ''}

                                <!-- State Section -->
                                <div class="mt-8">
                                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">State Information</div>
                                    <div id="state-section-${taskId}">
                                        ${StateHandler.createStateCard(report.state || {})}
                                    </div>
                                </div>

                                <!-- Workflow Progress Section -->
                                <div class="mt-8">
                                    <div class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Workflow Progress</div>
                                    <div class="space-y-4">
                                        ${Object.entries(report.workflow_stage?.stages || {}).map(([stage, info]) => `
                                            <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                                <div class="flex justify-between items-center">
                                                    <div class="font-medium text-gray-900 dark:text-white">
                                                        ${stage === '__start__' ? 'Start' :
                                                          stage === 'END' ? 'Complete' :
                                                          stage.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').trim()}
                                                    </div>
                                                    <div class="px-3 py-1 rounded-full text-sm ${
                                                        info.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                                                        info.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300' :
                                                        info.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300' :
                                                        'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300'
                                                    }">
                                                        ${info.status || 'Pending'}
                                                    </div>
                                                </div>
                                                ${info.started_at ? `
                                                    <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                                        Started: ${this.formatDate(info.started_at)}
                                                    </div>
                                                ` : ''}
                                                ${info.completed_at ? `
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        Completed: ${this.formatDate(info.completed_at)}
                                                    </div>
                                                    ${info.duration_ms ? `
                                                    <div class="text-sm text-green-600 dark:text-green-400 font-medium">
                                                        Duration: ${(info.duration_ms / 1000).toFixed(1)}s
                                                    </div>
                                                    ` : ''}
                                                ` : ''}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>

                                <!-- Metadata Section -->
                                <div class="mt-8 grid grid-cols-2 md:grid-cols-3 gap-4">
                                    <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Started At</div>
                                        <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                            ${this.formatDate(startedAt)}
                                        </div>
                                    </div>
                                    ${report.completed_at ? `
                                        <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed At</div>
                                            <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                                ${this.formatDate(report.completed_at)}
                                            </div>
                                        </div>
                                    ` : ''}
                                    ${report.metadata?.analysis_duration ? `
                                        <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Analysis Duration</div>
                                            <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                                ${report.metadata.analysis_duration.toFixed(1)}s
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </details>
                    `;
                }).join('')}
            </div>
        `;

        // Add event listeners for smooth transitions
        container.querySelectorAll('details').forEach(details => {
            const content = details.querySelector('.content');
            details.addEventListener('toggle', () => {
                if (details.open) {
                    content.style.maxHeight = content.scrollHeight + 'px';
                    content.style.opacity = '1';
                } else {
                    content.style.maxHeight = '0';
                    content.style.opacity = '0';
                }
            });
        });

        // Initialize workflow timers for in-progress reports
        if (typeof TimerHandler !== 'undefined') {
            reports.forEach(report => {
                if (report.status !== 'completed' && report.status !== 'failed') {
                    const taskId = report.task_id || (report.state && report.state.task_id) || report._id || 'unknown';
                    const startStage = report.workflow_stage?.stages?.['__start__'];

                    if (startStage && startStage.started_at) {
                        TimerHandler.startWorkflowTimer(taskId, startStage.started_at);
                    }
                }
            });
        }

        // Initialize state handlers
        container.querySelectorAll('[id^="state-section-"]').forEach(stateSection => {
            const reportId = stateSection.id.replace('state-section-', '');
            const report = reports.find(r => {
                const rid = r.task_id || (r.state && r.state.task_id) || r._id || 'default';
                return rid === reportId;
            });
            if (report && report.state && typeof StateHandler !== 'undefined' && typeof StateHandler.renderState === 'function') {
                try {
                    StateHandler.renderState(stateSection.id, report);
                } catch (e) {
                    console.error(`Error rendering state for report ${reportId}:`, e);
                }
            }
        });

        // Add chevron rotation for completed reports accordions
        container.querySelectorAll('.space-y-4 details').forEach(details => {
            const chevron = details.querySelector('svg');
            if (chevron) {
                details.addEventListener('toggle', () => {
                    chevron.style.transform = details.open ? 'rotate(0deg)' : 'rotate(-180deg)';
                });
            }
        });
    }

    static async startProgressTracking(taskId) {
        const POLL_INTERVAL = 2000; // Poll every 2 seconds
        let isComplete = false;
        let hasFailed = false;

        const pollStatus = async () => {
            try {
                const status = await ResearchDirectorAPI.getTaskStatus(taskId);
                ProgressHandler.updateProgressBar(taskId, status.workflow_stage);

                // Check if analysis is complete or failed
                const currentStage = status.workflow_stage.current;
                const currentStatus = status.workflow_stage.stages[currentStage]?.status;

                isComplete = currentStage === 'END' && currentStatus === 'completed';
                hasFailed = currentStatus === 'failed';

                if (!isComplete && !hasFailed) {
                    setTimeout(pollStatus, POLL_INTERVAL);
                } else {
                    // Stop any running timers for this task
                    if (typeof TimerHandler !== 'undefined') {
                        TimerHandler.clearTimer(taskId);
                    }

                    // Get the completed or failed report
                    const report = await ResearchDirectorAPI.getReport(taskId);

                    // Create the final report card
                    const reportCard = this.createReportCard(report);
                    reportCard.id = `report-${taskId}`;

                    // Find and replace the progress card with the final report
                    const progressCard = document.getElementById(`report-${taskId}`);
                    if (progressCard && progressCard.parentNode) {
                        // Add a fade-out effect to the progress card
                        progressCard.style.transition = 'opacity 0.5s ease-out';
                        progressCard.style.opacity = '0';

                        // After fade-out, replace with the report card
                        setTimeout(() => {
                            // Add fade-in effect to the new report card
                            reportCard.style.opacity = '0';
                            reportCard.style.transition = 'opacity 0.5s ease-in';

                            // Replace the card
                            progressCard.parentNode.replaceChild(reportCard, progressCard);

                            // Trigger fade-in
                            setTimeout(() => {
                                reportCard.style.opacity = '1';
                            }, 50);
                        }, 500);
                    }

                    // Show completion or failure toast
                    if (hasFailed) {
                        UIHandler.showToast('Analysis failed: ' + report.error || 'Unknown error', 'error');
                    } else {
                        UIHandler.showToast('Analysis completed successfully!', 'success');
                    }
                }
            } catch (error) {
                UIHandler.showToast('Error checking analysis status', 'error');
                setTimeout(pollStatus, POLL_INTERVAL);
            }
        };

        // Start polling
        await pollStatus();
    }
}

// Export the class
window.ReportHandler = ReportHandler;
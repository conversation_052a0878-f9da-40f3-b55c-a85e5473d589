// Director Router for Research Agents
// Handles URL-based navigation and accordion functionality

class DirectorRouter {
    constructor() {
        this.currentDirectorId = null;
        this.initFromUrl();
        this.setupEventListeners();
    }

    /**
     * Initialize the router based on the current URL
     */
    initFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/research-agents\/([a-f0-9]+)/);

        if (match && match[1]) {
            this.currentDirectorId = match[1];
        } else {
            this.currentDirectorId = null;
        }
    }

    /**
     * Set up event listeners for navigation
     */
    setupEventListeners() {
        // Listen for popstate events (browser back/forward)
        window.addEventListener('popstate', (event) => {
            this.initFromUrl();
            this.updateUI();
        });
    }

    /**
     * Navigate to a specific director
     * @param {string} directorId - The ID of the director to navigate to
     * @param {boolean} pushState - Whether to push a new state to the browser history
     */
    navigateToDirector(directorId, pushState = true) {
        this.currentDirectorId = directorId;

        if (pushState) {
            const url = directorId ? `/research-agents/${directorId}` : '/research-agents';
            window.history.pushState({ directorId }, '', url);
        }

        this.updateUI();
    }

    /**
     * Navigate to the main research agents page (no specific director)
     */
    navigateToMain() {
        this.navigateToDirector(null);
    }

    /**
     * Update the UI based on the current director ID
     */
    updateUI() {
        const directorCards = document.querySelectorAll('.director-card');
        let selectedCard = null;

        directorCards.forEach(card => {
            const cardDirectorId = card.dataset.directorId;

            if (this.currentDirectorId === null) {
                // Minimize all cards when on main page
                UIHandler.minimizeDirectorCard(card);
            } else if (cardDirectorId === this.currentDirectorId) {
                // Expand the selected director card
                UIHandler.expandDirectorCard(card);
                selectedCard = card;
            } else {
                // Minimize all other cards
                UIHandler.minimizeDirectorCard(card);
            }
        });

        // Scroll to the selected card if it exists
        if (selectedCard) {
            // Use a small timeout to ensure the DOM has updated
            setTimeout(() => {
                selectedCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        }
    }

    /**
     * Toggle a director card's expanded state
     * @param {string} directorId - The ID of the director to toggle
     */
    toggleDirector(directorId) {
        if (this.currentDirectorId === directorId) {
            // If clicking the currently expanded director, collapse it
            this.navigateToMain();
        } else {
            // Otherwise, expand the clicked director
            this.navigateToDirector(directorId);
        }
    }
}

// Initialize the router when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.directorRouter = new DirectorRouter();

    // If there's a director ID in the URL, wait for directors to load then update UI
    if (window.directorRouter.currentDirectorId) {
        // Check if directors are already loaded
        const checkDirectorsLoaded = () => {
            const directorCards = document.querySelectorAll('.director-card');
            if (directorCards.length > 0) {
                window.directorRouter.updateUI();
            } else {
                // Check again in a moment
                setTimeout(checkDirectorsLoaded, 100);
            }
        };

        // Start checking
        checkDirectorsLoaded();
    }
});

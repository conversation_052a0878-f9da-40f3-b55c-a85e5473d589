// Handler for displaying graph execution state
class StateHandler {
    static formatValue(value) {
        if (typeof value === 'object' && value !== null) {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    }

    static createStateCard(state) {
        if (!state || Object.keys(state).length === 0) {
            return `
                <div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-gray-500 dark:text-gray-400 text-sm">No state data available</p>
                </div>
            `;
        }

        // Filter out references and feature_requests fields
        const filteredState = { ...state };
        if ('references' in filteredState) delete filteredState.references;
        if ('feature_requests' in filteredState) delete filteredState.feature_requests;

        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Graph Execution State
                    </h3>
                    <div class="space-y-4">
                        ${this.createStateTree(filteredState)}
                    </div>
                </div>
            </div>
        `;
    }

    static createStateTree(obj, level = 0) {
        if (typeof obj !== 'object' || obj === null) {
            return this.createValueDisplay(obj);
        }

        return Object.entries(obj).map(([key, value]) => {
            // Skip references and feature_requests at any nesting level
            if (key === 'references' || key === 'feature_requests') {
                return '';
            }

            const isObject = typeof value === 'object' && value !== null;
            const hasContent = isObject && Object.keys(value).length > 0;

            return `
                <div class="rounded-lg border border-gray-200 dark:border-gray-700 ${level === 0 ? 'bg-gray-50 dark:bg-gray-800/50' : 'bg-white/50 dark:bg-gray-800/50'}">
                    <button class="w-full text-left px-4 py-3 flex items-center justify-between ${hasContent ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50' : ''}" 
                            ${hasContent ? 'onclick="this.nextElementSibling.classList.toggle(\'hidden\')"' : ''}>
                        <span class="font-medium text-sm text-gray-900 dark:text-gray-100">
                            ${key}
                        </span>
                        ${hasContent ? `
                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-200" 
                                 style="transform: rotate(-180deg)"
                                 fill="none" 
                                 stroke="currentColor" 
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" 
                                      stroke-linejoin="round" 
                                      stroke-width="2" 
                                      d="M19 9l-7 7-7-7" />
                            </svg>
                        ` : ''}
                    </button>
                    ${isObject ? `
                        <div class="hidden px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                            <div class="space-y-3">
                                ${this.createStateTree(value, level + 1)}
                            </div>
                        </div>
                    ` : `
                        <div class="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
                            ${this.createValueDisplay(value)}
                        </div>
                    `}
                </div>
            `;
        }).join('');
    }

    static createValueDisplay(value) {
        if (value === null || value === undefined) {
            return `<span class="text-gray-400 dark:text-gray-500 text-sm italic">null</span>`;
        }

        if (typeof value === 'boolean') {
            return `
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    value 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
                }">
                    ${value ? 'true' : 'false'}
                </span>
            `;
        }

        if (typeof value === 'number') {
            return `<span class="text-indigo-600 dark:text-indigo-400 font-mono text-sm">${value}</span>`;
        }

        if (typeof value === 'string') {
            if (value.startsWith('http://') || value.startsWith('https://')) {
                return `
                    <a href="${value}" 
                       target="_blank" 
                       class="text-blue-600 dark:text-blue-400 hover:underline text-sm break-all">
                        ${value}
                    </a>
                `;
            }
            return `<span class="text-gray-700 dark:text-gray-300 text-sm break-all">${value}</span>`;
        }

        return `<span class="text-gray-700 dark:text-gray-300 text-sm break-all">${this.formatValue(value)}</span>`;
    }

    static renderState(containerId, report) {
        const container = document.getElementById(containerId);
        if (!container) {
            return;
        }

        const state = report.state || {};
        container.innerHTML = this.createStateCard(state);

        // Add click handlers for rotating chevron icons
        container.querySelectorAll('button').forEach(button => {
            if (button.nextElementSibling) {
                const chevron = button.querySelector('svg');
                if (chevron) {
                    button.addEventListener('click', () => {
                        chevron.style.transform = button.nextElementSibling.classList.contains('hidden') 
                            ? 'rotate(0deg)' 
                            : 'rotate(-180deg)';
                    });
                }
            }
        });
    }

    static updateState(containerId, report) {
        const container = document.getElementById(containerId);
        if (!container) {
            return;
        }

        // Only update if the state has changed
        const currentState = JSON.stringify(report.state || {});
        if (container.dataset.currentState !== currentState) {
            container.dataset.currentState = currentState;
            this.renderState(containerId, report);
        }
    }
}

// Export the class
window.StateHandler = StateHandler; 
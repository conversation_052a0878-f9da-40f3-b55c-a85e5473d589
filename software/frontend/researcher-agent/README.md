# Research Agent Frontend

A web interface for managing and visualizing the Research Director Agents system. This frontend allows users to deploy research agents, monitor their progress, and view the generated reports.

## Directory Structure

```
researcher-agent/
├── js/
│   ├── api_client.js      # Handles API communication with the backend
│   ├── main.js            # Main entry point for the application
│   ├── progress_handler.js # Manages research progress visualization
│   ├── report_handler.js   # Handles report display and formatting
│   ├── state_handler.js    # Manages graph execution state display
│   └── ui_handler.js       # Handles UI elements and interactions
├── css/
│   └── styles.css         # Custom styles for the application
└── index.html             # Main HTML template (extends base.html)
```

## Functionality

### API Integration

The frontend communicates with the backend through the `api_client.js` file, which provides methods for:
- Fetching research directors and their reports
- Deploying research directors
- Getting task status and report data
- Updating director settings

### Data Visualization

The frontend displays research reports with comprehensive information including:

1. **Final Report**: The summarized research report generated by the director
2. **Business Question**: The original research question being investigated
3. **Research Plan**: The plan formulated by the director
4. **Research Analysts**: Collapsible accordion list of analysts with their backgrounds and assigned questions
5. **References**: Clickable links to related reports, each opening in a new window at `/simple-report/<report_id>`
6. **Completed Reports**: Collapsible accordion view of analyst reports with preview snippets
7. **Graph Execution State**: Detailed state information about the execution graph
8. **Workflow Progress**: Visual representation of the research workflow stages

### State Handling

The `state_handler.js` file manages the visualization of the graph execution state from the DirectorState object. This displays all the key-value pairs in the state object, including:
- director_id
- task_id 
- business_question
- plan
- final_report
- analysts
- completed_reports
- references

## Usage

The frontend is accessed at `/research-agents` and allows users to:
1. View a list of available research directors
2. Select a graph type for execution
3. Deploy a director to start a new analysis
4. View real-time progress of the analysis
5. Review completed reports with all associated data

# Researcher Agent

This module provides a UI for managing and deploying Research Director agents.

## Directory Structure
```
software/frontend/researcher-agent/
├── README.md               # Documentation for the researcher agent module
├── index.html              # Main HTML template
├── js/                     # JavaScript files
│   ├── api_client.js       # API client for interacting with backend
│   ├── main.js             # Main initialization script
│   ├── progress_handler.js # Handler for task progress tracking
│   ├── report_handler.js   # Handler for displaying research reports
│   ├── state_handler.js    # Handler for displaying graph execution state
│   └── ui_handler.js       # UI components and interaction handlers
└── css/                    # CSS stylesheets
```

## Features

### Research Directors
- View all research directors with their expertise areas and background
- Deploy directors to perform market analysis
- Enable/disable auto-deploy mode for continuous analysis
- Clean director memory to reset accumulated knowledge and insights

### Clean Memory Feature
- Button to reset all director's memory components
- Removes accumulated insights, company analyses, sector knowledge, and tool embeddings
- Resets performance metrics and unlinks all associated reports
- Preserves the director entity and configuration while clearing historical data
- Requires confirmation before executing due to irreversible nature

### Auto-Deploy Mode
- Toggle button that switches between play (auto-deploy) and stop modes
- When enabled, automatically starts a new analysis task when the previous one completes
- Persists in the backend, continuing to run even if the browser is closed
- Visual indicator shows whether auto-deploy is active (green play button) or running (red stop button)
- Task counter displays statistics for the current auto-deploy session:
  - Total tasks processed
  - Successfully completed tasks 
  - Failed tasks
  - Currently in-progress tasks
  - Success rate percentage
  - Timestamps for when auto-deploy started and when tasks were last completed/failed

### Research Reports
- View reports generated by directors
- Track progress of ongoing analyses with interactive progress bars
- Expand report sections to view detailed findings

## API Integration
- Communicates with backend API endpoints for director management and task deployment
- Real-time progress tracking with polling for task status updates 
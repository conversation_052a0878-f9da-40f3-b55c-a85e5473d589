{% extends "base.html" %}

{% block head %}
{{ super() }}
<!-- Add marked library for markdown parsing -->
<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script>
    // Fallback to basic text if marked fails to load
    if (typeof marked === 'undefined') {
        console.warn('Marked library not loaded, falling back to basic text formatting');
        window.marked = {
            parse: function(text) {
                return text.replace(/\*\*/g, '').replace(/\n/g, '<br>');
            }
        };
    }
</script>
<style>
    .prose {
        max-width: 65ch;
        line-height: 1.6;
    }
    .prose h1, .prose h2, .prose h3 {
        margin-top: 1.5em;
        margin-bottom: 0.5em;
        font-weight: 600;
    }
    .prose p {
        margin-top: 1em;
        margin-bottom: 1em;
    }
    .prose ul {
        list-style-type: disc;
        padding-left: 1.5em;
        margin-top: 0.5em;
        margin-bottom: 0.5em;
    }
    .prose strong {
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6 max-w-6xl">
    <!-- Header -->
    <div class="mb-4">
        <h1 class="text-2xl font-bold text-gray-900">Research Director Agents</h1>
        <p class="text-gray-600 mt-1 text-sm">Expert agents that analyze market opportunities in their specialized fields</p>
    </div>

    <!-- Directors List -->
    <div class="grid grid-cols-1 gap-2" id="directors-container">
        <!-- Directors will be dynamically inserted here -->
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg hidden"></div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include our JavaScript files -->
<script src="/frontend/researcher-agent/js/api_client.js"></script>
<script src="/frontend/researcher-agent/js/timer_handler.js"></script>
<script src="/frontend/researcher-agent/js/progress_handler.js"></script>
<script src="/frontend/researcher-agent/js/report_handler.js"></script>
<script src="/frontend/researcher-agent/js/state_handler.js"></script>
<script src="/frontend/researcher-agent/js/ui_handler.js"></script>
<script src="/frontend/researcher-agent/js/letta_memory.js"></script>
<script src="/frontend/researcher-agent/js/director_router.js"></script>
<script src="/frontend/researcher-agent/js/main.js"></script>
{% endblock %}
{% extends "base.html" %}

{% block title %}Feature Request Agent{% endblock %}

{% block header %}Feature Request Agent{% endblock %}

{% block content %}
<div class="bg-white shadow overflow-hidden sm:rounded-lg dark:bg-gray-800">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
            Feature Request Management
        </h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
            View and manage feature requests gathered by research agents.
        </p>
    </div>
    <div class="border-t border-gray-200 dark:border-gray-700">
        <div class="px-4 py-4 sm:p-6">
            <div id="feature-requests-loading" class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 dark:border-indigo-400"></div>
                <p class="mt-2 text-gray-500 dark:text-gray-400">Loading feature requests...</p>
            </div>
            
            <div id="feature-requests-container" class="hidden mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Feature requests column -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Features</h4>
                        <div id="feature-list" class="space-y-4">
                            <!-- Feature type requests will be populated here -->
                        </div>
                        <div id="no-features" class="hidden text-center py-5">
                            <p class="text-gray-500 dark:text-gray-400">No feature requests found.</p>
                        </div>
                    </div>
                    
                    <!-- Data requests column -->
                    <div>
                        <h4 class="text-base font-medium text-gray-900 dark:text-white mb-3">Data</h4>
                        <div id="data-list" class="space-y-4">
                            <!-- Data type requests will be populated here -->
                        </div>
                        <div id="no-data" class="hidden text-center py-5">
                            <p class="text-gray-500 dark:text-gray-400">No data requests found.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Toast notification for copy -->
            <div id="copy-toast" class="hidden fixed bottom-4 right-4 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 px-4 py-2 rounded shadow-lg z-50 transition-opacity duration-300">
                Copied to clipboard!
            </div>
            
            <!-- Delete confirmation modal -->
            <div id="delete-modal" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-xl max-w-md w-full mx-4">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                            Delete Feature Request
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Are you sure you want to delete this feature request? This action cannot be undone.
                            </p>
                        </div>
                        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                            <button id="confirm-delete-btn" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                                Delete
                            </button>
                            <button id="cancel-delete-btn" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Global variable to store the ID of the request to delete
let requestToDelete = null;

document.addEventListener('DOMContentLoaded', function() {
    fetchFeatureRequests();
    
    // Modal setup
    const deleteModal = document.getElementById('delete-modal');
    const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    
    // Close modal on cancel
    cancelDeleteBtn.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
        requestToDelete = null;
    });
    
    // Confirm delete action
    confirmDeleteBtn.addEventListener('click', function() {
        if (requestToDelete) {
            deleteFeatureRequest(requestToDelete);
        }
        deleteModal.classList.add('hidden');
    });
    
    // Close modal if clicked outside
    deleteModal.addEventListener('click', function(e) {
        if (e.target === deleteModal) {
            deleteModal.classList.add('hidden');
            requestToDelete = null;
        }
    });
});

async function fetchFeatureRequests() {
    try {
        const response = await fetch('/api/feature-requests');
        const data = await response.json();
        
        document.getElementById('feature-requests-loading').classList.add('hidden');
        document.getElementById('feature-requests-container').classList.remove('hidden');
        
        const featuresList = document.getElementById('feature-list');
        const dataList = document.getElementById('data-list');
        const noFeaturesMessage = document.getElementById('no-features');
        const noDataMessage = document.getElementById('no-data');
        
        // Clear previous content
        featuresList.innerHTML = '';
        dataList.innerHTML = '';
        
        if (data.feature_requests && data.feature_requests.length > 0) {
            // Separate requests by type and sort by status (completed at the bottom)
            const sortedRequests = data.feature_requests.sort((a, b) => {
                if (a.status === 'completed' && b.status !== 'completed') return 1;
                if (a.status !== 'completed' && b.status === 'completed') return -1;
                return new Date(b.created_at) - new Date(a.created_at); // Newest first
            });
            
            const featureRequests = sortedRequests.filter(r => r.feature_type === 'feature');
            const dataRequests = sortedRequests.filter(r => r.feature_type === 'data');
            
            // Display feature requests
            if (featureRequests.length > 0) {
                featureRequests.forEach(request => {
                    const requestElement = createFeatureRequestElement(request);
                    featuresList.appendChild(requestElement);
                });
                noFeaturesMessage.classList.add('hidden');
            } else {
                noFeaturesMessage.classList.remove('hidden');
            }
            
            // Display data requests
            if (dataRequests.length > 0) {
                dataRequests.forEach(request => {
                    const requestElement = createFeatureRequestElement(request);
                    dataList.appendChild(requestElement);
                });
                noDataMessage.classList.add('hidden');
            } else {
                noDataMessage.classList.remove('hidden');
            }
        } else {
            noFeaturesMessage.classList.remove('hidden');
            noDataMessage.classList.remove('hidden');
        }
    } catch (error) {
        document.getElementById('feature-requests-loading').classList.add('hidden');
        document.getElementById('feature-requests-container').classList.remove('hidden');
        
        document.getElementById('feature-list').innerHTML = '';
        document.getElementById('data-list').innerHTML = '';
        document.getElementById('no-features').classList.add('hidden');
        document.getElementById('no-data').classList.add('hidden');
        
        const errorMessage = `
            <div class="bg-red-50 dark:bg-red-900/30 p-4 rounded-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error loading feature requests</h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            <p>Please try again later or contact support.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('feature-list').innerHTML = errorMessage;
        document.getElementById('data-list').innerHTML = errorMessage;
    }
}

function createFeatureRequestElement(request) {
    const div = document.createElement('div');
    div.className = 'bg-gray-50 dark:bg-gray-700 shadow overflow-hidden sm:rounded-lg';
    
    const isCompleted = request.status === 'completed';
    
    div.innerHTML = `
        <div class="px-4 py-4 sm:px-6">
            <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium ${isCompleted ? 'line-through text-gray-500 dark:text-gray-400' : 'text-indigo-600 dark:text-indigo-400'}">
                    ${request.feature_type.toUpperCase()}
                </h4>
                <div class="ml-2 flex-shrink-0 flex space-x-2">
                    <button 
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full items-center
                        bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-800" 
                        onclick="copyToClipboard('${request.id}', '${request.feature_request.replace(/'/g, "\\'")}')">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        Copy
                    </button>
                    <button 
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full items-center
                        bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800" 
                        onclick="showDeleteModal('${request.id}')">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                    </button>
                </div>
            </div>
            
            <div class="mt-3">
                <p class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">${request.feature_request}</p>
            </div>
            
            <div class="mt-3 sm:flex sm:justify-between">
                <div class="sm:flex">
                    <p class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        ${new Date(request.created_at || Date.now()).toLocaleDateString()}
                    </p>
                    <p class="mt-2 flex items-center text-sm sm:mt-0 sm:ml-6 cursor-pointer" 
                        onclick="toggleStatus('${request.id}')"
                        title="Click to toggle status">
                        <svg class="flex-shrink-0 mr-1.5 h-5 w-5 ${isCompleted ? 'text-green-500 dark:text-green-400' : 'text-yellow-500 dark:text-yellow-400'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="${isCompleted ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}">
                            ${isCompleted ? 'Completed' : 'Pending'}
                        </span>
                    </p>
                </div>
            </div>
        </div>
    `;
    
    return div;
}

function showDeleteModal(requestId) {
    // Set the request ID to delete
    requestToDelete = requestId;
    // Show the modal
    document.getElementById('delete-modal').classList.remove('hidden');
}

async function deleteFeatureRequest(requestId) {
    try {
        const response = await fetch(`/api/feature-requests/${requestId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            try {
                const result = await response.json();
                
                // Show toast notification
                const toast = document.getElementById('copy-toast');
                toast.textContent = 'Feature request deleted!';
                toast.classList.remove('hidden', 'bg-green-100', 'dark:bg-green-900', 'text-green-800', 'dark:text-green-100');
                toast.classList.add('opacity-100', 'bg-red-100', 'dark:bg-red-900', 'text-red-800', 'dark:text-red-100');
                
                setTimeout(() => {
                    toast.classList.add('opacity-0');
                    setTimeout(() => {
                        toast.classList.add('hidden');
                        toast.classList.remove('opacity-0', 'bg-red-100', 'dark:bg-red-900', 'text-red-800', 'dark:text-red-100');
                        toast.textContent = 'Copied to clipboard!';
                        toast.classList.add('bg-green-100', 'dark:bg-green-900', 'text-green-800', 'dark:text-green-100');
                    }, 300);
                }, 2000);
                
                // Refresh the list
                await fetchFeatureRequests();
            } catch (jsonError) {
                // Still refresh the list even if JSON parsing fails
                await fetchFeatureRequests();
            }
        } else {
            let errorText = 'Failed to delete request';
            try {
                const errorData = await response.json();
                errorText = errorData.detail || errorText;
            } catch (parseError) {
                // If response is not JSON
                errorText = await response.text() || errorText;
            }
            
            // Show error toast
            const toast = document.getElementById('copy-toast');
            toast.textContent = `Error: ${errorText}`;
            toast.classList.remove('hidden', 'bg-green-100', 'dark:bg-green-900', 'text-green-800', 'dark:text-green-100');
            toast.classList.add('opacity-100', 'bg-red-100', 'dark:bg-red-900', 'text-red-800', 'dark:text-red-100');
            
            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    toast.classList.add('hidden');
                    toast.classList.remove('opacity-0', 'bg-red-100', 'dark:bg-red-900', 'text-red-800', 'dark:text-red-100');
                    toast.textContent = 'Copied to clipboard!';
                    toast.classList.add('bg-green-100', 'dark:bg-green-900', 'text-green-800', 'dark:text-green-100');
                }, 300);
            }, 3000);
        }
    } catch (error) {
        // Show error toast for exception
        const toast = document.getElementById('copy-toast');
        toast.textContent = `Error: ${error.message || 'Unknown error'}`;
        toast.classList.remove('hidden', 'bg-green-100', 'dark:bg-green-900', 'text-green-800', 'dark:text-green-100');
        toast.classList.add('opacity-100', 'bg-red-100', 'dark:bg-red-900', 'text-red-800', 'dark:text-red-100');
        
        setTimeout(() => {
            toast.classList.add('opacity-0');
            setTimeout(() => {
                toast.classList.add('hidden');
                toast.classList.remove('opacity-0', 'bg-red-100', 'dark:bg-red-900', 'text-red-800', 'dark:text-red-100');
                toast.textContent = 'Copied to clipboard!';
                toast.classList.add('bg-green-100', 'dark:bg-green-900', 'text-green-800', 'dark:text-green-100');
            }, 300);
        }, 3000);
    }
}

async function toggleStatus(requestId) {
    try {
        const response = await fetch(`/api/feature-requests/${requestId}/toggle`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            await response.json();
            
            // Refresh the list to show updated status
            await fetchFeatureRequests();
        }
    } catch (error) {
        // Error handled silently
    }
}

function copyToClipboard(id, text) {
    // Check if Clipboard API is available
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(() => {
            showToast();
        }).catch(err => {
            fallbackCopyToClipboard(text);
        });
    } else {
        // Fallback method
        fallbackCopyToClipboard(text);
    }
}

function fallbackCopyToClipboard(text) {
    try {
        // Create a temporary textarea element
        const textarea = document.createElement('textarea');
        textarea.value = text;
        
        // Make the textarea out of viewport
        textarea.style.position = 'fixed';
        textarea.style.left = '-999999px';
        textarea.style.top = '-999999px';
        
        document.body.appendChild(textarea);
        textarea.focus();
        textarea.select();
        
        // Execute copy command
        const successful = document.execCommand('copy');
        document.body.removeChild(textarea);
        
        if (successful) {
            showToast();
        }
    } catch (err) {
        // Error handled silently
    }
}

function showToast() {
    const toast = document.getElementById('copy-toast');
    toast.classList.remove('hidden');
    toast.classList.add('opacity-100');
    
    setTimeout(() => {
        toast.classList.add('opacity-0');
        setTimeout(() => {
            toast.classList.add('hidden');
            toast.classList.remove('opacity-0');
        }, 300);
    }, 2000);
}
</script>
{% endblock %} 
/**
 * Environment Code Component
 * 
 * This component handles the display of environment code
 * for each environment in the RL Environments page.
 */

const EnvironmentCodeComponent = {
    /**
     * Initialize the code component
     * @param {string} environmentId - The ID of the environment
     * @param {HTMLElement} containerElement - The container element to render the code in
     */
    async init(environmentId, containerElement) {
        if (!environmentId || !containerElement) {
            console.error('Environment ID or container element is missing');
            return;
        }

        // Create code container
        const codeContainer = document.createElement('div');
        codeContainer.className = 'mt-3';
        codeContainer.id = `code-container-${environmentId}`;
        
        // Add toggle button and container
        codeContainer.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <button id="toggle-code-btn-${environmentId}" class="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    <span>Show Code</span>
                </button>
            </div>
            <div id="code-content-${environmentId}" class="hidden">
                <div class="flex items-center justify-center p-4">
                    <svg class="animate-spin h-5 w-5 text-indigo-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-sm text-gray-600 dark:text-gray-300">Loading environment code...</span>
                </div>
            </div>
        `;
        
        // Append to container
        containerElement.appendChild(codeContainer);
        
        // Add event listener to toggle button
        const toggleButton = document.getElementById(`toggle-code-btn-${environmentId}`);
        const codeContent = document.getElementById(`code-content-${environmentId}`);
        
        if (toggleButton && codeContent) {
            toggleButton.addEventListener('click', async () => {
                const isHidden = codeContent.classList.contains('hidden');
                
                if (isHidden) {
                    // Show code content
                    codeContent.classList.remove('hidden');
                    toggleButton.querySelector('span').textContent = 'Hide Code';
                    
                    // Fetch code if not already loaded
                    if (!codeContent.getAttribute('data-loaded')) {
                        await this.fetchAndRenderCode(environmentId, codeContent);
                        codeContent.setAttribute('data-loaded', 'true');
                    }
                } else {
                    // Hide code content
                    codeContent.classList.add('hidden');
                    toggleButton.querySelector('span').textContent = 'Show Code';
                }
            });
        }
    },
    
    /**
     * Fetch code data from API and render it
     * @param {string} environmentId - The ID of the environment
     * @param {HTMLElement} container - The container element to render the code in
     */
    async fetchAndRenderCode(environmentId, container) {
        try {
            // Fetch environment data
            const response = await fetch(`/api/rl-environments/${environmentId}`);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch environment: ${response.statusText}`);
            }
            
            const environmentData = await response.json();
            
            // Render code
            this.renderCode(environmentData.code, container);
            
        } catch (error) {
            console.warn('Failed to load code:', error);
            
            // Show error message
            container.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <p class="text-sm text-red-700 dark:text-red-300">
                        Failed to load code
                    </p>
                </div>
            `;
        }
    },
    
    /**
     * Render code in the container with syntax highlighting
     * @param {string} code - The code to render
     * @param {HTMLElement} container - The container element to render the code in
     */
    renderCode(code, container) {
        if (!code) {
            // Show empty state
            container.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300">
                        No code available
                    </p>
                </div>
            `;
            return;
        }
        
        // Create code element with syntax highlighting
        const html = `
            <div class="bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="flex items-center justify-between bg-gray-100 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Environment Code</h4>
                    <button class="copy-code-btn text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300" data-code="${this.escapeHtml(code)}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                        </svg>
                    </button>
                </div>
                <div class="p-4 max-h-96 overflow-auto">
                    <pre class="text-xs text-gray-800 dark:text-gray-200 font-mono"><code class="language-python">${this.escapeHtml(code)}</code></pre>
                </div>
            </div>
        `;
        
        // Update container
        container.innerHTML = html;
        
        // Add event listener to copy button
        const copyButton = container.querySelector('.copy-code-btn');
        if (copyButton) {
            copyButton.addEventListener('click', () => {
                const codeToCopy = copyButton.getAttribute('data-code');
                navigator.clipboard.writeText(codeToCopy)
                    .then(() => {
                        // Show success message
                        const originalHTML = copyButton.innerHTML;
                        copyButton.innerHTML = `
                            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        `;
                        
                        // Reset after 2 seconds
                        setTimeout(() => {
                            copyButton.innerHTML = originalHTML;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy code:', err);
                    });
            });
        }
        
        // Apply syntax highlighting if Prism is available
        if (typeof Prism !== 'undefined') {
            Prism.highlightAllUnder(container);
        }
    },
    
    /**
     * Escape HTML special characters
     * @param {string} html - The HTML string to escape
     * @returns {string} The escaped HTML string
     */
    escapeHtml(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }
};

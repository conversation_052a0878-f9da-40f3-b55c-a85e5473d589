/**
 * Environment Schema Component
 *
 * This component handles the display of environment schema information
 * for each environment in the RL Environments page.
 */

const EnvironmentSchemaComponent = {
    /**
     * Initialize the schema component
     * @param {string} environmentId - The ID of the environment
     * @param {HTMLElement} containerElement - The container element to render the schema in
     */
    async init(environmentId, containerElement) {
        if (!environmentId || !containerElement) {
            console.error('Environment ID or container element is missing');
            return;
        }

        // Create schema container
        const schemaContainer = document.createElement('div');
        schemaContainer.className = 'mt-3 bg-gray-50 dark:bg-gray-700 p-4 rounded-md border border-gray-200 dark:border-gray-600';
        schemaContainer.id = `schema-container-${environmentId}`;

        // Add loading indicator
        schemaContainer.innerHTML = `
            <div class="flex items-center justify-center p-4">
                <svg class="animate-spin h-5 w-5 text-indigo-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600 dark:text-gray-300">Loading environment schema...</span>
            </div>
        `;

        // Append to container
        containerElement.appendChild(schemaContainer);

        // Fetch and render schema
        await this.fetchAndRenderSchema(environmentId, schemaContainer);
    },

    /**
     * Fetch schema data from API and render it
     * @param {string} environmentId - The ID of the environment
     * @param {HTMLElement} container - The container element to render the schema in
     */
    async fetchAndRenderSchema(environmentId, container) {
        try {
            // Fetch schema data
            const response = await fetch(`/api/rl-environments/${environmentId}/schema`);

            if (!response.ok) {
                throw new Error(`Failed to fetch environment schema: ${response.statusText}`);
            }

            const schemaData = await response.json();

            // Render schema
            this.renderSchema(schemaData, container);

        } catch (error) {
            console.warn('Failed to load schema:', error);

            // Show error message
            container.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300">
                        Doesn't exist
                    </p>
                </div>
            `;
        }
    },

    /**
     * Render schema data in the container
     * @param {Object} schemaData - The schema data from the API
     * @param {HTMLElement} container - The container element to render the schema in
     */
    renderSchema(schemaData, container) {
        // Check if we have any schema data to display
        if (!schemaData || !schemaData.schema) {
            // Show empty state
            container.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300">
                        Schema information not available
                    </p>
                </div>
            `;
            return;
        }

        // If there's an error but we still have some schema info, show a warning
        const hasError = schemaData.schema.error || schemaData.schema.error_creating_instance;
        const hasSchemaInfo = Object.keys(schemaData.schema).length > 0;

        // Build HTML for schema
        let html = `<div class="space-y-3">`;

        // We're no longer showing warnings for incomplete schema information
        // This is intentionally removed as we've simplified the environment code

        // Environment Name and Description
        if (schemaData.schema.name) {
            html += `
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                        <svg class="w-4 h-4 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>${schemaData.schema.name || 'Environment'}</span>
                    </h4>
                    ${schemaData.schema.description ?
                        `<p class="text-xs text-gray-600 dark:text-gray-400 mt-1">${schemaData.schema.description}</p>` : ''}
                </div>
            `;
        }

        // Observation and Action Spaces
        html += `<div class="grid grid-cols-1 md:grid-cols-2 gap-3">`;

        if (schemaData.schema.observation_space) {
            html += `
                <div class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                    <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Observation Space:</h5>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 font-mono">${schemaData.schema.observation_space}</p>
                </div>
            `;
        }

        if (schemaData.schema.action_space) {
            html += `
                <div class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                    <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Action Space:</h5>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 font-mono">${schemaData.schema.action_space}</p>
                </div>
            `;
        }

        html += `</div>`;

        // Reward Mechanism
        if (schemaData.schema.reward_mechanism) {
            html += `
                <div class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                    <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Reward Mechanism:</h5>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">${schemaData.schema.reward_mechanism}</p>
                </div>
            `;
        }

        // Training Data Sample
        if (schemaData.schema.training_data_sample) {
            html += `
                <div class="bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-600">
                    <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Training Data Sample:</h5>
                    <pre class="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-auto max-h-32">${JSON.stringify(schemaData.schema.training_data_sample, null, 2)}</pre>
                </div>
            `;
        }

        html += `</div>`;

        // Update container
        container.innerHTML = html;
    }
};

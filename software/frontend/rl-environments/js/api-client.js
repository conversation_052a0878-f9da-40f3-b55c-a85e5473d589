/**
 * API client for RL Environments
 */
const RLEnvironmentsAPI = {
    /**
     * Get all environments
     * @returns {Promise<Array>} List of environments
     */
    async getEnvironments() {
        try {
            const response = await fetch('/api/rl-environments');
            if (!response.ok) {
                throw new Error(`Failed to fetch environments: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching environments:', error);
            throw error;
        }
    },

    /**
     * Get available environments from filesystem
     * @returns {Promise<Array>} List of available environments
     */
    async getAvailableEnvironments() {
        try {
            const response = await fetch('/api/rl-environments/available-environments');
            if (!response.ok) {
                throw new Error(`Failed to fetch available environments: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching available environments:', error);
            throw error;
        }
    },

    /**
     * Get a specific environment by ID
     * @param {string} id Environment ID
     * @returns {Promise<Object>} Environment details
     */
    async getEnvironment(id) {
        try {
            const response = await fetch(`/api/rl-environments/${id}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch environment: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching environment ${id}:`, error);
            throw error;
        }
    },

    /**
     * Create a new environment
     * @param {Object} environment Environment data
     * @returns {Promise<Object>} Created environment
     */
    async createEnvironment(environment) {
        try {
            const response = await fetch('/api/rl-environments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(environment)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to create environment: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error creating environment:', error);
            throw error;
        }
    },

    /**
     * Update an existing environment
     * @param {string} id Environment ID
     * @param {Object} environment Updated environment data
     * @returns {Promise<Object>} Updated environment
     */
    async updateEnvironment(id, environment) {
        try {
            const response = await fetch(`/api/rl-environments/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(environment)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to update environment: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error updating environment ${id}:`, error);
            throw error;
        }
    },

    /**
     * Delete an environment
     * @param {string} id Environment ID
     * @returns {Promise<Object>} Response message
     */
    async deleteEnvironment(id) {
        try {
            const response = await fetch(`/api/rl-environments/${id}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to delete environment: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error deleting environment ${id}:`, error);
            throw error;
        }
    },

    /**
     * Check if an environment is valid using stable_baselines3.common.env_checker
     * @param {string} id Environment ID
     * @returns {Promise<Object>} Check result with valid status and message
     */
    async checkEnvironment(id) {
        try {
            const response = await fetch(`/api/rl-environments/${id}/check`, {
                method: 'GET'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Failed to check environment: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error checking environment ${id}:`, error);
            throw error;
        }
    },

    /**
     * Get schema information for an environment
     * @param {string} id Environment ID
     * @returns {Promise<Object>} Environment schema information
     */
    async getEnvironmentSchema(id) {
        try {
            const response = await fetch(`/api/rl-environments/${id}/schema`);
            if (!response.ok) {
                throw new Error(`Failed to fetch environment schema: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching environment schema for ${id}:`, error);
            throw error;
        }
    }
};

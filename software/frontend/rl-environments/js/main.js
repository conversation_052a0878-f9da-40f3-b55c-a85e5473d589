/**
 * Main JavaScript for RL Environments page
 */
function rlEnvironmentsApp() {
    return {
        // Data
        environments: [],
        isLoading: true,
        showAddEnvironmentModal: false,
        showEditEnvironmentModal: false,
        showDeleteConfirmModal: false,
        currentEnvironment: {
            name: '',
            description: '',
            code: '',
            is_active: true
        },
        environmentToDelete: null,
        notification: {
            show: false,
            message: '',
            type: 'info', // 'success', 'error', 'info'
            timeout: null
        },

        // Default environment template
        defaultEnvironmentTemplate: `#!/usr/bin/env python3
# Custom environment for reinforcement learning

import gymnasium as gym
from gymnasium import spaces
import numpy as np
import json
from typing import Tuple, Optional, Dict, Any
from rich.console import Console
from rich.panel import Panel

class CustomEnvironment(gym.Env):
    """
    Custom Environment for Reinforcement Learning.

    This environment template can be modified to create your own RL environment.
    """
    metadata = {"render_modes": ["human"]}

    def __init__(self, config_path: str = "custom_env_config.json", train_ratio: float = 0.8, is_training: bool = True):
        """
        Initialize the environment with configuration from the specified JSON file.

        Args:
            config_path: Path to the JSON file containing configuration
            train_ratio: Ratio of data to use for training (0.0 to 1.0)
            is_training: Whether this environment is used for training or testing
        """
        super(CustomEnvironment, self).__init__()

        # Store parameters
        self.config_path = config_path
        self.train_ratio = train_ratio
        self.is_training = is_training
        self.console = Console()

        # Define observation space
        # Example: Box space with 4 dimensions
        self.observation_space = spaces.Box(
            low=-10.0,
            high=10.0,
            shape=(4,),
            dtype=np.float32
        )

        # Define action space
        # Example: Discrete action space with 3 possible actions
        self.action_space = spaces.Discrete(3)

        # Initialize state
        self.state = np.zeros(4, dtype=np.float32)
        self.steps = 0
        self.max_steps = 100

        # Try to load configuration
        try:
            with open(config_path, 'r') as f:
                self.config = json.load(f)

            # Use configuration values if available
            if "max_steps" in self.config:
                self.max_steps = self.config["max_steps"]

        except FileNotFoundError:
            self.console.print(f"[yellow]Warning: {config_path} not found. Using default configuration.[/]")
            self.config = {"max_steps": 100}

    def reset(self, seed: Optional[int] = None, options: Optional[dict] = None) -> Tuple[np.ndarray, dict]:
        """
        Reset the environment to an initial state.

        Args:
            seed: Random seed
            options: Additional options for reset

        Returns:
            observation: Initial observation
            info: Additional information
        """
        super().reset(seed=seed)

        # Reset state
        self.state = self.np_random.uniform(low=-1.0, high=1.0, size=4)
        self.steps = 0

        return self.state.copy(), {}

    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, dict]:
        """
        Take a step in the environment.

        Args:
            action: The action to take

        Returns:
            observation: The new state
            reward: The reward for the action
            terminated: Whether the episode is done
            truncated: Whether the episode was truncated
            info: Additional information
        """
        # Validate action
        assert self.action_space.contains(action), f"Invalid action: {action}"

        # Update state based on action
        if action == 0:
            self.state[0] += 0.1
        elif action == 1:
            self.state[1] += 0.1
        else:
            self.state[2] += 0.1

        # Add some noise
        self.state += self.np_random.normal(0, 0.05, size=4)

        # Clip state to be within bounds
        self.state = np.clip(self.state, -10.0, 10.0)

        # Calculate reward (example: negative distance to target)
        reward = -np.sum(np.abs(self.state))

        # Increment step counter
        self.steps += 1

        # Check if episode is done
        terminated = False
        truncated = self.steps >= self.max_steps

        # Return step information
        return self.state.copy(), reward, terminated, truncated, {}

    def render(self):
        """
        Render the environment.
        """
        self.console.print(Panel(
            f"State: {self.state}\\nSteps: {self.steps}/{self.max_steps}",
            title="Environment State",
            border_style="blue"
        ))

# Example usage
if __name__ == "__main__":
    from stable_baselines3.common.env_checker import check_env

    # Create and check the environment
    env = CustomEnvironment()

    # Validate the environment
    print("Checking environment...")
    check_env(env)
    print("Environment check passed!")

    # Test the environment
    obs, _ = env.reset()
    print(f"Initial observation: {obs}")

    for i in range(10):
        action = env.action_space.sample()  # Random action
        obs, reward, terminated, truncated, info = env.step(action)
        print(f"Step {i+1}: Action={action}, Reward={reward}")
        env.render()

        if terminated or truncated:
            print("Environment reset")
            obs, _ = env.reset()
`,

        // Modal for check environment results
        showCheckResultModal: false,
        checkResult: {
            valid: false,
            message: '',
            output: '',
            error: ''
        },

        // Initialization
        init() {
            this.refreshEnvironments();

            // Set up Monaco editor if available
            if (typeof monaco !== 'undefined') {
                this.setupMonacoEditor();
            }
        },

        // Modal for check environment results
        showCheckResultModal: false,

        // Check if an environment is valid
        async checkEnvironment(environment) {
            try {
                this.showNotification('Checking environment...', 'info');

                const result = await RLEnvironmentsAPI.checkEnvironment(environment._id);

                // Store check result
                this.checkResult = result;

                // Show notification
                if (result.valid) {
                    this.showNotification('Environment is valid!', 'success');
                } else {
                    this.showNotification('Environment check failed: ' + result.message, 'error');
                }

                // Create a more user-friendly message
                let message = '';

                if (result.valid) {
                    message = `✅ Environment "${environment.name}" is valid!\n\n`;
                    if (result.output && result.output.trim()) {
                        message += `Output:\n${result.output}`;
                    } else {
                        message += "The environment passed all checks.";
                    }
                } else {
                    message = `❌ Environment check failed: ${result.message}\n\n`;

                    if (result.error) {
                        message += `Error details:\n${result.error}\n\n`;
                    }

                    if (result.output && result.output.trim()) {
                        message += `Output:\n${result.output}`;
                    }

                    // Add some helpful tips
                    message += "\n\nTips for fixing common issues:";
                    message += "\n• Make sure your class inherits from gym.Env or gymnasium.Env";
                    message += "\n• Implement all required methods: reset(), step(), and optionally render()";
                    message += "\n• Define observation_space and action_space in __init__";
                    message += "\n• Make sure step() returns (observation, reward, terminated, truncated, info)";
                    message += "\n• Make sure reset() returns (observation, info)";
                }

                // Show the message in an alert for now
                alert(message);
            } catch (error) {
                console.error('Failed to check environment:', error);
                this.showNotification('Failed to check environment: ' + error.message, 'error');
            }
        },

        // Fetch environments from API
        async refreshEnvironments() {
            this.isLoading = true;
            try {
                this.environments = await RLEnvironmentsAPI.getEnvironments();
                this.showNotification('Environments loaded successfully', 'success');

                // Load schema and code components for each environment after a short delay to allow DOM to update
                setTimeout(() => {
                    this.environments.forEach(env => {
                        this.loadEnvironmentSchema(env);
                        this.loadEnvironmentCode(env);
                    });
                }, 100);
            } catch (error) {
                console.error('Failed to load environments:', error);
                this.showNotification('Failed to load environments: ' + error.message, 'error');
            } finally {
                this.isLoading = false;
            }
        },

        // Show notification
        showNotification(message, type = 'info') {
            // Clear any existing timeout
            if (this.notification.timeout) {
                clearTimeout(this.notification.timeout);
            }

            // Set notification
            this.notification.message = message;
            this.notification.type = type;
            this.notification.show = true;

            // Auto-hide after 3 seconds
            this.notification.timeout = setTimeout(() => {
                this.notification.show = false;
            }, 3000);
        },

        // Open add environment modal
        openAddEnvironmentModal() {
            // Create a new environment with default values from plan_rl_env.py
            this.currentEnvironment = {
                name: 'New Environment',
                description: 'Custom environment for reinforcement learning based on plan_rl_env.py',
                code: this.getPlanRLEnvTemplate(),
                is_active: true
            };
            this.showAddEnvironmentModal = true;
        },

        // Get template based on plan_rl_env.py
        getPlanRLEnvTemplate() {
            return `#!/usr/bin/env python3
# Custom environment for reinforcement learning based on plan_rl_env.py

import gymnasium as gym
from gymnasium import spaces
import numpy as np
import json
from typing import Tuple, Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box
from rich.text import Text

class CustomEnvironment(gym.Env):
    """
    Custom Environment for Reinforcement Learning Agent.

    This environment is based on the PlanEvaluationEnv from plan_rl_env.py.
    It can be customized for your specific reinforcement learning task.
    """
    # This is required to properly inherit from gym.Env
    metadata = {"render_modes": ["human"]}

    def __init__(self, training_data_path: str = "plan_training.json", train_ratio: float = 0.8, is_training: bool = True):
        """
        Initialize the environment with training data from the specified JSON file.

        Args:
            training_data_path: Path to the JSON file containing training data
            train_ratio: Ratio of data to use for training (0.0 to 1.0)
            is_training: Whether this environment is used for training or testing
        """
        super(CustomEnvironment, self).__init__()

        # Store the train_ratio as an attribute
        self.train_ratio = train_ratio
        self.console = Console()
        self.training_data_path = training_data_path
        self.is_training = is_training

        try:
            # Load training data
            with open(training_data_path, 'r') as f:
                all_data = json.load(f)

            # Split data into training and testing sets
            total_examples = len(all_data)
            train_size = int(total_examples * train_ratio)

            if is_training:
                if train_ratio < 1.0:
                    # Use the first train_ratio portion for training
                    self.training_data = all_data[:train_size]
                else:
                    # Use all data for training
                    self.training_data = all_data
                print(f"Using {len(self.training_data)} examples for training")
            else:
                # Use the remaining portion for testing
                if train_ratio < 1.0:
                    self.training_data = all_data[train_size:]
                    print(f"Using {len(self.training_data)} examples for testing")
                else:
                    # If train_ratio is 1.0, use the same data for testing
                    self.training_data = all_data
                    print(f"Using {len(self.training_data)} examples for testing (same as training)")
        except FileNotFoundError:
            # If the file doesn't exist, create a minimal dataset for initialization
            self.console.print(f"[yellow]Warning: {training_data_path} not found. Using minimal dataset for initialization.[/]")
            self.training_data = [
                {
                    "data": "Example data point",
                    "vector": [0.0] * 384,  # Placeholder vector
                    "quality_score": 50.0   # Neutral score
                }
            ]

        self.current_index = 0

        # Get vector dimension from the first example
        self.vector_dim = len(self.training_data[0]["vector"])

        # Define observation space: vector embedding of the data
        self.observation_space = spaces.Box(
            low=-1.0,
            high=1.0,
            shape=(self.vector_dim,),
            dtype=np.float32
        )

        # Define action space: quality score (continuous value between 0 and 100)
        self.action_space = spaces.Box(
            low=0.0,
            high=100.0,
            shape=(1,),
            dtype=np.float32
        )

        # Initialize current data point
        self.current_data = None

    def reset(self, seed: Optional[int] = None, options: Optional[dict] = None) -> Tuple[np.ndarray, dict]:
        """
        Reset environment to the next data point.

        Returns:
            observation: The vector embedding of the current data
            info: Additional information
        """
        super().reset(seed=seed)

        # Get next data point
        self.current_data = self.training_data[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.training_data)

        # Return the vector embedding as observation
        observation = np.array(self.current_data["vector"], dtype=np.float32)

        info = {
            "data": self.current_data.get("data", ""),
            "quality_score": self.current_data.get("quality_score", 50.0)
        }

        return observation, info

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, dict]:
        """
        The agent predicts a quality score (action), and we compare it to the expert evaluation.

        Args:
            action: The predicted quality score as a single-element array

        Returns:
            observation: The same vector embedding (doesn't change within an episode)
            reward: Negative absolute error between prediction and expert evaluation
            terminated: Whether the episode is done
            truncated: Whether the episode was truncated (always False in this env)
            info: Additional information
        """
        # Extract the predicted quality score from the action
        predicted_score = float(action[0])

        # Get the expert evaluation or ground truth score
        expert_score = self.current_data.get("quality_score", 50.0)

        # Calculate error and reward
        error = abs(predicted_score - expert_score)

        # Reward is negative error (higher is better)
        # Scale to make the reward more meaningful
        reward = -error / 10.0  # Divide by 10 to make rewards between -10 and 0

        # Episode is done after one prediction
        terminated = True
        truncated = False

        info = {
            "predicted_score": predicted_score,
            "expert_score": expert_score,
            "error": error,
            "data": self.current_data.get("data", "")
        }

        # Return the same observation (doesn't change within an episode)
        observation = np.array(self.current_data["vector"], dtype=np.float32)

        return observation, reward, terminated, truncated, info

    def render(self):
        """
        Render the environment with rich panels for better visualization.
        """
        if self.current_data is None:
            self.console.print(Panel("Environment not reset yet",
                               title="Custom Environment",
                               border_style="red"))
            return

        # Create a table for the data
        table = Table(box=box.ROUNDED)
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")

        # Add rows with data
        data_text = self.current_data.get("data", "No data available")
        table.add_row("Data", Text(data_text, style="bright_blue"))

        expert_score = self.current_data.get("quality_score", 50.0)
        score_style = "green" if expert_score >= 80 else "yellow" if expert_score >= 50 else "red"
        table.add_row("Expert Score", Text(f"{expert_score:.1f}/100", style=score_style))

        # Display the table in a panel
        self.console.print(Panel(table,
                           title="Environment Analysis",
                           border_style="blue"))


# Example usage
if __name__ == "__main__":
    from stable_baselines3.common.env_checker import check_env

    # Create and check the environment
    env = CustomEnvironment()

    # Validate the environment
    print("Checking environment...")
    check_env(env)
    print("Environment check passed!")

    # Test the environment
    obs, info = env.reset()
    print(f"Initial observation shape: {obs.shape}")

    for i in range(3):
        action = env.action_space.sample()  # Random action
        obs, reward, terminated, truncated, info = env.step(action)
        print(f"Step {i+1}: Action={action[0]:.1f}, Reward={reward:.2f}")
        env.render()

        if terminated or truncated:
            print("Environment reset")
            obs, info = env.reset()
`;
        },

        // Load schema for an environment
        loadEnvironmentSchema(environment) {
            const containerId = `schema-container-wrapper-${environment._id}`;
            const container = document.getElementById(containerId);

            if (!container) {
                console.error(`Container with ID ${containerId} not found`);
                return;
            }

            // Initialize schema component if not already loaded
            if (container.getAttribute('data-loaded') !== 'true') {
                EnvironmentSchemaComponent.init(environment._id, container);
                container.setAttribute('data-loaded', 'true');
            }
        },

        // Load code for an environment
        loadEnvironmentCode(environment) {
            const containerId = `code-container-wrapper-${environment._id}`;
            const container = document.getElementById(containerId);

            if (!container) {
                console.error(`Container with ID ${containerId} not found`);
                return;
            }

            // Initialize code component if not already loaded
            if (container.getAttribute('data-loaded') !== 'true') {
                EnvironmentCodeComponent.init(environment._id, container);
                container.setAttribute('data-loaded', 'true');
            }
        },

        // Edit environment
        editEnvironment(environment) {
            this.currentEnvironment = { ...environment };
            this.showEditEnvironmentModal = true;
        },

        // Close environment modal
        closeEnvironmentModal() {
            this.showAddEnvironmentModal = false;
            this.showEditEnvironmentModal = false;
        },

        // Save environment (create or update)
        async saveEnvironment() {
            try {
                if (this.showEditEnvironmentModal) {
                    // Update existing environment
                    const id = this.currentEnvironment._id;
                    const { _id, ...updateData } = this.currentEnvironment;
                    await RLEnvironmentsAPI.updateEnvironment(id, updateData);
                    this.showNotification('Environment updated successfully', 'success');
                } else {
                    // Create new environment
                    await RLEnvironmentsAPI.createEnvironment(this.currentEnvironment);
                    this.showNotification('Environment created successfully', 'success');
                }

                // Close modal and refresh environments
                this.closeEnvironmentModal();
                await this.refreshEnvironments();
            } catch (error) {
                console.error('Failed to save environment:', error);
                this.showNotification('Failed to save environment: ' + error.message, 'error');
            }
        },

        // Confirm delete environment
        confirmDeleteEnvironment(environment) {
            this.environmentToDelete = environment;
            this.showDeleteConfirmModal = true;
        },

        // Delete environment
        async deleteEnvironment() {
            if (!this.environmentToDelete) return;

            try {
                await RLEnvironmentsAPI.deleteEnvironment(this.environmentToDelete._id);
                this.showNotification('Environment deleted successfully', 'success');
                this.showDeleteConfirmModal = false;
                this.environmentToDelete = null;
                await this.refreshEnvironments();
            } catch (error) {
                console.error('Failed to delete environment:', error);
                this.showNotification('Failed to delete environment: ' + error.message, 'error');
            }
        },

        // Toggle environment active status
        async toggleEnvironmentStatus(environment) {
            try {
                const updatedStatus = !environment.is_active;
                await RLEnvironmentsAPI.updateEnvironment(environment._id, { is_active: updatedStatus });

                // Update local state
                environment.is_active = updatedStatus;

                this.showNotification(
                    `Environment ${updatedStatus ? 'activated' : 'deactivated'} successfully`,
                    'success'
                );
            } catch (error) {
                console.error('Failed to toggle environment status:', error);
                this.showNotification('Failed to update environment status: ' + error.message, 'error');
            }
        },

        // Set up Monaco editor if available
        setupMonacoEditor() {
            // This would be implemented if Monaco editor is available
            console.log('Monaco editor setup would go here');
        }
    };
}

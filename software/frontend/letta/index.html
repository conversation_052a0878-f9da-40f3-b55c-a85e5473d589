{% extends "base.html" %}

{% block title %}Letta{% endblock %}
{% block meta_description %}Letta - A powerful LLM engine for Vero{% endblock %}

{% block header %}Letta{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Server Control Panel -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center lg:col-span-1">
            <h2 class="text-2xl font-semibold text-gray-800 dark:text-white mb-6">Letta Server</h2>

            <p class="text-gray-600 dark:text-gray-300 mb-8">
                Start the Letta server with ADE flag
            </p>

            <div id="server-status" class="mb-8 py-3 px-4 rounded-md bg-gray-50 dark:bg-gray-700">
                <p class="text-gray-700 dark:text-gray-300">
                    <span id="status-indicator" class="inline-block h-3 w-3 rounded-full bg-red-500 mr-2"></span>
                    <span id="status-text">Server is not running</span>
                </p>
            </div>

            <div class="space-y-4">
                <button id="new-server-btn" class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-900">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                    </svg>
                    Start Letta Server
                </button>

                <button id="force-stop-btn" class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900 opacity-50 cursor-not-allowed" disabled>
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Force Stop (Port 8283)
                </button>
            </div>
        </div>

        <!-- Agents Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 lg:col-span-2">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 dark:text-white">Letta Agents</h2>
            </div>

            <div id="loading-indicator" class="flex justify-center items-center py-12">
                <svg class="animate-spin h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="ml-3 text-gray-600 dark:text-gray-300">Loading agents...</span>
            </div>

            <div id="error-message" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">Failed to load Letta agents. Please make sure the Letta server is running.</span>
            </div>

            <div id="no-agents-message" class="hidden text-center py-8 text-gray-600 dark:text-gray-300">
                <p>No Letta agents found. Start the Letta server and create agents by connecting directors to memory.</p>
            </div>

            <div id="agents-table-container" class="hidden overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Agent ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Agent Name
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Connected Director
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody id="agents-table-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Agent rows will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_scripts %}
<script src="/frontend/letta/js/letta.js"></script>
<script src="/frontend/letta/js/agents.js"></script>
{% endblock %}
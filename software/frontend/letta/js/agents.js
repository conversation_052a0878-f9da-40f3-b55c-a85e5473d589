document.addEventListener('DOMContentLoaded', function() {
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');
    const loadingIndicator = document.getElementById('loading-indicator');
    const errorMessage = document.getElementById('error-message');
    const noAgentsMessage = document.getElementById('no-agents-message');
    const agentsTableContainer = document.getElementById('agents-table-container');
    const agentsTableBody = document.getElementById('agents-table-body');

    // Check server status on page load
    checkServerStatus();

    // Check server status every 5 seconds
    setInterval(checkServerStatus, 5000);

    // Load agents on page load
    loadAgents();

    // Function to check server status
    async function checkServerStatus() {
        try {
            const response = await fetch('/api/letta/server-status');
            if (!response.ok) {
                throw new Error('Failed to check server status');
            }

            const result = await response.json();
            updateServerStatus(result.running);

            // If server status changed to running, reload agents
            if (result.running && statusIndicator.classList.contains('bg-red-500')) {
                loadAgents();
            }
        } catch (error) {
            console.error('Error checking server status:', error);
            updateServerStatus(false);
        }
    }

    // Function to load Letta agents
    async function loadAgents() {
        try {
            // Show loading indicator
            loadingIndicator.classList.remove('hidden');
            errorMessage.classList.add('hidden');
            noAgentsMessage.classList.add('hidden');
            agentsTableContainer.classList.add('hidden');

            const response = await fetch('/api/letta/agents');

            if (!response.ok) {
                throw new Error('Failed to load Letta agents');
            }

            const agents = await response.json();

            // Hide loading indicator
            loadingIndicator.classList.add('hidden');

            if (agents.length === 0) {
                // Show no agents message
                noAgentsMessage.classList.remove('hidden');
            } else {
                // Populate and show agents table
                populateAgentsTable(agents);
                agentsTableContainer.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Error loading Letta agents:', error);

            // Hide loading indicator and show error message
            loadingIndicator.classList.add('hidden');
            errorMessage.classList.remove('hidden');
        }
    }

    // Function to populate the agents table
    function populateAgentsTable(agents) {
        // Clear existing rows
        agentsTableBody.innerHTML = '';

        // Add a row for each agent
        agents.forEach(agent => {
            const row = document.createElement('tr');

            // Agent ID cell
            const idCell = document.createElement('td');
            idCell.className = 'px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-500 dark:text-gray-300';
            idCell.textContent = agent.id;
            row.appendChild(idCell);

            // Agent Name cell
            const nameCell = document.createElement('td');
            nameCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white';
            nameCell.textContent = agent.name || 'Unnamed Agent';
            row.appendChild(nameCell);

            // Connected Director cell
            const directorCell = document.createElement('td');
            directorCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white';

            if (agent.director_id && agent.director_name) {
                const directorLink = document.createElement('a');
                directorLink.href = `/research-agents/directors/${agent.director_id}`;
                directorLink.className = 'text-indigo-600 dark:text-indigo-400 hover:underline';
                directorLink.textContent = agent.director_name;
                directorCell.appendChild(directorLink);
            } else {
                directorCell.textContent = 'Not connected';
                directorCell.className += ' text-gray-500 dark:text-gray-400';
            }

            row.appendChild(directorCell);

            // Status cell
            const statusCell = document.createElement('td');
            statusCell.className = 'px-6 py-4 whitespace-nowrap text-sm';

            const statusBadge = document.createElement('span');
            if (agent.director_id) {
                statusBadge.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
                statusBadge.textContent = 'Connected';
            } else {
                statusBadge.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                statusBadge.textContent = 'Available';
            }

            statusCell.appendChild(statusBadge);
            row.appendChild(statusCell);

            // Action cell with Delete button
            const actionCell = document.createElement('td');
            actionCell.className = 'px-6 py-4 whitespace-nowrap text-sm';

            const deleteButton = document.createElement('button');
            deleteButton.className = 'text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 focus:outline-none';
            deleteButton.innerHTML = `
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            `;
            deleteButton.title = 'Delete Agent';

            // Add event listener for delete button
            deleteButton.addEventListener('click', () => {
                if (confirm(`Are you sure you want to delete agent ${agent.id}?`)) {
                    deleteAgent(agent.id);
                }
            });

            actionCell.appendChild(deleteButton);
            row.appendChild(actionCell);

            // Add row to table
            agentsTableBody.appendChild(row);
        });
    }

    // Function to delete a Letta agent
    async function deleteAgent(agentId) {
        try {
            const response = await fetch(`/api/letta/agents/${agentId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to delete Letta agent');
            }

            await response.json();

            // Reload agents after successful deletion
            loadAgents();

        } catch (error) {
            console.error('Error deleting Letta agent:', error);
            alert('Failed to delete Letta agent. Please try again.');
        }
    }

    // Helper function to update the server status display
    function updateServerStatus(isRunning = false) {
        if (isRunning) {
            // Update status indicator
            statusIndicator.classList.remove('bg-red-500');
            statusIndicator.classList.add('bg-green-500');
            statusText.textContent = 'Server is running';
        } else {
            // Update status indicator
            statusIndicator.classList.remove('bg-green-500');
            statusIndicator.classList.add('bg-red-500');
            statusText.textContent = 'Server is not running';

            // Hide agents table and show error message
            loadingIndicator.classList.add('hidden');
            agentsTableContainer.classList.add('hidden');
            noAgentsMessage.classList.add('hidden');
            errorMessage.classList.remove('hidden');
        }
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const startServerBtn = document.getElementById('new-server-btn');
    const forceStopBtn = document.getElementById('force-stop-btn');
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');

    // Check server status on page load
    checkServerStatus();

    // Event listeners
    startServerBtn.addEventListener('click', function() {
        runLettaServer();
    });

    forceStopBtn.addEventListener('click', function() {
        forceStopServer();
    });

    // Check server status every 5 seconds
    setInterval(checkServerStatus, 5000);

    // Function to check server status
    async function checkServerStatus() {
        try {
            const response = await fetch('/api/letta/server-status');
            if (!response.ok) {
                throw new Error('Failed to check server status');
            }

            const result = await response.json();
            updateServerStatus(result.running);
        } catch (error) {
            console.error('Error checking server status:', error);
            // Don't show alert to avoid annoying the user
            updateServerStatus(false);
        }
    }

    // Function to run the Letta server
    async function runLettaServer() {
        try {
            // Disable button during request
            startServerBtn.disabled = true;
            startServerBtn.classList.add('opacity-75', 'cursor-not-allowed');
            startServerBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Starting Server...
            `;

            const response = await fetch('/api/letta/run-server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to start Letta server');
            }

            await response.json();

            // Check server status after a short delay
            setTimeout(checkServerStatus, 1000);

        } catch (error) {
            console.error('Error starting Letta server:', error);
            alert('Failed to start Letta server. Please try again.');

            // Reset button
            startServerBtn.disabled = false;
            startServerBtn.classList.remove('opacity-75', 'cursor-not-allowed');
            startServerBtn.innerHTML = `
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                </svg>
                Start Letta Server
            `;
        }
    }

    // Function to force stop the server
    async function forceStopServer() {
        try {
            // Disable button during request
            forceStopBtn.disabled = true;
            forceStopBtn.classList.add('opacity-75');
            forceStopBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Stopping Server...
            `;

            const response = await fetch('/api/letta/force-stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to stop Letta server');
            }

            await response.json();

            // Check server status after a short delay
            setTimeout(checkServerStatus, 1000);

        } catch (error) {
            console.error('Error stopping Letta server:', error);
            alert('Failed to stop Letta server. Please try again.');

            // Reset button state
            updateServerStatus(true);
        }
    }

    // Helper function to update the server status display
    function updateServerStatus(isRunning = false) {
        if (isRunning) {
            // Update status indicator
            statusIndicator.classList.remove('bg-red-500');
            statusIndicator.classList.add('bg-green-500');
            statusText.textContent = 'Server is running';

            // Update start button
            startServerBtn.disabled = true;
            startServerBtn.classList.add('opacity-75', 'cursor-not-allowed', 'bg-green-600', 'hover:bg-green-700');
            startServerBtn.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
            startServerBtn.innerHTML = `
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                Server Running
            `;

            // Enable force stop button
            forceStopBtn.disabled = false;
            forceStopBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            // Update status indicator
            statusIndicator.classList.remove('bg-green-500');
            statusIndicator.classList.add('bg-red-500');
            statusText.textContent = 'Server is not running';

            // Update start button
            startServerBtn.disabled = false;
            startServerBtn.classList.remove('opacity-75', 'cursor-not-allowed', 'bg-green-600', 'hover:bg-green-700');
            startServerBtn.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
            startServerBtn.innerHTML = `
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                </svg>
                Start Letta Server
            `;

            // Disable force stop button
            forceStopBtn.disabled = true;
            forceStopBtn.classList.add('opacity-50', 'cursor-not-allowed');
            forceStopBtn.innerHTML = `
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
                Force Stop (Port 8283)
            `;
        }
    }
});
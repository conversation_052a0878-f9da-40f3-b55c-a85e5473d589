{% extends "base.html" %}

{% block title %}Simple Workflow{% endblock %}

{% block header %}Simple Workflow{% endblock %}

{% block content %}
<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <h2 class="text-2xl font-semibold mb-4 dark:text-white">Simple Workflow</h2>
    <p class="text-gray-600 dark:text-gray-300">This is the simple workflow page.</p>
    <form id="workflow-form" class="space-y-6">
        <div>
            <label for="ticker" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ticker</label>
            <input type="text" id="ticker" name="ticker" value="AAPL" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
        </div>
        <div>
            <label for="data_loader" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Data Loader</label>
            <select id="data_loader" name="data_loader" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></select>
        </div>
        <div class="relative">
            <label for="features" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Features</label>
            <div class="absolute top-0 right-0 mt-1 mr-1 flex space-x-2">
                <button type="button" id="select-all" class="px-2 py-1 bg-blue-500 text-white rounded-md text-xs">Select All</button>
                <button type="button" id="deselect-all" class="px-2 py-1 bg-red-500 text-white rounded-md text-xs">Deselect All</button>
            </div>
            <div id="features" class="mt-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></div>
        </div>
        <div>
            <label for="target" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target</label>
            <select id="target" name="target" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></select>
        </div>
        <div>
            <label for="forecast_horizon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Forecast Horizon</label>
            <input type="number" id="forecast_horizon" name="forecast_horizon" value="7" min="1" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
        </div>
        <div>
            <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Model</label>
            <select id="model" name="model" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></select>
        </div>
        <button type="submit" id="submit-button" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded disabled:bg-slate-400">Submit</button>
    </form>
    <div id="progress-bar" class="mt-4 hidden">
        <div class="w-full bg-gray-200 rounded-full">
            <div id="progress-bar-fill" class="bg-blue-500 text-xs font-medium text-blue-100 text-center p-0.5 leading-none rounded-full" style="width: 0%">0%</div>
        </div>
    </div>
    <div id="workflow-result" class="mt-4"></div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    async function fetchOptions(endpoint) {
        const response = await fetch(endpoint);
        return response.json();
    }

    async function populateOptions() {
        const dataLoaders = await fetchOptions('/api/options/data-loaders');
        const features = await fetchOptions('/api/options/features');
        const models = await fetchOptions('/api/options/models');

        const dataLoaderSelect = document.getElementById('data_loader');
        dataLoaders.data_loaders.forEach(loader => {
            const option = document.createElement('option');
            option.value = loader;
            option.textContent = loader;
            dataLoaderSelect.appendChild(option);
        });

        const featuresDiv = document.getElementById('features');
        features.features.forEach(feature => {
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'flex items-center space-x-2';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = 'features';
            checkbox.value = feature;
            checkbox.checked = true;
            checkbox.className = 'form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out';
            checkbox.addEventListener('change', updateColumns);

            const label = document.createElement('label');
            label.textContent = feature;
            label.className = 'text-sm font-medium text-gray-700 dark:text-gray-300';

            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(label);
            featuresDiv.appendChild(checkboxContainer);
        });

        const modelSelect = document.getElementById('model');
        models.models.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            modelSelect.appendChild(option);
        });

        dataLoaderSelect.addEventListener('change', updateColumns);

        // Add event listeners to the select all and deselect all buttons
        document.getElementById('select-all').addEventListener('click', selectAllFeatures);
        document.getElementById('deselect-all').addEventListener('click', deselectAllFeatures);

        // Trigger change event to load initial columns
        dataLoaderSelect.dispatchEvent(new Event('change'));
    }

    async function updateColumns() {
        const ticker = document.getElementById('ticker').value;
        const dataLoaderSelect = document.getElementById('data_loader');
        const selectedLoader = dataLoaderSelect.value;
        const selectedFeatures = Array.from(document.querySelectorAll('input[name="features"]:checked')).map(el => el.value);

        const response = await fetch('/api/options/columns', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ticker: ticker,
                data_loader: selectedLoader,
                features: selectedFeatures,
                model: '',
                forecast_horizon: 1,
                target: ''
            })
        });

        const result = await response.json();
        const targetSelect = document.getElementById('target');
        targetSelect.innerHTML = '';
        result.columns.forEach(column => {
            const option = document.createElement('option');
            option.value = column;
            option.textContent = column;
            targetSelect.appendChild(option);
        });
    }

    function selectAllFeatures() {
        const checkboxes = document.querySelectorAll('input[name="features"]');
        checkboxes.forEach(checkbox => checkbox.checked = true);
        updateColumns();
    }

    function deselectAllFeatures() {
        const checkboxes = document.querySelectorAll('input[name="features"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
        updateColumns();
    }

    document.addEventListener('DOMContentLoaded', populateOptions);

    document.getElementById('workflow-form').addEventListener('submit', async function(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const data = {
            ticker: formData.get('ticker'),
            data_loader: formData.get('data_loader'),
            features: Array.from(formData.getAll('features')),
            model: formData.get('model'),
            forecast_horizon: parseInt(formData.get('forecast_horizon')),
            target: formData.get('target')
        };

        // Disable the submit button and show the progress bar
        const submitButton = document.getElementById('submit-button');
        submitButton.disabled = true;
        const progressBar = document.getElementById('progress-bar');
        const progressBarFill = document.getElementById('progress-bar-fill');
        progressBar.classList.remove('hidden');
        progressBarFill.style.width = '0%';
        progressBarFill.textContent = '0%';

        try {
            const response = await fetch('/api/simple-workflow', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            const taskId = result.report_id;

            // Poll the backend for the task status
            const pollInterval = 2000; // 2 seconds
            const pollTaskStatus = async () => {
                const statusResponse = await fetch(`/api/simple-workflow/status/${taskId}`);
                const statusResult = await statusResponse.json();

                if (statusResult.status === 'completed') {
                    progressBarFill.style.width = '100%';
                    progressBarFill.textContent = '100%';
                    setTimeout(() => {
                        progressBar.classList.add('hidden');
                        document.getElementById('workflow-result').innerHTML = `
                            <div class="bg-green-100 text-green-800 p-4 rounded">
                                <p>Workflow completed successfully.</p>
                                <p>Report ID: ${statusResult.report_id}</p>
                            </div>
                        `;
                        submitButton.disabled = false;
                    }, 1000); // Fade out after 1 second
                } else if (statusResult.status === 'failed') {
                    progressBar.classList.add('hidden');
                    document.getElementById('workflow-result').innerHTML = `
                        <div class="bg-red-100 text-red-800 p-4 rounded">
                            <p>An error occurred: ${statusResult.error}</p>
                        </div>
                    `;
                    submitButton.disabled = false;
                } else {
                    // Update the progress bar
                    progressBarFill.style.width = `${parseInt(progressBarFill.style.width) + 10}%`;
                    progressBarFill.textContent = `${parseInt(progressBarFill.style.width)}%`;
                    setTimeout(pollTaskStatus, pollInterval);
                }
            };

            pollTaskStatus();
        } catch (error) {
            progressBar.classList.add('hidden');
            document.getElementById('workflow-result').innerHTML = `
                <div class="bg-red-100 text-red-800 p-4 rounded">
                    <p>An error occurred: ${error.message}</p>
                </div>
            `;
            submitButton.disabled = false;
        }
    });
</script>
{% endblock %}
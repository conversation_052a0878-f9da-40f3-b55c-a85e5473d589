// Global variable for Monaco editor
let monacoEditor = null;
let allLLMs = []; // Store all LLMs for filtering

// Initialize Monaco Editor
function initMonacoEditor() {
    require.config({ paths: { 'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.36.1/min/vs' }});

    require(['vs/editor/editor.main'], function() {
        // Create the editor
        monacoEditor = monaco.editor.create(document.getElementById('monaco-editor-container'), {
            value: '',
            language: 'python',
            theme: document.body.classList.contains('dark') ? 'vs-dark' : 'vs',
            automaticLayout: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 14,
            tabSize: 4,
            insertSpaces: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            glyphMargin: false,
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3
        });

        // Update the hidden textarea when the editor content changes
        monacoEditor.onDidChangeModelContent(() => {
            document.getElementById('connectionCode').value = monacoEditor.getValue();
        });

        // Set initial value if available
        const initialValue = document.getElementById('connectionCode').value;
        if (initialValue) {
            monacoEditor.setValue(initialValue);
        }

        // Listen for dark mode changes
        const darkModeObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class') {
                    const isDarkMode = document.body.classList.contains('dark');
                    monaco.editor.setTheme(isDarkMode ? 'vs-dark' : 'vs');
                }
            });
        });

        darkModeObserver.observe(document.body, { attributes: true });
    });
}

document.addEventListener('DOMContentLoaded', () => {
    loadLLMs();
    setupFormHandlers();
    setupModelTypeListener();
    setupTemplateButton();
    setupSearch();
    initMonacoEditor();
});

let currentLLMId = null;

function setupSearch() {
    const searchInput = document.getElementById('llmSearch');
    if (searchInput) {
        searchInput.addEventListener('input', filterLLMs);
    }
}

function filterLLMs() {
    const searchInput = document.getElementById('llmSearch');
    const searchTerm = searchInput.value.toLowerCase().trim();
    
    if (!searchTerm) {
        // If search is empty, show all LLMs
        displayLLMs(allLLMs);
        return;
    }
    
    // Filter LLMs by name, model_type, and connection_code
    const filteredLLMs = allLLMs.filter(llm => {
        return llm.name.toLowerCase().includes(searchTerm) || 
               llm.model_type.toLowerCase().includes(searchTerm) ||
               (llm.connection_code && llm.connection_code.toLowerCase().includes(searchTerm));
    });
    
    displayLLMs(filteredLLMs);
}

function displayLLMs(llms) {
    const tableBody = document.getElementById('llmTableBody');
    const emptyState = document.getElementById('emptyState');
    const tableContainer = tableBody.closest('.bg-white.dark\\:bg-gray-800');
    const modelCountEl = document.getElementById('modelCount');
    
    // Update model count
    if (modelCountEl) {
        modelCountEl.textContent = llms.length;
    }
    
    tableBody.innerHTML = '';
    
    if (llms.length === 0) {
        // Show empty state based on whether we're filtering or not
        const searchInput = document.getElementById('llmSearch');
        if (searchInput && searchInput.value.trim()) {
            // We're filtering - show "No results found"
            emptyState.innerHTML = `
                <svg class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No Results Found</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 text-center mt-1">Try adjusting your search criteria.</p>
                <button onclick="clearSearch()" class="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
                    Clear Search
                </button>
            `;
        } else {
            // No LLMs at all - show "No LLM Models"
            emptyState.innerHTML = `
                <svg class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No LLM Models</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 text-center mt-1">Get started by adding your first LLM model configuration.</p>
                <button onclick="openAddModal()" class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    Add LLM Model
                </button>
            `;
        }
        
        emptyState.classList.remove('hidden');
        tableContainer.classList.add('hidden');
    } else {
        // Show table and hide empty state
        emptyState.classList.add('hidden');
        tableContainer.classList.remove('hidden');
        
        // Sort LLMs: default first, then enabled, then by name
        llms.sort((a, b) => {
            if (a.is_default !== b.is_default) return b.is_default ? 1 : -1;
            if (a.is_enabled !== b.is_enabled) return b.is_enabled ? 1 : -1;
            return a.name.localeCompare(b.name);
        });
        
        llms.forEach(llm => {
            const row = createLLMRow(llm);
            tableBody.appendChild(row);
        });
    }
}

function clearSearch() {
    const searchInput = document.getElementById('llmSearch');
    if (searchInput) {
        searchInput.value = '';
        filterLLMs();
    }
}

async function loadLLMs() {
    try {
        const llms = await LLMApiHandler.getLLMs();
        // Store all LLMs globally for filtering
        allLLMs = llms;
        // Display LLMs
        displayLLMs(llms);
    } catch (error) {
        showError('Failed to load LLM models');
    }
}

function createLLMRow(llm) {
    const defaultBadge = llm.is_default ? 
        `<span title="Default Model" class="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-md bg-[#18181B] dark:bg-[#27272A] text-[#FAFAFA] dark:text-white text-xs font-medium shadow-[0_2px_10px_-3px_rgba(0,0,0,0.1)] border border-[#27272A]/20 dark:border-white/10 transition-all duration-200 hover:shadow-[0_2px_10px_-3px_rgba(0,0,0,0.2)] group">
            <svg class="w-3.5 h-3.5 mr-1 text-[#A1A1AA] group-hover:text-white transition-colors duration-200" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.49991 0.876892C7.77674 0.875275 8.01631 1.05577 8.10968 1.31934L9.45969 5.00684H13.4171C13.6976 5.00226 13.9548 5.18833 14.0435 5.45358C14.1322 5.71883 14.0331 6.01335 13.7976 6.18631L10.7977 8.42402L12.1407 12.1152C12.2342 12.3765 12.1392 12.6735 11.9058 12.8494C11.6724 13.0254 11.3542 13.0496 11.0968 12.9116L7.4999 10.6249L3.90393 12.9116C3.64654 13.0502 3.3278 13.0261 3.09374 12.85C2.85968 12.6739 2.76402 12.3763 2.85793 12.1147L4.19991 8.42402L1.2009 6.18631C0.966829 6.0139 0.867429 5.72159 0.955029 5.45731C1.04263 5.19303 1.29814 5.00731 1.57726 5.00684H5.53894L6.89012 1.31934C6.98513 1.05192 7.22994 0.877075 7.49991 0.876892ZM7.49991 2.1458L6.33027 5.31934C6.23874 5.57487 5.99319 5.7522 5.71885 5.75684H2.2496L4.83458 7.65536C5.04995 7.81863 5.13512 8.10274 5.04382 8.36223L3.88873 11.5461L6.97976 9.56772C7.20699 9.41205 7.50258 9.41205 7.72982 9.56772L10.8209 11.5461L9.66643 8.36223C9.57526 8.10297 9.66041 7.81924 9.87522 7.65629L12.4622 5.75684H8.99283C8.71955 5.75233 8.47519 5.5755 8.38407 5.32097L7.49991 2.1458Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
            </svg>
            <span class="font-medium opacity-90 group-hover:opacity-100 transition-opacity duration-200">default</span>
        </span>` : '';
        
    // Create model type badge with appropriate color
    const modelTypeBadge = llm.model_type === 'Embedding' 
        ? `<span class="inline-flex items-center rounded-md bg-blue-50 dark:bg-blue-900/30 px-2 py-1 text-xs font-medium text-blue-700 dark:text-blue-300 ring-1 ring-inset ring-blue-700/10 dark:ring-blue-700/30">Embedding</span>`
        : `<span class="inline-flex items-center rounded-md bg-purple-50 dark:bg-purple-900/30 px-2 py-1 text-xs font-medium text-purple-700 dark:text-purple-300 ring-1 ring-inset ring-purple-700/10 dark:ring-purple-700/30">Completion</span>`;
    
    // Create status badge
    const statusBadge = llm.is_enabled
        ? `<span class="inline-flex items-center rounded-full bg-green-50 dark:bg-green-900/20 px-2 py-1 text-xs font-medium text-green-700 dark:text-green-300 ring-1 ring-inset ring-green-600/20 dark:ring-green-500/30">
            <svg class="mr-1 h-1.5 w-1.5 fill-green-500" viewBox="0 0 6 6" aria-hidden="true"><circle cx="3" cy="3" r="3"></circle></svg>
            Enabled
          </span>`
        : `<span class="inline-flex items-center rounded-full bg-red-50 dark:bg-red-900/20 px-2 py-1 text-xs font-medium text-red-700 dark:text-red-300 ring-1 ring-inset ring-red-600/20 dark:ring-red-500/30">
            <svg class="mr-1 h-1.5 w-1.5 fill-red-500" viewBox="0 0 6 6" aria-hidden="true"><circle cx="3" cy="3" r="3"></circle></svg>
            Disabled
          </span>`;
    
    // Create connection code preview with better truncation
    const connectionPreview = llm.connection_code 
        ? `<div class="max-w-xs truncate font-mono text-xs bg-gray-50 dark:bg-gray-900/30 px-2 py-1 rounded border border-gray-200 dark:border-gray-700" title="${llm.connection_code.replace(/"/g, '&quot;')}">
            ${llm.connection_code.split('\n')[0]}
          </div>`
        : `<span class="text-gray-400 dark:text-gray-500 text-sm italic">No code</span>`;
    
    // Create action buttons with improved styling
    const actionButtons = `
        <div class="flex items-center justify-end space-x-2">
            <button onclick="editLLM('${llm._id}')" class="text-xs px-2 py-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200">
                Edit
            </button>
            <button onclick="toggleLLM('${llm._id}')" class="text-xs px-2 py-1 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 transition-colors duration-200">
                ${llm.is_enabled ? 'Disable' : 'Enable'}
            </button>
            <button onclick="deleteLLM('${llm._id}')" class="text-xs px-2 py-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200">
                Delete
            </button>
            ${llm.model_type === 'Embedding' ?
            `<button onclick="testEmbedding('${llm._id}')" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                Test
            </button>` :
            `<button onclick="testLLM('${llm._id}')" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                Test
            </button>`}
        </div>
    `;

    const row = document.createElement('tr');
    row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200';
    row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
            <div class="flex items-center">
                <span>${llm.name || '-'}</span>
                ${defaultBadge}
            </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
            ${modelTypeBadge}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
            ${connectionPreview}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
            ${statusBadge}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            ${actionButtons}
        </td>
    `;
    return row;
}

function setupFormHandlers() {
    const form = document.getElementById('llmForm');
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const modelType = document.getElementById('modelType').value;
        const connectionCodeValue = monacoEditor ? monacoEditor.getValue() : document.getElementById('connectionCode').value;

        // Collect data from the simplified form
        const formData = {
            name: document.getElementById('name').value,
            model_type: modelType,
            connection_code: connectionCodeValue,
            is_default: document.getElementById('isDefault').checked,
            is_enabled: true // Assuming we want to enable on create/update, or fetch existing status?
                            // Let's keep it simple and default to true. Toggle is separate.
        };
        
        // Debug logging
        console.log('Submitting LLM model with data:', JSON.stringify(formData, null, 2));

        try {
            let response;
            let url;
            let method;
            let body = JSON.stringify(formData);

            if (currentLLMId) {
                url = `/api/llm/llm/${currentLLMId}`;
                method = 'PUT';
                console.log(`Updating LLM with ID: ${currentLLMId}`);
            } else {
                url = '/api/llm/llm';
                method = 'POST';
                console.log('Creating new LLM model');
            }

            // Make raw fetch to better debug the issue
            const rawResponse = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: body
            });

            if (!rawResponse.ok) {
                let errorMessage = `Server returned ${rawResponse.status} ${rawResponse.statusText}`;

                try {
                    const errorData = await rawResponse.json();
                    console.error('Error response data:', errorData);
                    errorMessage = errorData.detail || errorMessage;
                } catch (jsonError) {
                    console.error('Failed to parse error response:', jsonError);
                    const textResponse = await rawResponse.text();
                    console.error('Raw error response:', textResponse);
                }

                throw new Error(errorMessage);
            }

            response = await rawResponse.json();
            console.log('API response:', response);
            closeModal();
            loadLLMs();
        } catch (error) {
            console.error('API error details:', error);
            showError('Failed to save LLM model: ' + (error.message || 'Unknown error'));
        }
    });
}

function setupModelTypeListener() {
    const modelTypeSelect = document.getElementById('modelType');
    modelTypeSelect.addEventListener('change', function() {
        // updateFormFields(this.value); // No longer needed as provider-specific fields are gone
        // Update connection code template when model type changes
        // This might still be useful if templates depend on type
        const templateButton = document.getElementById('templateButton');
        if (templateButton) {
            // Trigger template generation logic if it depends on modelType
            // For now, just log or do nothing if templates are generic
            console.log("Model type changed to:", this.value); 
        }
    });
}

async function editLLM(id) {
    try {
        const llm = await LLMApiHandler.getLLM(id);
        currentLLMId = id;

        document.getElementById('modalTitle').innerText = 'Edit LLM Configuration';
        document.getElementById('name').value = llm.name || '';
        // Set model type
        const modelTypeSelect = document.getElementById('modelType');
        modelTypeSelect.value = llm.model_type || 'Completion';

        // Set visible fields
        document.getElementById('connectionCode').value = llm.connection_code || '';
        document.getElementById('isDefault').checked = llm.is_default || false;
        // is_enabled is not directly editable in the form, handled by toggle

        // Update Monaco editor if it's initialized
        if (monacoEditor) {
            monacoEditor.setValue(llm.connection_code || '');
        }

        openModal();
    } catch (error) {
        showError('Failed to load LLM model details');
    }
}

async function deleteLLM(id) {
    if (!confirm('Are you sure you want to delete this LLM configuration?')) return;
    try {
        await LLMApiHandler.deleteLLM(id);
        loadLLMs();
    } catch (error) {
        showError('Failed to delete LLM configuration');
    }
}

async function toggleLLM(id) {
    try {
        await LLMApiHandler.toggleLLM(id);
        loadLLMs();
    } catch (error) {
        showError('Failed to toggle LLM configuration');
    }
}

function openAddModal() {
    currentLLMId = null;
    document.getElementById('modalTitle').innerText = 'Add LLM Configuration';
    document.getElementById('llmForm').reset();
    
    // Clear any existing values
    document.getElementById('connectionCode').value = '';
    
    // Update Monaco editor if it's initialized
    if (monacoEditor) {
        monacoEditor.setValue('');
    }
    
    openModal();
}

function openModal() {
    document.getElementById('llmModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('llmModal').classList.add('hidden');
}

function showError(message) {
    alert(message);
    console.error(message);
}

function setupTemplateButton() {
    const templateButton = document.getElementById('templateButton');
    if (!templateButton) return;

    templateButton.addEventListener('click', function() {
        const modelType = document.getElementById('modelType').value;

        let template = '';

        if (modelType === 'Embedding') {
            template = `# Create an Embedding model
from langchain_ollama import OllamaEmbeddings

# You can use any embedding model provider
model = OllamaEmbeddings(
    model="nomic-embed-text"
)

# Alternative: OpenAI Embeddings
# from langchain_openai import OpenAIEmbeddings
# model = OpenAIEmbeddings(
#     model="text-embedding-ada-002",
#     openai_api_key="YOUR_API_KEY"
# )`;
        } else {
            template = `# Create a Chat model
from langchain_openai import ChatOpenAI

# You can use any chat model provider
model = ChatOpenAI(
    model="gpt-3.5-turbo",
    openai_api_key="YOUR_API_KEY",
    temperature=0.7,
    max_tokens=4096
)

# Alternative: Groq
# from langchain_groq import ChatGroq
# model = ChatGroq(
#     model="llama3-70b-8192",
#     groq_api_key="YOUR_API_KEY",
#     temperature=0.7,
#     max_tokens=4096
# )

# Alternative: Ollama (local)
# from langchain_ollama import ChatOllama
# model = ChatOllama(
#     model="llama3",
#     temperature=0.7
# )`;
        }

        if (template && monacoEditor) {
            // Set the template in the Monaco editor
            monacoEditor.setValue(template);
            // Also update the hidden textarea
            document.getElementById('connectionCode').value = template;
        }
    });
}

async function testLLM(id) {
    try {
        const response = await fetch(`/api/llm/llm/${id}/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: "Hello world"
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to test LLM');
        }

        const result = await response.json();

        // Show terminal output
        const terminalOutput = document.getElementById('terminalOutput');
        const terminalText = document.getElementById('terminalText');
        terminalOutput.classList.remove('hidden');

        // Format and display the response
        const timestamp = new Date().toLocaleTimeString();
        const metadataString = result.metadata ? JSON.stringify(result.metadata, null, 2) : 'No metadata received.';

        const newOutput = `[${timestamp}] Testing LLM (ID: ${id})...
` +
            `Prompt: \"Hello world\"\n` +
            `Response: \"${result.response}\"\n` +
            `Metadata: ${metadataString}\n` +
            `${'-'.repeat(50)}\n\n`;

        // Append new output instead of replacing
        terminalText.innerHTML += newOutput;

        // Scroll to bottom of terminal
        terminalText.scrollTop = terminalText.scrollHeight;

        // Scroll terminal into view
        terminalOutput.scrollIntoView({ behavior: 'smooth', block: 'end' });
    } catch (error) {
        showError(error.message || 'Failed to test LLM model');
    }
}

async function testEmbedding(id) {
    try {
        const response = await fetch(`/api/llm/llm/${id}/embed-query`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: "Hello world"
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to test embedding model');
        }

        const result = await response.json();

        // Show terminal output
        const terminalOutput = document.getElementById('terminalOutput');
        const terminalText = document.getElementById('terminalText');
        terminalOutput.classList.remove('hidden');

        // Format and display the response
        const timestamp = new Date().toLocaleTimeString();
        const embeddingLength = result.embedding.length;
        const firstFew = result.embedding.slice(0, 5).map(n => n.toFixed(6));
        const metadataString = result.metadata ? JSON.stringify(result.metadata, null, 2) : 'N/A for embedding tests.';

        const newOutput = `[${timestamp}] Testing Embedding Model (ID: ${id})...
` +
            `Text: \"Hello world\"\n` +
            `Embedding dimension: ${embeddingLength}\n` +
            `First few values: [${firstFew.join(', ')}...]\n` +
            `${'-'.repeat(50)}\n\n`;

        // Append new output instead of replacing
        terminalText.innerHTML += newOutput;

        // Scroll to bottom of terminal
        terminalText.scrollTop = terminalText.scrollHeight;

        // Scroll terminal into view
        terminalOutput.scrollIntoView({ behavior: 'smooth', block: 'end' });
    } catch (error) {
        showError(error.message || 'Failed to test embedding model');
    }
}
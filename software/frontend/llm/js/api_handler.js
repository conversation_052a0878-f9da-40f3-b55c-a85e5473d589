class LLMApiHandler {
    static async createLLM(data) {
        try {
            console.log('Sending POST request to /api/llm/llm with data:', JSON.stringify(data, null, 2));
            const response = await fetch('/api/llm/llm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API error response:', errorData);
                throw new Error(errorData.detail || `Failed to create LLM model (${response.status})`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error creating LLM:', error);
            throw error;
        }
    }

    static async getLLMs() {
        try {
            const response = await fetch('/api/llm/llm');
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API error response:', errorData);
                throw new Error(errorData.detail || `Failed to fetch LLMs (${response.status})`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching LLMs:', error);
            throw error;
        }
    }

    static async getLLM(id) {
        try {
            const response = await fetch(`/api/llm/llm/${id}`);
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API error response:', errorData);
                throw new Error(errorData.detail || `Failed to fetch LLM (${response.status})`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching LLM:', error);
            throw error;
        }
    }

    static async updateLLM(id, data) {
        try {
            console.log(`Sending PUT request to /api/llm/llm/${id} with data:`, JSON.stringify(data, null, 2));
            const response = await fetch(`/api/llm/llm/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API error response:', errorData);
                throw new Error(errorData.detail || `Failed to update LLM model (${response.status})`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error updating LLM:', error);
            throw error;
        }
    }

    static async deleteLLM(id) {
        try {
            const response = await fetch(`/api/llm/llm/${id}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API error response:', errorData);
                throw new Error(errorData.detail || `Failed to delete LLM model (${response.status})`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Error deleting LLM:', error);
            throw error;
        }
    }

    static async toggleLLM(id) {
        try {
            const response = await fetch(`/api/llm/llm/${id}/toggle`, {
                method: 'PATCH'
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API error response:', errorData);
                throw new Error(errorData.detail || `Failed to toggle LLM model status (${response.status})`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Error toggling LLM:', error);
            throw error;
        }
    }
}
{% extends "base.html" %}

{% block title %}LLM Models{% endblock %}

{% block header %}LLM Models{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header with Add and Search -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div class="flex items-center">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Models</h2>
            <div id="modelCount" class="ml-2 inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10 dark:bg-gray-800 dark:text-gray-400 dark:ring-gray-600/20">0</div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <div class="relative rounded-md shadow-sm w-full sm:w-64">
                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <input type="text" id="llmSearch" class="block w-full rounded-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-gray-800 dark:text-white dark:ring-gray-700 dark:focus:ring-indigo-500" placeholder="Search models...">
            </div>
            
            <button onclick="openAddModal()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                Add LLM Model
            </button>
        </div>
    </div>

    <!-- LLM Models Card Layout -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-900/50">
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">Connection</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="llmTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- Table rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Empty State - Will show if no models -->
    <div id="emptyState" class="hidden flex flex-col items-center justify-center p-8 bg-white dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
        <svg class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">No LLM Models</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 text-center mt-1">Get started by adding your first LLM model configuration.</p>
        <button onclick="openAddModal()" class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Add LLM Model
        </button>
    </div>

    <!-- Terminal Output -->
    <div id="terminalOutput" class="hidden mt-6">
        <div class="bg-black rounded-lg p-4 font-mono text-sm">
            <div class="flex items-center mb-2 space-x-2">
                <div class="w-3 h-3 rounded-full bg-red-500"></div>
                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                <span class="text-gray-400 ml-2">Terminal Output</span>
            </div>
            <div id="terminalText" class="text-green-400 whitespace-pre-wrap"></div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
<div id="llmModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4" id="modalTitle">Add LLM Configuration</h3>
            <form id="llmForm" class="space-y-4">
                <input type="hidden" id="llmId">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Configuration Name</label>
                    <input type="text" id="name" name="name" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label for="modelType" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Model Type</label>
                    <select id="modelType" name="modelType" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600">
                        <option value="Completion">Completion</option>
                        <option value="Embedding">Embedding</option>
                    </select>
                </div>
                <div id="embeddingSettings" class="hidden">
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/30 rounded-md mb-4">
                        <p class="text-sm text-blue-800 dark:text-blue-200">
                            Embedding models are used to convert text into vector representations.
                        </p>
                    </div>
                </div>
                <div>
                    <label for="connectionCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Connection Code</label>
                    <div class="mt-1 relative">
                        <!-- Hidden textarea for form submission -->
                        <textarea id="connectionCode" name="connectionCode" style="display: none;"></textarea>
                        <!-- Monaco editor container -->
                        <div id="monaco-editor-container" style="height: 300px; border: 1px solid #d1d5db; border-radius: 0.375rem;"></div>
                        <div class="absolute top-2 right-2">
                            <button type="button" id="templateButton" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span>Insert Template</span>
                            </button>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Python code to create the Langchain model. Must define a variable named 'model'. Use the 'config' dictionary for parameters.</p>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="isDefault" name="isDefault" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                    <label for="isDefault" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Set as default model for this type</label>
                </div>
                <div class="mt-5 sm:mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Monaco Editor -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.36.1/min/vs/loader.min.js"></script>

<script src="/frontend/llm/js/api_handler.js"></script>
<script src="/frontend/llm/js/ui_handler.js"></script>
{% endblock %}
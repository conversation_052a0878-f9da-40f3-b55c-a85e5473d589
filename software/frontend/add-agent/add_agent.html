{% extends "base.html" %}

{% block title %}Add Research Agent{% endblock %}

{% block head %}
{{ super() }}
{% endblock %}

{% block header %}Add Research Agent{% endblock %}

{% block content %}
<div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
    <div class="mb-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Create a New Research Director Agent</h2>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Research agents analyze market opportunities and provide detailed reports on sectors and companies.
        </p>
    </div>

    <form id="agent-form" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Agent Name</label>
                <input type="text" id="name" name="name" required
                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100"
                    value="Dr. Michael Burry">
                <div id="name-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
            </div>

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Professional Title</label>
                <input type="text" id="title" name="title" required
                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100"
                    value="Hedge Fund Manager & Market Analyst">
                <div id="title-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
            </div>

            <!-- Experience Years -->
            <div>
                <label for="experience_years" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Years of Experience</label>
                <input type="number" id="experience_years" name="experience_years" required min="1" max="50"
                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100"
                    value="25">
                <div id="experience_years-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
            </div>

            <!-- Expertise -->
            <div>
                <label for="expertise" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Areas of Expertise</label>
                <input type="text" id="expertise" name="expertise" required
                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100"
                    value="Value Investing, Market Bubbles, Contrarian Strategy, Healthcare, Real Estate">
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Separate multiple areas with commas</div>
                <div id="expertise-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
            </div>
        </div>

        <!-- Analysis Style -->
        <div>
            <label for="analysis_style" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Analysis Style</label>
            <textarea id="analysis_style" name="analysis_style" rows="3" required
                class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100">Deeply analytical and contrarian approach. Examines market fundamentals with intense focus on data and financial statements. Skeptical of conventional wisdom and capable of identifying market inefficiencies that others miss. Particularly adept at recognizing unsustainable market conditions and asset bubbles.</textarea>
            <div id="analysis_style-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
        </div>

        <!-- Background -->
        <div>
            <label for="background" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Professional Background</label>
            <textarea id="background" name="background" rows="3" required
                class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100">MD from Vanderbilt University School of Medicine. Founded Scion Capital hedge fund, which operated from 2000-2008 with exceptional returns. Famous for successfully predicting and profiting from the 2008 housing market collapse. Post-2008, manages personal investments with a focus on water, farmland, and gold.</textarea>
            <div id="background-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
        </div>

        <!-- Personality -->
        <div>
            <label for="personality" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Agent Personality</label>
            <textarea id="personality" name="personality" rows="3" required
                class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100">Intensely private and focused. Highly detail-oriented with exceptional pattern recognition abilities. Unafraid to take controversial positions against market consensus. Patient and willing to maintain positions for extended periods. Communicates in direct, data-driven manner with little concern for social niceties.</textarea>
            <div id="personality-error" class="mt-1 text-sm text-red-600 dark:text-red-400 hidden"></div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-between items-center">
            <button type="button" id="ai-generated-fields-btn"
                class="inline-flex items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                AI Generated Fields
            </button>
            <button type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                Create Agent
            </button>
        </div>
    </form>

    <!-- Status Message -->
    <div id="status-message" class="mt-4 p-4 rounded-md hidden"></div>

    <!-- Existing Directors Section -->
    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Existing Research Directors</h3>
            <div id="delete-status" class="text-sm hidden"></div>
        </div>

        <div id="directors-list" class="space-y-4">
            {% if directors %}
                {% for director in directors %}
                <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white">{{ director.name }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ director.title }}</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ director.total_reports }} reports
                                </span>
                            </div>
                            <button
                                type="button"
                                class="delete-director-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                                data-director-id="{{ director.id }}"
                                data-director-name="{{ director.name }}"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="text-xs text-gray-500 dark:text-gray-400">Expertise:</div>
                        <div class="flex flex-wrap gap-1 mt-1">
                            {% for expertise in director.expertise %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                {{ expertise }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <a href="/research-agents" class="text-sm text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300">
                            View details →
                        </a>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    No research directors found. Create your first one above!
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 mx-4">
            <div class="mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Confirm Deletion</h3>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Are you sure you want to delete the agent "<span id="delete-agent-name" class="font-medium"></span>"? This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-end space-x-3">
                <button id="cancel-delete" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors">
                    Cancel
                </button>
                <button id="confirm-delete" class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- AI Generated Fields Modal -->
    <div id="ai-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full p-6 mx-4">
            <div class="mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">AI Generated Agent Fields</h3>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Enter a business question or domain description, and AI will generate appropriate agent profile fields.
                </p>
            </div>
            <div class="mb-4">
                <label for="user-request" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business Question or Domain</label>
                <textarea id="user-request" rows="4"
                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-800 dark:text-gray-100"
                    placeholder="Example: I need an expert in cryptocurrency markets who can analyze Bitcoin price trends and provide insights on blockchain technology adoption."></textarea>
            </div>
            <div id="ai-status" class="mb-4 p-3 rounded-md hidden"></div>
            <div class="flex justify-end space-x-3">
                <button id="cancel-ai" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md transition-colors">
                    Cancel
                </button>
                <button id="generate-fields" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 rounded-md transition-colors">
                    <span>Generate Fields</span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/frontend/add-agent/js/agent_form_handler.js"></script>
<script src="/frontend/add-agent/js/ai_field_generator.js"></script>
{% endblock %}
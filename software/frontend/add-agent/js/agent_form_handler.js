/**
 * Agent Form Handler
 * Handles form submission for creating new research director agents
 * and displays existing agents
 */

document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('agent-form');
    const statusMessage = document.getElementById('status-message');
    const directorsList = document.getElementById('directors-list');
    const deleteModal = document.getElementById('delete-modal');
    const deleteAgentName = document.getElementById('delete-agent-name');
    const confirmDeleteBtn = document.getElementById('confirm-delete');
    const cancelDeleteBtn = document.getElementById('cancel-delete');
    const deleteStatus = document.getElementById('delete-status');

    let currentDirectorId = null;

    // Refresh the directors list when the page loads
    // This will ensure the list is up-to-date even if the server-side rendering missed some
    refreshDirectorsList();

    // Set up event delegation for delete buttons
    directorsList.addEventListener('click', (e) => {
        const deleteBtn = e.target.closest('.delete-director-btn');
        if (deleteBtn) {
            const directorId = deleteBtn.dataset.directorId;
            const directorName = deleteBtn.dataset.directorName;

            // Show the confirmation modal
            deleteAgentName.textContent = directorName;
            currentDirectorId = directorId;
            deleteModal.classList.remove('hidden');
        }
    });

    // Handle cancel button in modal
    cancelDeleteBtn.addEventListener('click', () => {
        deleteModal.classList.add('hidden');
        currentDirectorId = null;
    });

    // Handle clicking outside the modal to close it
    deleteModal.addEventListener('click', (e) => {
        if (e.target === deleteModal) {
            deleteModal.classList.add('hidden');
            currentDirectorId = null;
        }
    });

    // Handle confirm delete button
    confirmDeleteBtn.addEventListener('click', async () => {
        if (!currentDirectorId) return;

        try {
            // Disable the button and show loading state
            confirmDeleteBtn.disabled = true;
            confirmDeleteBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Deleting...';

            // Send delete request
            const response = await fetch(`/api/add-agent/${currentDirectorId}`, {
                method: 'DELETE'
            });

            // Hide the modal
            deleteModal.classList.add('hidden');

            if (response.ok) {
                // Show success message
                showDeleteStatus('Agent deleted successfully', 'success');

                // Refresh the directors list
                refreshDirectorsList();
            } else {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to delete agent');
            }
        } catch (error) {
            console.error('Error deleting director:', error);
            showDeleteStatus(`Error: ${error.message}`, 'error');
        } finally {
            // Reset button state
            confirmDeleteBtn.disabled = false;
            confirmDeleteBtn.textContent = 'Delete';
            currentDirectorId = null;
        }
    });

    // Function to show delete status message
    function showDeleteStatus(message, type) {
        deleteStatus.textContent = message;
        deleteStatus.classList.remove('hidden', 'text-green-600', 'text-red-600');

        if (type === 'success') {
            deleteStatus.classList.add('text-green-600', 'dark:text-green-400');
        } else {
            deleteStatus.classList.add('text-red-600', 'dark:text-red-400');
        }

        // Hide the message after 3 seconds
        setTimeout(() => {
            deleteStatus.classList.add('hidden');
        }, 3000);
    }

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Reset error messages
        document.querySelectorAll('[id$="-error"]').forEach(el => {
            el.classList.add('hidden');
            el.textContent = '';
        });

        // Collect form data
        const formData = {
            name: document.getElementById('name').value,
            title: document.getElementById('title').value,
            experience_years: parseInt(document.getElementById('experience_years').value),
            expertise: document.getElementById('expertise').value.split(',').map(item => item.trim()),
            analysis_style: document.getElementById('analysis_style').value,
            background: document.getElementById('background').value,
            personality: document.getElementById('personality').value
        };

        try {
            // Submit data
            const response = await fetch('/api/add-agent/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                const error = new Error(errorData.detail || 'Failed to create agent');

                // If it's a 400 error (like duplicate name), it might have field-specific errors
                if (response.status === 400) {
                    // Check if the error message mentions a specific field
                    const errorMsg = errorData.detail.toLowerCase();
                    if (errorMsg.includes('name')) {
                        error.fields = { name: errorData.detail };
                    }
                }

                throw error;
            }

            const result = await response.json();

            // Show success message
            statusMessage.classList.remove('hidden', 'bg-red-100', 'text-red-800');
            statusMessage.classList.add('bg-green-100', 'text-green-800');
            statusMessage.textContent = `Agent "${result.name}" created successfully! Redirecting to research agents page...`;

            // Reset form
            form.reset();

            // Refresh the directors list instead of redirecting
            setTimeout(() => {
                refreshDirectorsList();
                form.reset();
                statusMessage.classList.add('hidden');
            }, 2000);

        } catch (error) {
            // Show error message
            statusMessage.classList.remove('hidden', 'bg-green-100', 'text-green-800');
            statusMessage.classList.add('bg-red-100', 'text-red-800');
            statusMessage.textContent = error.message;

            // Show field errors if available
            if (error.fields) {
                Object.entries(error.fields).forEach(([field, message]) => {
                    const errorEl = document.getElementById(`${field}-error`);
                    if (errorEl) {
                        errorEl.textContent = message;
                        errorEl.classList.remove('hidden');
                    }
                });
            }
        }
    });

    /**
     * Fetches the latest list of directors and updates the UI
     */
    async function refreshDirectorsList() {
        try {
            const response = await fetch('/api/research-agents/directors');
            if (!response.ok) {
                throw new Error('Failed to fetch directors');
            }

            const data = await response.json();
            const directors = data.directors || [];

            // Sort directors by name
            directors.sort((a, b) => a.name.localeCompare(b.name));

            // Update the directors list
            if (directors.length === 0) {
                directorsList.innerHTML = `
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        No research directors found. Create your first one above!
                    </div>
                `;
                return;
            }

            // Generate HTML for each director
            const directorsHtml = directors.map(director => `
                <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white">${director.name}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">${director.title}</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    ${director.total_reports} reports
                                </span>
                            </div>
                            <button
                                type="button"
                                class="delete-director-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                                data-director-id="${director.id}"
                                data-director-name="${director.name}"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="text-xs text-gray-500 dark:text-gray-400">Expertise:</div>
                        <div class="flex flex-wrap gap-1 mt-1">
                            ${director.expertise.map(exp => `
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                    ${exp}
                                </span>
                            `).join('')}
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <a href="/research-agents" class="text-sm text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300">
                            View details →
                        </a>
                    </div>
                </div>
            `).join('');

            directorsList.innerHTML = directorsHtml;
        } catch (error) {
            console.error('Error refreshing directors list:', error);
        }
    }
});

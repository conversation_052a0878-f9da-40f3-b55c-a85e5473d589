/**
 * AI Field Generator
 * Handles the AI-generated fields functionality for the agent form
 */

document.addEventListener('DOMContentLoaded', () => {
    // Modal elements
    const aiModal = document.getElementById('ai-modal');
    const aiGeneratedFieldsBtn = document.getElementById('ai-generated-fields-btn');
    const cancelAiBtn = document.getElementById('cancel-ai');
    const generateFieldsBtn = document.getElementById('generate-fields');
    const userRequestTextarea = document.getElementById('user-request');
    const aiStatus = document.getElementById('ai-status');

    // Form fields
    const nameInput = document.getElementById('name');
    const titleInput = document.getElementById('title');
    const experienceYearsInput = document.getElementById('experience_years');
    const expertiseInput = document.getElementById('expertise');
    const analysisStyleInput = document.getElementById('analysis_style');
    const backgroundInput = document.getElementById('background');
    const personalityInput = document.getElementById('personality');

    // Show the AI modal when the button is clicked
    aiGeneratedFieldsBtn.addEventListener('click', () => {
        aiModal.classList.remove('hidden');
        userRequestTextarea.focus();

        // Reset the status message
        aiStatus.classList.add('hidden');
        aiStatus.textContent = '';
        aiStatus.className = 'mb-4 p-3 rounded-md hidden';
    });

    // Hide the modal when the cancel button is clicked
    cancelAiBtn.addEventListener('click', () => {
        aiModal.classList.add('hidden');
    });

    // Hide the modal when clicking outside of it
    aiModal.addEventListener('click', (e) => {
        if (e.target === aiModal) {
            aiModal.classList.add('hidden');
        }
    });

    // Handle the generate fields button click
    generateFieldsBtn.addEventListener('click', async () => {
        const userRequest = userRequestTextarea.value.trim();

        if (!userRequest) {
            showAiStatus('Please enter a business question or domain description.', 'error');
            return;
        }

        // Show loading state
        showAiStatus('Generating agent profile...', 'loading');
        generateFieldsBtn.disabled = true;
        generateFieldsBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Generating...';

        try {
            // Send the request to the API
            const response = await fetch('/api/add-agent/generate-profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ business_question: userRequest })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to generate agent profile');
            }

            const result = await response.json();

            // Debug the result
            console.log('API response:', result);

            // Fill the form fields with the generated profile
            // Handle different response formats
            if (typeof result === 'object' && result !== null) {
                nameInput.value = result.name || '';
                titleInput.value = result.title || '';
                experienceYearsInput.value = result.experience_years || '';
                expertiseInput.value = Array.isArray(result.expertise) ? result.expertise.join(', ') : '';
                analysisStyleInput.value = result.analysis_style || '';
                backgroundInput.value = result.background || '';
                personalityInput.value = result.personality || '';
            } else {
                showAiStatus('Received unexpected response format from server', 'error');
                console.error('Unexpected response format:', result);
                return;
            }

            // Show success message
            showAiStatus('Agent profile generated successfully!', 'success');

            // Close the modal after a delay
            setTimeout(() => {
                aiModal.classList.add('hidden');
            }, 1500);

        } catch (error) {
            console.error('Error generating agent profile:', error);
            showAiStatus(`Error: ${error.message}`, 'error');
        } finally {
            // Reset button state
            generateFieldsBtn.disabled = false;
            generateFieldsBtn.innerHTML = '<span>Generate Fields</span>';
        }
    });

    // Helper function to show status messages
    function showAiStatus(message, type) {
        aiStatus.textContent = message;
        aiStatus.classList.remove('hidden', 'bg-green-100', 'text-green-800', 'bg-red-100', 'text-red-800', 'bg-blue-100', 'text-blue-800');

        if (type === 'success') {
            aiStatus.classList.add('bg-green-100', 'text-green-800');
        } else if (type === 'error') {
            aiStatus.classList.add('bg-red-100', 'text-red-800');
        } else if (type === 'loading') {
            aiStatus.classList.add('bg-blue-100', 'text-blue-800');
        }
    }
});

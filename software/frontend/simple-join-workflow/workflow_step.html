{% extends "base.html" %}

{% block title %}Workflow Step{% endblock %}

{% block header %}Workflow Step{% endblock %}

{% block content %}
<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <h2 class="text-2xl font-semibold mb-4 dark:text-white">Workflow Configuration</h2>
    <p class="text-gray-600 dark:text-gray-300">Configure your machine learning workflow after data join</p>
    
    <form id="workflow-form" class="space-y-6">
        <div class="relative">
            <label for="features" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Features</label>
            <div class="absolute top-0 right-0 mt-1 mr-1 flex space-x-2">
                <button type="button" id="select-all" class="px-2 py-1 bg-blue-500 text-white rounded-md text-xs">Select All</button>
                <button type="button" id="deselect-all" class="px-2 py-1 bg-red-500 text-white rounded-md text-xs">Deselect All</button>
            </div>
            <div id="features" class="mt-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></div>
        </div>
        <div>
            <label for="target" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Column</label>
            <select id="target" name="target" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></select>
        </div>
        <div>
            <label for="forecast_horizon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Forecast Horizon</label>
            <input type="number" id="forecast_horizon" name="forecast_horizon" value="7" min="1" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
        </div>
        <div>
            <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Model</label>
            <select id="model" name="model" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></select>
        </div>
        <button type="submit" id="submit-button" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded disabled:bg-slate-400">Submit Workflow</button>
    </form>
    
    <div id="progress-bar" class="mt-4 hidden">
        <div class="w-full bg-gray-200 rounded-full">
            <div id="progress-bar-fill" class="bg-blue-500 text-xs font-medium text-blue-100 text-center p-0.5 leading-none rounded-full" style="width: 0%">0%</div>
        </div>
    </div>
    
    <div id="workflow-result" class="mt-4"></div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/workflow_step.js"></script>
{% endblock %}

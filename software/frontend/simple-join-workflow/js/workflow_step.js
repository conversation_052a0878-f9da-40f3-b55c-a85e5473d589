class WorkflowStep {
  constructor() {
    this.previewData = null;
    this.selectedFeatures = new Set();
    this.selectedModel = null;
    this.selectedTarget = null;
    this.loadModels();
    this.loadFeatures();
  }

  async loadFeatures() {
    try {
      const response = await fetch('/api/simple-join-workflow/workflow-step/options');
      const data = await response.json();
      
      if (data.features && this.featuresContainer) {
        this.featuresContainer.innerHTML = '';
        data.features.forEach(feature => {
          const div = document.createElement('div');
          div.className = 'flex items-center';
          div.innerHTML = `
            <input type="checkbox" id="feature-${feature}" name="features" value="${feature}" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded">
            <label for="feature-${feature}" class="ml-2 block text-sm text-gray-900 dark:text-white">${feature}</label>
          `;
          this.featuresContainer?.appendChild(div);
        });
      }
    } catch (error) {
      console.error('Error loading features:', error);
    }
  }

  async loadModels() {
    try {
      const response = await fetch('/api/simple-join-workflow/workflow-step/options');
      const data = await response.json();
      
      const modelSelect = document.getElementById('model');
      if (modelSelect && data.models) {
        modelSelect.innerHTML = '<option value="">Select model</option>';
        data.models.forEach(model => {
          modelSelect.innerHTML += `<option value="${model}">${model}</option>`;
        });
      }
    } catch (error) {
      console.error('Error loading models:', error);
    }
  }

  initialize(previewData) {
    this.previewData = previewData;
    
    // Get form elements
    this.form = document.getElementById('workflow-form');
    this.targetSelect = document.getElementById('target');
    this.featuresContainer = document.getElementById('features-container');
    this.modelSelect = document.getElementById('model');
    
    if (!this.form || !this.targetSelect || !this.featuresContainer || !this.modelSelect) {
      return;
    }
    
    // Setup event listeners
    this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    document.getElementById('select-all')?.addEventListener('click', () => this.toggleAllFeatures(true));
    document.getElementById('deselect-all')?.addEventListener('click', () => this.toggleAllFeatures(false));
    
    // Initialize form
    this.populateTargetColumns();
    this.loadFeatures(); // Reload features when initializing
  }

  populateTargetColumns() {
    if (!this.previewData || !this.previewData.length) {
      return;
    }

    const columns = Object.keys(this.previewData[0]);
    
    // Populate target select with numeric columns
    this.targetSelect.innerHTML = '<option value="">Select target column</option>';
    columns.forEach(column => {
      const value = this.previewData[0][column];
      if (typeof value === 'number') {
        this.targetSelect.innerHTML += `<option value="${column}">${column}</option>`;
      }
    });
  }

  toggleAllFeatures(selected) {
    const checkboxes = document.querySelectorAll('input[name="features"]');
    checkboxes.forEach(checkbox => {
      checkbox.checked = selected;
    });
  }

  async handleSubmit(event) {
    event.preventDefault();
    
    try {
      const selectedFeatures = Array.from(document.querySelectorAll('input[name="features"]:checked')).map(cb => cb.value);
      const selectedModel = this.modelSelect.value;
      const selectedTarget = this.targetSelect.value;
      const forecastHorizon = parseInt(document.getElementById('forecast_horizon').value) || 5; // Default to 5 if not set
      
      if (!selectedFeatures.length) {
        throw new Error('Please select at least one feature');
      }
      
      if (!selectedModel) {
        throw new Error('Please select a model');
      }
      
      if (!selectedTarget) {
        throw new Error('Please select a target column');
      }

      if (isNaN(forecastHorizon) || forecastHorizon < 1) {
        throw new Error('Please enter a valid forecast horizon (minimum 1)');
      }

      // Get join information from the data preview
      const dataPreview = window.dataJoinWorkflow;
      
      const workflowData = {
        features: selectedFeatures,
        model: selectedModel,
        target: selectedTarget,
        preview: this.previewData,
        join_type: dataPreview.getJoinType(),
        left_loader: dataPreview.getLeftLoader(),
        right_loader: dataPreview.getRightLoader(),
        left_ticker: dataPreview.getLeftTicker(),
        right_ticker: dataPreview.getRightTicker(),
        forecast_horizon: forecastHorizon
      };
      
      
      const response = await fetch('/api/simple-join-workflow/workflow-step/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to run workflow');
      }
      
      const result = await response.json();

      // Show success message
      const successDiv = document.getElementById('workflow-success');
      successDiv.textContent = 'Workflow started successfully! Task ID: ' + result.task_id;
      successDiv.classList.remove('hidden');

      // Hide error message if shown
      document.getElementById('workflow-error').classList.add('hidden');
      
    } catch (error) {
      console.error('Error submitting workflow:', error);
      
      // Show error message
      const errorDiv = document.getElementById('workflow-error');
      errorDiv.textContent = error.message || 'Failed to submit workflow';
      errorDiv.classList.remove('hidden');

      // Hide success message if shown
      document.getElementById('workflow-success').classList.add('hidden');
    }

    return false;
  }
}

// Create a single instance of WorkflowStep
window.workflowStep = new WorkflowStep();

class DataJoinWorkflow {
  constructor() {
    this.table = null;
    this.currentData = null;
    this.leftLoader = null;
    this.rightLoader = null;
    this.leftTicker = 'AAPL';
    this.rightTicker = 'AAPL';
    this.leftColumn = null;
    this.rightColumn = null;
    this.previewData = null;
    window.dataPreview = this;  // Make instance globally accessible
    this.setupEventListeners();
    this.loadDataLoaders();
  }

  setupEventListeners() {
    // Setup loader selection
    document.getElementById('left-loader').addEventListener('change', (e) => {
      this.leftLoader = e.target.value;
      if (this.leftLoader) {
        this.updateTickerInput('left');
        // Set default ticker
        document.getElementById('left-ticker').value = 'AAPL';
        this.leftTicker = 'AAPL';
        this.updateColumns('left');
      }
    });
    
    document.getElementById('right-loader').addEventListener('change', (e) => {
      this.rightLoader = e.target.value;
      if (this.rightLoader) {
        this.updateTickerInput('right');
        // Set default ticker
        document.getElementById('right-ticker').value = 'AAPL';
        this.rightTicker = 'AAPL';
        this.updateColumns('right');
      }
    });
    
    // Setup ticker inputs with blur event
    document.getElementById('left-ticker').addEventListener('blur', async (e) => {
      if (!this.leftLoader) return;
      
      const prevTicker = this.leftTicker;
      const newTicker = e.target.value.toUpperCase();
      e.target.value = newTicker; // Convert to uppercase immediately
      
      if (newTicker === prevTicker) return; // Don't update if ticker hasn't changed
      
      try {
        this.leftTicker = newTicker;
        await this.updateColumns('left');
      } catch (error) {
        this.leftTicker = 'AAPL';
        e.target.value = 'AAPL';
        this.showToast(`Invalid ticker: ${newTicker}. Reset to AAPL`, 'error');
        await this.updateColumns('left');
      }
    });
    
    document.getElementById('right-ticker').addEventListener('blur', async (e) => {
      if (!this.rightLoader) return;
      
      const prevTicker = this.rightTicker;
      const newTicker = e.target.value.toUpperCase();
      e.target.value = newTicker; // Convert to uppercase immediately
      
      if (newTicker === prevTicker) return; // Don't update if ticker hasn't changed
      
      try {
        this.rightTicker = newTicker;
        await this.updateColumns('right');
      } catch (error) {
        this.rightTicker = 'AAPL';
        e.target.value = 'AAPL';
        this.showToast(`Invalid ticker: ${newTicker}. Reset to AAPL`, 'error');
        await this.updateColumns('right');
      }
    });

    document.getElementById('join-form').addEventListener('submit', (e) => this.handleJoinSubmit(e));
  }

  updateTickerInput(side) {
    const tickerInput = document.getElementById(`${side}-ticker`);
    const loader = side === 'left' ? this.leftLoader : this.rightLoader;
    
    if (loader) {
      tickerInput.style.display = 'block';
      tickerInput.required = true;
    } else {
      tickerInput.style.display = 'none';
      tickerInput.required = false;
    }
  }

  async loadDataLoaders() {
    try {
      const response = await fetch('/api/simple-join-workflow/loaders');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const loaders = await response.json();
      
      // Populate both loader dropdowns
      const leftSelect = document.getElementById('left-loader');
      const rightSelect = document.getElementById('right-loader');
      
      
      // Clear existing options
      leftSelect.innerHTML = '<option value="">Select a loader</option>';
      rightSelect.innerHTML = '<option value="">Select a loader</option>';
      
      if (Array.isArray(loaders)) {
        loaders.forEach(loader => {
          
          const leftOpt = document.createElement('option');
          leftOpt.value = loader;
          leftOpt.textContent = loader;
          leftSelect.appendChild(leftOpt);
          
          const rightOpt = document.createElement('option');
          rightOpt.value = loader;
          rightOpt.textContent = loader;
          rightSelect.appendChild(rightOpt);
        });
      } else {
        console.error('Loaders is not an array:', loaders);
      }
      
    } catch (error) {
      console.error('Error loading data loaders:', error);
      alert('Failed to load data loaders');
    }
  }

  async updateColumns(side, isRetry = false) {
    const loader = side === 'left' ? this.leftLoader : this.rightLoader;
    const ticker = side === 'left' ? this.leftTicker : this.rightTicker;
    const columnContainer = document.getElementById(`${side}_column_container`);
    
    if (!loader || !ticker) {
      return;
    }

    try {
      // Create AbortController to handle timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      // Wrap the fetch in a try-catch to prevent console errors
      let response;
      try {
        response = await fetch(`/api/simple-join-workflow/columns/${loader}?ticker=${ticker}`, {
          signal: controller.signal,
          // Add this to prevent error from being logged to console
          credentials: 'same-origin'
        });
      } catch {
        response = null;
      } finally {
        clearTimeout(timeoutId);
      }
      
      if (!response?.ok) {
        throw new Error(`Invalid ticker: ${ticker}`);
      }
      
      const data = await response.json();
      
      // Clear existing options
      columnContainer.innerHTML = '';
      
      // Create column options
      data.columns.forEach((column) => {
        const columnDiv = document.createElement('div');
        columnDiv.className = 'column-option p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors';
        columnDiv.setAttribute('data-column', column.name);

        const content = `
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                ${this.getTypeIcon(column.type)}
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">${column.name}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  ${column.type}${column.description ? ` • ${column.description}` : ''}
                </div>
              </div>
            </div>
            <div class="selected-indicator hidden">
              <svg class="w-5 h-5 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
        `;

        columnDiv.innerHTML = content;
        columnDiv.addEventListener('click', () => this.selectColumn(side, column.name));
        columnContainer.appendChild(columnDiv);
      });
      
    } catch (error) {
      // Only attempt to reset to AAPL if this isn't already a retry attempt
      if (!isRetry && ticker !== 'AAPL') {
        // Reset the ticker input to AAPL
        if (side === 'left') {
          this.leftTicker = 'AAPL';
          document.getElementById('left-ticker').value = 'AAPL';
        } else {
          this.rightTicker = 'AAPL';
          document.getElementById('right-ticker').value = 'AAPL';
        }
        
        // Show toast notification
        this.showToast(`Invalid ticker: ${ticker}. Reset to AAPL`, 'error');
        
        // Retry with AAPL (with isRetry flag)
        return this.updateColumns(side, true);
      } else {
        // If this is a retry or AAPL failed, show error and clear the container
        columnContainer.innerHTML = '<div class="p-2 text-red-500">Failed to load columns. Please try again later.</div>';
        this.showToast('Failed to load columns. Please try again later.', 'error');
      }
    }
  }

  async performJoin() {
    try {
      const leftLoader = this.leftLoader;
      const rightLoader = this.rightLoader;
      const leftTicker = this.leftTicker;
      const rightTicker = this.rightTicker;

      if (!leftLoader || !rightLoader) {
        throw new Error('Please select both data loaders');
      }

      if (!leftTicker || !rightTicker) {
        throw new Error('Please enter tickers for both data loaders');
      }

      const joinType = this.getJoinType();
      console.log(`DEBUG: Performing join with type: ${joinType}`);
      
      // Create base request object
      const joinRequest = {
        left_loader: leftLoader,
        right_loader: rightLoader,
        left_ticker: leftTicker,
        right_ticker: rightTicker,
        join_type: joinType
      };
      
      // Only include join columns for standard joins, not for forward_fill
      if (joinType !== 'forward_fill') {
        joinRequest.left_on = 'Date';
        joinRequest.right_on = 'Date';
      }

      console.log('DEBUG: Join request payload:', JSON.stringify(joinRequest));

      const response = await fetch('/api/simple-join-workflow/join', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(joinRequest)
      });

      if (!response.ok) {
        // Handle error response and extract detailed information
        try {
          const errorData = await response.json();
          console.error('Error response details:', errorData);
          throw new Error(errorData.detail || JSON.stringify(errorData) || 'Failed to perform join');
        } catch (jsonError) {
          // If response isn't valid JSON
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Server error ${response.status}: ${response.statusText}`);
        }
      }

      const result = await response.json();
      console.log('DEBUG: Join response data first few records:', result.data?.slice(0, 3));
      console.log('DEBUG: Join response data length:', result.data?.length);
      
      // Modify column names in the joined data
      if (result.data && Array.isArray(result.data)) {
        this.previewData = result.data.map(row => {
          const modifiedRow = {};
          Object.entries(row).forEach(([key, value]) => {
            if (key.endsWith('_y')) {
              // Replace _y with _sub for right loader columns
              const newKey = key.replace('_y', '_sub');
              modifiedRow[newKey] = value;
            } else if (key.endsWith('_x')) {
              // Remove _x suffix for left (main) loader columns
              const newKey = key.replace('_x', '');
              modifiedRow[newKey] = value;
            } else {
              modifiedRow[key] = value;
            }
          });
          return modifiedRow;
        });
      } else {
        this.previewData = [];
      }
      
      // Update the preview table
      this.updatePreviewTable(this.previewData);

      // Show workflow step container first
      const workflowStepContainer = document.getElementById('workflow-step-container');
      if (workflowStepContainer) {
        workflowStepContainer.classList.remove('hidden');
        // Give DOM time to update before initializing workflow step
        setTimeout(() => {
          if (window.workflowStep) {
            window.workflowStep.initialize(this.previewData);
          }
        }, 100);
      }

      return false;
    } catch (error) {
      console.error('Error performing join:', error);
      throw error;
    }
  }

  handleJoinSubmit(event) {
    event.preventDefault();
    this.performJoin();
    return false;
  }

  validateJoinInputs() {
    if (!this.leftLoader || !this.rightLoader) {
      alert("Please select both data loaders");
      return false;
    }
    if (!this.leftTicker || !this.rightTicker) {
      alert("Please enter tickers for both data loaders");
      return false;
    }
    if (!this.leftColumn || !this.rightColumn) {
      alert("Please select columns for joining");
      return false;
    }
    return true;
  }

  getTypeIcon(type) {
    const iconClass = "w-5 h-5";
    switch (type) {
      case "datetime":
        return `<svg class="${iconClass} text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>`;
      case "number":
      case "integer":
        return `<svg class="${iconClass} text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
        </svg>`;
      default:
        return `<svg class="${iconClass} text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"></path>
        </svg>`;
    }
  }

  toggleLoading(show) {
    document
      .getElementById("loading-spinner")
      .classList.toggle("hidden", !show);
  }

  initializeDataTable(data) {
    if (!data || data.length === 0) {
      document.getElementById("preview-container").classList.add("hidden");
      alert("No data available for preview");
      return;
    }

    // Show the preview container
    document.getElementById("preview-container").classList.remove("hidden");

    // Destroy existing DataTable if it exists
    if (this.table) {
      this.table.destroy();
    }

    const columns = Object.keys(data[0]).map((key) => ({
      title: key,
      data: key,
      render: (data, type) => {
        if (type === "display") {
          if (key === "Date" && data) {
            return new Date(data).toLocaleDateString();
          }
          if (typeof data === "number") {
            return data.toLocaleString(undefined, {
              maximumFractionDigits: 4,
            });
          }
        }
        return data ?? "N/A";
      },
    }));

    this.table = $("#preview-table").DataTable({
      data: data,
      columns: columns,
      pageLength: 25,
      scrollX: true,
      scrollCollapse: true,
      autoWidth: true,
      dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"Bf>rtip',
      language: {
        paginate: {
          previous: "&lt;",
          next: "&gt;",
        },
      },
      buttons: [
        {
          extend: "copy",
          className: "dt-button btn-export",
        },
        {
          extend: "csv",
          className: "dt-button btn-export",
        },
        {
          extend: "excel",
          className: "dt-button btn-export",
        },
      ],
      drawCallback: function () {
        $(".paginate_button").addClass(
          "px-3 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-md mx-1",
        );
        $(".paginate_button.current").addClass(
          "bg-black text-white hover:bg-gray-800",
        );

        // Style export buttons
        $(".btn-export").addClass(
          "px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 mr-2",
        );
      },
    });

    // Handle window resize
    window.addEventListener("resize", () => {
      if (this.table) {
        this.table.columns.adjust();
      }
    });
  }

  getPreviewData() {
    return this.previewData;
  }

  getJoinType() {
    const selectedJoinType = document.querySelector('input[name=\"join_type\"]:checked');
    return selectedJoinType ? selectedJoinType.value : 'inner'; // Default to inner if none selected
  }

  getLeftLoader() {
    return this.leftLoader;
  }

  getRightLoader() {
    return this.rightLoader;
  }

  getLeftTicker() {
    return this.leftTicker;
  }

  getRightTicker() {
    return this.rightTicker;
  }

  selectColumn(side, columnName) {
    const columnContainer = document.getElementById(`${side}_column_container`);
    columnContainer.querySelectorAll('.column-option').forEach((opt) => {
      opt.classList.remove('bg-primary-50', 'dark:bg-primary-900/50');
      opt.querySelector('.selected-indicator').classList.add('hidden');
    });

    const selectedColumn = columnContainer.querySelector(`[data-column="${columnName}"]`);
    if (selectedColumn) {
      selectedColumn.classList.add('bg-primary-50', 'dark:bg-primary-900/50');
      selectedColumn.querySelector('.selected-indicator').classList.remove('hidden');
      this[`${side}Column`] = columnName;
    }
  }

  updatePreviewTable(data) {
    if (!data || data.length === 0) {
      document.getElementById("preview-container").classList.add("hidden");
      alert("No data available for preview");
      return;
    }

    // Show the preview container
    document.getElementById("preview-container").classList.remove("hidden");

    // Destroy existing DataTable if it exists
    if (this.table) {
      this.table.destroy();
    }

    const columns = Object.keys(data[0]).map((key) => ({
      title: key,
      data: key,
      render: (data, type) => {
        if (type === "display") {
          if (key === "Date" && data) {
            return new Date(data).toLocaleDateString();
          }
          if (typeof data === "number") {
            return data.toLocaleString(undefined, {
              maximumFractionDigits: 4,
            });
          }
        }
        return data ?? "N/A";
      },
    }));

    this.table = $("#preview-table").DataTable({
      data: data,
      columns: columns,
      pageLength: 25,
      scrollX: true,
      scrollCollapse: true,
      autoWidth: true,
      dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"Bf>rtip',
      language: {
        paginate: {
          previous: "&lt;",
          next: "&gt;",
        },
      },
      buttons: [
        {
          extend: "copy",
          className: "dt-button btn-export",
        },
        {
          extend: "csv",
          className: "dt-button btn-export",
        },
        {
          extend: "excel",
          className: "dt-button btn-export",
        },
      ],
      drawCallback: function () {
        $(".paginate_button").addClass(
          "px-3 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-md mx-1",
        );
        $(".paginate_button.current").addClass(
          "bg-black text-white hover:bg-gray-800",
        );

        // Style export buttons
        $(".btn-export").addClass(
          "px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 mr-2",
        );
      },
    });

    // Handle window resize
    window.addEventListener("resize", () => {
      if (this.table) {
        this.table.columns.adjust();
      }
    });
  }

  showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
      document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `px-4 py-2 rounded shadow-lg transition-all duration-300 transform translate-x-full
      ${type === 'error' ? 'bg-red-500 text-white' : 'bg-gray-800 text-white'}`;
    toast.textContent = message;

    // Add to container
    toastContainer.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full');
    }, 10);

    // Remove after delay
    setTimeout(() => {
      toast.classList.add('translate-x-full');
      setTimeout(() => {
        toastContainer.removeChild(toast);
        if (toastContainer.children.length === 0) {
          document.body.removeChild(toastContainer);
        }
      }, 300);
    }, 3000);
  }
}

// Initialize the workflow when the document is ready
document.addEventListener("DOMContentLoaded", () => {
  window.dataJoinWorkflow = new DataJoinWorkflow();
});

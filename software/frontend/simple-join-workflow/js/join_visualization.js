class JoinVisualization {
  constructor() {
    this.currentJoinType = "inner";
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateVisualization("inner"); // Default visualization
  }

  setupEventListeners() {
    // Listen for join type changes
    document.querySelectorAll('input[name="join_type"]').forEach((radio) => {
      radio.addEventListener("change", (e) => {
        this.currentJoinType = e.target.value;
        this.updateVisualization(e.target.value);
      });
    });

    // Listen for successful join to potentially update visualization
    document.addEventListener("joinCompleted", (e) => {
      this.handleJoinCompletion(e.detail);
    });
  }

  updateVisualization(joinType) {
    const diagram = document.getElementById("join-diagram");
    if (!diagram) return;

    diagram.innerHTML = ""; // Clear existing visualization
    diagram.className = "flex justify-center items-center min-h-[120px] p-4 bg-gray-50 dark:bg-gray-900 rounded-md transition-all duration-300";

    // Create the visualization based on join type
    switch (joinType) {
      case "inner":
        this.createInnerJoinVisualization(diagram);
        break;
      case "left":
        this.createLeftJoinVisualization(diagram);
        break;
      case "right":
        this.createRightJoinVisualization(diagram);
        break;
      case "outer":
        this.createOuterJoinVisualization(diagram);
        break;
      case "forward_fill":
        this.createForwardFillVisualization(diagram);
        break;
    }
  }

  createInnerJoinVisualization(container) {
    const visualContainer = document.createElement("div");
    visualContainer.className = "relative w-full max-w-md aspect-[2/1] flex items-center justify-center";
    
    // Create SVG for visualization
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("viewBox", "0 0 200 100");
    svg.setAttribute("class", "w-full h-full");
    
    // Left circle
    const leftCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    leftCircle.setAttribute("cx", "70");
    leftCircle.setAttribute("cy", "50");
    leftCircle.setAttribute("r", "40");
    leftCircle.setAttribute("fill", "rgba(59, 130, 246, 0.2)");
    leftCircle.setAttribute("stroke", "rgb(59, 130, 246)");
    leftCircle.setAttribute("stroke-width", "1.5");
    leftCircle.setAttribute("class", "transition-all duration-300");
    
    // Right circle
    const rightCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    rightCircle.setAttribute("cx", "130");
    rightCircle.setAttribute("cy", "50");
    rightCircle.setAttribute("r", "40");
    rightCircle.setAttribute("fill", "rgba(16, 185, 129, 0.2)");
    rightCircle.setAttribute("stroke", "rgb(16, 185, 129)");
    rightCircle.setAttribute("stroke-width", "1.5");
    rightCircle.setAttribute("class", "transition-all duration-300");
    
    // Intersection area (clip path technique)
    const intersection = document.createElementNS("http://www.w3.org/2000/svg", "path");
    const d = this.calculateIntersectionPath(70, 50, 40, 130, 50, 40);
    intersection.setAttribute("d", d);
    intersection.setAttribute("fill", "rgba(124, 58, 237, 0.5)");
    intersection.setAttribute("class", "transition-all duration-500");
    
    // Add elements to SVG in correct order
    svg.appendChild(leftCircle);
    svg.appendChild(rightCircle);
    svg.appendChild(intersection);
    
    // Left label
    const leftLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    leftLabel.setAttribute("x", "40");
    leftLabel.setAttribute("y", "50");
    leftLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    leftLabel.setAttribute("text-anchor", "middle");
    leftLabel.setAttribute("dominant-baseline", "middle");
    leftLabel.textContent = "Left";
    
    // Right label
    const rightLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    rightLabel.setAttribute("x", "160");
    rightLabel.setAttribute("y", "50");
    rightLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    rightLabel.setAttribute("text-anchor", "middle");
    rightLabel.setAttribute("dominant-baseline", "middle");
    rightLabel.textContent = "Right";
    
    // Add labels
    svg.appendChild(leftLabel);
    svg.appendChild(rightLabel);
    
    visualContainer.appendChild(svg);
    container.appendChild(visualContainer);
    
    // Add description
    this.addJoinDescription(container, "inner");

    // Add glowing pulse animation to intersection
    this.pulseAnimation(intersection);
  }

  createLeftJoinVisualization(container) {
    const visualContainer = document.createElement("div");
    visualContainer.className = "relative w-full max-w-md aspect-[2/1] flex items-center justify-center";
    
    // Create SVG for visualization
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("viewBox", "0 0 200 100");
    svg.setAttribute("class", "w-full h-full");
    
    // Left circle (fully opaque)
    const leftCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    leftCircle.setAttribute("cx", "70");
    leftCircle.setAttribute("cy", "50");
    leftCircle.setAttribute("r", "40");
    leftCircle.setAttribute("fill", "rgba(59, 130, 246, 0.3)");
    leftCircle.setAttribute("stroke", "rgb(59, 130, 246)");
    leftCircle.setAttribute("stroke-width", "2");
    leftCircle.setAttribute("class", "transition-all duration-300");
    
    // Right circle (semi-transparent)
    const rightCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    rightCircle.setAttribute("cx", "130");
    rightCircle.setAttribute("cy", "50");
    rightCircle.setAttribute("r", "40");
    rightCircle.setAttribute("fill", "rgba(16, 185, 129, 0.1)");
    rightCircle.setAttribute("stroke", "rgb(16, 185, 129)");
    rightCircle.setAttribute("stroke-width", "1");
    rightCircle.setAttribute("stroke-dasharray", "3,2");
    rightCircle.setAttribute("class", "transition-all duration-300");
    
    // Intersection area
    const intersection = document.createElementNS("http://www.w3.org/2000/svg", "path");
    const d = this.calculateIntersectionPath(70, 50, 40, 130, 50, 40);
    intersection.setAttribute("d", d);
    intersection.setAttribute("fill", "rgba(124, 58, 237, 0.4)");
    intersection.setAttribute("class", "transition-all duration-500");
    
    // Add elements to SVG in correct order
    svg.appendChild(rightCircle);
    svg.appendChild(leftCircle);
    svg.appendChild(intersection);
    
    // Left label
    const leftLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    leftLabel.setAttribute("x", "40");
    leftLabel.setAttribute("y", "50");
    leftLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    leftLabel.setAttribute("text-anchor", "middle");
    leftLabel.setAttribute("dominant-baseline", "middle");
    leftLabel.textContent = "Left";
    
    // Right label
    const rightLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    rightLabel.setAttribute("x", "160");
    rightLabel.setAttribute("y", "50");
    rightLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    rightLabel.setAttribute("text-anchor", "middle");
    rightLabel.setAttribute("dominant-baseline", "middle");
    rightLabel.textContent = "Right";
    
    // Add labels
    svg.appendChild(leftLabel);
    svg.appendChild(rightLabel);
    
    visualContainer.appendChild(svg);
    container.appendChild(visualContainer);
    
    // Add description
    this.addJoinDescription(container, "left");
    
    // Add animation
    this.pulseAnimation(leftCircle);
  }

  createRightJoinVisualization(container) {
    const visualContainer = document.createElement("div");
    visualContainer.className = "relative w-full max-w-md aspect-[2/1] flex items-center justify-center";
    
    // Create SVG for visualization
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("viewBox", "0 0 200 100");
    svg.setAttribute("class", "w-full h-full");
    
    // Left circle (semi-transparent)
    const leftCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    leftCircle.setAttribute("cx", "70");
    leftCircle.setAttribute("cy", "50");
    leftCircle.setAttribute("r", "40");
    leftCircle.setAttribute("fill", "rgba(59, 130, 246, 0.1)");
    leftCircle.setAttribute("stroke", "rgb(59, 130, 246)");
    leftCircle.setAttribute("stroke-width", "1");
    leftCircle.setAttribute("stroke-dasharray", "3,2");
    leftCircle.setAttribute("class", "transition-all duration-300");
    
    // Right circle (fully opaque)
    const rightCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    rightCircle.setAttribute("cx", "130");
    rightCircle.setAttribute("cy", "50");
    rightCircle.setAttribute("r", "40");
    rightCircle.setAttribute("fill", "rgba(16, 185, 129, 0.3)");
    rightCircle.setAttribute("stroke", "rgb(16, 185, 129)");
    rightCircle.setAttribute("stroke-width", "2");
    rightCircle.setAttribute("class", "transition-all duration-300");
    
    // Intersection area
    const intersection = document.createElementNS("http://www.w3.org/2000/svg", "path");
    const d = this.calculateIntersectionPath(70, 50, 40, 130, 50, 40);
    intersection.setAttribute("d", d);
    intersection.setAttribute("fill", "rgba(124, 58, 237, 0.4)");
    intersection.setAttribute("class", "transition-all duration-500");
    
    // Add elements to SVG in correct order
    svg.appendChild(leftCircle);
    svg.appendChild(rightCircle);
    svg.appendChild(intersection);
    
    // Left label
    const leftLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    leftLabel.setAttribute("x", "40");
    leftLabel.setAttribute("y", "50");
    leftLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    leftLabel.setAttribute("text-anchor", "middle");
    leftLabel.setAttribute("dominant-baseline", "middle");
    leftLabel.textContent = "Left";
    
    // Right label
    const rightLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    rightLabel.setAttribute("x", "160");
    rightLabel.setAttribute("y", "50");
    rightLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    rightLabel.setAttribute("text-anchor", "middle");
    rightLabel.setAttribute("dominant-baseline", "middle");
    rightLabel.textContent = "Right";
    
    // Add labels
    svg.appendChild(leftLabel);
    svg.appendChild(rightLabel);
    
    visualContainer.appendChild(svg);
    container.appendChild(visualContainer);
    
    // Add description
    this.addJoinDescription(container, "right");
    
    // Add animation
    this.pulseAnimation(rightCircle);
  }

  createOuterJoinVisualization(container) {
    const visualContainer = document.createElement("div");
    visualContainer.className = "relative w-full max-w-md aspect-[2/1] flex items-center justify-center";
    
    // Create SVG for visualization
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("viewBox", "0 0 200 100");
    svg.setAttribute("class", "w-full h-full");
    
    // Union area (slightly larger than both circles)
    const union = document.createElementNS("http://www.w3.org/2000/svg", "path");
    const d = this.calculateUnionPath(70, 50, 40, 130, 50, 40);
    union.setAttribute("d", d);
    union.setAttribute("fill", "rgba(124, 58, 237, 0.2)");
    union.setAttribute("stroke", "rgba(124, 58, 237, 0.6)");
    union.setAttribute("stroke-width", "1");
    union.setAttribute("class", "transition-all duration-500");
    
    // Left circle
    const leftCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    leftCircle.setAttribute("cx", "70");
    leftCircle.setAttribute("cy", "50");
    leftCircle.setAttribute("r", "40");
    leftCircle.setAttribute("fill", "rgba(59, 130, 246, 0.25)");
    leftCircle.setAttribute("stroke", "rgb(59, 130, 246)");
    leftCircle.setAttribute("stroke-width", "1.5");
    leftCircle.setAttribute("class", "transition-all duration-300");
    
    // Right circle
    const rightCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    rightCircle.setAttribute("cx", "130");
    rightCircle.setAttribute("cy", "50");
    rightCircle.setAttribute("r", "40");
    rightCircle.setAttribute("fill", "rgba(16, 185, 129, 0.25)");
    rightCircle.setAttribute("stroke", "rgb(16, 185, 129)");
    rightCircle.setAttribute("stroke-width", "1.5");
    rightCircle.setAttribute("class", "transition-all duration-300");
    
    // Intersection area
    const intersection = document.createElementNS("http://www.w3.org/2000/svg", "path");
    const intD = this.calculateIntersectionPath(70, 50, 40, 130, 50, 40);
    intersection.setAttribute("d", intD);
    intersection.setAttribute("fill", "rgba(124, 58, 237, 0.4)");
    intersection.setAttribute("class", "transition-all duration-500");
    
    // Add elements to SVG in correct order
    svg.appendChild(union);
    svg.appendChild(leftCircle);
    svg.appendChild(rightCircle);
    svg.appendChild(intersection);
    
    // Left label
    const leftLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    leftLabel.setAttribute("x", "40");
    leftLabel.setAttribute("y", "50");
    leftLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    leftLabel.setAttribute("text-anchor", "middle");
    leftLabel.setAttribute("dominant-baseline", "middle");
    leftLabel.textContent = "Left";
    
    // Right label
    const rightLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    rightLabel.setAttribute("x", "160");
    rightLabel.setAttribute("y", "50");
    rightLabel.setAttribute("class", "text-[10px] font-medium fill-gray-700 dark:fill-gray-300");
    rightLabel.setAttribute("text-anchor", "middle");
    rightLabel.setAttribute("dominant-baseline", "middle");
    rightLabel.textContent = "Right";
    
    // Add labels
    svg.appendChild(leftLabel);
    svg.appendChild(rightLabel);
    
    visualContainer.appendChild(svg);
    container.appendChild(visualContainer);
    
    // Add description
    this.addJoinDescription(container, "outer");
    
    // Add animation
    this.pulseAnimation(union);
  }

  createForwardFillVisualization(container) {
    const visualContainer = document.createElement("div");
    visualContainer.className = "relative w-full max-w-md aspect-[2/1] flex items-center justify-center";
    
    // Create SVG for visualization
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("viewBox", "0 0 200 100");
    svg.setAttribute("class", "w-full h-full");
    
    // Create gradient
    const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
    
    const gradient = document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
    gradient.setAttribute("id", "timeGradient");
    gradient.setAttribute("x1", "0%");
    gradient.setAttribute("y1", "0%");
    gradient.setAttribute("x2", "100%");
    gradient.setAttribute("y2", "0%");
    
    const stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
    stop1.setAttribute("offset", "0%");
    stop1.setAttribute("stop-color", "rgba(59, 130, 246, 0.8)");
    
    const stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
    stop2.setAttribute("offset", "100%");
    stop2.setAttribute("stop-color", "rgba(16, 185, 129, 0.8)");
    
    gradient.appendChild(stop1);
    gradient.appendChild(stop2);
    defs.appendChild(gradient);
    svg.appendChild(defs);
    
    // Timeline base
    const timeline = document.createElementNS("http://www.w3.org/2000/svg", "rect");
    timeline.setAttribute("x", "20");
    timeline.setAttribute("y", "60");
    timeline.setAttribute("width", "160");
    timeline.setAttribute("height", "2");
    timeline.setAttribute("fill", "rgba(156, 163, 175, 0.5)");
    timeline.setAttribute("rx", "1");
    
    // Time points
    const timePoints = [];
    const timeLabels = [];
    const fillConnectors = [];
    
    for (let i = 0; i < 6; i++) {
        const x = 30 + i * 30;
        
        // Time point
        const point = document.createElementNS("http://www.w3.org/2000/svg", "circle");
        point.setAttribute("cx", x.toString());
        point.setAttribute("cy", "61");
        point.setAttribute("r", "3");
        point.setAttribute("class", i % 2 === 0 ? "fill-blue-500" : "fill-green-500");
        timePoints.push(point);
        
        // Time label
        const label = document.createElementNS("http://www.w3.org/2000/svg", "text");
        label.setAttribute("x", x.toString());
        label.setAttribute("y", "73");
        label.setAttribute("class", "text-[8px] fill-gray-500 dark:fill-gray-400");
        label.setAttribute("text-anchor", "middle");
        label.textContent = `t${i+1}`;
        timeLabels.push(label);
        
        // Data points (above timeline)
        if (i % 2 === 0) {
            // Blue data points (source)
            const dataPoint = document.createElementNS("http://www.w3.org/2000/svg", "circle");
            dataPoint.setAttribute("cx", x.toString());
            dataPoint.setAttribute("cy", "40");
            dataPoint.setAttribute("r", "6");
            dataPoint.setAttribute("fill", "rgba(59, 130, 246, 0.8)");
            dataPoint.setAttribute("stroke", "white");
            dataPoint.setAttribute("stroke-width", "1");
            svg.appendChild(dataPoint);
            
            // Data point label
            const dataLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
            dataLabel.setAttribute("x", x.toString());
            dataLabel.setAttribute("y", "40");
            dataLabel.setAttribute("class", "text-[8px] font-bold fill-white");
            dataLabel.setAttribute("text-anchor", "middle");
            dataLabel.setAttribute("dominant-baseline", "middle");
            dataLabel.textContent = "x";
            svg.appendChild(dataLabel);
            
            // If not the last blue point, create fill connector to next point
            if (i < 5) {
                const connector = document.createElementNS("http://www.w3.org/2000/svg", "path");
                connector.setAttribute("d", `M ${x} 40 L ${x + 15} 35 L ${x + 30} 40`);
                connector.setAttribute("stroke", "rgba(59, 130, 246, 0.3)");
                connector.setAttribute("stroke-width", "1.5");
                connector.setAttribute("stroke-dasharray", "2,2");
                connector.setAttribute("fill", "none");
                fillConnectors.push(connector);
            }
        } else {
            // Green point (forward filled value)
            const dataPoint = document.createElementNS("http://www.w3.org/2000/svg", "circle");
            dataPoint.setAttribute("cx", x.toString());
            dataPoint.setAttribute("cy", "40");
            dataPoint.setAttribute("r", "6");
            dataPoint.setAttribute("fill", "rgba(16, 185, 129, 0.8)");
            dataPoint.setAttribute("stroke", "white");
            dataPoint.setAttribute("stroke-width", "1");
            dataPoint.setAttribute("opacity", "0.8");
            svg.appendChild(dataPoint);
            
            // Arrow pointing from previous blue point to this green point
            const arrow = document.createElementNS("http://www.w3.org/2000/svg", "path");
            arrow.setAttribute("d", `M ${x-20} 27 Q ${x-10} 20 ${x} 27`);
            arrow.setAttribute("fill", "none");
            arrow.setAttribute("stroke", "rgba(124, 58, 237, 0.6)");
            arrow.setAttribute("stroke-width", "1.5");
            arrow.setAttribute("marker-end", "url(#arrowhead)");
            svg.appendChild(arrow);
            
            // Data point label
            const dataLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
            dataLabel.setAttribute("x", x.toString());
            dataLabel.setAttribute("y", "40");
            dataLabel.setAttribute("class", "text-[8px] font-bold fill-white");
            dataLabel.setAttribute("text-anchor", "middle");
            dataLabel.setAttribute("dominant-baseline", "middle");
            dataLabel.textContent = "x";
            svg.appendChild(dataLabel);
        }
    }
    
    // Add arrowhead marker
    const arrowMarker = document.createElementNS("http://www.w3.org/2000/svg", "marker");
    arrowMarker.setAttribute("id", "arrowhead");
    arrowMarker.setAttribute("markerWidth", "6");
    arrowMarker.setAttribute("markerHeight", "4");
    arrowMarker.setAttribute("refX", "6");
    arrowMarker.setAttribute("refY", "2");
    arrowMarker.setAttribute("orient", "auto");
    
    const arrowPath = document.createElementNS("http://www.w3.org/2000/svg", "path");
    arrowPath.setAttribute("d", "M0,0 L6,2 L0,4 Z");
    arrowPath.setAttribute("fill", "rgba(124, 58, 237, 0.6)");
    
    arrowMarker.appendChild(arrowPath);
    defs.appendChild(arrowMarker);
    
    // Add title
    const title = document.createElementNS("http://www.w3.org/2000/svg", "text");
    title.setAttribute("x", "100");
    title.setAttribute("y", "20");
    title.setAttribute("class", "text-[10px] font-semibold fill-gray-700 dark:fill-gray-300");
    title.setAttribute("text-anchor", "middle");
    title.textContent = "Forward Fill Time Series";
    
    // Add all elements to SVG
    svg.appendChild(timeline);
    
    // Add fill connectors first (behind points)
    fillConnectors.forEach(connector => svg.appendChild(connector));
    
    // Add time points and labels
    timePoints.forEach(point => svg.appendChild(point));
    timeLabels.forEach(label => svg.appendChild(label));
    
    svg.appendChild(title);
    
    // Add tooltip/legend
    const legend = document.createElementNS("http://www.w3.org/2000/svg", "g");
    legend.setAttribute("transform", "translate(10, 85)");
    
    // Blue dot
    const blueDot = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    blueDot.setAttribute("cx", "5");
    blueDot.setAttribute("cy", "5");
    blueDot.setAttribute("r", "3");
    blueDot.setAttribute("fill", "rgba(59, 130, 246, 0.8)");
    
    // Blue label
    const blueLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    blueLabel.setAttribute("x", "12");
    blueLabel.setAttribute("y", "7");
    blueLabel.setAttribute("class", "text-[7px] fill-gray-700 dark:fill-gray-300");
    blueLabel.textContent = "Source data";
    
    // Green dot
    const greenDot = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    greenDot.setAttribute("cx", "65");
    greenDot.setAttribute("cy", "5");
    greenDot.setAttribute("r", "3");
    greenDot.setAttribute("fill", "rgba(16, 185, 129, 0.8)");
    
    // Green label
    const greenLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
    greenLabel.setAttribute("x", "72");
    greenLabel.setAttribute("y", "7");
    greenLabel.setAttribute("class", "text-[7px] fill-gray-700 dark:fill-gray-300");
    greenLabel.textContent = "Filled data";
    
    legend.appendChild(blueDot);
    legend.appendChild(blueLabel);
    legend.appendChild(greenDot);
    legend.appendChild(greenLabel);
    
    svg.appendChild(legend);
    
    visualContainer.appendChild(svg);
    container.appendChild(visualContainer);
    
    // Add description
    this.addJoinDescription(container, "forward_fill");
    
    // Animate the arrows to show filling forward
    this.animateForwardFill(fillConnectors);
  }

  calculateIntersectionPath(x1, y1, r1, x2, y2, r2) {
    // Calculate the distance between the centers
    const d = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    
    // No intersection if circles are too far apart or one is inside the other
    if (d > r1 + r2 || d < Math.abs(r1 - r2)) {
      return "";
    }
    
    // Calculate intersection points
    const a = (r1 ** 2 - r2 ** 2 + d ** 2) / (2 * d);
    const h = Math.sqrt(r1 ** 2 - a ** 2);
    
    const x3 = x1 + a * (x2 - x1) / d;
    const y3 = y1 + a * (y2 - y1) / d;
    
    const x4 = x3 + h * (y2 - y1) / d;
    const y4 = y3 - h * (x2 - x1) / d;
    
    const x5 = x3 - h * (y2 - y1) / d;
    const y5 = y3 + h * (x2 - x1) / d;
    
    // Calculate angles for arc creation
    const angle1 = Math.atan2(y4 - y1, x4 - x1);
    const angle2 = Math.atan2(y5 - y1, x5 - x1);
    
    const angle3 = Math.atan2(y4 - y2, x4 - x2);
    const angle4 = Math.atan2(y5 - y2, x5 - x2);
    
    // Create path that forms the intersection
    let path = `M ${x4},${y4} `;
    path += `A ${r1},${r1} 0 0 1 ${x5},${y5} `;
    path += `A ${r2},${r2} 0 0 1 ${x4},${y4} `;
    
    return path;
  }

  calculateUnionPath(x1, y1, r1, x2, y2, r2) {
    // For simplicity, we'll make a rounded rectangle that encompasses both circles
    const minX = Math.min(x1 - r1, x2 - r2) - 5;
    const minY = Math.min(y1 - r1, y2 - r2) - 5;
    const maxX = Math.max(x1 + r1, x2 + r2) + 5;
    const maxY = Math.max(y1 + r1, y2 + r2) + 5;
    const width = maxX - minX;
    const height = maxY - minY;
    
    // Rounded rectangle as path
    return `M ${minX + 10},${minY} 
            h ${width - 20} 
            q 10,0 10,10 
            v ${height - 20} 
            q 0,10 -10,10 
            h ${-(width - 20)} 
            q -10,0 -10,-10 
            v ${-(height - 20)} 
            q 0,-10 10,-10 z`;
  }

  pulseAnimation(element) {
    // Create subtle pulse animation
    let opacity = 0.7;
    let increasing = false;
    
    const pulse = () => {
      if (increasing) {
        opacity += 0.02;
        if (opacity >= 0.9) increasing = false;
      } else {
        opacity -= 0.02;
        if (opacity <= 0.4) increasing = true;
      }
      
      element.setAttribute("opacity", opacity.toString());
      requestAnimationFrame(pulse);
    };
    
    pulse();
  }

  animateForwardFill(connectors) {
    // Animate the fill connectors with a moving dash pattern
    connectors.forEach(connector => {
      let dashOffset = 0;
      
      const animate = () => {
        dashOffset -= 0.5;
        if (dashOffset < -20) dashOffset = 0;
        
        connector.setAttribute("stroke-dashoffset", dashOffset.toString());
        requestAnimationFrame(animate);
      };
      
      animate();
    });
  }

  addJoinDescription(container, joinType) {
    const descriptions = {
      inner: "Shows only matching records from both sources",
      left: "Shows all records from left source and matching records from right",
      right: "Shows all records from right source and matching records from left",
      outer: "Shows all records from both sources",
      forward_fill: "Fills gaps in time series data by carrying forward values from previous records"
    };

    const description = document.createElement("div");
    description.className = "text-center text-sm text-gray-600 dark:text-gray-400 mt-4";
    description.textContent = descriptions[joinType] || "";
    container.appendChild(description);
  }

  handleJoinCompletion(detail) {
    // Show the workflow step container
    const workflowStepContainer = document.getElementById(
      "workflow-step-container",
    );
    if (workflowStepContainer) {
      workflowStepContainer.classList.remove("hidden");

      // Scroll to workflow section smoothly
      workflowStepContainer.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }

    // Dispatch event for workflow step initialization
    document.dispatchEvent(
      new CustomEvent("joinCompletedWorkflowReady", {
        detail: detail,
      }),
    );
  }
}

// Initialize the visualization when the document is ready
document.addEventListener("DOMContentLoaded", () => {
  window.joinVisualization = new JoinVisualization();
});

{% extends "base.html" %}

{% block title %}Simple Join Workflow{% endblock %}
{% block header %}Simple Join Workflow{% endblock %}

{% block head %}
<style>
    /* DataTables Pagination Styling */
    .dataTables_wrapper .dataTables_paginate {
        padding-top: 1rem;
        display: flex;
        gap: 0.5rem;
        align-items: center;
        justify-content: flex-end;
    }

    .dataTables_wrapper .paginate_button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 2.5rem;
        height: 2.5rem;
        padding: 0 0.75rem;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 150ms;
        cursor: pointer;
        background: transparent;
        color: rgb(17 24 39);
    }

    .dark .dataTables_wrapper .paginate_button {
        color: rgb(243 244 246);
    }

    .dataTables_wrapper .paginate_button.current {
        background-color: black;
        color: white;
    }

    .dark .dataTables_wrapper .paginate_button.current {
        background-color: rgb(248 250 252);
        color: rgb(15 23 42);
    }

    .dataTables_wrapper .paginate_button:hover:not(.current) {
        background-color: rgb(243 244 246);
    }

    .dark .dataTables_wrapper .paginate_button:hover:not(.current) {
        background-color: rgb(31 41 55);
    }

    .dataTables_wrapper .paginate_button.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* DataTables Info and Length Menu Styling */
    .dataTables_info, .dataTables_length {
        color: rgb(55 65 81);
        font-size: 0.875rem;
    }

    .dark .dataTables_info, .dark .dataTables_length {
        color: rgb(209 213 219);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto p-6 space-y-6">
    <!-- Main Join Configuration Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-semibold mb-6 dark:text-white">Data Join Configuration</h2>

        <!-- Join Configuration Form -->
        <form id="join-form" class="space-y-6">
            <!-- Data Loader Selection Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left (Main) Data Loader -->
                <div class="relative p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/5">
                    <div class="absolute -top-3 left-3 px-2 bg-black text-white text-xs font-semibold rounded">
                        MAIN LOADER
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label for="left-loader" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Primary Data Source
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-50 text-primary-700 dark:bg-primary-900/50 dark:text-primary-200">
                                    Main
                                </span>
                            </label>
                            <select id="left-loader" name="left-loader" 
                                class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="">Select a loader</option>
                            </select>
                        </div>
                        <div>
                            <label for="left-ticker" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Ticker Symbol
                            </label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <input type="text" id="left-ticker" name="left-ticker" value="AAPL" 
                                    class="block w-full px-3 py-2 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                                    placeholder="Enter ticker" />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right (Secondary) Data Loader -->
                <div class="p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 border-dashed">
                    <div class="space-y-4">
                        <div>
                            <label for="right-loader" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Secondary Data Source
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                                    Auxiliary
                                </span>
                            </label>
                            <select id="right-loader" name="right-loader" 
                                class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 sm:text-sm">
                                <option value="">Select a loader</option>
                            </select>
                        </div>
                        <div>
                            <label for="right-ticker" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Ticker Symbol
                            </label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <input type="text" id="right-ticker" name="right-ticker" value="AAPL" 
                                    class="block w-full px-3 py-2 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 sm:text-sm"
                                    placeholder="Enter ticker" />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Column Selection -->
            <div class="grid grid-cols-2 gap-4 mt-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Left Join Column</label>
                    <div id="left_column_container" class="mt-1 border border-gray-300 rounded-md max-h-60 overflow-y-auto dark:border-gray-600">
                        <!-- Columns will be dynamically added here -->
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Right Join Column</label>
                    <div id="right_column_container" class="mt-1 border border-gray-300 rounded-md max-h-60 overflow-y-auto dark:border-gray-600">
                        <!-- Columns will be dynamically added here -->
                    </div>
                </div>
            </div>

            <!-- Join Type Selection and Visualization -->
            <div class="space-y-6">
                <!-- Join Type Selection -->
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Join Type
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <label class="relative flex items-center justify-center p-4 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer hover:border-black dark:hover:border-gray-500 transition-colors group">
                            <input type="radio" name="join_type" value="inner" checked class="absolute h-0 w-0 opacity-0 peer">
                            <div class="flex flex-col items-center space-y-2 peer-checked:text-black dark:peer-checked:text-white">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-black dark:group-hover:text-white transition-colors">Inner Join</span>
                            </div>
                            <div class="absolute inset-0 border-2 border-transparent rounded-lg peer-checked:border-black dark:peer-checked:border-white transition-colors"></div>
                        </label>
                        <label class="relative flex items-center justify-center p-4 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer hover:border-black dark:hover:border-gray-500 transition-colors group">
                            <input type="radio" name="join_type" value="left" class="absolute h-0 w-0 opacity-0 peer">
                            <div class="flex flex-col items-center space-y-2 peer-checked:text-black dark:peer-checked:text-white">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-black dark:group-hover:text-white transition-colors">Left Join</span>
                            </div>
                            <div class="absolute inset-0 border-2 border-transparent rounded-lg peer-checked:border-black dark:peer-checked:border-white transition-colors"></div>
                        </label>
                        <label class="relative flex items-center justify-center p-4 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer hover:border-black dark:hover:border-gray-500 transition-colors group">
                            <input type="radio" name="join_type" value="right" class="absolute h-0 w-0 opacity-0 peer">
                            <div class="flex flex-col items-center space-y-2 peer-checked:text-black dark:peer-checked:text-white">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-black dark:group-hover:text-white transition-colors">Right Join</span>
                            </div>
                            <div class="absolute inset-0 border-2 border-transparent rounded-lg peer-checked:border-black dark:peer-checked:border-white transition-colors"></div>
                        </label>
                        <label class="relative flex items-center justify-center p-4 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer hover:border-black dark:hover:border-gray-500 transition-colors group">
                            <input type="radio" name="join_type" value="outer" class="absolute h-0 w-0 opacity-0 peer">
                            <div class="flex flex-col items-center space-y-2 peer-checked:text-black dark:peer-checked:text-white">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-black dark:group-hover:text-white transition-colors">Outer Join</span>
                            </div>
                            <div class="absolute inset-0 border-2 border-transparent rounded-lg peer-checked:border-black dark:peer-checked:border-white transition-colors"></div>
                        </label>
                        <label class="relative flex items-center justify-center p-4 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer hover:border-black dark:hover:border-gray-500 transition-colors group">
                            <input type="radio" name="join_type" value="forward_fill" class="absolute h-0 w-0 opacity-0 peer">
                            <div class="flex flex-col items-center space-y-2 peer-checked:text-black dark:peer-checked:text-white">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-black dark:group-hover:text-white transition-colors">Forward Fill Join</span>
                            </div>
                            <div class="absolute inset-0 border-2 border-transparent rounded-lg peer-checked:border-black dark:peer-checked:border-white transition-colors"></div>
                        </label>
                    </div>
                </div>

                <!-- Join Visualization -->
                <div class="relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-gray-100 dark:via-gray-800 to-transparent opacity-50"></div>
                    <div class="relative p-6 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Join Visualization</h3>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Visual representation of selected join type</div>
                        </div>
                        <div id="join-diagram" class="flex justify-center items-center min-h-[120px] p-4 bg-gray-50 dark:bg-gray-900 rounded-md transition-all duration-300">
                            <!-- Join type diagram will be dynamically updated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" id="perform-join" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background h-10 px-4 py-2 bg-black text-white hover:bg-gray-800 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90 shadow-sm">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"></path>
                    </svg>
                    Perform Join
                </button>
            </div>
        </form>
    </div>

    <!-- Join Preview Card -->
    <div id="preview-container" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hidden">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold dark:text-white">Join Preview</h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Showing preview of joined data
            </div>
        </div>
        <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
                <div class="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                    <table id="preview-table" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflow Step Container (Hidden by default) -->
    <div id="workflow-step-container" class="hidden mt-8">
        <h2 class="text-2xl font-bold mb-4 dark:text-white">Configure Workflow</h2>
        <form id="workflow-form" class="space-y-6">
            <!-- Features Selection -->
            <div class="relative">
                <label for="features" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Features</label>
                <div class="absolute top-0 right-0 mt-1 mr-1 flex space-x-2">
                    <button type="button" id="select-all" class="px-2 py-1 bg-blue-500 text-white rounded-md text-xs">Select All</button>
                    <button type="button" id="deselect-all" class="px-2 py-1 bg-red-500 text-white rounded-md text-xs">Deselect All</button>
                </div>
                <div id="features-container" class="mt-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></div>
            </div>

            <!-- Target Selection -->
            <div>
                <label for="target" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Column</label>
                <select id="target" name="target" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="">Select target column</option>
                </select>
            </div>

            <!-- Model Selection -->
            <div>
                <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Model</label>
                <select id="model" name="model" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="">Select model</option>
                </select>
            </div>

            <!-- Forecast Horizon -->
            <div>
                <label for="forecast_horizon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Forecast Horizon</label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="number" 
                           name="forecast_horizon" 
                           id="forecast_horizon" 
                           min="1" 
                           value="5"
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" 
                           placeholder="Enter number of periods to forecast">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 sm:text-sm">periods</span>
                    </div>
                </div>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Number of periods to forecast ahead</p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90">
                    Run Workflow
                </button>
            </div>
        </form>

        <!-- Error and Success Messages -->
        <div id="workflow-error" class="hidden mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"></div>
        <div id="workflow-success" class="hidden mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative"></div>
    </div>
    
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-primary-500"></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- DataTables and Extensions -->
<script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.html5.min.js"></script>

<!-- Custom Scripts -->
<script src="/frontend/simple-join-workflow/js/join_visualization.js"></script>
<script src="/frontend/simple-join-workflow/js/data_preview.js"></script>
<script src="/frontend/simple-join-workflow/js/workflow_step.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    window.workflowStep = new WorkflowStep();
});
</script>
{% endblock %}

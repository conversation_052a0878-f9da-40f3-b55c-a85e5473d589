# Anthropic Prompt Engineering Best Practices

This document outlines the best practices for prompt engineering with <PERSON><PERSON><PERSON>'s <PERSON> models, based on official Anthropic documentation and our practical experience. Following these guidelines will help create more effective, reliable, and maintainable prompts.

## Core Principles

### 1. Use XML Structure for Complex Prompts

XML tags provide clear structure and organization to complex prompts, making them more readable and effective:

```xml
<role>
You are a financial analyst specializing in market research and investment recommendations.
</role>

<task>
Analyze the provided company data and recommend investment actions.
</task>
```

**Benefits:**
- Creates visual separation between different components
- Makes prompts easier to maintain and update
- Helps <PERSON> understand the hierarchical relationship between elements
- Allows referencing specific sections by tag name

**Best Practices:**
- Use descriptive tag names that match their content
- Nest tags for hierarchical relationships
- Keep tag names consistent throughout the prompt
- Close all tags properly

### 2. Be Direct and Specific

Claude performs best with clear, explicit instructions:

```xml
<instructions>
1. Analyze the stock price data for the past 30 days
2. Identify key support and resistance levels
3. Calculate the RSI and MACD indicators
4. Determine if the stock is overbought or oversold
5. Provide a BUY, SELL, or HOLD recommendation
</instructions>
```

**Best Practices:**
- Use numbered lists for sequential steps
- Break complex tasks into smaller, manageable steps
- Specify exactly what you want in the output
- Provide clear success criteria

### 3. Provide Contextual Information

Include relevant context to help Claude understand the task:

```xml
<context>
This analysis will be presented to senior investment managers who need actionable insights for a quarterly portfolio rebalancing. They are particularly concerned about inflation risks and interest rate changes.
</context>
```

**Best Practices:**
- Explain who the output is for
- Describe how the output will be used
- Provide relevant background information
- Specify any constraints or requirements

### 4. Use Role-Based Prompting

Assigning Claude a specific role helps it adopt the right tone and expertise:

```xml
<role>
You are a VP of Financial Research with 15 years of experience in equity analysis and portfolio management. You specialize in technical analysis and have a track record of identifying market trends early.
</role>
```

**Best Practices:**
- Be specific about the role's expertise and background
- Include relevant qualifications and experience
- Specify the role's typical responsibilities
- Align the role with the task requirements

### 5. Include Error Handling Instructions

Explicitly instruct Claude on how to handle errors or unexpected situations:

```xml
<error_handling>
When tools fail or encounter constraints:
1. NEVER ask for human confirmation or intervention
2. If a tool requires specific parameters (e.g., minimum forecast days):
   - Automatically adjust parameters to meet the tool's requirements
   - Continue with the analysis using the adjusted parameters
3. If a tool fails completely:
   - Try ONE more time with adjusted parameters
   - If it still fails, document the failure and continue
4. ALWAYS make autonomous decisions - there is no human in the loop

<example>
INCORRECT (asking for confirmation):
"The tool requires a minimum of 5 days, but the request is for 3 days. 
Should I proceed with a 5-day forecast instead?"

CORRECT (making autonomous decision):
"I notice the tool requires a minimum of 5 days, while our request is for 3 days. 
I'll automatically adjust to use a 5-day forecast to meet the requirements and continue."
</example>
</error_handling>
```

**Best Practices:**
- Provide explicit instructions for handling common errors
- Include examples of correct and incorrect responses
- Specify when to retry vs. when to continue
- Clarify if human intervention should be requested or avoided

## Advanced Techniques

### 1. Use Examples to Demonstrate Desired Output

Examples are powerful for showing Claude exactly what you want:

```xml
<examples>
<example>
QUESTION: Should we invest in AAPL stock this quarter?
ANALYSIS:
- Current price: $175.34
- 50-day moving average: $168.92
- RSI: 62 (neutral)
- Recent earnings: Beat expectations by 8%
- Sector performance: Technology up 12% YTD
RECOMMENDATION: BUY with 75% confidence
REASONING: Strong technical indicators, positive earnings momentum, and favorable sector trends.
</example>
</examples>
```

**Best Practices:**
- Provide 2-3 diverse examples for complex tasks
- Make examples realistic and representative
- Include both input and expected output
- Highlight key formatting elements

### 2. Implement Chain-of-Thought Reasoning

Guide Claude through explicit reasoning steps:

```xml
<reasoning_steps>
When analyzing investment opportunities:
1. First, examine the company's financial fundamentals (P/E ratio, debt levels, revenue growth)
2. Next, evaluate technical indicators (price trends, volume patterns, momentum)
3. Then, consider market conditions and sector performance
4. Assess risk factors specific to the company and industry
5. Finally, weigh all factors to determine a recommendation
</reasoning_steps>
```

**Best Practices:**
- Break complex reasoning into clear sequential steps
- Explain the purpose of each step
- Include verification or validation steps
- Encourage explicit intermediate conclusions

### 3. Define Success Criteria

Clearly define what makes a successful response:

```xml
<success_criteria>
A successful investment analysis will:
- Directly answer the original question
- Include specific numerical data and metrics
- Provide a clear recommendation with confidence level
- Present balanced supporting and opposing arguments
- Maintain a professional tone appropriate for financial advisors
- Draw clear connections between data and recommendations
</success_criteria>
```

**Best Practices:**
- Be specific about quality standards
- Include both content and format requirements
- Specify any constraints or limitations
- Provide metrics for self-evaluation if possible

### 4. Use Consistent Formatting

Maintain consistent formatting throughout your prompts:

```xml
<format>
Your analysis should follow this structure:
1. SUMMARY: Brief overview of recommendation (2-3 sentences)
2. METRICS: Key financial and technical indicators in bullet points
3. RECOMMENDATION: Clear BUY/SELL/HOLD with confidence percentage
4. REASONING: 3-5 bullet points supporting your recommendation
5. RISKS: 2-3 bullet points of potential downsides or concerns
</format>
```

**Best Practices:**
- Use consistent heading levels
- Maintain consistent list formatting
- Use the same terminology throughout
- Specify exact output structure when needed

## Tool-Specific Best Practices

### 1. Tool Sequence Handling

When working with multiple tools in sequence:

```xml
<tool_sequence_handling>
When the business question specifies tools to use:
1. NEVER modify, remove, or substitute any tool names specified
2. ALWAYS pass the complete, unmodified tool sequence to the analyst
3. Ensure ALL tools mentioned are used in the EXACT order specified
4. Understand that the tool sequence is critical because:
   - Each tool provides specific data needed for comprehensive analysis
   - Tools are designed to work together in sequence for optimal results
   - Skipping tools leads to incomplete data and flawed analysis
</tool_sequence_handling>
```

**Best Practices:**
- Preserve the exact tool sequence specified in requests
- Document the purpose of each tool in the sequence
- Provide instructions for handling tool failures
- Specify how outputs from one tool should feed into the next

### 2. Autonomous Decision Making

For systems without human intervention:

```xml
<autonomous_decision_making>
You must operate completely autonomously without human intervention:
1. Make decisions independently when faced with constraints or limitations
2. Automatically adjust parameters when tools require different specifications
3. Document all adjustments and decisions in your final output
4. Continue with analysis using best available alternatives when ideal options aren't possible
5. NEVER ask for confirmation or wait for human input
</autonomous_decision_making>
```

**Best Practices:**
- Be explicit about autonomous operation requirements
- Provide clear decision-making frameworks
- Include examples of autonomous decisions
- Specify documentation requirements for decisions made

## Implementation Examples

### Example 1: Financial Analyst Prompt

```xml
<role>
You are a senior financial analyst specializing in equity research and investment recommendations.
</role>

<task>
Analyze the provided stock data and provide an investment recommendation.
</task>

<analysis_process>
1. Review the historical price and volume data
2. Calculate key technical indicators (RSI, MACD, Moving Averages)
3. Identify support and resistance levels
4. Analyze recent news sentiment
5. Compare performance against sector benchmarks
</analysis_process>

<output_format>
Provide your analysis in the following format:
- SUMMARY: Brief overview of findings
- METRICS: Key technical indicators with values
- RECOMMENDATION: Clear BUY/SELL/HOLD with confidence level (0-100%)
- REASONING: 3-5 bullet points supporting your recommendation
- RISKS: 2-3 potential concerns or counterarguments
</output_format>
```

### Example 2: Autonomous Tool Agent

```xml
<role>
You are an autonomous financial research agent with access to multiple analysis tools.
</role>

<available_tools>
- TodayDateTool: Retrieves current market date
- SimpleWorkflowTool: Performs technical analysis on stock data
- DataVizInsightTool: Generates visual insights from financial data
</available_tools>

<workflow>
1. Identify which tools are needed based on the business question
2. Execute tools in the specified sequence if provided
3. Process and analyze the data from each tool
4. Synthesize findings into a comprehensive report
</workflow>

<error_handling>
When tools fail or have constraints:
1. Automatically adjust parameters to meet requirements
2. Try once more with adjusted parameters if a tool fails
3. Document all adjustments in your final report
4. NEVER ask for human confirmation or input
</error_handling>
```

## Conclusion

Following these best practices will help create more effective, reliable, and maintainable prompts for Anthropic's Claude models. The key principles of structured XML formatting, clear instructions, contextual information, and explicit error handling will significantly improve prompt performance, especially for complex tasks requiring autonomous decision-making.

For more detailed information, refer to [Anthropic's official documentation](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/overview).

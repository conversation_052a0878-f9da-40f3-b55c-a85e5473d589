import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from .base_model import BaseModel

class CandleModel(BaseModel):
    """Pattern recognition model based on candlestick patterns and technical analysis."""
    
    description = "Technical analysis based on candlestick patterns"
    
    def __init__(self, n_estimators=100, random_state=42, min_features=2):
        self.model = RandomForestRegressor(n_estimators=n_estimators, random_state=random_state)
        self.scaler = StandardScaler()
        self.feature_importance = None
        self.min_features = min_features

    def run(self, data: pd.DataFrame, predict_column: str = 'Target', forecast_horizon: int = 1) -> tuple[pd.DataFrame, dict]:
        # Prepare data using the BaseModel method
        features, target = self.prepare_data(data, predict_column, self.min_features)

        # Fill missing values
        features = features.ffill().bfill()
        target = target.ffill().bfill()

        # Scale features
        scaled_features = self.scaler.fit_transform(features)

        # Fit the model
        self.model.fit(scaled_features, target)

        # Calculate feature importance
        self.feature_importance = pd.Series(self.model.feature_importances_, index=features.columns).sort_values(ascending=False)

        # Make historical predictions
        historical_predictions = self.model.predict(scaled_features)

        # Make future predictions
        future_predictions = []
        last_features = scaled_features[-1:].copy()
        for _ in range(forecast_horizon):
            prediction = self.model.predict(last_features)[0]
            future_predictions.append(prediction)
            last_features = np.roll(last_features, -1)
            last_features[0, -1] = prediction

        # Generate future dates
        future_dates = self.generate_future_dates(data.index[-1], forecast_horizon)

        # Prepare result DataFrame
        result = data.copy()
        result[f'{predict_column}_Predicted'] = historical_predictions

        for i, date in enumerate(future_dates):
            result.loc[date, f'{predict_column}_Predicted'] = future_predictions[i]

        # Calculate evaluation metrics
        evaluation_metrics = self.calculate_metrics(target, historical_predictions)

        return result, evaluation_metrics

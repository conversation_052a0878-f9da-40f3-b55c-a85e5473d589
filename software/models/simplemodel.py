import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from .base_model import BaseModel

class SimpleModel(BaseModel):
    """Simple statistical model using linear regression with basic transformations."""
    
    description = "Basic statistical model with trend detection"
    
    def __init__(self, min_features=2):
        self.model = LinearRegression()
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_importance = None
        self.feature_names = None
        self.min_features = min_features

    def run(self, data: pd.DataFrame, predict_column: str = 'Close', forecast_horizon: int = 1) -> tuple[pd.DataFrame, dict]:
        # Ensure data is sorted by date
        data = data.sort_index()

        # Prepare data using the BaseModel method
        features, target = self.prepare_data(data, predict_column, self.min_features)

        # Store feature names
        self.feature_names = features.columns.tolist()

        # Handle missing values
        features = self._preprocess_data(features)
        target = self._preprocess_data(target.values.reshape(-1, 1)).ravel()

        # Scale features
        scaled_features = self.scaler.fit_transform(features)

        # Fit the model
        self.model.fit(scaled_features, target)

        # Calculate feature importance
        self.feature_importance = pd.Series(self.model.coef_, index=self.feature_names).sort_values(ascending=False)

        # Make predictions on historical data
        historical_predictions = self.model.predict(scaled_features)

        # Make future predictions
        future_predictions = self._make_future_predictions(scaled_features, forecast_horizon)

        # Prepare results
        result = self._prepare_results(data, target, historical_predictions, future_predictions, predict_column)

        # Calculate evaluation metrics
        evaluation_metrics = self.calculate_metrics(target, historical_predictions)

        return result, evaluation_metrics

    def _preprocess_data(self, data):
        if isinstance(data, pd.DataFrame):
            return pd.DataFrame(self.imputer.fit_transform(data), columns=data.columns, index=data.index)
        else:
            return self.imputer.fit_transform(data.reshape(-1, 1))

    def _make_future_predictions(self, last_features, forecast_horizon):
        future_predictions = []
        current_features = last_features[-1:].copy()

        for _ in range(forecast_horizon):
            prediction = self.model.predict(current_features)[0]
            future_predictions.append(prediction)
            current_features = np.roll(current_features, -1)
            current_features[0, -1] = prediction

        return future_predictions

    def _prepare_results(self, data, target, historical_predictions, future_predictions, predict_column):
        result = data.copy()
        result[f'{predict_column}_Predicted'] = historical_predictions

        future_dates = self.generate_future_dates(data.index[-1], len(future_predictions))
        future_df = pd.DataFrame({f'{predict_column}_Predicted': future_predictions}, index=future_dates)

        return pd.concat([result, future_df])

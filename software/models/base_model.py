from abc import ABC, abstractmethod
import pandas as pd
import numpy as np

class BaseModel(ABC):
    """
    Abstract base class for all models.
    """

    @abstractmethod
    def run(self, data, predict_column=None, forecast_horizon=1):
        """
        Run the model on the given data.

        This method should encapsulate the entire process of the model,
        which may include training, prediction, and evaluation, depending
        on the specific model implementation.

        Args:
            data (pd.DataFrame): The input data.
            predict_column (str, optional): The column to predict. If None, the model should decide.
            forecast_horizon (int, optional): Number of future time steps to forecast.

        Returns:
            tuple: (result DataFrame, evaluation metrics dict)
        """
        pass

    @staticmethod
    def generate_future_dates(last_date, forecast_horizon):
        """Generate future dates for forecasting."""
        return pd.date_range(start=last_date + pd.Timedelta(days=1), periods=forecast_horizon)

    @staticmethod
    def calculate_metrics(actual, predicted):
        """Calculate evaluation metrics."""
        from sklearn.metrics import mean_squared_error, r2_score
        return {
            'MSE': mean_squared_error(actual, predicted),
            'R2': r2_score(actual, predicted)
        }

    @staticmethod
    def prepare_data(data, predict_column, min_features=0):
        """
        Prepare data for modeling, ensuring predict column is not used as a feature
        and that there are enough features for the model.

        Args:
            data (pd.DataFrame): The input data.
            predict_column (str): The column to predict.
            min_features (int): Minimum number of required features.

        Returns:
            tuple: (features DataFrame, target Series)
        """
        if predict_column not in data.columns:
            raise ValueError(f"Predict column '{predict_column}' not found in data")

        numeric_columns = data.select_dtypes(include=[np.number]).columns
        if predict_column not in numeric_columns:
            raise ValueError(f"Predict column '{predict_column}' is not numeric")

        features = data[numeric_columns].drop(columns=[predict_column])
        target = data[predict_column]

        if len(features.columns) < min_features:
            raise ValueError(f"Not enough numeric features. Required: {min_features}, Found: {len(features.columns)}")

        return features, target

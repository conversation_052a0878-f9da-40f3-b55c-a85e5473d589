import pandas as pd
import numpy as np
from sklearn.ensemble import <PERSON>ForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from .base_model import BaseModel

class MLModel(BaseModel):
    """Traditional machine learning model using Random Forest regressor."""
    
    description = "Traditional machine learning based on ensemble methods"
    
    def __init__(self, n_estimators=1000, random_state=42, min_features=2):
        self.rf_model = RandomForestRegressor(n_estimators=n_estimators, random_state=random_state)
        self.gb_model = GradientBoostingRegressor(n_estimators=n_estimators, random_state=random_state)
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_importance = None
        self.min_features = min_features

    def run(self, data: pd.DataFrame, predict_column: str = 'Target', forecast_horizon: int = 1) -> tuple[pd.DataFrame, dict]:
        # Prepare data using the BaseModel method
        features, target = self.prepare_data(data, predict_column, self.min_features)
        
        # Remove columns with all NaN values
        features = features.dropna(axis=1, how='all')
        
        # Preserve the index for later use
        feature_index = features.index
        feature_columns = features.columns
        
        # Impute missing values
        imputed_features = self.imputer.fit_transform(features)
        # Create DataFrame with original index and columns
        features = pd.DataFrame(imputed_features, index=feature_index, columns=feature_columns)
        
        target = pd.Series(self.imputer.fit_transform(target.values.reshape(-1, 1)).ravel(), index=target.index)

        # Scale features
        scaled_features = self.scaler.fit_transform(features)

        # Fit models
        self.rf_model.fit(scaled_features, target)
        self.gb_model.fit(scaled_features, target)

        # Make historical predictions
        rf_predictions = self.rf_model.predict(scaled_features)
        gb_predictions = self.gb_model.predict(scaled_features)
        historical_predictions = (rf_predictions + gb_predictions) / 2

        # Make future predictions
        future_predictions = []
        last_features = scaled_features[-1:].copy()
        for _ in range(forecast_horizon):
            rf_pred = self.rf_model.predict(last_features)[0]
            gb_pred = self.gb_model.predict(last_features)[0]
            prediction = (rf_pred + gb_pred) / 2
            future_predictions.append(prediction)
            last_features = np.roll(last_features, -1)
            last_features[0, -1] = prediction

        # Generate future dates
        future_dates = self.generate_future_dates(data.index[-1], forecast_horizon)

        # Prepare result DataFrame
        result = data.copy()
        result[f'{predict_column}_Predicted'] = historical_predictions

        for i, date in enumerate(future_dates):
            result.loc[date, f'{predict_column}_Predicted'] = future_predictions[i]

        # Calculate evaluation metrics
        evaluation_metrics = self.calculate_metrics(target, historical_predictions)

        # Calculate feature importance
        rf_importance = pd.Series(self.rf_model.feature_importances_, index=features.columns)
        gb_importance = pd.Series(self.gb_model.feature_importances_, index=features.columns)
        self.feature_importance = (rf_importance + gb_importance) / 2
        self.feature_importance = self.feature_importance.sort_values(ascending=False)

        return result, evaluation_metrics

import pandas as pd
import numpy as np
from sklearn.linear_model import LassoCV
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import r2_score
from .base_model import BaseModel

class InteractionModel(BaseModel):
    """Model that captures interactions between features using polynomial features and Lasso regression."""
    
    description = "Captures interactions between features using polynomial transformation"
    
    def __init__(self, max_degree=2, n_jobs=-1, cv=5, max_iter=10000, min_features=2):
        self.model = LassoCV(cv=cv, max_iter=max_iter, n_jobs=n_jobs)
        self.scaler = StandardScaler()
        self.poly = PolynomialFeatures(degree=max_degree, include_bias=False)
        self.n_jobs = n_jobs
        self.feature_importance = None
        self.best_features = None
        self.cv = cv
        self.min_features = min_features

    def run(self, data: pd.DataFrame, predict_column: str = 'Target', forecast_horizon: int = 1) -> tuple[pd.DataFrame, dict]:
        # Prepare data using the BaseModel method
        features, target = self.prepare_data(data, predict_column, self.min_features)

        # Fill missing values
        features = features.ffill().bfill()
        target = target.ffill().bfill()

        # Generate polynomial features
        X = self.poly.fit_transform(features)
        X = self.scaler.fit_transform(X)

        # Fit the model
        self.model.fit(X, target)

        # Calculate feature importance
        mask = self.model.coef_ != 0
        self.best_features = self.poly.get_feature_names_out(features.columns)[mask]
        importance = np.abs(self.model.coef_[mask])
        self.feature_importance = pd.Series(importance, index=self.best_features).sort_values(ascending=False)

        # Make historical predictions
        historical_predictions = self.model.predict(X)

        # Make future predictions
        future_predictions = []
        last_features = X[-1:].copy()
        for _ in range(forecast_horizon):
            prediction = self.model.predict(last_features)[0]
            future_predictions.append(prediction)
            last_features = np.roll(last_features, -1, axis=1)
            last_features[0, -1] = prediction

        # Generate future dates
        future_dates = self.generate_future_dates(data.index[-1], forecast_horizon)

        # Prepare result DataFrame
        result = data.copy()
        result[f'{predict_column}_Predicted'] = historical_predictions

        future_df = pd.DataFrame({f'{predict_column}_Predicted': future_predictions}, index=future_dates)
        result = pd.concat([result, future_df])

        # Calculate evaluation metrics
        evaluation_metrics = self.calculate_metrics(target, historical_predictions)

        return result, evaluation_metrics

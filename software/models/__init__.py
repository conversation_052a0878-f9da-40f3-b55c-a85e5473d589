import os
import importlib
from .base_model import BaseModel

MODEL_CLASSES = []

for file in os.listdir(os.path.dirname(__file__)):
    if file.endswith('.py') and file not in ['__init__.py', 'base_model.py']:
        module_name = file[:-3]
        module = importlib.import_module(f'.{module_name}', package='models')
        for name, obj in module.__dict__.items():
            if isinstance(obj, type) and issubclass(obj, BaseModel) and obj is not BaseModel:
                globals()[name] = obj
                MODEL_CLASSES.append(obj)

def get_model_descriptions():
    """Get descriptions of all available models."""
    descriptions = {}
    for cls in MODEL_CLASSES:
        # Try to get the description from the class attribute
        if hasattr(cls, 'description'):
            descriptions[cls.__name__] = cls.description
        # Fallback to class docstring
        elif cls.__doc__:
            descriptions[cls.__name__] = cls.__doc__.split('\n')[0]
        else:
            descriptions[cls.__name__] = "No description available"
    return descriptions

__all__ = [name for name, obj in globals().items()
           if isinstance(obj, type) and issubclass(obj, BaseModel) and obj is not BaseModel]
__all__.append('MODEL_CLASSES')
__all__.append('get_model_descriptions')

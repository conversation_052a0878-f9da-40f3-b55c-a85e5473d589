import logging

from numpy._typing import _32Bit
from main import StockAnalysisApp
from utils.constants import DEFAULT_TICKER, DEFAULT_PERIOD, DEFAULT_INTERVAL
from utils.feature_list import DEFAULT_FEATURES
from utils.console_output import ConsoleOutput
from models import *
from data import *

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    try:
        # Initialize StockAnalysisApp
        app = StockAnalysisApp(DEFAULT_TICKER)

        # 1. Load data
        app.load_data(LongTermInvestmentLoader)

        # 2. Add features
        app.add_features(DEFAULT_FEATURES)

        # 3. Run model
        app.run_model(TimeSeriesTransformerModel, predict='Close', forecast_horizon=7)

        # 4. Generate report and store in MongoDB
        report_id = app.generate_report_new()
        ConsoleOutput.print_info(f"Report stored with ID: {report_id}")

    except Exception as e:
        ConsoleOutput.print_error(f"An error occurred: {str(e)}")
        logging.error(f"An error occurred: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()

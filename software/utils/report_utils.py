import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error

def format_value(value):
    return f"{value:.4f}" if isinstance(value, float) else str(value)

def calculate_model_score(data, target_col):
    actual_data = data[data[target_col].notna()]
    actual = actual_data[target_col]
    predicted = actual_data[f'{target_col}_Predicted']

    r2 = 1 - ((actual - predicted) ** 2).sum() / ((actual - actual.mean()) ** 2).sum()
    mse = mean_squared_error(actual, predicted)
    mae = mean_absolute_error(actual, predicted)
    rmse = np.sqrt(mse)
    mape = np.mean(np.abs((actual - predicted) / actual)) * 100

    residuals = actual - predicted
    residual_std = np.std(residuals)

    direction_actual = np.sign(actual.diff())
    direction_predicted = np.sign(predicted.diff())
    directional_accuracy = (direction_actual == direction_predicted).mean()

    composite_score = (
        0.3 * r2 +
        0.2 * (1 / (1 + mse)) +
        0.2 * (1 / (1 + mae)) +
        0.2 * (1 / (1 + residual_std)) +
        0.1 * directional_accuracy
    ) * 100

    return {
        'R-squared': r2,
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'MAPE': mape,
        'Residual Std': residual_std,
        'Directional Accuracy': directional_accuracy,
        'Composite Score': min(max(composite_score, 0), 100)
    }

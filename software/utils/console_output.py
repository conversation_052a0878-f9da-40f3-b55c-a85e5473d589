from rich.console import Console

class ConsoleOutput:
    console = Console()

    @classmethod
    def print_success(cls, message):
        cls.console.print(f"[bold green]{message}[/bold green]")

    @classmethod
    def print_info(cls, message):
        cls.console.print(f"[bold blue]{message}[/bold blue]")

    @classmethod
    def print_warning(cls, message):
        cls.console.print(f"[bold yellow]{message}[/bold yellow]")

    @classmethod
    def print_error(cls, message):
        cls.console.print(f"[bold red]{message}[/bold red]")

    @classmethod
    def print_debug(cls, message, color="white"):
        cls.console.print(f"[{color}]{message}[/{color}]")

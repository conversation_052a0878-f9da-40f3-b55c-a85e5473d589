from datetime import datetime, timezone

def format_time_difference(start_time: datetime, end_time: datetime = None) -> str:
    if end_time is None:
        end_time = datetime.now(timezone.utc)

    # Ensure both times are timezone-aware
    if start_time.tzinfo is None:
        start_time = start_time.replace(tzinfo=timezone.utc)
    if end_time.tzinfo is None:
        end_time = end_time.replace(tzinfo=timezone.utc)

    diff = end_time - start_time
    seconds = int(diff.total_seconds())

    if seconds < 0:  # Handle case where times are out of order
        return "just now"

    minutes = seconds // 60
    hours = minutes // 60
    days = hours // 24
    weeks = days // 7

    if weeks > 0:
        return f"{weeks} week{'s' if weeks > 1 else ''} and {days % 7} day{'s' if days % 7 > 1 else ''}"
    elif days > 0:
        return f"{days} day{'s' if days > 1 else ''} and {hours % 24} hour{'s' if hours % 24 > 1 else ''}"
    elif hours > 0:
        return f"{hours} hour{'s' if hours > 1 else ''} and {minutes % 60} minute{'s' if minutes % 60 > 1 else ''}"
    elif minutes > 0:
        return f"{minutes} minute{'s' if minutes > 1 else ''}"
    else:
        return f"{seconds} second{'s' if seconds > 1 else ''}"

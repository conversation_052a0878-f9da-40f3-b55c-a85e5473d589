import asyncio
import sys
import os
from typing import List, Dict
from langchain.schema import HumanMessage
from rich.console import Console

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
sys.path.insert(0, project_root)

from software.db.llm_repository import LLMRepository
from software.ai.llm.llm_connect import get_llm_connect

console = Console()

async def list_active_llms() -> List[Dict]:
    """List all active LLM configs"""
    try:
        repo = LLMRepository()
        llms = await repo.list_llms()
        active_llms = [llm for llm in llms if llm.get("is_enabled", True)]
        
        console.print("\n[bold green]=== Active LLM Configs ===[/bold green]")
        if not active_llms:
            console.print("[yellow]No active LLM configurations found.[/yellow]")
            return []

        for llm in active_llms:
            console.print(f"""[yellow]
Config ID: {llm['_id']}
Name: {llm.get('name', 'N/A')}
Type: {llm.get('model_type', 'N/A')}
Default: {llm.get('is_default', False)}
Enabled: {llm.get('is_enabled', True)}
Created: {llm.get('created_at', 'N/A')}
Updated: {llm.get('updated_at', 'N/A')}
Connection Code Snippet: {llm.get('connection_code', '')[:80]}...
[/yellow]""")
            
        return active_llms
    except Exception as e:
        console.print(f"[bold red]Error listing LLM configs: {str(e)}[/bold red]")
        return []

async def test_llm_invoke():
    """Test each active LLM config with a simple message"""
    try:
        llm_connect = get_llm_connect()
        active_llms = await list_active_llms()
        
        if not active_llms:
            # Message already printed by list_active_llms
            return
            
        console.print("\n[bold green]=== Testing LLM Config Responses ===[/bold green]")
        
        for llm_config in active_llms:
            config_id = llm_config['_id']
            config_name = llm_config.get('name', 'Unnamed')
            model_type = llm_config.get('model_type', 'Unknown')

            console.print(f"\n[bold blue]Testing '{config_name}' (ID: {config_id}, Type: {model_type})[/bold blue]")
            
            # Only test Completion models with invoke
            if model_type != "Completion":
                console.print(f"[cyan]Skipping invoke test for non-Completion model.[/cyan]")
                continue
                
            try:
                # Get the specific LLM instance by its config ID
                llm_instance = await llm_connect.get_llm_by_id(config_id)
                
                if not llm_instance:
                    console.print(f"[red]Failed to load LLM instance for ID {config_id}[/red]")
                    continue

                if not isinstance(llm_instance, BaseChatModel):
                    console.print(f"[red]Loaded instance for {config_id} is not a Chat Model ({type(llm_instance)}). Skipping invoke.[/red]")
                    continue

                messages = [HumanMessage(content="\no_think Hi")]
                
                start_time = asyncio.get_event_loop().time()
                # Use the specific instance
                response = await llm_instance.ainvoke(messages)
                end_time = asyncio.get_event_loop().time()
                
                response_content = getattr(response, 'content', str(response))

                console.print(f"""[green]
Response: {response_content}
Time taken: {end_time - start_time:.2f} seconds
[/green]""")
                
            except Exception as e:
                console.print(f"[red]Error testing config '{config_name}' (ID: {config_id}): {str(e)}[/red]")
                # Optionally print traceback for debugging
                # import traceback
                # console.print(traceback.format_exc())
                
    except Exception as e:
        console.print(f"[bold red]Error in test execution: {str(e)}[/bold red]")

async def main():
    """Main test function"""
    try:
        await test_llm_invoke()
    except Exception as e:
        console.print(f"[bold red]Main execution error: {str(e)}[/bold red]")

if __name__ == "__main__":
    # Add BaseChatModel import here for the isinstance check
    from langchain.chat_models.base import BaseChatModel
    asyncio.run(main()) 
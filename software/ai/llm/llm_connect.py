import warnings
from typing import Optional, Dict, Any, List, Union
from langchain.schema import BaseMessage
from db.llm_repository import LLMRepository
from db.research_director_repository import ResearchDirectorRepository
from langchain.chat_models.base import BaseChatModel
from langchain.embeddings.base import Embeddings
import asyncio
import os
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
from rich.style import Style
from db.graph_repository import GraphRepository
import traceback
from bson import ObjectId
import rich
from rich.pretty import Pretty
import random

# Import necessary classes for the exec context
from langchain_nvidia_ai_endpoints import ChatNVIDIA
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq
from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_google_genai import ChatGoogleGenerativeAI

console = Console()

def format_llm_info(model: str, temperature: float, llm_id: str, source: Optional[str] = None, stage_name: Optional[str] = None, model_class: str = None, model_type: str = "Completion") -> Panel:
    """Create a beautifully formatted panel for LLM information, including source and stage."""
    # Check if it's a thinking model
    thinking_models = ["deepseek-r1:1.5b"]  # Add other thinking models here
    is_thinking = model in thinking_models

    title_text = f"🤖 {source or 'Unknown Source'} {model_type} Model Connected"
    title = Text(title_text, style="bold yellow")

    # Create the model info content with nice formatting
    content_lines = [
        ("Model: ", "bold yellow"), (f"{model}\n", "bright_white"),
        ("Class: ", "bold yellow"), (f"{model_class or 'Unknown'}\n", "bright_white"),
        ("Temperature: ", "bold yellow"), (f"{temperature:.2f}\n", "bright_white"),
        ("LLM ID: ", "bold yellow"), (f"{llm_id}\n", "bright_white"),
    ]

    if stage_name:
        content_lines.append(("Stage Name: ", "bold yellow"))
        content_lines.append((f"{stage_name}\n", "bright_white"))

    content_lines.extend([
        ("Source: ", "bold yellow"), (f"{source or 'N/A'}\n", "bright_white"),
        ("Thinking Model: ", "bold yellow"), (f"{is_thinking}", "bright_white")
    ])

    content = Text.assemble(*content_lines)

    return Panel(
        content,
        title=title,
        border_style="yellow",
        padding=(1, 2),
        expand=False
    )

class LLMConnect:
    def __init__(self):
        self._llm_cache = {}
        self._embedding_cache = {}
        self._repository = LLMRepository()
        self._graph_repo = GraphRepository()
        self._director_repo = ResearchDirectorRepository()
        self._thinking_models = {"deepseek-r1:1.5b"}  # Set of models that use thinking tags

    def _clean_thinking_output(self, content: str) -> str:
        """Clean output from thinking models by removing <think> tags and their contents."""
        import re
        # Remove <think> blocks and their contents
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
        # Clean up any remaining whitespace and ensure single newlines
        content = re.sub(r'\n\s*\n', '\n', content.strip())
        # If content is empty after cleaning, return original content
        return content if content.strip() else content

    async def get_llm_by_graph_stage(self, graph_name: str, stage_name: str) -> Optional[Union[BaseChatModel, Embeddings]]:
        """Get an LLM or Embedding instance configured for a specific graph and stage."""
        try:
            cache_key = f"{graph_name}_{stage_name}"
            if cache_key in self._llm_cache:
                return self._llm_cache[cache_key]

            if cache_key in self._embedding_cache:
                return self._embedding_cache[cache_key]

            # Get the graph configuration
            graph = await self._graph_repo.get_graph(graph_name)
            if not graph:
                console.print(Panel(f"⚠️  Graph not found: {graph_name}", style="yellow"))
                return await self.get_llm()

            # Find the stage configuration
            stage = next((s for s in graph.get("stages", []) if s["name"] == stage_name), None)
            if not stage:
                console.print(Panel(f"⚠️  Stage not found in graph: {stage_name}", style="yellow"))
                return await self.get_llm()

            # Check if the stage has a specific LLM configured
            llm_id = stage.get("llm_id")
            if llm_id:
                model = await self.get_llm_by_id(llm_id, source="Stage", stage_name=stage_name)
                if model:
                    # Store in appropriate cache based on model type
                    if isinstance(model, Embeddings):
                        self._embedding_cache[cache_key] = model
                    else:
                        self._llm_cache[cache_key] = model
                    return model

            # If no specific model is configured or it failed, check the model type
            model_type = stage.get("model_type")
            if model_type == "Embedding":
                return await self.get_embedding()
            else:
                return await self.get_llm()

        except Exception as e:
            console.print(Panel(f"❌ Error getting model for graph stage: {str(e)}", style="yellow"))
            # Try to determine if this is an embedding stage based on the error
            try:
                if stage_name and isinstance(stage_name, dict) and stage_name.get("model_type") == "Embedding":
                    return await self.get_embedding()
            except:
                pass
            return await self.get_llm()

    async def get_llm_for_stage(self, director_id: str, stage: str) -> Optional[Union[BaseChatModel, Embeddings]]:
        """
        Get an LLM or Embedding instance for a stage based on director's configuration.
        This function:
        1. Gets the director's configuration
        2. Finds the associated graph type
        3. Gets the LLM or Embedding model configured for that graph and stage

        Args:
            director_id (str): The ID of the director
            stage (str): The stage name

        Returns:
            Optional[Union[BaseChatModel, Embeddings]]: A configured LLM or Embedding instance or default LLM if not found
        """
        try:
            # --- Debug Start --- Removed
            # console.print(Panel(
            #     f"Director ID: {director_id}\nStage: {stage}",
            #     title="[cyan]get_llm_for_stage: Inputs[/cyan]",
            #     border_style="cyan"
            # ))
            # --- Debug End --- Removed
            director = await self._director_repo.get_director(director_id)
            # --- Debug Start --- Removed
            # console.print(Panel(
            #     Pretty(director, expand_all=True) if director else "Director not found",
            #     title="[cyan]get_llm_for_stage: Director Config[/cyan]",
            #     border_style="cyan"
            # ))
            # --- Debug End --- Removed
            if not director:
                # console.print(Panel("Director not found, falling back to default LLM", title="[yellow]Fallback[/yellow]")) # Removed fallback print
                return await self.get_llm()

            graph_type = director.get("graph_type")
            # --- Debug Start --- Removed
            # console.print(Panel(
            #     f"Graph Type from Director: {graph_type}",
            #     title="[cyan]get_llm_for_stage: Graph Type[/cyan]",
            #     border_style="cyan"
            # ))
            # --- Debug End --- Removed
            if not graph_type:
                # console.print(Panel("Graph Type not found in Director config, falling back to default LLM", title="[yellow]Fallback[/yellow]")) # Removed fallback print
                return await self.get_llm()

            # Fetch the graph document by name to get its ID
            graph_doc = await self._graph_repo.get_graph_by_name(graph_type)
            # --- Debug Start --- Removed
            # console.print(Panel(
            #     Pretty(graph_doc, expand_all=True) if graph_doc else f"Graph document not found for name: {graph_type}",
            #     title="[cyan]get_llm_for_stage: Graph Doc by Name[/cyan]",
            #     border_style="cyan"
            # ))
            # --- Debug End --- Removed
            if not graph_doc or '_id' not in graph_doc:
                console.print(Panel(f"❌ Graph document not found or missing ID for name: {graph_type}. Falling back to default LLM", title="[yellow]Fallback[/yellow]")) # Keep original error print
                # Fallback to default LLM if graph lookup fails
                return await self.get_llm()
            graph_id = str(graph_doc['_id']) # Use the actual ID
            # --- Debug Start --- Removed
            # console.print(Panel(
            #     f"Resolved Graph ID: {graph_id}",
            #     title="[cyan]get_llm_for_stage: Resolved Graph ID[/cyan]",
            #     border_style="cyan"
            # ))
            # --- Debug End --- Removed

            # Get the model using the graph_id
            model = await self.get_llm_by_graph_stage(graph_id, stage)
            # --- Debug Start --- Removed
            # console.print(Panel(
            #     model_info,
            #     title="[cyan]get_llm_for_stage: Model from Graph Stage[/cyan]",
            #     border_style="cyan"
            # ))
            # --- Debug End --- Removed

            # If model is None, check if we need an embedding model instead
            if model is None:
                # --- Debug Start --- Removed
                # console.print(Panel("Model is None, checking if stage requires Embedding model", title="[yellow]Checking Embedding[/yellow]"))
                # --- Debug End --- Removed
                # Check if this stage is marked for embedding in the graph configuration
                # Use graph_id for the lookup here as well
                graph = await self._graph_repo.get_graph(graph_id)
                stage_config = next((s for s in graph.get("stages", []) if s["name"] == stage), None) if graph else None
                # --- Debug Start --- Removed
                # console.print(Panel(
                #     Pretty(stage_config, expand_all=True) if stage_config else f"No stage config found for stage '{stage}' in graph {graph_id}",
                #     title="[yellow]Embedding Check: Stage Config[/yellow]",
                #     border_style="yellow"
                # ))
                # --- Debug End --- Removed

                if stage_config and stage_config.get("model_type") == "Embedding":
                    # This stage needs an embedding model
                    # --- Debug Start --- Removed
                    # console.print(Panel("Stage requires Embedding model, attempting to fetch default Embedding model", title="[yellow]Fetching Embedding[/yellow]"))
                    # --- Debug End --- Removed
                    embedding_model = await self.get_embedding()
                    # --- Debug Start --- Removed
                    # console.print(Panel(
                    #     f"Returning Embedding model: {type(embedding_model)}",
                    #     title="[green]Returning Model[/green]",
                    #     border_style="green"
                    # ))
                    # --- Debug End --- Removed
                    return embedding_model
                else:
                    # --- Debug Start --- Removed
                    # console.print(Panel("Stage does not require Embedding model or config not found, returning None (will likely fallback later)", title="[yellow]Embedding Check Result[/yellow]"))
                    # --- Debug End --- Removed
                    pass # No action needed here if not embedding

            # --- Debug Start --- Removed
            # console.print(Panel(
            #     f"Returning model: {type(model)}",
            #     title="[green]Returning Model[/green]",
            #     border_style="green"
            # ))
            # --- Debug End --- Removed
            return model

        except Exception as e:
            console.print(Panel(f"❌ Error getting model for stage: {str(e)}", style="yellow")) # Keep original error print
            return await self.get_llm()

    async def get_llm_by_id(self, llm_id: str, source: Optional[str] = None, stage_name: Optional[str] = None) -> Optional[Union[BaseChatModel, Embeddings]]:
        """
        Get an LLM or Embedding instance by its ID using stored connection code.

        Args:
            llm_id (str): The ID of the LLM model config to use.
            source (Optional[str]): The source of the LLM (e.g., "Stage" or "Default").
            stage_name (Optional[str]): The name of the stage associated with the LLM.

        Returns:
            Optional[Union[BaseChatModel, Embeddings]]: A configured LLM or Embedding instance or None if config not found, disabled, or connection code fails.
        """
        # Check cache first
        if llm_id in self._llm_cache:
            return self._llm_cache[llm_id]
        if llm_id in self._embedding_cache:
            return self._embedding_cache[llm_id]

        try:
            # Silently load model without intermediate output
            with Progress(
                SpinnerColumn(),
                TextColumn("[bold yellow]Loading model..."),
                TimeElapsedColumn(),
                console=console
            ) as progress:
                task = progress.add_task("Loading", total=1)

                # Fetch model config
                llm_config = await self._repository.get_llm(llm_id)

                if not llm_config or not llm_config.get("is_enabled", True):
                    if not llm_config:
                        console.print(Panel(f"⚠️ LLM config {llm_id} not found", style="yellow"))
                    else:
                        console.print(Panel(f"⚠️ LLM config {llm_id} is disabled", style="yellow"))
                    return None

                connection_code = llm_config.get("connection_code")
                if not connection_code or not connection_code.strip():
                    console.print(Panel(f"❌ Missing connection code for LLM config {llm_id}", style="yellow"))
                    return None

                # Prepare execution context
                exec_context = {
                    "config": llm_config,
                    "ChatNVIDIA": ChatNVIDIA,
                    "ChatOpenAI": ChatOpenAI,
                    "ChatGroq": ChatGroq,
                    "ChatOllama": ChatOllama,
                    "OllamaEmbeddings": OllamaEmbeddings,
                    "ChatGoogleGenerativeAI": ChatGoogleGenerativeAI,
                    "BaseChatModel": BaseChatModel,
                    "Embeddings": Embeddings,
                    "os": os,
                }
                local_vars = {}

                # Execute connection code
                try:
                    exec(connection_code, exec_context, local_vars)
                except Exception as e:
                    console.print(Panel(f"❌ Error executing connection code: {str(e)}", style="yellow"))
                    return None

                model_instance = local_vars.get("model")
                if not model_instance:
                    console.print(Panel(f"❌ Connection code for {llm_id} did not define a 'model' variable", style="yellow"))
                    return None

                if not isinstance(model_instance, (BaseChatModel, Embeddings)):
                    console.print(Panel(f"❌ Created object for {llm_id} is not a valid Langchain ChatModel or Embeddings instance", style="yellow"))
                    return None

                # Finalize model setup and complete progress bar silently
                progress.update(task, completed=1)

                # Cache the successfully created model
                model_type = llm_config.get("model_type", "Completion") # Default to Completion if type missing
                if isinstance(model_instance, Embeddings) or model_type == "Embedding":
                    self._embedding_cache[llm_id] = model_instance
                    # Display simple panel for embedding models
                    console.print(format_llm_info(
                        model=llm_config.get('name', 'Unknown'),
                        temperature=0.0, # Embeddings don't have temperature
                        llm_id=llm_id, # Pass llm_id
                        source=source, # Pass source
                        stage_name=stage_name, # Pass stage_name
                        model_class=model_instance.__class__.__name__,
                        model_type="Embedding"
                    ))
                else:
                    self._llm_cache[llm_id] = model_instance
                    # Display detailed panel for LLM models
                    console.print(format_llm_info(
                        model=llm_config.get('name', 'Unknown'),
                        temperature=llm_config.get('temperature', 0.0),
                        llm_id=llm_id, # Pass llm_id
                        source=source, # Pass source
                        stage_name=stage_name, # Pass stage_name
                        model_class=model_instance.__class__.__name__
                    ))

            return model_instance

        except Exception as e:
            console.print(Panel(f"❌ Error creating model instance: {str(e)}", style="yellow"))
            return None

    async def get_random_llm_from_list(self, llm_ids: List[str], source: Optional[str] = None, stage_name: Optional[str] = None) -> Optional[Union[BaseChatModel, Embeddings]]:
        """
        Selects a random LLM ID from the provided list and gets the instance.

        Args:
            llm_ids (List[str]): A list of LLM model config IDs.
            source (Optional[str]): The source of the LLM (e.g., "Stage" or "Default").
            stage_name (Optional[str]): The name of the stage associated with the LLM.

        Returns:
            Optional[Union[BaseChatModel, Embeddings]]: A configured LLM or Embedding instance or None if the list is empty or the selected ID fails.
        """
        if not llm_ids:
            console.print(Panel("⚠️ LLM ID list is empty, cannot select a random model.", style="yellow"))
            return None

        selected_llm_id = random.choice(llm_ids)
        # console.print(Panel(f"Randomly selected LLM ID: {selected_llm_id} from list: {llm_ids}", title="[magenta]Random Selection[/magenta]", border_style="magenta")) # Debug print removed
        return await self.get_llm_by_id(selected_llm_id, source=source, stage_name=stage_name)

    async def get_connection(self, model_type: str, stage_id: Optional[str] = None) -> Optional[Union[BaseChatModel, Embeddings]]:
        """
        Gets a Langchain connection (ChatModel or Embeddings) based on model type
        and optional stage ID, falling back to the default.

        Args:
            model_type (str): "Completion" or "Embedding".
            stage_id (Optional[str]): The ID of the graph stage to check for specific config.

        Returns:
            Optional[Union[BaseChatModel, Embeddings]]: The connection instance or None.
        """
        model_instance: Optional[Union[BaseChatModel, Embeddings]] = None
        llm_id_to_use: Optional[str] = None
        source: str = "Default"

        # Try stage-specific model if stage_id is provided
        if stage_id:
            try:
                stage_config = await self._graph_repo.get_stage_config(stage_id)
                if stage_config and stage_config.get("llm_id"):
                    stage_llm_id = stage_config["llm_id"]
                    # Call get_llm_by_id with Stage context
                    specific_model = await self.get_llm_by_id(stage_llm_id, source="Stage", stage_name=stage_id)

                    if specific_model:
                        config = await self._repository.get_llm(stage_llm_id)
                        actual_type = config.get("model_type") if config else None

                        if actual_type == model_type:
                            model_instance = specific_model
                            llm_id_to_use = stage_llm_id
                            source = f"Stage {stage_id}"
            except Exception:
                pass

        # Fallback to default if needed - silently query without debug output
        if not model_instance:
            try:
                # This is a silent operation now - no debug panels
                default_config = await self._repository.get_default_llm(model_type, debug=False)

                if default_config and default_config.get("_id"):
                    default_llm_id = default_config["_id"]
                    # Call get_llm_by_id with Default context
                    model_instance = await self.get_llm_by_id(default_llm_id, source="Default")
                    if model_instance:
                        # source = f"Default" # Source is now set in the call above
                        llm_id_to_use = default_llm_id
                else:
                    error_title = Text(f"⚠️ No {model_type} Configuration Found", style="bold yellow")
                    error_content = Text.assemble(
                        (f"No {model_type} model is configured as default.\n\n", "white"),
                        ("Please set up a default model at ", "white"),
                        ("/llm", "bold yellow"),
                        (" endpoint.", "white")
                    )
                    console.print(Panel(
                        error_content,
                        title=error_title,
                        border_style="yellow",
                        padding=(1, 2),
                        expand=False
                    ))
            except Exception as e:
                console.print(Panel(f"❌ Error finding default model: {str(e)}", style="yellow"))

        if not model_instance:
            console.print(Panel(f"❌ Could not establish {model_type} connection", style="yellow"))

        return model_instance

    async def get_llm(self, stage_id: Optional[str] = None) -> Optional[BaseChatModel]:
        """Gets the appropriate Completion LLM connection."""
        connection = await self.get_connection(model_type="Completion", stage_id=stage_id)
        if connection and isinstance(connection, BaseChatModel):
            return connection
        elif connection:
            console.print(Panel(f"⚠️ Expected BaseChatModel but received {type(connection)}", style="yellow"))
            raise ValueError(f"Expected BaseChatModel but received {type(connection)}")
        else:
            error_title = Text("❌ No Default LLM Configured", style="bold yellow")
            error_content = Text.assemble(
                ("No default Completion model is configured. Please configure at /llm endpoint.", "white")
            )
            console.print(Panel(
                error_content,
                title=error_title,
                border_style="yellow",
                padding=(1, 2),
                expand=False
            ))
            raise ValueError("No default Completion LLM configured. Please configure a default LLM via the /llm endpoint.")

    async def get_embedding(self, stage_id: Optional[str] = None) -> Optional[Embeddings]:
        """Gets the appropriate Embedding model connection."""
        connection = await self.get_connection(model_type="Embedding", stage_id=stage_id)
        if connection and isinstance(connection, Embeddings):
            return connection
        elif connection:
            console.print(Panel(f"⚠️ Expected Embeddings but received {type(connection)}", style="yellow"))
            raise ValueError(f"Expected Embeddings but received {type(connection)}")
        else:
            error_title = Text("❌ No Default Embedding Model Configured", style="bold yellow")
            error_content = Text.assemble(
                ("No default Embedding model is configured. Please configure at /llm endpoint.", "white")
            )
            console.print(Panel(
                error_content,
                title=error_title,
                border_style="yellow",
                padding=(1, 2),
                expand=False
            ))
            raise ValueError("No default Embedding model configured. Please configure a default Embedding model via the /llm endpoint.")

    async def ainvoke(self, messages: list[BaseMessage], stage_id: Optional[str] = None, **kwargs) -> Any:
        """Invokes the default completion LLM or stage-specific if ID provided."""
        try:
            llm = await self.get_llm(stage_id=stage_id)
            return await llm.ainvoke(messages, **kwargs)
        except ValueError as e:
            console.print(Panel(f"❌ Error during LLM invocation: {str(e)}", style="yellow"))
            raise

    async def embed_documents(self, texts: List[str], stage_id: Optional[str] = None) -> Optional[List[List[float]]]:
        """Embeds documents using the appropriate embedding model."""
        try:
            embedding_model = await self.get_embedding(stage_id=stage_id)
            return await embedding_model.aembed_documents(texts)
        except ValueError as e:
            console.print(Panel(f"❌ Error embedding documents: {str(e)}", style="yellow"))
            raise
        except Exception as e:
            console.print(Panel(f"❌ Error embedding documents: {str(e)}", style="yellow"))
            return None

    async def embed_query(self, text: str, stage_id: Optional[str] = None) -> Optional[List[float]]:
        """Embeds a query using the appropriate embedding model."""
        try:
            embedding_model = await self.get_embedding(stage_id=stage_id)
            return await embedding_model.aembed_query(text)
        except ValueError as e:
            console.print(Panel(f"❌ Error embedding query: {str(e)}", style="yellow"))
            raise
        except Exception as e:
            console.print(Panel(f"❌ Error embedding query: {str(e)}", style="yellow"))
            return None

    async def embed_query_default(self, text: str) -> Optional[List[float]]:
        """Embeds a query using the default embedding model."""
        return await self.embed_query(text=text, stage_id=None)

    def embed_query_default_sync(self, text: str) -> Optional[List[float]]:
        """Embeds a query using the default embedding model (synchronous version)."""
        embedding_model = self.get_embedding_sync()
        if embedding_model is None:
            return None
        return embedding_model.embed_query(text)

    def get_connection_sync(self, model_type: str) -> Optional[Union[BaseChatModel, Embeddings]]:
        """Gets a Langchain connection (ChatModel or Embeddings) synchronously based on model type.

        This is a synchronous version of get_connection that doesn't use async/await.
        It's designed to be used in contexts where async/await is not available.

        Args:
            model_type (str): "Completion" or "Embedding".

        Returns:
            Optional[Union[BaseChatModel, Embeddings]]: The connection instance or None.
        """
        try:
            # Get the default LLM config from MongoDB using the sync method
            config = self._repository.get_default_llm_sync(model_type, debug=False)

            if not config:
                console.print(Panel(f"⚠️ No default {model_type} model configured", style="yellow"))
                return None

            llm_id = config.get("_id")
            connection_code = config.get("connection_code", "")

            if not connection_code:
                console.print(Panel(f"❌ No connection code for {model_type} model {llm_id}", style="yellow"))
                return None

            # Set up execution context with necessary imports
            exec_context = {
                "config": config,
                "ChatNVIDIA": ChatNVIDIA,
                "ChatOpenAI": ChatOpenAI,
                "ChatGroq": ChatGroq,
                "ChatOllama": ChatOllama,
                "OllamaEmbeddings": OllamaEmbeddings,
                "ChatGoogleGenerativeAI": ChatGoogleGenerativeAI,
                "BaseChatModel": BaseChatModel,
                "Embeddings": Embeddings,
                "os": os,
            }
            local_vars = {}

            # Execute the connection code
            try:
                exec(connection_code, exec_context, local_vars)
            except Exception as e:
                console.print(Panel(f"❌ Error executing connection code: {str(e)}", style="yellow"))
                return None

            model_instance = local_vars.get("model")
            if not model_instance:
                console.print(Panel(f"❌ Connection code for {llm_id} did not define a 'model' variable", style="yellow"))
                return None

            # Verify the model instance is of the correct type
            if model_type == "Embedding" and not isinstance(model_instance, Embeddings):
                console.print(Panel(f"❌ Created object for {llm_id} is not a valid Embeddings instance", style="yellow"))
                return None
            elif model_type == "Completion" and not isinstance(model_instance, BaseChatModel):
                console.print(Panel(f"❌ Created object for {llm_id} is not a valid BaseChatModel instance", style="yellow"))
                return None

            return model_instance

        except Exception as e:
            console.print(Panel(f"❌ Error getting sync {model_type} model: {str(e)}", style="yellow"))
            return None

    def get_embedding_sync(self) -> Optional[Embeddings]:
        """Gets a synchronous embedding model using the default model config from MongoDB."""
        model = self.get_connection_sync("Embedding")
        if model and isinstance(model, Embeddings):
            return model
        return None

    def get_default_llm_sync(self, llm_type: str, debug: bool = True) -> Optional[Dict]:
        """Get the default LLM model for a given type (sync version)"""
        return self._repository.get_default_llm_sync(llm_type, debug)

    def embed_documents_default_sync(self, texts: List[str]) -> Optional[List[List[float]]]:
        """Embeds documents using the default embedding model (synchronous version).

        This function is designed to be used with ClusterSemanticChunker which expects a synchronous
        embedding function that takes a list of texts and returns a list of embeddings.
        """
        embedding_model = self.get_embedding_sync()
        if embedding_model is None:
            console.print(Panel("❌ No embedding model available for synchronous document embedding", style="yellow"))
            return None

        try:
            # Process the texts in batches to avoid overwhelming the model
            batch_size = 32  # Adjust based on your embedding model's capabilities
            all_embeddings = []

            for i in range(0, len(texts), batch_size):
                batch = texts[i:i+batch_size]
                batch_embeddings = embedding_model.embed_documents(batch)
                all_embeddings.extend(batch_embeddings)

            return all_embeddings
        except Exception as e:
            console.print(Panel(f"❌ Error in synchronous document embedding: {str(e)}", style="yellow"))
            return None

    async def embed_documents_default(self, texts: List[str]) -> Optional[List[List[float]]]:
        """Embeds documents using the default embedding model."""
        return await self.embed_documents(texts=texts, stage_id=None)

    async def ainvoke_for_graph(self, graph_name: str, stage_name: str, messages: list[BaseMessage], stage_id: Optional[str] = None, **kwargs) -> Any:
        """Invoke the appropriate LLM for a specific graph stage, using stage_id if provided."""
        # Determine model type (assume Completion unless stage indicates otherwise)
        model_type = "Completion" # Default
        try:
            # Check graph config for explicit type
            graph = await self._graph_repo.get_graph(graph_name)
            if graph:
                stage_conf = next((s for s in graph.get("stages", []) if s.get("name") == stage_name), None)
                if stage_conf and stage_conf.get("model_type") == "Embedding":
                     model_type = "Embedding"
        except Exception:
            pass

        try:
            # Get the connection silently
            llm = await self.get_connection(model_type=model_type, stage_id=stage_id)

            if not llm:
                raise ValueError(f"Could not get {model_type} LLM connection for graph '{graph_name}', stage '{stage_name}'")

            # Ensure we have a ChatModel for invoking
            if not isinstance(llm, BaseChatModel):
                 raise TypeError(f"Expected a BaseChatModel for invoking, but got {type(llm)} for stage {stage_name}")

            # Handle thinking models
            model_name = self._llm_cache.get(getattr(llm, '_llm_id', None), {}).get("model_name", "Unknown")
            is_thinking = self._is_thinking_model(model_name)

            # Prepare input for thinking models
            if is_thinking and messages:
                last_message_content = messages[-1].content
                messages[-1].content = f"<think>Analyze the request and plan the response.</think> {last_message_content}"

            # Invoke the model silently
            response = await llm.ainvoke(messages, **kwargs)

            # Clean output if it was a thinking model
            if is_thinking and hasattr(response, 'content'):
                response.content = self._clean_thinking_output(response.content)

            return response
        except (ValueError, TypeError) as e:
            console.print(Panel(f"❌ Error with LLM for graph stage: {str(e)}", style="yellow"))
            raise
        except Exception as e:
            console.print(Panel(f"❌ Unexpected error with LLM for graph stage: {str(e)}", style="yellow"))
            raise

    def _is_thinking_model(self, model_name: str) -> bool:
        """Check if the model is a thinking model that uses thinking tags."""
        return model_name in self._thinking_models

# Create a singleton instance
_llm_connect: Optional[LLMConnect] = None

def get_llm_connect() -> LLMConnect:
    """Get or create the LLMConnect singleton instance"""
    global _llm_connect
    if _llm_connect is None:
        _llm_connect = LLMConnect()
    return _llm_connect

# LLM Connection Module

This directory contains the code for connecting to LLM models configured in the database.

## Files

- `llm_connect.py`: Original implementation (legacy)
- `llm_connect_v2.py`: New, cleaner implementation
- `test_llm.py`: Test script for the original implementation
- `test_llm_v2.py`: Test script for the new implementation

## Usage

### Getting a Model Connector

```python
from software.ai.llm.llm_connect_v2 import get_model_connector

# Get the singleton instance
connector = get_model_connector()
```

### Getting a Model by ID

```python
# Async
model = await connector.get_model_by_id("model_id")

# Sync
model = connector.get_model_by_id_sync("model_id")
```

### Getting Default Models

```python
# Async
completion_model = await connector.get_completion_model()
embedding_model = await connector.get_embedding_model()

# Sync
completion_model = connector.get_completion_model_sync()
embedding_model = connector.get_embedding_model_sync()
```

### Embedding Text

```python
# Async
embedding = await connector.embed_query("This is a test query")
embeddings = await connector.embed_documents(["Document 1", "Document 2"])

# Sync
embedding = connector.embed_query_sync("This is a test query")
embeddings = connector.embed_documents_sync(["Document 1", "Document 2"])
```

### Invoking a Chat Model

```python
from langchain.schema import HumanMessage

messages = [HumanMessage(content="Hello, how are you?")]
response = await connector.invoke_chat(messages)
```

### Getting a Model for a Graph Stage

```python
model = await connector.get_model_for_graph_stage("graph_name", "stage_name")
```

## Architecture

The new implementation is organized into three main classes:

1. `ModelFactory`: Creates model instances from connection code
2. `ModelCache`: Caches model instances for reuse
3. `ModelConnector`: Main interface for getting model instances

This design provides better separation of concerns, making the code more maintainable and easier to understand.

## Model Configuration

Models are configured in the database with the following fields:

- `name`: The name of the model
- `model_type`: The type of model (Completion or Embedding)
- `connection_code`: Python code that creates a model instance
- `is_default`: Whether the model is the default for its type
- `is_enabled`: Whether the model is active

The connection code should define a variable named `model` that is an instance of `BaseChatModel` for Completion models or `Embeddings` for Embedding models.

Example connection code for a Completion model:

```python
from langchain_openai import ChatOpenAI

model = ChatOpenAI(
    model="gpt-3.5-turbo",
    openai_api_key="YOUR_API_KEY",
    temperature=0.7,
    max_tokens=4096
)
```

Example connection code for an Embedding model:

```python
from langchain_ollama import OllamaEmbeddings

model = OllamaEmbeddings(
    model="nomic-embed-text"
)
```

## Testing

To test the new implementation, run:

```bash
python -m software.ai.llm.test_llm_v2
```

This will test all the functionality of the new implementation, including:

- Getting models by ID
- Getting default models
- Invoking chat models
- Embedding text

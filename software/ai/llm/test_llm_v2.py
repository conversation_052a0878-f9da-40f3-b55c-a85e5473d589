"""
Test script for llm_connect_v2.py

This script tests the functionality of the new ModelConnector class.
"""

import asyncio
import sys
import os
import logging
from typing import List, Dict
from langchain.schema import HumanMessage

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
sys.path.insert(0, project_root)

from software.db.llm_repository import LLMRepository
from software.ai.llm.llm_connect_v2 import get_model_connector
from langchain.chat_models.base import BaseChatModel
from langchain.embeddings.base import Embeddings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Also print to console
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
logger.addHandler(console_handler)

async def list_active_llms() -> List[Dict]:
    """List all active LLM configs"""
    try:
        repo = LLMRepository()
        llms = await repo.list_llms()
        active_llms = [llm for llm in llms if llm.get("is_enabled", True)]

        logger.info("=== Active LLM Configs ===")
        if not active_llms:
            logger.warning("No active LLM configurations found.")
            return []

        for llm in active_llms:
            logger.info(f"""
Config ID: {llm['_id']}
Name: {llm.get('name', 'N/A')}
Type: {llm.get('model_type', 'N/A')}
Default: {llm.get('is_default', False)}
Enabled: {llm.get('is_enabled', True)}
Created: {llm.get('created_at', 'N/A')}
Updated: {llm.get('updated_at', 'N/A')}
Connection Code Snippet: {llm.get('connection_code', '')[:80]}...
""")

        return active_llms
    except Exception as e:
        logger.error(f"Error listing LLM configs: {str(e)}")
        return []

async def test_get_model_by_id():
    """Test getting models by ID"""
    try:
        connector = get_model_connector()
        active_llms = await list_active_llms()

        if not active_llms:
            return

        logger.info("=== Testing get_model_by_id ===")

        for llm_config in active_llms:
            config_id = llm_config['_id']
            config_name = llm_config.get('name', 'Unnamed')
            model_type = llm_config.get('model_type', 'Unknown')

            logger.info(f"Testing '{config_name}' (ID: {config_id}, Type: {model_type})")

            try:
                # Get the model instance by its config ID
                model_instance = await connector.get_model_by_id(config_id)

                if not model_instance:
                    logger.error(f"Failed to load model instance for ID {config_id}")
                    continue

                if model_type == "Completion":
                    if isinstance(model_instance, BaseChatModel):
                        logger.info(f"Successfully loaded BaseChatModel: {type(model_instance).__name__}")
                    else:
                        logger.error(f"Expected BaseChatModel but got {type(model_instance).__name__}")
                elif model_type == "Embedding":
                    if isinstance(model_instance, Embeddings):
                        logger.info(f"Successfully loaded Embeddings: {type(model_instance).__name__}")
                    else:
                        logger.error(f"Expected Embeddings but got {type(model_instance).__name__}")

            except Exception as e:
                logger.error(f"Error testing config '{config_name}' (ID: {config_id}): {str(e)}")

    except Exception as e:
        logger.error(f"Error in test execution: {str(e)}")

async def test_default_models():
    """Test getting default models"""
    try:
        connector = get_model_connector()

        logger.info("=== Testing Default Models ===")

        # Test default completion model
        try:
            logger.info("Testing default completion model...")
            completion_model = await connector.get_completion_model()
            if completion_model:
                logger.info(f"Successfully loaded default completion model: {type(completion_model).__name__}")
            else:
                logger.warning("No default completion model available")
        except Exception as e:
            logger.error(f"Error getting default completion model: {str(e)}")

        # Test default embedding model
        try:
            logger.info("Testing default embedding model...")
            embedding_model = await connector.get_embedding_model()
            if embedding_model:
                logger.info(f"Successfully loaded default embedding model: {type(embedding_model).__name__}")
            else:
                logger.warning("No default embedding model available")
        except Exception as e:
            logger.error(f"Error getting default embedding model: {str(e)}")

    except Exception as e:
        logger.error(f"Error in test execution: {str(e)}")

async def test_chat_invoke():
    """Test invoking a chat model"""
    try:
        connector = get_model_connector()

        logger.info("=== Testing Chat Invoke ===")

        try:
            # Get the default completion model
            completion_model = await connector.get_completion_model()
            if not completion_model:
                logger.warning("No default completion model available for testing")
                return

            # Test with a simple message
            messages = [HumanMessage(content="Hello, how are you?")]

            logger.info("Invoking chat model...")
            start_time = asyncio.get_event_loop().time()
            response = await connector.invoke_chat(messages)
            end_time = asyncio.get_event_loop().time()

            response_content = getattr(response, 'content', str(response))

            logger.info(f"""
Response: {response_content}
Time taken: {end_time - start_time:.2f} seconds
""")

        except Exception as e:
            logger.error(f"Error invoking chat model: {str(e)}")

    except Exception as e:
        logger.error(f"Error in test execution: {str(e)}")

async def test_embedding():
    """Test embedding functionality"""
    try:
        connector = get_model_connector()

        logger.info("=== Testing Embedding ===")

        try:
            # Test query embedding
            logger.info("Testing embed_query...")
            embedding = await connector.embed_query("This is a test query")
            if embedding:
                logger.info(f"Successfully embedded query: {len(embedding)} dimensions")
            else:
                logger.warning("Failed to embed query")

            # Test document embedding
            logger.info("Testing embed_documents...")
            embeddings = await connector.embed_documents(["Document 1", "Document 2"])
            if embeddings:
                logger.info(f"Successfully embedded documents: {len(embeddings)} documents, {len(embeddings[0])} dimensions")
            else:
                logger.warning("Failed to embed documents")

        except Exception as e:
            logger.error(f"Error testing embedding: {str(e)}")

    except Exception as e:
        logger.error(f"Error in test execution: {str(e)}")

async def main():
    """Main test function"""
    try:
        await test_get_model_by_id()
        await test_default_models()
        await test_chat_invoke()
        await test_embedding()
    except Exception as e:
        logger.error(f"Main execution error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())

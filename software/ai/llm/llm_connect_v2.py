"""
LLM Connection Module (v2)

This module provides a clean, simple interface for connecting to LLM models
configured in the database. It supports both Completion (BaseChatModel) and
Embedding models, with both async and sync operations.

Key components:
- ModelFactory: Creates model instances from connection code
- ModelCache: Caches model instances for reuse
- ModelConnector: Main interface for getting model instances

Usage:
    # Get the singleton instance
    connector = get_model_connector()

    # Get a model by ID (async)
    model = await connector.get_model_by_id("model_id")

    # Get default completion model (async)
    llm = await connector.get_completion_model()

    # Get default embedding model (sync)
    embeddings = connector.get_embedding_model_sync()
"""

import os
import logging
from typing import Dict, Any, Optional, Union, List, TypeVar, Generic, Callable
from functools import wraps

# Langchain imports
from langchain.schema import BaseMessage
from langchain.chat_models.base import BaseChatModel
from langchain.embeddings.base import Embeddings

# Database imports
from db.llm_repository import LLMRepository
from db.graph_repository import GraphRepository

# Model provider imports (for exec context)
from langchain_nvidia_ai_endpoints import ChatNVIDIA
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_groq import ChatGroq
from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_anthropic import ChatAnthropic
from langchain_deepseek import ChatDeepSeek
from langchain_mistralai import ChatMistralAI
from langchain_cerebras import ChatCerebras

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic typing
T = TypeVar('T')
ModelType = Union[BaseChatModel, Embeddings]


class ModelFactory:
    """
    Factory class for creating model instances from connection code.

    This class handles the execution of connection code stored in the database
    to create model instances (BaseChatModel or Embeddings).
    """

    @staticmethod
    def create_model_from_code(config: Dict[str, Any]) -> Optional[ModelType]:
        """
        Create a model instance from connection code.

        Args:
            config: The model configuration from the database

        Returns:
            A BaseChatModel or Embeddings instance, or None if creation fails
        """
        connection_code = config.get("connection_code", "")
        if not connection_code or not connection_code.strip():
            logger.error(f"Missing connection code for model {config.get('_id')}")
            return None

        # Prepare execution context with necessary imports
        exec_context = {
            "config": config,
            "ChatNVIDIA": ChatNVIDIA,
            "ChatOpenAI": ChatOpenAI,
            "ChatGroq": ChatGroq,
            "ChatOllama": ChatOllama,
            "OllamaEmbeddings": OllamaEmbeddings,
            "OpenAIEmbeddings": OpenAIEmbeddings,
            "ChatGoogleGenerativeAI": ChatGoogleGenerativeAI,
            "ChatAnthropic": ChatAnthropic,
            "ChatDeepSeek": ChatDeepSeek,
            "ChatMistralAI": ChatMistralAI,
            "ChatCerebras": ChatCerebras,
            "BaseChatModel": BaseChatModel,
            "Embeddings": Embeddings,
            "os": os,
        }
        local_vars = {}

        # Execute connection code
        try:
            exec(connection_code, exec_context, local_vars)
        except Exception as e:
            logger.error(f"Error executing connection code: {str(e)}")
            return None

        # Get the model instance
        model_instance = local_vars.get("model")
        if not model_instance:
            logger.error(f"Connection code for {config.get('_id')} did not define a 'model' variable")
            return None

        # Validate model instance type
        model_type = config.get("model_type", "")
        if model_type == "Embedding" and not isinstance(model_instance, Embeddings):
            logger.error(f"Created object for {config.get('_id')} is not a valid Embeddings instance")
            return None
        elif model_type == "Completion" and not isinstance(model_instance, BaseChatModel):
            logger.error(f"Created object for {config.get('_id')} is not a valid BaseChatModel instance")
            return None

        return model_instance


class ModelCache(Generic[T]):
    """
    Cache for model instances.

    This class provides a simple cache for model instances to avoid
    recreating them unnecessarily.
    """

    def __init__(self):
        """Initialize an empty cache."""
        self._cache: Dict[str, T] = {}

    def get(self, key: str) -> Optional[T]:
        """
        Get a model instance from the cache.

        Args:
            key: The cache key (usually model ID)

        Returns:
            The cached model instance, or None if not found
        """
        return self._cache.get(key)

    def set(self, key: str, value: T) -> None:
        """
        Add a model instance to the cache.

        Args:
            key: The cache key (usually model ID)
            value: The model instance to cache
        """
        self._cache[key] = value

    def clear(self) -> None:
        """Clear the cache."""
        self._cache.clear()


class ModelConnector:
    """
    Main interface for connecting to LLM models.

    This class provides methods for getting model instances from the database,
    with caching for performance.
    """

    def __init__(self):
        """Initialize the connector with repositories and caches."""
        self._llm_repository = LLMRepository()
        self._graph_repository = GraphRepository()
        self._completion_cache = ModelCache[BaseChatModel]()
        self._embedding_cache = ModelCache[Embeddings]()

    async def get_model_by_id(self, model_id: str) -> Optional[ModelType]:
        """
        Get a model instance by its ID.

        Args:
            model_id: The ID of the model configuration

        Returns:
            A BaseChatModel or Embeddings instance, or None if not found or creation fails
        """
        # Check caches first
        cached_completion = self._completion_cache.get(model_id)
        if cached_completion:
            return cached_completion

        cached_embedding = self._embedding_cache.get(model_id)
        if cached_embedding:
            return cached_embedding

        # Get model config from database
        model_config = await self._llm_repository.get_llm(model_id)
        if not model_config:
            logger.warning(f"Model configuration not found for ID: {model_id}")
            return None

        # Check if model is enabled
        if not model_config.get("is_enabled", True):
            logger.warning(f"Model {model_id} is disabled")
            return None

        # Create model instance
        model_instance = ModelFactory.create_model_from_code(model_config)
        if not model_instance:
            return None

        # Cache the model instance
        model_type = model_config.get("model_type", "")
        if model_type == "Embedding" and isinstance(model_instance, Embeddings):
            self._embedding_cache.set(model_id, model_instance)
        elif model_type == "Completion" and isinstance(model_instance, BaseChatModel):
            self._completion_cache.set(model_id, model_instance)

        return model_instance

    async def get_default_model(self, model_type: str) -> Optional[ModelType]:
        """
        Get the default model for a specific type.

        Args:
            model_type: The model type ("Completion" or "Embedding")

        Returns:
            A BaseChatModel or Embeddings instance, or None if not found or creation fails
        """
        # Get default model config from database
        model_config = await self._llm_repository.get_default_llm(model_type, debug=False)
        if not model_config:
            logger.warning(f"No default {model_type} model configured")
            return None

        # Get model instance by ID
        model_id = model_config.get("_id")
        return await self.get_model_by_id(model_id)

    async def get_completion_model(self) -> Optional[BaseChatModel]:
        """
        Get the default completion model.

        Returns:
            A BaseChatModel instance, or None if not found or creation fails
        """
        model = await self.get_default_model("Completion")
        if model and isinstance(model, BaseChatModel):
            return model
        elif model:
            logger.error(f"Expected BaseChatModel but received {type(model)}")
            raise ValueError(f"Expected BaseChatModel but received {type(model)}")
        else:
            logger.error("No default Completion model configured")
            raise ValueError("No default Completion model configured")

    async def get_embedding_model(self) -> Optional[Embeddings]:
        """
        Get the default embedding model.

        Returns:
            An Embeddings instance, or None if not found or creation fails
        """
        model = await self.get_default_model("Embedding")
        if model and isinstance(model, Embeddings):
            return model
        elif model:
            logger.error(f"Expected Embeddings but received {type(model)}")
            raise ValueError(f"Expected Embeddings but received {type(model)}")
        else:
            logger.error("No default Embedding model configured")
            raise ValueError("No default Embedding model configured")

    def get_model_by_id_sync(self, model_id: str) -> Optional[ModelType]:
        """
        Get a model instance by its ID (synchronous version).

        Args:
            model_id: The ID of the model configuration

        Returns:
            A BaseChatModel or Embeddings instance, or None if not found or creation fails
        """
        # Check caches first
        cached_completion = self._completion_cache.get(model_id)
        if cached_completion:
            return cached_completion

        cached_embedding = self._embedding_cache.get(model_id)
        if cached_embedding:
            return cached_embedding

        # Get model config from database
        model_config = self._llm_repository.get_llm_sync(model_id)
        if not model_config:
            logger.warning(f"Model configuration not found for ID: {model_id}")
            return None

        # Check if model is enabled
        if not model_config.get("is_enabled", True):
            logger.warning(f"Model {model_id} is disabled")
            return None

        # Create model instance
        model_instance = ModelFactory.create_model_from_code(model_config)
        if not model_instance:
            return None

        # Cache the model instance
        model_type = model_config.get("model_type", "")
        if model_type == "Embedding" and isinstance(model_instance, Embeddings):
            self._embedding_cache.set(model_id, model_instance)
        elif model_type == "Completion" and isinstance(model_instance, BaseChatModel):
            self._completion_cache.set(model_id, model_instance)

        return model_instance

    def get_default_model_sync(self, model_type: str) -> Optional[ModelType]:
        """
        Get the default model for a specific type (synchronous version).

        Args:
            model_type: The model type ("Completion" or "Embedding")

        Returns:
            A BaseChatModel or Embeddings instance, or None if not found or creation fails
        """
        # Get default model config from database
        model_config = self._llm_repository.get_default_llm_sync(model_type, debug=False)
        if not model_config:
            logger.warning(f"No default {model_type} model configured")
            return None

        # Get model instance by ID
        model_id = model_config.get("_id")
        return self.get_model_by_id_sync(model_id)

    def get_completion_model_sync(self) -> Optional[BaseChatModel]:
        """
        Get the default completion model (synchronous version).

        Returns:
            A BaseChatModel instance, or None if not found or creation fails
        """
        model = self.get_default_model_sync("Completion")
        if model and isinstance(model, BaseChatModel):
            return model
        elif model:
            logger.error(f"Expected BaseChatModel but received {type(model)}")
            return None
        else:
            logger.error("No default Completion model configured")
            return None

    def get_embedding_model_sync(self) -> Optional[Embeddings]:
        """
        Get the default embedding model (synchronous version).

        Returns:
            An Embeddings instance, or None if not found or creation fails
        """
        model = self.get_default_model_sync("Embedding")
        if model and isinstance(model, Embeddings):
            return model
        elif model:
            logger.error(f"Expected Embeddings but received {type(model)}")
            return None
        else:
            logger.error("No default Embedding model configured")
            return None

    async def get_model_for_graph_stage(self, graph_name: str, stage_name: str) -> Optional[ModelType]:
        """
        Get a model instance for a specific graph stage.

        Args:
            graph_name: The name of the graph
            stage_name: The name of the stage

        Returns:
            A BaseChatModel or Embeddings instance, or None if not found or creation fails
        """
        # Create cache key for this graph stage
        cache_key = f"{graph_name}_{stage_name}"

        # Check caches first
        cached_completion = self._completion_cache.get(cache_key)
        if cached_completion:
            return cached_completion

        cached_embedding = self._embedding_cache.get(cache_key)
        if cached_embedding:
            return cached_embedding

        # Get graph configuration
        graph = await self._graph_repository.get_graph(graph_name)
        if not graph:
            logger.warning(f"Graph not found: {graph_name}")
            return await self.get_completion_model()

        # Find the stage configuration
        stage = None
        for s in graph.get("stages", []):
            if s.get("name") == stage_name:
                stage = s
                break

        if not stage:
            logger.warning(f"Stage not found: {stage_name} in graph {graph_name}")
            return await self.get_completion_model()

        # Check if the stage has a specific model configured
        model_id = stage.get("llm_id")
        if model_id:
            model = await self.get_model_by_id(model_id)
            if model:
                # Cache the model with the graph stage key
                if isinstance(model, BaseChatModel):
                    self._completion_cache.set(cache_key, model)
                elif isinstance(model, Embeddings):
                    self._embedding_cache.set(cache_key, model)
                return model

        # If no specific model is configured or it failed, check the model type
        model_type = stage.get("model_type")
        if model_type == "Embedding":
            return await self.get_embedding_model()
        else:
            return await self.get_completion_model()

    async def embed_query(self, text: str, model_id: Optional[str] = None) -> Optional[List[float]]:
        """
        Embed a query using an embedding model.

        Args:
            text: The text to embed
            model_id: Optional model ID to use (uses default if None)

        Returns:
            A list of floats representing the embedding, or None if embedding fails
        """
        try:
            # Get the embedding model
            embedding_model = None
            if model_id:
                embedding_model = await self.get_model_by_id(model_id)
                if embedding_model and not isinstance(embedding_model, Embeddings):
                    logger.error(f"Model {model_id} is not an Embeddings instance")
                    embedding_model = None

            if not embedding_model:
                embedding_model = await self.get_embedding_model()

            # Embed the query
            return await embedding_model.aembed_query(text)
        except Exception as e:
            logger.error(f"Error embedding query: {str(e)}")
            return None

    def embed_query_sync(self, text: str, model_id: Optional[str] = None) -> Optional[List[float]]:
        """
        Embed a query using an embedding model (synchronous version).

        Args:
            text: The text to embed
            model_id: Optional model ID to use (uses default if None)

        Returns:
            A list of floats representing the embedding, or None if embedding fails
        """
        try:
            # Get the embedding model
            embedding_model = None
            if model_id:
                embedding_model = self.get_model_by_id_sync(model_id)
                if embedding_model and not isinstance(embedding_model, Embeddings):
                    logger.error(f"Model {model_id} is not an Embeddings instance")
                    embedding_model = None

            if not embedding_model:
                embedding_model = self.get_embedding_model_sync()

            if not embedding_model:
                logger.error("No embedding model available")
                return None

            # Embed the query
            return embedding_model.embed_query(text)
        except Exception as e:
            logger.error(f"Error embedding query: {str(e)}")
            return None

    async def embed_documents(self, texts: List[str], model_id: Optional[str] = None) -> Optional[List[List[float]]]:
        """
        Embed multiple documents using an embedding model.

        Args:
            texts: The texts to embed
            model_id: Optional model ID to use (uses default if None)

        Returns:
            A list of embeddings, or None if embedding fails
        """
        try:
            # Get the embedding model
            embedding_model = None
            if model_id:
                embedding_model = await self.get_model_by_id(model_id)
                if embedding_model and not isinstance(embedding_model, Embeddings):
                    logger.error(f"Model {model_id} is not an Embeddings instance")
                    embedding_model = None

            if not embedding_model:
                embedding_model = await self.get_embedding_model()

            # Embed the documents
            return await embedding_model.aembed_documents(texts)
        except Exception as e:
            logger.error(f"Error embedding documents: {str(e)}")
            return None

    def embed_documents_sync(self, texts: List[str], model_id: Optional[str] = None) -> Optional[List[List[float]]]:
        """
        Embed multiple documents using an embedding model (synchronous version).

        Args:
            texts: The texts to embed
            model_id: Optional model ID to use (uses default if None)

        Returns:
            A list of embeddings, or None if embedding fails
        """
        try:
            # Get the embedding model
            embedding_model = None
            if model_id:
                embedding_model = self.get_model_by_id_sync(model_id)
                if embedding_model and not isinstance(embedding_model, Embeddings):
                    logger.error(f"Model {model_id} is not an Embeddings instance")
                    embedding_model = None

            if not embedding_model:
                embedding_model = self.get_embedding_model_sync()

            if not embedding_model:
                logger.error("No embedding model available")
                return None

            # Embed the documents
            return embedding_model.embed_documents(texts)
        except Exception as e:
            logger.error(f"Error embedding documents: {str(e)}")
            return None

    async def invoke_chat(self, messages: List[BaseMessage], model_id: Optional[str] = None, **kwargs) -> Any:
        """
        Invoke a chat model with messages.

        Args:
            messages: The messages to send to the model
            model_id: Optional model ID to use (uses default if None)
            **kwargs: Additional arguments to pass to the model

        Returns:
            The model response
        """
        try:
            # Get the chat model
            chat_model = None
            if model_id:
                chat_model = await self.get_model_by_id(model_id)
                if chat_model and not isinstance(chat_model, BaseChatModel):
                    logger.error(f"Model {model_id} is not a BaseChatModel instance")
                    chat_model = None

            if not chat_model:
                chat_model = await self.get_completion_model()

            # Invoke the model
            return await chat_model.ainvoke(messages, **kwargs)
        except Exception as e:
            logger.error(f"Error invoking chat model: {str(e)}")
            raise


# Singleton instance
_model_connector: Optional[ModelConnector] = None

def get_model_connector() -> ModelConnector:
    """
    Get the singleton ModelConnector instance.

    Returns:
        The ModelConnector instance
    """
    global _model_connector
    if _model_connector is None:
        _model_connector = ModelConnector()
    return _model_connector

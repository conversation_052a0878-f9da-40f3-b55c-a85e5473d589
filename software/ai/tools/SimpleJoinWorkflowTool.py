from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from typing import Dict, Any, Optional, Type, List, Sequence, TypedDict
from pydantic import BaseModel, Field
from software.ai.llm.llm_connect import get_llm_connect
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from api.database import get_sync_db
from data import registry
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from main import StockAnalysisApp
from db.report_repository import ReportFactory, ReportRepository
import pandas as pd
from langchain_core.prompts import PromptTemplate

# Prompt templates for LLM instructions
DATASOURCE_SELECTION_PROMPT = PromptTemplate.from_template("""You are a financial data expert. Your job is to pick the best data sources for analysis.

Analyze the user's business question to determine the data types needed (e.g., 5-minute prices, daily prices, quarterly reports).
Then select the most appropriate data sources from the available options that contain the required data.

Follow these guidelines:
1. **Match Data Granularity:** Choose sources matching the time intervals (5-minute, daily, etc.) mentioned in the question - this is highest priority.
2. **For Company Comparisons:** Use the same data source for both companies when comparing similar metrics. Only use different sources if the analysis requires different data types.
3. **For Single Company Analysis:** Select different specialized data sources that provide complementary information.
4. **Extract Tickers:** Identify all ticker symbols mentioned in the question (typically in parentheses).
5. **Return Class Names:** Only provide the exact loader class names (e.g., 'YahooFinanceLoader').

Always prioritize data granularity compatibility over other considerations.
""")

JOIN_TYPE_SELECTION_PROMPT = PromptTemplate.from_template("""You are a data integration specialist. Your task is to determine the optimal join strategy.

Carefully analyze both datasets to make the best join decision. The join column is always 'Date'.

Follow this decision process in strict order:

1. **Time Granularity Check:** Compare the time steps in both datasets.
   * If datasets have DIFFERENT frequencies (e.g., minutes vs. days or daily vs. quarterly):
     * ALWAYS use 'forward_fill' regardless of what join type the user requested
     * Reason: This ensures proper time-based alignment without data loss

2. **User Preference:** Only if time steps are IDENTICAL:
   * Check if the business question explicitly requests a specific join type
   * If YES: Use the requested join type (inner, outer, left, right)

3. **Default Selection:** If time steps match and no join type was specified:
   * Use 'inner' join as the default approach

Your primary goal is to ensure proper time alignment between datasets with different granularities.
""")

class DatasourceSelectionWithTicker(BaseModel):
    """Schema for DatasourceSelectionWithTicker input parameters"""
    main_datasource: str = Field(
        ...,
        description="The datasource to select from the available datasources.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    auxiliary_datasource: str = Field(
        ...,
        description="The auxiliary datasource to join with the main datasource.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    main_ticker: str = Field(
        ...,
        description="The ticker of the main company to analyze.",
        examples=["AAPL", "META", "AMZN", "TSLA", "MSFT", "NVDA", "NFLX"]
    )
    auxiliary_ticker: str = Field(
        ...,
        description="The ticker of the auxiliary company to analyze.",
        examples=["AAPL", "META", "AMZN", "TSLA", "MSFT", "NVDA", "NFLX"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the datasource from the available datasources."
    )

class SimpleJoinWorkflowToolSchema(BaseModel):
    """Schema for SimpleJoinWorkflowTool input parameters"""
    business_question: str = Field(
        ...,
        description="A business question requiring data analysis using joined financial datasets. The question can either involve comparing two different tickers or analyzing a single ticker using two distinct data sources (e.g., price and sentiment, or data with different time granularities). The request should include ticker(s) in parentheses and specify the join type and forecast horizon where appropriate.",
        min_length=10,
        examples=[
            "How does Meta (META) stock price compare to Apple (AAPL) over the past month? Join using forward fill and forecast the next 5 days.",
            "Can Amazon (AMZN) quarterly earnings data joined with its daily price history predict price movements after earnings announcements?",
            "Analyze the correlation between Tesla (TSLA) stock price and its Twitter sentiment data using inner join and create a 7-day forecast.",
            "Does Microsoft (MSFT) trading volume have any leading effect on Nvidia (NVDA) price movements? Use forward fill and predict next 10 days.",
            "How do economic indicators affect Netflix (NFLX) stock price? Join the datasets and forecast future performance."
        ]
    )

class JoinTypeSelection(BaseModel):
    """Schema for join type and column selection"""
    join_type: str = Field(
        ...,
        description="The type of join to perform between the main and auxiliary datasources.",
        examples=["inner", "outer", "left", "right", "forward_fill"]
    )
    join_column: str = Field(
        ...,
        description="The column to join on, typically a date or identifier column that exists in both datasets.",
        examples=["Date", "Ticker", "ID"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting this join type and column based on the data samples."
    )

class FeaturesSelection(BaseModel):
    """Schema for features selection"""
    features: List[str] = Field(
        ...,
        description="The features to select from the available features."
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the features from the available features."
    )

class ModelSelection(BaseModel):
    """Schema for ModelSelection input parameters"""
    model: str = Field(
        ...,
        description="The model to select from the available models. ignore the description after the hyphen, get only the class name ending with 'Model'"
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the model from the available models."
    )

class TargetSelection(BaseModel):
    """Schema for TargetSelection input parameters"""
    target: str = Field(
        ...,
        description="The target to select from the available targets."
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the target from the available targets."
    )

class ForecastHorizon(BaseModel):
    """Schema for ForecastHorizon input parameters"""
    forecast_horizon: int = Field(
        ...,
        description="Number of time units to forecast ahead (hours/minutes/seconds based on datasource interval units)",
        ge=5
    )
    reasoning: str = Field(
        ...,
        description="Reasoning for selecting this forecast horizon"
    )

class WorkflowState(TypedDict):
    """State definition for the workflow"""
    messages: Sequence[BaseMessage]
    business_question: str
    main_datasource: str
    auxiliary_datasource: str
    main_ticker: str
    auxiliary_ticker: str
    join_type: str
    join_column: str
    features: List[str]
    model: str
    target: str
    forecast_horizon: int
    available_datasources: List[str]
    available_features: List[str]
    available_models: List[str]
    available_targets: List[str]
    # Sample data summaries as markdown
    main_data_summary: str
    auxiliary_data_summary: str
    # Add fields for reasoning
    datasource_reasoning: str
    join_type_reasoning: str
    features_reasoning: str
    model_reasoning: str
    target_reasoning: str
    forecast_horizon_reasoning: str
    formatted_answer: str
    # Apps
    main_app: pd.DataFrame
    auxiliary_app: pd.DataFrame
    joined_data: pd.DataFrame
    # Add report_id field
    predictions: pd.DataFrame
    performance: Dict[str, float]
    report_id: str | None

class SimpleJoinWorkflowTool(BaseTool):
    """Tool for joining financial data from multiple sources, analyzing relationships, and generating price forecasts"""
    
    name: str = "SimpleJoinWorkflowTool"
    description: str = "Joins historical financial data from multiple sources, performs analysis using technical indicators, and generates forecasts. Supports comparing different companies or analyzing a single company using diverse data sources with different granularities."
    category: str = "Data Analysis"
    version: str = "1.0.0"
    args_schema: Type[BaseModel] = SimpleJoinWorkflowToolSchema
    
    async def _arun(self, business_question: str) -> Dict[str, Any]:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with parameter: {business_question}", "Tool Execution")
        
        workflow = StateGraph(WorkflowState)

        workflow.add_node("list_datasources", self.list_datasources)
        workflow.add_node("select_datasource_with_ticker", self.select_datasource_with_ticker)
        workflow.add_node("show_sample_columns", self.show_sample_columns)
        workflow.add_node("select_join_type_and_column", self.select_join_type_and_column)
        workflow.add_node("join_datasets", self.join_datasets)
        workflow.add_node("list_features", self.list_features)
        workflow.add_node("select_features", self.select_features)
        workflow.add_node("add_features_to_joined_data", self.add_features_to_joined_data)
        workflow.add_node("list_models", self.list_models)
        workflow.add_node("select_model", self.select_model)
        workflow.add_node("list_targets", self.list_targets)    
        workflow.add_node("select_target", self.select_target)
        workflow.add_node("select_forecast_horizon", self.select_forecast_horizon)
        workflow.add_node("run_model", self.run_model)
        workflow.add_node("generate_report", self.generate_report)
        workflow.add_node("process_report", self.process_report)
        workflow.add_edge(START, "list_datasources")
        workflow.add_edge("list_datasources", "select_datasource_with_ticker")
        workflow.add_edge("select_datasource_with_ticker", "show_sample_columns")
        workflow.add_edge("show_sample_columns", "select_join_type_and_column")
        workflow.add_edge("select_join_type_and_column", "join_datasets")
        workflow.add_edge("join_datasets", "list_features")
        workflow.add_edge("list_features", "select_features")
        workflow.add_edge("select_features", "add_features_to_joined_data")
        workflow.add_edge("add_features_to_joined_data", "list_models")
        workflow.add_edge("list_models", "select_model")
        workflow.add_edge("select_model", "list_targets")
        workflow.add_edge("list_targets", "select_target")
        workflow.add_edge("select_target", "select_forecast_horizon")
        workflow.add_edge("select_forecast_horizon", "run_model")
        workflow.add_edge("run_model", "generate_report")
        workflow.add_edge("generate_report", "process_report")
        workflow.add_edge("process_report", END)

        compiled_graph = workflow.compile()

        initial_state = {
            "messages": [HumanMessage(content=business_question)],
            "business_question": business_question,
            "main_datasource": "",
            "auxiliary_datasource": "",
            "main_ticker": "",
            "auxiliary_ticker": "",
            "join_type": "",
            "join_column": "",
            "features": [],
            "available_datasources": [],
            "available_features": [],
            "main_data_summary": "",
            "auxiliary_data_summary": "",
            "datasource_reasoning": "",
            "join_type_reasoning": "",
            "features_reasoning": "",
            "formatted_answer": "Analysis pending. Currently only listing available datasources.",
            "report_id": None
        }

        final_state = await compiled_graph.ainvoke(initial_state)
        
        # Show pretty result summary
        return final_state.get('formatted_answer', "Analysis completed but no formatted answer available.")

    def list_datasources(self, state: WorkflowState) -> WorkflowState:
        """List all available data sources synchronously."""
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        state["available_datasources"] = [ds["name"]+ " (" + ds["description"] + ")" for ds in datasources_cursor]
        print_step(f"Available datasources:\n• {'\n• '.join(state['available_datasources'])}", "SimpleJoinWorkflowTool: List Datasources", "gold1")
        return state
    
    async def select_datasource_with_ticker(self, state: WorkflowState) -> WorkflowState:
        """Select the main and auxiliary datasource from the available datasources."""
        available_datasources = state["available_datasources"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DatasourceSelectionWithTicker)
        system_message = SystemMessage(content=DATASOURCE_SELECTION_PROMPT.format())
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable datasources: {available_datasources}\nSelect the most appropriate datasources and tickers for this analysis.")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["main_datasource"] = response.main_datasource
        state["auxiliary_datasource"] = response.auxiliary_datasource
        state["datasource_reasoning"] = response.reasoning # Store reasoning
        state["main_ticker"] = response.main_ticker
        state["auxiliary_ticker"] = response.auxiliary_ticker
        print_step(f"Selected datasource: [bold]{response.main_datasource}[/bold] and [bold]{response.auxiliary_datasource}[/bold] \nSelected tickers: [bold]{response.main_ticker}[/bold] and [bold]{response.auxiliary_ticker}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "SimpleJoinWorkflowTool: Select Datasource", "gold1")
        return state

    def show_sample_columns(self, state: WorkflowState) -> WorkflowState:
        """Show the sample columns of main and auxiliary datasources."""
        main_datasource = state["main_datasource"]
        auxiliary_datasource = state["auxiliary_datasource"]
        main_ticker = state["main_ticker"]
        auxiliary_ticker = state["auxiliary_ticker"]
        
        # Initialize markdown summaries
        main_data_md = []
        auxiliary_data_md = []
        
        # Main datasource samples
        loader_class = registry.get_loader_class(main_datasource)
        if loader_class:
            app = StockAnalysisApp(ticker=main_ticker)
            app.load_data(loader_class=loader_class)
            state["main_app"] = app
            # Generate markdown summary for main datasource
            main_data_md.append(f"## Main Datasource: {main_datasource} for {main_ticker}\n")
            
            # Add column data types
            main_data_md.append("### Column Data Types\n")
            for col, dtype in app.data.dtypes.items():
                main_data_md.append(f"- **{col}**: {dtype}")
                print(f"{col}: {dtype}")
            main_data_md.append("\n")
            
            # Add top 5 rows
            print_step(f"Main datasource ({main_datasource}) for {main_ticker} - Top 5 rows:", "SimpleJoinWorkflowTool: Sample Data", "gold1")
            top_df = app.data.head()
            print(top_df)
            main_data_md.append("### Top 5 Rows\n")
            main_data_md.append("```\n" + top_df.to_string() + "\n```\n")
            
            # Add bottom 5 rows
            print_step(f"Main datasource ({main_datasource}) for {main_ticker} - Bottom 5 rows:", "SimpleJoinWorkflowTool: Sample Data", "gold1")
            bottom_df = app.data.tail()
            print(bottom_df)
            main_data_md.append("### Bottom 5 Rows\n")
            main_data_md.append("```\n" + bottom_df.to_string() + "\n```\n")
        
        # Auxiliary datasource samples
        loader_class = registry.get_loader_class(auxiliary_datasource)
        if loader_class:
            app = StockAnalysisApp(ticker=auxiliary_ticker)
            app.load_data(loader_class=loader_class)
            state["auxiliary_app"] = app
            # Generate markdown summary for auxiliary datasource
            auxiliary_data_md.append(f"## Auxiliary Datasource: {auxiliary_datasource} for {auxiliary_ticker}\n")
            
            # Add column data types
            auxiliary_data_md.append("### Column Data Types\n")
            for col, dtype in app.data.dtypes.items():
                auxiliary_data_md.append(f"- **{col}**: {dtype}")
                print(f"{col}: {dtype}")
            auxiliary_data_md.append("\n")
            
            # Add top 5 rows
            print_step(f"Auxiliary datasource ({auxiliary_datasource}) for {auxiliary_ticker} - Top 5 rows:", "SimpleJoinWorkflowTool: Sample Data", "gold1")
            top_df = app.data.head()
            print(top_df)
            auxiliary_data_md.append("### Top 5 Rows\n")
            auxiliary_data_md.append("```\n" + top_df.to_string() + "\n```\n")
            
            # Add bottom 5 rows
            print_step(f"Auxiliary datasource ({auxiliary_datasource}) for {auxiliary_ticker} - Bottom 5 rows:", "SimpleJoinWorkflowTool: Sample Data", "gold1")
            bottom_df = app.data.tail()
            print(bottom_df)
            auxiliary_data_md.append("### Bottom 5 Rows\n")
            auxiliary_data_md.append("```\n" + bottom_df.to_string() + "\n```\n")
        
        # Store markdown summaries in state
        state["main_data_summary"] = "\n".join(main_data_md)
        state["auxiliary_data_summary"] = "\n".join(auxiliary_data_md)
        
        return state

    async def select_join_type_and_column(self, state: WorkflowState) -> WorkflowState:
        """Select the join type and column to join on."""
        main_datasource = state["main_datasource"]
        auxiliary_datasource = state["auxiliary_datasource"]
        main_ticker = state["main_ticker"]
        auxiliary_ticker = state["auxiliary_ticker"]
        main_data_summary = state["main_data_summary"]
        auxiliary_data_summary = state["auxiliary_data_summary"]
        
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(JoinTypeSelection)
        
        system_message = SystemMessage(content=JOIN_TYPE_SELECTION_PROMPT.format())
        
        prompt = f"""
Business Question: {state["business_question"]}

Data Summaries:
{main_data_summary}
{auxiliary_data_summary}

Select join type and column based on the rules.
"""
        
        human_message = HumanMessage(content=prompt)
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        
        state["join_type"] = response.join_type
        state["join_column"] = response.join_column
        state["join_type_reasoning"] = response.reasoning
        
        print_step(f"Selected join type: [bold]{response.join_type}[/bold] on column [bold]{response.join_column}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "SimpleJoinWorkflowTool: Select Join Type", "gold1")
        
        return state

    def join_datasets(self, state: WorkflowState) -> WorkflowState:
        """Join the main and auxiliary datasets."""
        main_app = state["main_app"]
        auxiliary_app = state["auxiliary_app"]
        join_type = state["join_type"]
        join_column = state["join_column"]
        
        print_debug(f"Before join - Main DF columns: {main_app.data.columns.tolist()}", "Date Column Tracking")
        print_debug(f"Before join - Main DF index name: {main_app.data.index.name}", "Date Column Tracking")
        print_debug(f"Before join - Aux DF columns: {auxiliary_app.data.columns.tolist()}", "Date Column Tracking")
        print_debug(f"Before join - Aux DF index name: {auxiliary_app.data.index.name}", "Date Column Tracking")
        
        # Handle forward_fill join type
        if join_type == "forward_fill":
            # First set the Date as index for both dataframes if not already
            if 'Date' in main_app.data.columns:
                main_app.data = main_app.data.set_index('Date')
            if 'Date' in auxiliary_app.data.columns:
                auxiliary_app.data = auxiliary_app.data.set_index('Date')

            # Convert indices to datetime
            main_app.data.index = pd.to_datetime(main_app.data.index)
            auxiliary_app.data.index = pd.to_datetime(auxiliary_app.data.index)

            # Sort both dataframes by index
            main_app.data = main_app.data.sort_index()
            auxiliary_app.data = auxiliary_app.data.sort_index()

            # Perform a regular merge on indices
            joined_data = pd.merge(
                main_app.data,
                auxiliary_app.data,
                left_index=True, 
                right_index=True,
                how='left',  # Keep all dates from main_app
                suffixes=('', '_sub')
            )

            # Forward fill missing values - using ffill() instead of deprecated fillna(method='ffill')
            joined_data = joined_data.ffill()
        else:
            # Handle standard join types (inner, outer, left, right)
            # Ensure join column has the same data type in both dataframes
            if join_column in main_app.data.columns and join_column in auxiliary_app.data.columns:
                # Get data types
                main_dtype = main_app.data[join_column].dtype
                aux_dtype = auxiliary_app.data[join_column].dtype
                
                # Convert to same type if different (prefer object over numeric for safety)
                if main_dtype != aux_dtype:
                    if pd.api.types.is_numeric_dtype(main_dtype) and pd.api.types.is_object_dtype(aux_dtype):
                        main_app.data[join_column] = main_app.data[join_column].astype(str)
                    elif pd.api.types.is_numeric_dtype(aux_dtype) and pd.api.types.is_object_dtype(main_dtype):
                        auxiliary_app.data[join_column] = auxiliary_app.data[join_column].astype(str)
            
            joined_data = pd.merge(
                main_app.data,
                auxiliary_app.data,
                on=join_column,
                how=join_type,
                suffixes=('', '_sub')
            )
            print_debug(f"After standard merge - Joined data columns: {joined_data.columns.tolist()}", "Date Column Tracking")
            
            # Ensure 'Date' is in columns or rename the join column if it's date-like
            if join_column != 'Date' and 'Date' not in joined_data.columns:
                if pd.api.types.is_datetime64_any_dtype(joined_data[join_column]):
                    # If join column is datetime type, rename it to 'Date'
                    joined_data = joined_data.rename(columns={join_column: 'Date'})
                    print_debug(f"Renamed {join_column} to Date: {joined_data.columns.tolist()}", "Date Column Tracking")

        state["joined_data"] = joined_data
        print_step(f"Joined data: {joined_data.head(100)}", "SimpleJoinWorkflowTool: Join Datasets", "gold1")
        print_debug(f"Final joined_data columns: {joined_data.columns.tolist()}", "Date Column Tracking")
        print_debug(f"Final joined_data index name: {joined_data.index.name}", "Date Column Tracking")
        print_debug(f"Final joined_data index type: {type(joined_data.index)}", "Date Column Tracking")
        
        return state

    def list_features(self, state: WorkflowState) -> WorkflowState:
        """List all available features for the selected datasource."""
        features = {}
        for name, feature_class in FEATURE_CLASSES.items():
            description = feature_class.__doc__ or "No description available"
            description = description.strip().split('\n')[0]
            features[name] = description
        # Format the output string correctly
        feature_lines = [f"{name} - {desc}" for name, desc in features.items()]
        print_step(f"Available features: \n• {'\n• '.join(feature_lines)}", "SimpleJoinWorkflowTool: List Features", "gold1")
        state["available_features"] = feature_lines
        return state
    
    async def select_features(self, state: WorkflowState) -> WorkflowState:
        """Select the features from the available features."""
        available_features = state["available_features"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(FeaturesSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the features from the available features that are most relevant to the business question. ignore the description after the hyphen.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable features: {available_features}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["features"] = response.features
        print_step(f"Selected features: [bold]{response.features}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "SimpleJoinWorkflowTool: Select Features", "gold1")
        state["features_reasoning"] = response.reasoning # Store reasoning
        return state
    
    def add_features_to_joined_data(self, state: WorkflowState) -> WorkflowState:
        """Add the features to the joined data."""
        joined_data = state["joined_data"]
        features = state["features"]
        if joined_data is not None and not joined_data.empty:
            app = StockAnalysisApp(ticker=state["main_ticker"])
            app.data = joined_data
            app.add_features(feature_names=features)
            state["joined_data"] = app.data
            print_step(f"Added features to joined data: {app.data}", "SimpleJoinWorkflowTool: Add Features", "gold1")
        return state
    
    def list_models(self, state: WorkflowState) -> WorkflowState:
        """List all available models."""
        models = [
            f"{cls.__name__} - {(getattr(cls, 'description', cls.__doc__ or 'No description available')).strip().splitlines()[0]}"
            for cls in MODEL_CLASSES
        ]
        print_step(f"Available models: \n• {'\n• '.join(models)}", "SimpleJoinWorkflowTool: List Models", "gold1")
        state["available_models"] = models
        return state
    
    async def select_model(self, state: WorkflowState) -> WorkflowState:
        """Select the model from the available models."""
        available_models = state["available_models"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(ModelSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the model from the available models that is most relevant to the business question. ignore the description after the hyphen, get only the class name ending with 'Model'")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable models: {available_models}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["model"] = response.model
        print_step(f"Selected model: [bold]{response.model}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "SimpleJoinWorkflowTool: Select Model", "gold1")
        state["model_reasoning"] = response.reasoning # Store reasoning
        return state

    def list_targets(self, state: WorkflowState) -> WorkflowState:
        """List all available numeric targets after loading data and features."""
        joined_data = state["joined_data"]
        numeric_columns = joined_data.select_dtypes(include=['number']).columns.tolist()
        state["available_targets"] = numeric_columns
        print_step(f"Available targets: \n• {'\n• '.join(numeric_columns)}", "SimpleJoinWorkflowTool: List Targets", "gold1")
        return state
    
    async def select_target(self, state: WorkflowState) -> WorkflowState:
        """Select the target from the available targets."""
        main_datasource = state["main_datasource"]
        auxiliary_datasource = state["auxiliary_datasource"]
        datasource_reasoning = state["datasource_reasoning"]
        join_type = state["join_type"]
        join_type_reasoning = state["join_type_reasoning"]
        selected_model = state["model"]
        model_reasoning = state["model_reasoning"]
        available_targets = state["available_targets"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(TargetSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the target from the available targets that is most relevant to the business question. The target would be a column we want to forecast. Consider the join type and columns that have postfix '_sub', to understand the relationship between the main and auxiliary datasources.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nMain datasource: {main_datasource}\nAuxiliary datasource: {auxiliary_datasource}\nDatasource reasoning: {datasource_reasoning}\nJoin type: {join_type}\nJoin type reasoning: {join_type_reasoning}\nSelected model: {selected_model}\nModel reasoning: {model_reasoning}\nAvailable targets: {available_targets}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["target"] = response.target
        print_step(f"Selected target: [bold]{response.target}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "SimpleJoinWorkflowTool: Select Target", "gold1")
        state["target_reasoning"] = response.reasoning # Store reasoning
        return state

    async def select_forecast_horizon(self, state: WorkflowState) -> WorkflowState:
        """Select the forecast horizon based on the business question and consider the joined data to find the forecast horizons."""
        business_question = state["business_question"]
        joined_data = state["joined_data"]
        target = state["target"]
        
        # Get target statistics
        target_stats = joined_data[target].describe()
        
        # For Date analysis, ensure we're working with datetime objects
        joined_data_copy = joined_data.copy()
        if joined_data_copy.index.name == 'Date':
            # Convert index to datetime if needed
            if not pd.api.types.is_datetime64_any_dtype(joined_data_copy.index):
                joined_data_copy.index = pd.to_datetime(joined_data_copy.index)
                
            # Calculate time intervals between consecutive dates
            date_series = joined_data_copy.index
            date_diffs = date_series[1:] - date_series[:-1]
            
            # Get time interval statistics
            interval_stats = {
                'min_interval': date_diffs.min(),
                'max_interval': date_diffs.max(),
                'mean_interval': date_diffs.mean(),
                'median_interval': date_diffs.median(),
                'most_common': date_diffs.value_counts().index[0] if len(date_diffs.value_counts()) > 0 else None
            }
            
            join_column_stats = f"Date interval stats: {interval_stats}"
        else:
            join_column_stats = "Date is not the index column"
            
        print_step(f"Target stats: {target_stats} \nJoin column stats: {join_column_stats}", 
                  "SimpleJoinWorkflowTool: Select Forecast Horizon", "gold1")
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(ForecastHorizon)
        system_message = SystemMessage(content="You are a ML expert selecting forecast horizon for the business question. Consider time units vary by datasource (hours/minutes/seconds/days). Forecast horizon is how far ahead we predict.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nTarget stats: {target_stats} \nJoin column stats: {join_column_stats}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["forecast_horizon"] = response.forecast_horizon
        print_step(f"Selected forecast horizon: [bold]{response.forecast_horizon}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "SimpleJoinWorkflowTool: Select Forecast Horizon", "gold1")
        state["forecast_horizon_reasoning"] = response.reasoning # Store reasoning
        return state
    

    def run_model(self, state: WorkflowState) -> WorkflowState:
        """Run the model with the selected parameters."""
        joined_data = state["joined_data"]
        target = state["target"]
        forecast_horizon = state["forecast_horizon"]
        model = state["model"]
        model_class = next((cls for cls in MODEL_CLASSES if cls.__name__ == model), None)
        app = StockAnalysisApp(ticker=state["main_ticker"])
        app.data = joined_data
        result, metrics = app.run_model(model_class=model_class, predict=target, forecast_horizon=forecast_horizon)
        
        # Ensure the index name is 'Date'
        if isinstance(result.index, pd.DatetimeIndex):
            print_debug(f"Setting DatetimeIndex name to 'Date'", "Date Column Tracking")
            result.index.name = 'Date'
            print_debug(f"Result index name now: {result.index.name}", "Date Column Tracking")
        
        state["predictions"] = result
        state["performance"] = metrics
        print_step(f"Performance: {metrics} , Predictions: {result}", "SimpleJoinWorkflowTool: Run Model", "gold1")
        return state
    

    async def generate_report(self, state: WorkflowState) -> WorkflowState:
        """Generate a report from the predictions and performance metrics."""
        # Check what data we have before processing
        print_debug(f"Before report generation - Joined data columns: {state['joined_data'].columns.tolist()}", "Date Column Tracking")
        print_debug(f"Before report generation - Joined data index: {state['joined_data'].index.name}", "Date Column Tracking")
        print_debug(f"Before report generation - Main app data columns: {state['main_app'].data.columns.tolist()}", "Date Column Tracking")
        print_debug(f"Before report generation - Aux app data columns: {state['auxiliary_app'].data.columns.tolist()}", "Date Column Tracking")
        
        if "predictions" in state and not state["predictions"].empty:
            print_debug(f"Using predictions data with columns: {state['predictions'].columns.tolist()}", "Date Column Tracking")
            print_debug(f"Predictions index type: {type(state['predictions'].index)}", "Date Column Tracking")
            print_debug(f"Predictions index name: {state['predictions'].index.name}", "Date Column Tracking")
            target_df = state["predictions"].copy()
            
            # Ensure DatetimeIndex has name='Date'
            if isinstance(target_df.index, pd.DatetimeIndex):
                if target_df.index.name is None or target_df.index.name != 'Date':
                    target_df.index.name = 'Date'
                    print_debug(f"Set predictions index name to 'Date'", "Date Column Tracking")
        else:
            print_debug("No predictions data available, using joined_data", "Date Column Tracking")
            target_df = state["joined_data"].copy()
            if isinstance(target_df.index, pd.DatetimeIndex) and (target_df.index.name is None or target_df.index.name != 'Date'):
                target_df.index.name = 'Date'
        
        # Make sure main_df, aux_df, and target_df have Date as index if they're not already
        main_df = state["main_app"].data.copy()
        aux_df = state["auxiliary_app"].data.copy()
        
        print_debug(f"Before report generation - target_df columns: {target_df.columns.tolist()}", "Date Column Tracking")
        print_debug(f"Before report generation - target_df index: {target_df.index.name}", "Date Column Tracking")
        
        # Ensure all DataFrames have DatetimeIndex with name='Date'
        if isinstance(main_df.index, pd.DatetimeIndex) and (main_df.index.name is None or main_df.index.name != 'Date'):
            main_df.index.name = 'Date'
            print_debug("Set main_df index name to 'Date'", "Date Column Tracking")
            
        if isinstance(aux_df.index, pd.DatetimeIndex) and (aux_df.index.name is None or aux_df.index.name != 'Date'):
            aux_df.index.name = 'Date'
            print_debug("Set aux_df index name to 'Date'", "Date Column Tracking")
        
        # DO NOT reset the index - leave Date as the index
        print_debug(f"Final target_df columns: {target_df.columns.tolist()}", "Date Column Tracking")
        print_debug(f"Final target_df index: {target_df.index.name}", "Date Column Tracking")
        
        report_params = {
            "main_df": main_df,
            "aux_df": aux_df,
            "target_df": target_df,
            "main_datasource_id": state["main_datasource"],
            "aux_datasource_id": state["auxiliary_datasource"],
            "join_column": state["join_column"],
            "join_type": state["join_type"],
            "features": state["features"],
            "model_name": state["model"],
            "prediction_column": state["target"],
            "forecast_horizon": state["forecast_horizon"],
            "performance": state["performance"],
            "main_ticker": state["main_ticker"]
        }
        print_debug(f"Report params - target_df columns: {report_params['target_df'].columns.tolist()}", "Date Column Tracking")
        print_debug(f"Report params - target_df index: {report_params['target_df'].index.name}", "Date Column Tracking")
        print_debug(f"Data passed to ReportFactory - Columns: {target_df.columns.tolist()}, Index: {target_df.index.name}", "Report Generation Debug")

        report = ReportFactory.create_join_report(report_params)
        report_repository = await ReportRepository.create() # Use create() to get initialized instance
        report_id = await report_repository.insert_report(report)
        print_step(f"Report ID: {report_id}", "SimpleJoinWorkflowTool: Generate Report", "gold1")
        state["report_id"] = report_id
        return state
    
    async def process_report(self, state: WorkflowState) -> WorkflowState:
        """Process the report and return the formatted answer in markdown."""
        report_repository = await ReportRepository.create() # Use create() to get initialized instance
        report = await report_repository.get_report(state["report_id"])

        if report is None:
            state["error"] = f"Report with ID {state['report_id']} not found."
            print_step(f"Error: {state['error']}", "SimpleWorkflowTool: Process Report", "red")
            return state

        forecast_horizon = report["workflow"]["model"]["forecast_horizon"]
        predicted_col = f"{state['target']}_Predicted"
        actual_col = state['target']
        last_actual_idx = state["predictions"].index[-forecast_horizon-1]
        last_actual_data = state["predictions"].loc[last_actual_idx]
        forecast_data = state["predictions"].iloc[-forecast_horizon:]

        #construct the last actual and forecast data
        last_actual = {
            "date": last_actual_idx.strftime('%Y-%m-%d'),
            "value": float(last_actual_data[actual_col])
        }
        forecast = [
            {
                "date": idx.strftime('%Y-%m-%d'),
                "value": float(row[predicted_col])
            }
            for idx, row in forecast_data.iterrows()
        ]

        last_actual_value = float(last_actual_data.get(actual_col, 0))
        final_pred = forecast[-1].get("value", 0)
        pct_change = ((final_pred / last_actual_value) - 1) * 100 if last_actual_value else 0
        direction_text = "Upward" if final_pred > last_actual_value else "Downward"

        # Format available datasources, features, models, targets as markdown lists
        def format_md_list(items):
            return "\n".join([f"- {item}" for item in items])

        # Construct markdown output
        markdown_output = f"""# Investment Analysis Report

## Business Question
{state["business_question"]}

## Ticker Analysis
**Main Ticker**: {state["main_ticker"]}
**Auxiliary Ticker**: {state["auxiliary_ticker"]}

### Available Data Sources
{format_md_list(state["available_datasources"])}

## Data Source Selection
**Main Data Source**: {state["main_datasource"]}  
**Auxiliary Data Source**: {state["auxiliary_datasource"]}
**Reasoning**: {state["datasource_reasoning"]}

## Join Type Selection
**Join Type**: {state["join_type"]}
**Join Column**: {state["join_column"]}
**Reasoning**: {state["join_type_reasoning"]}

## Feature Selection
### Available Features
{format_md_list(state["available_features"])}

**Selected Features**:
{format_md_list(state["features"])}

**Reasoning**: {state["features_reasoning"]}

## Model Selection
### Available Models
{format_md_list(state["available_models"])}

**Selected Model**: {state["model"]}  
**Reasoning**: {state["model_reasoning"]}

## Target Selection
### Available Targets
{format_md_list(state["available_targets"])}

**Selected Target**: {state["target"]}  
**Reasoning**: {state["target_reasoning"]}

## Forecast Horizon Selection
**Selected Horizon**: {state["forecast_horizon"]} days  
**Reasoning**: {state["forecast_horizon_reasoning"]}

## Model Performance
**Performance Metrics**:
{format_md_list([f"{k}: {v:.4f}" for k, v in state["performance"].items()])}

## Forecast Results
**Last Known Value**: {last_actual["value"]:.2f} ({last_actual["date"]})  
**Predicted Value**: {final_pred:.2f} ({forecast[-1]["date"]}, horizon: {state["forecast_horizon"]} days)  
**Direction**: {direction_text}  
**Predicted Change**: {pct_change:.2f}%

**Report ID**: {state["report_id"]}
"""

        state["formatted_answer"] = markdown_output
        
        print_step(f"Report:\\n{markdown_output}", "SimpleJoinWorkflowTool: Process Report", "gold1")
        return state


if __name__ == "__main__":
    import asyncio
    
    async def main():
        tool = SimpleJoinWorkflowTool()
        result = await tool._arun(business_question="Compare Apple (AAPL) and Microsoft (MSFT) daily closing prices over the past year using inner join.")
        print(result)

    asyncio.run(main())
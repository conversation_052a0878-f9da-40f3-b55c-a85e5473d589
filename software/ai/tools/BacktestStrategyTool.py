from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from typing import Dict, Any, Optional, Type, List, Sequence, TypedDict
from software.ai.llm.llm_connect import get_llm_connect
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from data import registry
from api.database import get_sync_db
from main import StockAnalysisApp
import pandas as pd

class BacktestStrategyToolSchema(BaseModel):
    """Schema for BacktestStrategyTool input parameters"""
    business_question: str = Field(
        ...,
        description="A specific backtesting request focused on price movements, technical indicators, or trading patterns. The question should reference a ticker in parentheses and can target various prediction horizons (hours to months) and metrics (price levels, momentum indicators, volatility patterns).",
        min_length=10,
        examples=[
            "Backtest a strategy that buys Meta (META) when the RSI is below 30 and sells when it's above 70 over the past year.",
            "Evaluate the performance of a moving average crossover strategy on Amazon (AMZN) with a 50-day and 200-day moving average over the past 2 years.",
            "Backtest a Bollinger Bands strategy on Tesla (TSLA) by buying when the price touches the lower band and selling when it touches the upper band over the past 6 months.",
            "Backtest a strategy that buys Microsoft (MSFT) when the MACD crosses above the signal line and sells when it crosses below over the past 1 year."
            "Backtest a strategy that buys Apple (AAPL) when the price breaks above the upper Bollinger Band and sells when it breaks below the lower band over the past 3 years.",
            "Backtest a strategy that buys Google (GOOGL) when the stochastic oscillator is below 20 and sells when it's above 80 over the past 18 months."
        ]
    )
class TickerSelection(BaseModel):
    """Schema for TickerSelection input parameters"""
    ticker: str = Field(
        ...,
        description="The ticker to select from the business question considering the available data sources.",
        examples=["META", "AMZN", "AMD", "JPM", "MSFT"]
    )

class DatasourceSelection(BaseModel):
    """Schema for DatasourceSelection input parameters"""
    datasource: str = Field(
        ...,
        description="The datasource to select from the available datasources.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the datasource from the available datasources."
    )

class WorkflowState(TypedDict):
    """State for the workflow"""
    messages: Sequence[BaseMessage]
    business_question: str
    available_datasources: List[str]
    ticker: str
    datasource: str
    datasource_reasoning: str
    start_date: str
    end_date: str
    rows: int
    app: Any
    sample_data: pd.DataFrame
    code: str
    parsed_code: str
    chart_result: Optional[str]
    execution_output: Optional[str]
    execution_error: Optional[str]
    chart_interpretation: Optional[str]
    interpretation_error: Optional[str]
    formatted_answer: str

class BacktestStrategyTool(BaseTool):
    """A powerful backtesting tool that leverages AI to develop, implement, and evaluate trading strategies."""

    name: str = "BacktestStrategyTool"
    description: str = "A powerful backtesting tool that leverages AI to develop, implement, and evaluate trading strategies. This tool selects appropriate data loaders, pulls historical market data, generates optimized trading strategies using AI, executes backtests against historical data, and produces comprehensive performance reports with key metrics like returns, drawdowns, Sharpe ratio, and win rate. Perfect for validating investment hypotheses and quantifying strategy performance before real-world implementation."
    category: str = "Backtesting"
    version: str = "1.0.0"
    args_schema: Type[BaseModel] = BacktestStrategyToolSchema

    async def _arun(self, business_question: str) -> Dict[str, Any]:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with parameter: {business_question}", "Tool Execution")
        workflow = StateGraph(WorkflowState)
        workflow.add_node("list_datasources", self.list_datasources)
        workflow.add_node("extract_ticker", self.extract_ticker)
        workflow.add_node("select_datasource", self.select_datasource)
        workflow.add_node("list_date_ranges", self.list_date_ranges)
        workflow.add_node("extract_sample_data", self.extract_sample_data)
        workflow.add_node("create_chart", self.create_chart)
        workflow.add_node("parse_chart_code", self.parse_chart_code)
        workflow.add_node("execute_code", self.execute_code)
        workflow.add_node("read_chart", self.read_chart)

        workflow.add_edge(START, "list_datasources")
        workflow.add_edge("list_datasources", "extract_ticker")
        workflow.add_edge("extract_ticker", "select_datasource")
        workflow.add_edge("select_datasource", "list_date_ranges")
        workflow.add_edge("list_date_ranges", "extract_sample_data")
        workflow.add_edge("extract_sample_data", "create_chart")
        workflow.add_edge("create_chart", "parse_chart_code")
        workflow.add_edge("parse_chart_code", "execute_code")
        workflow.add_edge("execute_code", "read_chart")
        workflow.add_edge("read_chart", END)

        compiled_graph = workflow.compile()
        initial_state = {
            "messages": [HumanMessage(content=business_question)],
            "business_question": business_question,
            "formatted_answer": "Analysis pending. Currently only listing available datasources.",
        }

        final_state = await compiled_graph.ainvoke(initial_state)

        return final_state['formatted_answer']

    def list_datasources(self, state: WorkflowState) -> WorkflowState:
        """List all available data sources synchronously."""
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        state["available_datasources"] = [ds["name"]+ " (" + ds["description"] + ")" for ds in datasources_cursor]
        print_step(f"Available datasources:\n• {'\n• '.join(state['available_datasources'])}", "BacktestStrategyTool: List Datasources", "wheat1")
        return state

    async def extract_ticker(self, state: WorkflowState) -> WorkflowState:
        """Extract the ticker from the business question."""
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(TickerSelection)
        business_question = state["business_question"]

        system_message = SystemMessage(content=f"""
<role>
You are a financial data parser specializing in extracting stock ticker symbols from text.
</role>

<task>
Extract the stock ticker symbol from the business question.
</task>

<output_format>
{TickerSelection.model_json_schema()}
</output_format>

<extraction_rules>
1. Look for stock symbols in parentheses (e.g., "Apple (AAPL)")
2. Return only the ticker symbol without parentheses
3. If multiple tickers exist, select the most relevant one to the main strategy
</extraction_rules>
""")

        human_message = HumanMessage(content=f"""
<business_question>
{business_question}
</business_question>

Extract the ticker symbol from this business question.
""")

        messages = [system_message, human_message]
        try:
            response = await structured_model.ainvoke(messages)
            if response and hasattr(response, 'ticker'):
                state["ticker"] = response.ticker
                print_step(f"Extracted ticker: {response.ticker}", "BacktestStrategyTool: Extract Ticker", "wheat1")
            else:
                # Fallback to a simple extraction if structured output fails
                ticker = "AMZN"  # Default fallback ticker
                if "(" in business_question and ")" in business_question:
                    start = business_question.find("(") + 1
                    end = business_question.find(")", start)
                    if start < end:
                        extracted = business_question[start:end].strip()
                        if extracted:
                            ticker = extracted

                state["ticker"] = ticker
                print_step(f"Extracted ticker (fallback): {ticker}", "BacktestStrategyTool: Extract Ticker", "wheat1")
        except Exception as e:
            # Fallback to a simple extraction if structured output fails
            ticker = "AMZN"  # Default fallback ticker
            if "(" in business_question and ")" in business_question:
                start = business_question.find("(") + 1
                end = business_question.find(")", start)
                if start < end:
                    extracted = business_question[start:end].strip()
                    if extracted:
                        ticker = extracted

            state["ticker"] = ticker
            print_step(f"Extracted ticker (error fallback): {ticker}", "BacktestStrategyTool: Extract Ticker", "wheat1")
        return state

    async def select_datasource(self, state: WorkflowState) -> WorkflowState:
        """Select the datasource from the available datasources."""
        available_datasources = state["available_datasources"]
        business_question = state["business_question"]
        ticker = state["ticker"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DatasourceSelection)

        system_message = SystemMessage(content=f"""
<role>
You are a financial data specialist with expertise in selecting optimal data sources for backtesting trading strategies.
</role>

<task>
Select the most appropriate datasource for backtesting a trading strategy based on the business question and available datasources.
</task>

<context>
The selection will be used to retrieve historical market data for {ticker} to backtest a trading strategy.
The quality and completeness of the data source directly impacts the accuracy of the backtesting results.
</context>

<selection_criteria>
1. Data completeness: Choose sources with comprehensive historical data
2. Data frequency: Select sources that match the timeframe in the business question
3. Data reliability: Prioritize sources known for accurate and clean data
4. Data coverage: Prioritize sources that provide the necessary data for the trading strategy
</selection_criteria>

<output_format>
You must respond using this exact schema:
{DatasourceSelection.model_json_schema()}
</output_format>

<error_handling>
If multiple datasources seem equally appropriate:
- Compare their descriptions for relevant data coverage
- Select the one with the most comprehensive data
- Provide clear reasoning for your selection
</error_handling>
""")

        human_message = HumanMessage(content=f"""
<business_question>
{business_question}
</business_question>

<ticker>
{ticker}
</ticker>

<available_datasources>
{available_datasources}
</available_datasources>

Select exactly ONE datasource from the list that is most appropriate for this backtesting strategy.
Ignore parentheses descriptions when providing your final datasource name.
""")

        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["datasource"] = response.datasource
        print_step(f"Selected datasource: [bold]{response.datasource}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "BacktestStrategyTool: Select Datasource", "wheat1")
        state["datasource_reasoning"] = response.reasoning # Store reasoning
        return state

    async def list_date_ranges(self, state: WorkflowState) -> WorkflowState:
        """List start and end dates available for the selected datasource."""
        loader_class = registry.get_loader_class(state["datasource"])
        if loader_class:
            app = StockAnalysisApp(ticker=state["ticker"])
            app.load_data(loader_class=loader_class) # Load all data

            if app.data is not None and not app.data.empty:
                start_date = app.data.index.min()
                end_date = app.data.index.max()
                rows = app.data.shape[0]
                state["app"] = app
                state["start_date"] = start_date
                state["end_date"] = end_date
                state["rows"] = rows
                print_step(f"Available date ranges: {start_date} to {end_date} with {rows} rows", "BacktestStrategyTool: List Date Ranges", "wheat1")
        return state

    async def extract_sample_data(self, state: WorkflowState) -> WorkflowState:
        """Extract the data from the selected date range."""
        app = state["app"]
        start_date = state["start_date"]
        end_date = state["end_date"]
        rows = state["rows"]

        # Filter existing data by date range instead of reloading
        filtered_data = app.data[
            (app.data.index >= start_date) &
            (app.data.index <= end_date)
        ]

        # Get evenly distributed samples across the date range
        if len(filtered_data) > rows:
            # Calculate indices to sample evenly across the range
            indices = []
            if rows > 1:
                step = len(filtered_data) / rows
                for i in range(rows):
                    indices.append(int(i * step))
            else:
                indices = [0]

            sample_data = filtered_data.iloc[indices]
        else:
            sample_data = filtered_data

        # Sort chronologically for display
        sample_data = sample_data.sort_index(ascending=True)
        state["sample_data"] = sample_data
        print_step(f"Extracted data: {sample_data}", "BacktestStrategyTool: Extract Data", "wheat1")
        return state

    async def create_chart(self, state: WorkflowState) -> WorkflowState:
        """Create visualization charts for backtesting strategy simulation.

        This node generates appropriate visualizations based on the sample data
        and the backtesting strategy described in the business question.
        """
        # Debug information about state
        print_debug(f"State keys: {list(state.keys())}", "BacktestStrategyTool: Create Chart - State")
        print_debug(f"Business question: {state.get('business_question', 'Not found')}", "BacktestStrategyTool: Create Chart - Input")

        # Define variables that might be referenced in the generated code
        import uuid
        ticker = state.get("ticker", "").lower()
        unique_id = str(uuid.uuid4())[:8]
        print_debug(f"Ticker: {ticker}, Unique ID: {unique_id}", "BacktestStrategyTool: Create Chart - Setup")

        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        print_debug(f"LLM model loaded: {model.__class__.__name__}", "BacktestStrategyTool: Create Chart - Setup")

        system_message = SystemMessage(content=f"""
<role>
You are a quantitative finance expert specializing in developing, implementing, and evaluating trading strategies through backtesting.
</role>

<task>
Create Python code using matplotlib and mplfinance that implements and visualizes a complete backtesting strategy simulation based on the business question and sample data.
</task>

<context>
This backtesting tool validates investment hypotheses and quantifies strategy performance before real-world implementation.
The strategy will be applied to {state["ticker"]} using technical indicators that you will generate based on the trading rules described in the business question.
The simulation will start with an initial investment of $10,000 to provide realistic portfolio growth metrics.
</context>

<output_requirements>
1. Create a function called `create_chart` that takes a pandas DataFrame as its only parameter
2. Implement the complete trading strategy logic described in the business question
3. Generate a consistent 4-panel visualization with:
   - Panel 1: Price chart with buy/sell signals and overlaid technical indicators
   - Panel 2: Strategy returns vs. buy-and-hold returns (percentage)
   - Panel 3: $10,000 initial investment growth for both strategy and buy-and-hold
   - Panel 4: Drawdown analysis
4. Include a text box with key performance metrics
5. Save the chart to `software/library/images` with a filename pattern: `backtest_strategy_{ticker}_{unique_id}.png`
6. Return the path to the saved image
</output_requirements>

<technical_guidelines>
<position_tracking>
Implement strict non-overlapping trade management:
- Initialize position_state = 0 (flat/no position)
- Generate buy signals ONLY when position_state = 0
- Generate sell signals ONLY when position_state = 1
- Update position_state immediately after each trade
- Verify no consecutive buys or sells can occur
</position_tracking>

<visualization_template>
Create a professional multi-panel backtesting visualization using mplfinance and matplotlib:

1. Figure and Layout Setup:
   - Use mpf.plot() with returnfig=True to get figure and axes
   - Apply style='yahoo' or another professional mpf style
   - Use figsize=(12, 10) for a large, detailed chart
   - IMPORTANT: For panel_ratios, use only (4, 1) for price and volume panels
   - Create additional subplots manually after getting the figure from mpf.plot()

2. Price Panel (Primary):
   - Use candlestick chart with type='candle'
   - Add moving averages as colored lines with addplot
   - Mark buy/sell signals using the correct approach:
     * Create Series with NaN values and same index as df
     * Set values only at signal points (buy_markers.loc[buy_signals] = df.loc[buy_signals, 'High'])
     * Add as scatter plot with mpf.make_addplot(buy_markers, type='scatter')
   - Format y-axis with dollar signs

3. Volume Panel:
   - Include volume=True in mpf.plot for automatic volume display
   - Add volume moving average as an additional plot if needed

4. Performance Panels:
   - After getting fig, axes from mpf.plot(), create additional subplots with fig.add_subplot()
   - Example:
     * fig.add_subplot(4, 1, 3, sharex=axes[0])
     * fig.add_subplot(4, 1, 4, sharex=axes[0])
   - Plot equity curve, returns, and drawdowns in these additional panels

5. Performance Metrics Box:
   - Add text box with key metrics using axes[0].text():
     * Total Return (%)
     * Sharpe Ratio
     * Maximum Drawdown (%)
     * Win Rate (%)
     * Final $10K Value ($)
   - Use bbox parameter with props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)

6. Chart Annotations and Styling:
   - Add clear title with strategy name and ticker
   - Add gridlines with appropriate alpha for readability
   - Format axes with financial conventions ($ for prices, % for returns)

7. Save and Return:
   - Define file_path = os.path.join('software/library/images', f'backtest_strategy_{ticker.lower()}_{unique_id}.png')
   - Save with plt.savefig(file_path, dpi=100, bbox_inches='tight')
   - Call plt.close(fig) to release memory
   - Return file_path as the last line of the function

Example for creating additional panels:
```python
# Get the main figure and axes from mplfinance
fig, axes = mpf.plot(df, type='candle', addplot=addplot, volume=True,
                    style='yahoo', figsize=(12, 10),
                    panel_ratios=(4, 1), # Only use 2 panels here
                    title=f'50-day MA Crossover Strategy - {ticker.upper()}',
                    returnfig=True)

# Create additional subplots for performance metrics
# Adjust the figure to make room for the new subplots
plt.subplots_adjust(hspace=0.3)

# Create two more axes for equity curve and drawdown
ax_equity = fig.add_subplot(4, 1, 3, sharex=axes[0])
ax_drawdown = fig.add_subplot(4, 1, 4, sharex=axes[0])

# Plot equity curve
ax_equity.plot(df.index, df['Strategy_Equity'], label='Strategy')
ax_equity.plot(df.index, df['Buy_Hold_Equity'], label='Buy-and-Hold')
ax_equity.legend(loc='upper left')
ax_equity.set_title('Equity Curve')
ax_equity.set_ylabel('Equity')
ax_equity.grid(True, alpha=0.3)

# Plot drawdown
ax_drawdown.plot(df.index, df['Strategy_Equity'] / df['Strategy_Equity'].cummax() - 1, label='Drawdown')
ax_drawdown.legend(loc='upper left')
ax_drawdown.set_title('Drawdown')
ax_drawdown.set_ylabel('Drawdown')
ax_drawdown.grid(True, alpha=0.3)
```
</visualization_template>

<performance_metrics>
Calculate these comprehensive performance metrics:

1. Return Metrics:
   - Total Return (%): ((Final Portfolio Value / Initial Investment) - 1) × 100
   - Annualized Return (%): ((1 + Total Return)^(252/trading_days)) - 1) × 100
   - Excess Return (%): Strategy Return - Benchmark Return
   - $10,000 Growth: Final value of $10,000 initial investment

2. Risk Metrics:
   - Maximum Drawdown (%): (Trough Value - Peak Value) / Peak Value × 100
   - Volatility (%): Standard deviation of daily returns × sqrt(252) × 100
   - Downside Deviation (%): Standard deviation of negative returns only × sqrt(252) × 100
   - Value at Risk (95%): 5th percentile of daily returns × sqrt(252) × 100

3. Risk-Adjusted Performance:
   - Sharpe Ratio: (Mean Daily Return) / (Standard Deviation of Daily Returns) × sqrt(252)
   - Sortino Ratio: (Mean Daily Return) / (Downside Deviation) × sqrt(252)
   - Calmar Ratio: Annualized Return / Maximum Drawdown
   - Information Ratio: Excess Return / Tracking Error

4. Trade Statistics:
   - Win Rate (%): (Number of Winning Trades / Total Trades) × 100
   - Profit Factor: Gross Profits / Gross Losses
   - Average Win (%): Mean return of winning trades
   - Average Loss (%): Mean return of losing trades
   - Win/Loss Ratio: Average Win / |Average Loss|
   - Average Holding Period (days): Mean duration of trades

5. Market Exposure:
   - Time in Market (%): (Days with active position / Total days) × 100
   - Average Position Size (%): Mean percentage of capital deployed
</performance_metrics>

<error_handling>
Implement robust financial data error handling:

1. Data Quality Issues:
   - Missing Values: Apply forward-fill (ffill) for price data gaps
   - Weekend/Holiday Gaps: Use business day calendars for proper date alignment
   - Split/Dividend Adjustments: Verify price data is properly adjusted
   - Outliers: Detect and handle extreme values using rolling z-scores

2. Calculation Safeguards:
   - Division by Zero: Use np.divide with 'ignore' parameter for ratio calculations
   - Log of Zero/Negative: Apply np.log1p instead of np.log for returns
   - NaN Propagation: Use pandas' skipna=True for statistical functions
   - Infinity Values: Replace with np.nan before calculations

3. Indicator Robustness:
   - Insufficient History: Dynamically adjust lookback periods for indicators
   - Indicator Crossovers: Implement state machines to prevent false signals
   - Signal Conflicts: Create priority rules for contradicting indicators
   - Parameter Sensitivity: Test indicator stability across parameter ranges

4. Visualization Integrity:
   - Y-Axis Scaling: Use appropriate scales for each panel (log scale for long periods)
   - Date Alignment: Ensure all panels share identical x-axis dates
   - Color Consistency: Maintain consistent color scheme for buy/sell signals
   - Annotation Overlap: Implement smart positioning to prevent text overlap

5. Performance Calculation Accuracy:
   - Return Compounding: Use geometric not arithmetic means for multi-period returns
   - Drawdown Precision: Calculate using high-water mark methodology
   - Trading Costs: Include realistic slippage and commission models
   - Benchmark Alignment: Ensure strategy and benchmark use identical time periods
</error_handling>
</technical_guidelines>

<output_format>
Return only the Python code wrapped between ```python and ``` markers.
Do not include explanations, validation code, or example usage outside the create_chart function.

CRITICAL: Your create_chart function MUST end with a return statement that returns the file path.
Example of correct function ending:

    # Save the chart
    file_path = os.path.join('software/library/images', f'backtest_strategy_{ticker.lower()}_{unique_id}.png')
    plt.savefig(file_path, dpi=100, bbox_inches='tight')
    plt.close(fig)

    # Return the file path as the last line
    return file_path
</output_format>
""")

        human_message = HumanMessage(content=f"""
<business_question>
{state["business_question"]}
</business_question>

<sample_data>
{state["sample_data"]}
</sample_data>

<data_columns>
{", ".join(state["sample_data"].columns)}
</data_columns>

<implementation_requirements>
1. Implement the exact trading strategy described in the business question with precision
2. Calculate all performance metrics specified in the performance_metrics section
3. Create a professional multi-panel visualization following the visualization_template guidelines:
   - Adapt chart types and indicators to match the specific strategy
   - Include price, volume, and performance panels at minimum
   - Add relevant technical indicators based on strategy logic
   - Ensure all panels share consistent x-axis alignment

4. Financial Calculation Requirements:
   - Start with $10,000 initial investment and track growth
   - Calculate both strategy and buy-hold benchmark returns
   - Use proper financial math (compound returns, not simple)
   - Apply appropriate annualization factors (√252 for daily data)
   - Handle trading costs if specified in business question

5. Visualization Quality Standards:
   - Use professional financial charting conventions
   - Implement clear, readable formatting for all text elements
   - Ensure proper scaling for all chart components
   - Apply consistent color scheme throughout
   - Include comprehensive but uncluttered legends

6. File Handling Requirements:
   - Save chart with filename: backtest_strategy_{ticker}_{unique_id}.png
   - Use high-resolution output (dpi=100 minimum)
   - Set bbox_inches='tight' to prevent clipping
   - Close the figure to prevent memory leaks

7. CRITICAL: The create_chart function MUST return the full file path as a string
   - Example: return os.path.join('software/library/images', f'backtest_strategy_{ticker}_{unique_id}.png')
   - This is the LAST line of the function
</implementation_requirements>

<filename_variables>
- ticker: {ticker}
- unique_id: {unique_id}
</filename_variables>

<important_notes>
1. You MUST define all variables used in your function within the function scope.
2. Do NOT reference any variables that are not defined within the function or passed as parameters.
3. The ticker and unique_id variables should be defined within your function as:
   ticker = "{ticker}"
   unique_id = "{unique_id}"
4. When creating the file path, use these variables directly:
   file_path = os.path.join('software/library/images', f'backtest_strategy_{ticker}_{unique_id}.png')
5. Make sure to import all necessary libraries at the top of your function.
</important_notes>
"""
        )

        response = await model.ainvoke([system_message, human_message])
        print_step(f"Generated chart code for backtesting strategy", "BacktestStrategyTool: Create Chart Code", "wheat1")

        # Update the state with the chart code
        state["code"] = response.content
        return state

    async def parse_chart_code(self, state: WorkflowState) -> WorkflowState:
        """Parse the Python code from the state's code field.

        This node extracts the actual Python code from the markdown code block
        and stores it in a clean format for execution.
        """
        code = state.get("code", "")
        print_debug(f"Code length: {len(code)}", "BacktestStrategyTool: Parse Chart Code - Input")
        print_debug(f"Code preview (first 100 chars): {code[:100]}...", "BacktestStrategyTool: Parse Chart Code - Input")

        # Extract code between ```python and ``` markers
        if "```python" in code and "```" in code[code.find("```python") + 9:]:
            print_debug("Found ```python and ``` markers", "BacktestStrategyTool: Parse Chart Code - Processing")
            start_idx = code.find("```python") + 9
            end_idx = code.find("```", start_idx)
            parsed_code = code[start_idx:end_idx].strip()
            print_debug(f"Extracted code between markers (length: {len(parsed_code)})", "BacktestStrategyTool: Parse Chart Code - Processing")

            # Store the parsed code in the state
            state["parsed_code"] = parsed_code

            # Print the last 20 lines of the code to check the return statement
            last_20_lines = "\n".join(parsed_code.split("\n")[-20:])
            print_debug(f"Last 20 lines of code:\n{last_20_lines}", "BacktestStrategyTool: Parse Chart Code - Debug")
        else:
            print_debug("Could not find proper code markers, using fallback method", "BacktestStrategyTool: Parse Chart Code - Warning")
            # Handle case where code is not properly formatted
            state["parsed_code"] = code.replace("```python", "").replace("```", "").strip()
            print_debug(f"Fallback parsed code length: {len(state['parsed_code'])}", "BacktestStrategyTool: Parse Chart Code - Processing")

        # Debug the first few lines of parsed code
        parsed_preview = "\n".join(state["parsed_code"].split("\n")[:5]) + "\n..."
        print_debug(f"Parsed code preview (first 5 lines):\n{parsed_preview}", "BacktestStrategyTool: Parse Chart Code - Output")

        print_step("Parsed chart code for execution", "BacktestStrategyTool: Parse Chart Code", "wheat1")
        return state

    async def execute_code(self, state: WorkflowState) -> WorkflowState:
        """Execute the parsed chart code with the sample data.

        This node takes the parsed Python code and executes it using the sample data
        to generate the visualization.
        """
        import os
        import sys
        from io import StringIO
        import traceback

        parsed_code = state.get("parsed_code", "")
        sample_data = state.get("sample_data")

        # Debug information about state
        print_debug(f"State keys: {list(state.keys())}", "BacktestStrategyTool: Execute Chart Code - State")

        if not parsed_code or sample_data is None or sample_data.empty:
            print_debug("Missing code or data for execution", "BacktestStrategyTool: Execute Chart Code - Error")
            if not parsed_code:
                print_debug("Parsed code is empty", "BacktestStrategyTool: Execute Chart Code - Error")
            if sample_data is None:
                print_debug("Sample data is None", "BacktestStrategyTool: Execute Chart Code - Error")
            elif sample_data.empty:
                print_debug("Sample data is empty", "BacktestStrategyTool: Execute Chart Code - Error")
            print_step("Missing code or data for execution", "BacktestStrategyTool: Execute Chart Code", "wheat1")
            return state

        # Debug information about data
        print_debug(f"Sample data shape: {sample_data.shape}", "BacktestStrategyTool: Execute Chart Code - Data")
        print_debug(f"Sample data columns: {sample_data.columns.tolist()}", "BacktestStrategyTool: Execute Chart Code - Data")

        # Create directory for images if it doesn't exist
        image_dir = "software/library/images"
        os.makedirs(image_dir, exist_ok=True)
        print_debug(f"Image directory created/verified: {image_dir}", "BacktestStrategyTool: Execute Chart Code - Setup")

        # Debug the first few lines of code
        code_preview = "\n".join(parsed_code.split("\n")[:10]) + "\n..."
        print_debug(f"Code preview (first 10 lines):\n{code_preview}", "BacktestStrategyTool: Execute Chart Code - Code")

        # Prepare a temporary module to execute the code
        try:
            # Create a namespace for execution
            namespace = {
                "pd": pd,
                "plt": __import__("matplotlib.pyplot").pyplot,
                "np": __import__("numpy"),
                "uuid": __import__("uuid"),
                "mpf": __import__("mplfinance"),
                "os": __import__("os"),
                "df": sample_data,  # Pass the sample data as df
                "data": sample_data  # Also pass as data for flexibility
            }
            print_debug(f"Namespace created with keys: {list(namespace.keys())}", "BacktestStrategyTool: Execute Chart Code - Setup")

            # Capture stdout to get any print output
            old_stdout = sys.stdout
            redirected_output = StringIO()
            sys.stdout = redirected_output

            # Execute the code
            print_debug("Executing code...", "BacktestStrategyTool: Execute Chart Code - Execution")
            try:
                exec(parsed_code, namespace)
                print_debug("Code execution completed", "BacktestStrategyTool: Execute Chart Code - Execution")

                # Debug the namespace after execution
                print_debug(f"Namespace after execution: {[k for k in namespace.keys() if not k.startswith('__')]}", "BacktestStrategyTool: Execute Chart Code - Debug")

                # Check if create_chart function exists
                if "create_chart" in namespace:
                    print_debug(f"create_chart function source:\n{namespace['create_chart'].__code__}", "BacktestStrategyTool: Execute Chart Code - Debug")
            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print_debug(f"Error during code execution: {str(e)}\n{error_details}", "BacktestStrategyTool: Execute Chart Code - Error")

            # Check if create_chart function was defined
            if "create_chart" in namespace:
                print_debug("create_chart function found in namespace", "BacktestStrategyTool: Execute Chart Code - Execution")
                # Call the create_chart function with the sample data
                result = None
                try:
                    print_debug(f"Sample data type: {type(sample_data)}, shape: {sample_data.shape}", "BacktestStrategyTool: Execute Chart Code - Debug")
                    print_debug("Calling create_chart function with sample data...", "BacktestStrategyTool: Execute Chart Code - Execution")

                    # Add a custom wrapper to debug the function
                    def debug_create_chart(df):
                        print_debug("Inside debug_create_chart wrapper", "BacktestStrategyTool: Execute Chart Code - Debug")
                        try:
                            print_debug("Calling create_chart function...", "BacktestStrategyTool: Execute Chart Code - Debug")
                            result = namespace["create_chart"](df)
                            print_debug(f"create_chart returned: {result} (type: {type(result)})", "BacktestStrategyTool: Execute Chart Code - Debug")

                            # Additional validation of the result
                            if result is None:
                                print_debug("ERROR: create_chart returned None", "BacktestStrategyTool: Execute Chart Code - Error")
                            elif not isinstance(result, str):
                                print_debug(f"ERROR: create_chart returned non-string value: {result} (type: {type(result)})", "BacktestStrategyTool: Execute Chart Code - Error")
                            elif not result.endswith('.png'):
                                print_debug(f"ERROR: create_chart returned path without .png extension: {result}", "BacktestStrategyTool: Execute Chart Code - Error")
                            else:
                                print_debug(f"Valid file path returned: {result}", "BacktestStrategyTool: Execute Chart Code - Success")

                                # Check if the file exists
                                if os.path.exists(result):
                                    print_debug(f"File exists at path: {result}", "BacktestStrategyTool: Execute Chart Code - Success")
                                else:
                                    print_debug(f"File does NOT exist at path: {result}", "BacktestStrategyTool: Execute Chart Code - Error")

                                    # Check if the directory exists
                                    dir_path = os.path.dirname(result)
                                    if os.path.exists(dir_path):
                                        print_debug(f"Directory exists: {dir_path}", "BacktestStrategyTool: Execute Chart Code - Debug")
                                        print_debug(f"Directory contents: {os.listdir(dir_path)}", "BacktestStrategyTool: Execute Chart Code - Debug")
                                    else:
                                        print_debug(f"Directory does NOT exist: {dir_path}", "BacktestStrategyTool: Execute Chart Code - Error")

                            return result
                        except Exception as e:
                            import traceback
                            error_details = traceback.format_exc()
                            print_debug(f"Exception in debug_create_chart: {str(e)}\n{error_details}", "BacktestStrategyTool: Execute Chart Code - Error")
                            raise

                    result = debug_create_chart(sample_data)

                    print_debug(f"create_chart function returned: {result} (type: {type(result)})", "BacktestStrategyTool: Execute Chart Code - Execution")
                    if result is None:
                        print_debug("create_chart function returned None", "BacktestStrategyTool: Execute Chart Code - Error")
                        state["execution_error"] = "create_chart function returned None"
                    else:
                        state["chart_result"] = str(result)
                        print_debug(f"Setting chart_result in state to: {state['chart_result']}", "BacktestStrategyTool: Execute Chart Code - Debug")

                    # Verify the chart file exists
                    if result and os.path.exists(result):
                        state["chart_result"] = result
                        print_debug(f"Chart file exists at path: {result}", "BacktestStrategyTool: Execute Chart Code - Verification")
                        print_debug(f"Updated state with chart_result: {state['chart_result']}", "BacktestStrategyTool: Execute Chart Code - State")
                    elif result:
                        abs_result = os.path.abspath(result)
                        print_debug(f"Chart file not found at relative path, checking absolute path: {abs_result}", "BacktestStrategyTool: Execute Chart Code - Verification")
                        if os.path.exists(abs_result):
                            # Update the result to use the absolute path
                            state["chart_result"] = abs_result
                            print_debug(f"Chart file exists at absolute path: {abs_result}", "BacktestStrategyTool: Execute Chart Code - Verification")
                            print_debug(f"Updated state with chart_result: {state['chart_result']}", "BacktestStrategyTool: Execute Chart Code - State")
                        else:
                            print_debug(f"Chart file not found at either path: {result} or {abs_result}", "BacktestStrategyTool: Execute Chart Code - Error")
                            print_step(f"Chart file not found after creation at path: {result} or {abs_result}", "BacktestStrategyTool: Execute Chart Code", "wheat1")

                    # Debug state after chart creation
                    print_debug(f"State keys after chart creation: {list(state.keys())}", "BacktestStrategyTool: Execute Chart Code - State")
                    if "chart_result" in state:
                        print_debug(f"chart_result in state: {state['chart_result']}", "BacktestStrategyTool: Execute Chart Code - State")
                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    print_debug(f"Error calling create_chart function: {str(e)}\n{error_details}", "BacktestStrategyTool: Execute Chart Code - Error")
                    state["execution_error"] = f"Error calling create_chart function: {str(e)}"
            else:
                print_debug("No create_chart function found in the code", "BacktestStrategyTool: Execute Chart Code - Error")
                print_debug(f"Available functions in namespace: {[k for k in namespace.keys() if callable(namespace[k]) and not k.startswith('__')]}", "BacktestStrategyTool: Execute Chart Code - Error")
                print_step("No create_chart function found in the code", "BacktestStrategyTool: Execute Chart Code", "wheat1")

            # Restore stdout
            sys.stdout = old_stdout
            output = redirected_output.getvalue()

            if output:
                state["execution_output"] = output
                print_debug(f"Execution output (first 500 chars):\n{output[:500]}...", "BacktestStrategyTool: Execute Chart Code - Output")

            print_step("Successfully executed chart code", "BacktestStrategyTool: Execute Chart Code", "wheat1")

        except Exception as e:
            error_msg = f"Error executing chart code: {str(e)}"
            state["execution_error"] = error_msg
            print_debug(f"Exception details:\n{traceback.format_exc()}", "BacktestStrategyTool: Execute Chart Code - Exception")
            print_step(error_msg, "BacktestStrategyTool: Execute Chart Code", "wheat1")

        return state

    async def read_chart(self, state: WorkflowState) -> WorkflowState:
        """Interpret the generated chart using LLM vision capabilities.

        This node takes the chart image path and uses an LLM with vision capabilities
        to interpret the chart and answer the business question about the backtesting strategy.
        """
        import os
        import base64

        # Get the chart result (path to the image) and business question
        chart_path = state.get("chart_result", "")
        business_question = state.get("business_question", "")
        ticker = state.get("ticker", "")

        print_debug(f"State keys: {list(state.keys())}", "BacktestStrategyTool: Read Chart - State")
        print_debug(f"Chart path: {chart_path}", "BacktestStrategyTool: Read Chart - Input")
        print_debug(f"Business question: {business_question}", "BacktestStrategyTool: Read Chart - Input")
        print_debug(f"Ticker: {ticker}", "BacktestStrategyTool: Read Chart - Input")

        # Check if the chart path exists and is valid
        if not chart_path:
            print_debug("No chart path found in state", "BacktestStrategyTool: Read Chart - Error")
            print_debug(f"State keys: {list(state.keys())}", "BacktestStrategyTool: Read Chart - Debug")
            print_debug(f"State content: {state}", "BacktestStrategyTool: Read Chart - Debug")
            print_step("No chart path found", "BacktestStrategyTool: Read Chart", "wheat1")
            return state

        # Convert to absolute path if it's a relative path
        if not os.path.isabs(chart_path):
            abs_chart_path = os.path.abspath(chart_path)
            print_debug(f"Converting relative path to absolute: {abs_chart_path}", "BacktestStrategyTool: Read Chart - Processing")
        else:
            abs_chart_path = chart_path
            print_debug(f"Using absolute path: {abs_chart_path}", "BacktestStrategyTool: Read Chart - Processing")

        if not os.path.exists(abs_chart_path):
            print_debug(f"Chart file not found at path: {abs_chart_path}", "BacktestStrategyTool: Read Chart - Error")
            print_debug("Checking directory contents:", "BacktestStrategyTool: Read Chart - Debugging")

            # List directory contents for debugging
            chart_dir = os.path.dirname(abs_chart_path)
            if os.path.exists(chart_dir):
                dir_contents = os.listdir(chart_dir)
                print_debug(f"Directory contents of {chart_dir}: {dir_contents}", "BacktestStrategyTool: Read Chart - Debugging")
            else:
                print_debug(f"Directory does not exist: {chart_dir}", "BacktestStrategyTool: Read Chart - Debugging")

            print_step(f"Chart file not found at path: {abs_chart_path}", "BacktestStrategyTool: Read Chart", "wheat1")
            return state

        print_debug(f"Chart file verified at path: {abs_chart_path}", "BacktestStrategyTool: Read Chart - Verification")

        try:
            # Get LLM with vision capabilities
            llm_connect = get_llm_connect()
            print_debug("Getting LLM with vision capabilities", "BacktestStrategyTool: Read Chart - LLM")
            model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")
            print_debug(f"Vision model loaded: {model.__class__.__name__}", "BacktestStrategyTool: Read Chart - LLM")

            # Create system prompt using Anthropic best practices
            system_prompt = f"""
<role>
You are a quantitative finance expert specializing in developing, implementing, and evaluating trading strategies through backtesting.
</role>

<task>
Analyze the backtesting strategy chart and provide a comprehensive evaluation of the strategy's performance, focusing on key metrics and actionable insights.
</task>

<context>
This backtesting tool is designed to validate investment hypotheses and quantify strategy performance before real-world implementation.
The chart visualizes a complete backtesting simulation for {ticker} implementing the strategy described in the business question.
</context>

<analysis_requirements>
1. Evaluate the strategy's overall performance using the key metrics shown:
   - Total returns
   - Sharpe ratio
   - Maximum drawdown
   - Win rate (% of profitable trades)
   - Average win/loss ratio
2. Identify key buy and sell signals and their effectiveness
3. Compare the strategy to a buy-and-hold approach
4. Analyze drawdowns and risk-adjusted performance
5. Identify market conditions where the strategy performs well or poorly
6. Assess the strategy's robustness and potential for real-world implementation
</analysis_requirements>

<output_format>
Provide a comprehensive analysis with:
- Executive summary of strategy performance (2-3 sentences)
- Detailed performance metrics analysis with exact figures
- Trade analysis (entry/exit points, win rate, average gains/losses)
- Risk assessment (drawdowns, volatility, risk-adjusted returns)
- Market condition analysis (when the strategy works best)
- Implementation considerations (transaction costs, slippage, etc.)
- Potential optimizations and improvements
</output_format>
"""

            # Create a human prompt with the business question and image
            human_prompt = f"""
<business_question>
{business_question}
</business_question>

<data_available>
{', '.join(state["sample_data"].columns)}
</data_available>

<evaluation_request>
Please provide a comprehensive evaluation of this backtesting strategy with:
1. Detailed analysis of all performance metrics visible in the chart
2. Assessment of the strategy's effectiveness in different market conditions
3. Evaluation of risk-adjusted returns and drawdown management
4. Comparison to buy-and-hold performance
5. Specific recommendations for strategy optimization
6. Assessment of real-world implementation viability
</evaluation_request>

The chart is attached. Focus on quantitative analysis and actionable insights.
"""

            # Read the image file and convert to base64
            print_debug(f"Reading image file: {abs_chart_path}", "BacktestStrategyTool: Read Chart - Image Processing")
            with open(abs_chart_path, "rb") as image_file:
                image_data = image_file.read()
                print_debug(f"Image data read: {len(image_data)} bytes", "BacktestStrategyTool: Read Chart - Image Processing")
                base64_image = base64.b64encode(image_data).decode("utf-8")
                print_debug(f"Base64 encoding completed: {len(base64_image)} chars", "BacktestStrategyTool: Read Chart - Image Processing")

            # Determine the MIME type based on file extension
            mime_type = "image/png"  # Default to PNG
            if abs_chart_path.lower().endswith(".jpg") or abs_chart_path.lower().endswith(".jpeg"):
                mime_type = "image/jpeg"
            print_debug(f"MIME type determined: {mime_type}", "BacktestStrategyTool: Read Chart - Image Processing")

            # Create the data URL
            data_url = f"data:{mime_type};base64,{base64_image}"
            print_debug("Data URL created for image", "BacktestStrategyTool: Read Chart - Image Processing")

            # Include the image in the message using base64
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=[
                    {"type": "text", "text": human_prompt},
                    {"type": "image_url", "image_url": {"url": data_url}}
                ])
            ]

            # Get the interpretation from the LLM
            response = await model.ainvoke(messages)

            # Store the interpretation in the state
            state["chart_interpretation"] = response.content

            # Create a comprehensive markdown report
            datasource = state.get("datasource", "")
            datasource_reasoning = state.get("datasource_reasoning", "")

            # Build the markdown report with improved visual presentation
            markdown_report = f"""
# Backtesting Strategy Performance Report

## Strategy Definition
{business_question}

## Performance Visualization
![{ticker.upper()} Strategy Performance Chart]({state["chart_result"]})

## Comprehensive Strategy Analysis
{response.content}

## Implementation Details

### Data Parameters
- **Ticker:** {ticker}
- **Date Range:** {state.get("start_date", "Unknown")} to {state.get("end_date", "Unknown")}
- **Data Source:** {datasource}
- **Data Selection Reasoning:** {datasource_reasoning}

### Data Available
- **Columns:** {', '.join(state["sample_data"].columns)}

### Methodology
This backtesting simulation implements the exact trading rules specified in the strategy definition, calculating comprehensive performance metrics including returns, drawdowns, Sharpe ratio, and win rate. The analysis compares strategy performance to a buy-and-hold baseline to quantify added value.
"""

            # Update the formatted answer with the markdown report
            state["formatted_answer"] = markdown_report

            # Print the formatted answer
            print_step("Generated strategy analysis", "BacktestStrategyTool: Read Chart", "wheat1")

        except Exception as e:
            error_msg = f"Error interpreting chart: {str(e)}"
            state["interpretation_error"] = error_msg

            # Create a basic markdown report even in case of error
            markdown_report = f"""
# Backtesting Strategy Performance Report

## Strategy Definition
{business_question}

## Performance Visualization
![{ticker.upper()} Strategy Performance Chart]({state["chart_result"]})

## Comprehensive Strategy Analysis
*Error interpreting chart: {str(e)}*

## Implementation Details

### Data Parameters
- **Ticker:** {ticker}
- **Date Range:** {state.get("start_date", "Unknown")} to {state.get("end_date", "Unknown")}
- **Data Source:** {datasource}

### Data Available
- **Columns:** {', '.join(state["sample_data"].columns)}

### Methodology
This backtesting simulation implements the exact trading rules specified in the strategy definition, calculating comprehensive performance metrics including returns, drawdowns, Sharpe ratio, and win rate. The analysis compares strategy performance to a buy-and-hold baseline to quantify added value.
"""

            # Update the formatted answer with the markdown report
            state["formatted_answer"] = markdown_report

            # Print the formatted answer with error indication
            print_step("Generated strategy report (with errors)", "BacktestStrategyTool: Read Chart", "wheat1")

        return state

if __name__ == "__main__":
    import asyncio
    async def main():
        tool = BacktestStrategyTool()
        result = await tool._arun(business_question="Backtest a simple strategy for Apple (AAPL) that buys when the price crosses above the 50-day moving average and sells when it crosses below. Test over 3 months.")
        print(result)

    asyncio.run(main())
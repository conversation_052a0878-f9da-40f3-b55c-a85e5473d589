from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage
)
from typing import Dict, Any, Optional, Type, List, Sequence, TypedDict, Tuple
from software.ai.llm.llm_connect import get_llm_connect
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from data import registry
from api.database import get_sync_db
from main import StockAnalysisApp
import uuid

class BacktestStrategyToolSchema(BaseModel):
    """Schema for BacktestStrategyTool input parameters"""
    business_question: str = Field(
        ...,
        description="A specific backtesting request focused on price movements, technical indicators, or trading patterns. The question should reference a ticker in parentheses and can target various prediction horizons (hours to months) and metrics (price levels, momentum indicators, volatility patterns).",
        min_length=10,
        examples=[
            "Backtest a strategy that buys Meta (META) when the RSI is below 30 and sells when it's above 70 over the past year.",
            "Evaluate the performance of a moving average crossover strategy on Amazon (AMZN) with a 50-day and 200-day moving average over the past 2 years.",
            "Backtest a Bollinger Bands strategy on Tesla (TSLA) by buying when the price touches the lower band and selling when it touches the upper band over the past 6 months.",
            "Backtest a strategy that buys Microsoft (MSFT) when the MACD crosses above the signal line and sells when it crosses below over the past 1 year."
            "Backtest a strategy that buys Apple (AAPL) when the price breaks above the upper Bollinger Band and sells when it breaks below the lower band over the past 3 years.",
            "Backtest a strategy that buys Google (GOOGL) when the stochastic oscillator is below 20 and sells when it's above 80 over the past 18 months."
        ]
    )
class TickerSelection(BaseModel):
    """Schema for TickerSelection input parameters"""
    ticker: str = Field(
        ...,
        description="The ticker to select from the business question considering the available data sources.",
        examples=["META", "AMZN", "AMD", "JPM", "MSFT"]
    )

class DatasourceSelection(BaseModel):
    """Schema for DatasourceSelection input parameters"""
    datasource: str = Field(
        ...,
        description="The datasource to select from the available datasources.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the datasource from the available datasources."
    )

class SimpleStrategyMethods(BaseModel):
    """Simplified schema for AI-generated strategy methods"""
    calculate_indicators_code: str = Field(..., description="Python code for calculate_indicators() method body")
    should_long_code: str = Field(..., description="Python code for should_long() method body")
    should_exit_long_code: str = Field(..., description="Python code for should_exit_long() method body")
    should_short_code: str = Field(default="return False", description="Python code for should_short() method body")
    should_exit_short_code: str = Field(default="return False", description="Python code for should_exit_short() method body")
    indicators_for_chart: List[str] = Field(..., description="List of indicator column names to display in chart")

class OptimizationResult(BaseModel):
    """Results from hyperparameter optimization"""
    best_parameters: Dict[str, Any] = Field(..., description="Best parameter combination found")
    best_return: float = Field(..., description="Best total return achieved")
    best_sharpe: float = Field(..., description="Best Sharpe ratio achieved")
    best_composite_score: float = Field(..., description="Best composite score achieved")
    best_max_drawdown: float = Field(..., description="Max drawdown of best strategy")
    best_total_trades: int = Field(..., description="Number of trades in best strategy")
    in_sample_return: float = Field(..., description="In-sample return of best strategy")
    out_of_sample_return: float = Field(..., description="Out-of-sample return of best strategy")
    total_combinations_tested: int = Field(..., description="Number of parameter combinations tested")
    valid_combinations: int = Field(..., description="Number of combinations passing risk constraints")
    optimization_summary: str = Field(..., description="Summary of optimization results")

class WorkflowState(TypedDict):
    """State for the workflow"""
    messages: Sequence[BaseMessage]
    business_question: str
    available_datasources: List[str]
    ticker: str
    datasource: str
    datasource_reasoning: str
    start_date: str
    end_date: str
    rows: int
    app: Any  # Contains the StockAnalysisApp with data
    unique_id: str
    raw_strategy_code: str
    strategy_methods: SimpleStrategyMethods
    original_performance: Optional[Dict[str, Any]]
    optimization_result: Optional[OptimizationResult]
    chart_path: Optional[str]
    analysis_report: str
    formatted_answer: str

class BacktestStrategyTool(BaseTool):
    """A powerful backtesting tool that leverages AI to develop, implement, and evaluate trading strategies."""

    name: str = "BacktestStrategyTool"
    description: str = "A powerful backtesting tool that leverages AI to develop, implement, and evaluate trading strategies. This tool selects appropriate data loaders, pulls historical market data, generates optimized trading strategies using AI, executes backtests against historical data, and produces comprehensive performance reports with key metrics like returns, drawdowns, Sharpe ratio, and win rate. Perfect for validating investment hypotheses and quantifying strategy performance before real-world implementation."
    category: str = "Backtesting"
    version: str = "1.0.0"
    args_schema: Type[BaseModel] = BacktestStrategyToolSchema

    async def _arun(self, business_question: str) -> Dict[str, Any]:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with parameter: {business_question}", "Tool Execution")
        workflow = StateGraph(WorkflowState)

        # Add all nodes
        workflow.add_node("list_datasources", self.list_datasources)
        workflow.add_node("extract_ticker", self.extract_ticker)
        workflow.add_node("select_datasource", self.select_datasource)
        workflow.add_node("list_date_ranges", self.list_date_ranges)
        workflow.add_node("extract_sample_data", self.extract_sample_data)
        workflow.add_node("generate_raw_strategy_code", self.generate_raw_strategy_code)
        workflow.add_node("parse_strategy_code", self.parse_strategy_code)
        workflow.add_node("create_strategy_template", self.create_strategy_template)
        workflow.add_node("run_original_backtest", self.run_original_backtest)
        workflow.add_node("run_optimization", self.run_optimization)
        workflow.add_node("create_optimized_chart", self.create_optimized_chart)
        workflow.add_node("analyze_results", self.analyze_results)

        # Define workflow edges
        workflow.add_edge(START, "list_datasources")
        workflow.add_edge("list_datasources", "extract_ticker")
        workflow.add_edge("extract_ticker", "select_datasource")
        workflow.add_edge("select_datasource", "list_date_ranges")
        workflow.add_edge("list_date_ranges", "extract_sample_data")
        workflow.add_edge("extract_sample_data", "generate_raw_strategy_code")
        workflow.add_edge("generate_raw_strategy_code", "parse_strategy_code")
        workflow.add_edge("parse_strategy_code", "create_strategy_template")
        workflow.add_edge("create_strategy_template", "run_original_backtest")
        workflow.add_edge("run_original_backtest", "run_optimization")
        workflow.add_edge("run_optimization", "create_optimized_chart")
        workflow.add_edge("create_optimized_chart", "analyze_results")
        workflow.add_edge("analyze_results", END)

        compiled_graph = workflow.compile()
        initial_state = {
            "messages": [HumanMessage(content=business_question)],
            "business_question": business_question,
            "formatted_answer": "Starting backtest strategy analysis...",
        }

        final_state = await compiled_graph.ainvoke(initial_state)

        return final_state['formatted_answer']

    def list_datasources(self, state: WorkflowState) -> WorkflowState:
        """List all available data sources synchronously."""
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        state["available_datasources"] = [ds["name"]+ " (" + ds["description"] + ")" for ds in datasources_cursor]
        print_step(f"Available datasources:\n• {'\n• '.join(state['available_datasources'])}", "BacktestStrategyTool: List Datasources", "wheat1")
        return state

    async def extract_ticker(self, state: WorkflowState) -> WorkflowState:
        """Extract the ticker from the business question."""
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(TickerSelection)
        business_question = state["business_question"]

        system_message = SystemMessage(content=f"""
<role>
You are a financial data parser specializing in extracting stock ticker symbols from text.
</role>

<task>
Extract the stock ticker symbol from the business question.
</task>

<output_format>
{TickerSelection.model_json_schema()}
</output_format>

<extraction_rules>
1. Look for stock symbols in parentheses (e.g., "Apple (AAPL)")
2. Return only the ticker symbol without parentheses
3. If multiple tickers exist, select the most relevant one to the main strategy
</extraction_rules>
""")

        human_message = HumanMessage(content=f"""
<business_question>
{business_question}
</business_question>

Extract the ticker symbol from this business question.
""")

        messages = [system_message, human_message]
        try:
            response = await structured_model.ainvoke(messages)
            if response and hasattr(response, 'ticker'):
                state["ticker"] = response.ticker
                print_step(f"Extracted ticker: {response.ticker}", "BacktestStrategyTool: Extract Ticker", "wheat1")
            else:
                # Fallback to a simple extraction if structured output fails
                ticker = "AMZN"  # Default fallback ticker
                if "(" in business_question and ")" in business_question:
                    start = business_question.find("(") + 1
                    end = business_question.find(")", start)
                    if start < end:
                        extracted = business_question[start:end].strip()
                        if extracted:
                            ticker = extracted

                state["ticker"] = ticker
                print_step(f"Extracted ticker (fallback): {ticker}", "BacktestStrategyTool: Extract Ticker", "wheat1")
        except Exception as e:
            # Fallback to a simple extraction if structured output fails
            ticker = "AMZN"  # Default fallback ticker
            if "(" in business_question and ")" in business_question:
                start = business_question.find("(") + 1
                end = business_question.find(")", start)
                if start < end:
                    extracted = business_question[start:end].strip()
                    if extracted:
                        ticker = extracted

            state["ticker"] = ticker
            print_step(f"Extracted ticker (error fallback): {ticker}", "BacktestStrategyTool: Extract Ticker", "wheat1")
        return state

    async def select_datasource(self, state: WorkflowState) -> WorkflowState:
        """Select the datasource from the available datasources."""
        available_datasources = state["available_datasources"]
        business_question = state["business_question"]
        ticker = state["ticker"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DatasourceSelection)

        system_message = SystemMessage(content=f"""
<role>
You are a financial data specialist with expertise in selecting optimal data sources for backtesting trading strategies.
</role>

<task>
Select the most appropriate datasource for backtesting a trading strategy based on the business question and available datasources.
</task>

<context>
The selection will be used to retrieve historical market data for {ticker} to backtest a trading strategy.
The quality and completeness of the data source directly impacts the accuracy of the backtesting results.
</context>

<selection_criteria>
1. Data completeness: Choose sources with comprehensive historical data
2. Data frequency: Select sources that match the timeframe in the business question
3. Data reliability: Prioritize sources known for accurate and clean data
4. Data coverage: Prioritize sources that provide the necessary data for the trading strategy
</selection_criteria>

<output_format>
You must respond using this exact schema:
{DatasourceSelection.model_json_schema()}
</output_format>

<error_handling>
If multiple datasources seem equally appropriate:
- Compare their descriptions for relevant data coverage
- Select the one with the most comprehensive data
- Provide clear reasoning for your selection
</error_handling>
""")

        human_message = HumanMessage(content=f"""
<business_question>
{business_question}
</business_question>

<ticker>
{ticker}
</ticker>

<available_datasources>
{available_datasources}
</available_datasources>

Select exactly ONE datasource from the list that is most appropriate for this backtesting strategy.
Ignore parentheses descriptions when providing your final datasource name.
""")

        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["datasource"] = response.datasource
        print_step(f"Selected datasource: [bold]{response.datasource}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "BacktestStrategyTool: Select Datasource", "wheat1")
        state["datasource_reasoning"] = response.reasoning # Store reasoning
        return state

    async def list_date_ranges(self, state: WorkflowState) -> WorkflowState:
        """List start and end dates available for the selected datasource."""
        loader_class = registry.get_loader_class(state["datasource"])
        if loader_class:
            app = StockAnalysisApp(ticker=state["ticker"])
            app.load_data(loader_class=loader_class) # Load all data

            if app.data is not None and not app.data.empty:
                start_date = app.data.index.min()
                end_date = app.data.index.max()
                rows = app.data.shape[0]
                state["app"] = app
                # Convert timestamps to strings for serialization
                state["start_date"] = str(start_date)
                state["end_date"] = str(end_date)
                state["rows"] = rows
                print_step(f"Available date ranges: {start_date} to {end_date} with {rows} rows", "BacktestStrategyTool: List Date Ranges", "wheat1")
        return state

    async def extract_sample_data(self, state: WorkflowState) -> WorkflowState:
        """Extract the data from the selected date range."""
        app = state["app"]
        start_date_str = state["start_date"]
        end_date_str = state["end_date"]

        # Convert string dates back to pandas timestamps for filtering
        import pandas as pd
        start_date = pd.to_datetime(start_date_str)
        end_date = pd.to_datetime(end_date_str)

        # Use full data for backtesting (not just samples)
        filtered_data = app.data[
            (app.data.index >= start_date) &
            (app.data.index <= end_date)
        ]

        # Sort chronologically
        sample_data = filtered_data.sort_index(ascending=True)

        # Store data in app object to avoid serialization issues with state
        # The DataFrame will be accessed via app.data in subsequent nodes
        app.filtered_data = sample_data

        # Generate unique ID for this backtest
        state["unique_id"] = str(uuid.uuid4())[:8]

        print_step(f"Extracted {len(sample_data)} rows of data for backtesting", "BacktestStrategyTool: Extract Data", "wheat1")
        return state

    async def generate_raw_strategy_code(self, state: WorkflowState) -> WorkflowState:
        """Generate complete strategy code using LLM with fake conversation technique."""
        print_step("Generating raw strategy code with AI", "BacktestStrategyTool: Generate Code", "wheat1")

        try:
            business_question = state["business_question"]
            ticker = state["ticker"]
            data_source = state["datasource"]

            llm_connect = get_llm_connect()
            model = await llm_connect.get_llm()

            # Dynamic system message based on Anthropic best practices
            system_message = SystemMessage(content=f"""<role>
You are a quantitative trading strategy expert with 15+ years of experience in algorithmic trading systems, specializing in generating precise Python method implementations for financial strategies.
</role>

<task>
Generate complete Python strategy code components for any given business question. Extract the exact trading logic, indicators, and conditions from the business question and implement them with perfect accuracy.
</task>

<code_requirements>
Generate exactly these components in this precise order:
1. def calculate_indicators(self): - Method body only, calculate all required technical indicators
2. def should_long(self): - Method body only, implement exact entry conditions from business question
3. def should_exit_long(self): - Method body only, implement exact exit conditions from business question
4. def should_short(self): - Method body only, return False if not specified in business question
5. def should_exit_short(self): - Method body only, return False if not specified in business question
6. indicators_for_chart = [...] - Python list of indicator column names for charting
7. optimization_config = {{...}} - Dictionary with indicators and thresholds arrays for parameter optimization
</code_requirements>

<technical_specifications>
- Use pandas_ta syntax exclusively: ta.rsi(), ta.sma(), ta.ema(), ta.macd(), ta.bbands(), ta.stochrsi()
- Access current values: self.df['indicator_name'].iloc[-1]
- Include proper validation: len(self.df) >= minimum_periods and pd.isna() checks
- Return boolean values from should_* methods
- Generate realistic optimization parameter ranges around base values
- Write clean, production-ready code without comments, imports, classes, or example usage
- Extract exact thresholds and conditions from the business question
</technical_specifications>

<optimization_config_format>
optimization_config = {{
    'indicators': [
        {{'name': 'parameter_name', 'base_value': X, 'min_value': Y, 'max_value': Z, 'step': S}}
    ],
    'thresholds': [
        {{'name': 'threshold_name', 'base_value': X, 'min_value': Y, 'max_value': Z, 'step': S}}
    ]
}}
</optimization_config_format>

<output_format>
Provide only the raw Python method bodies and variables without any markdown formatting, imports, classes, or explanations. No ```python blocks.
</output_format>""")

            # Fake human message example
            fake_human_message = HumanMessage(content="Backtest a strategy that buys Meta (META) when the RSI is below 30 and sells when it's above 70 over the past year.")

            # Fake AI response with perfect code structure - no markdown, no imports, no classes
            fake_ai_response = AIMessage(content="""def calculate_indicators(self):
    if len(self.df) < 15:
        return
    self.df['rsi'] = ta.rsi(self.df['Close'], length=14)

def should_long(self):
    if len(self.df) < 15 or pd.isna(self.df['rsi'].iloc[-1]):
        return False
    return self.df['rsi'].iloc[-1] < 30

def should_exit_long(self):
    if len(self.df) < 15 or pd.isna(self.df['rsi'].iloc[-1]):
        return False
    return self.df['rsi'].iloc[-1] > 70

def should_short(self):
    return False

def should_exit_short(self):
    return False

indicators_for_chart = ['rsi']

optimization_config = {
    'indicators': [
        {'name': 'rsi_period', 'base_value': 14, 'min_value': 10, 'max_value': 20, 'step': 2}
    ],
    'thresholds': [
        {'name': 'oversold_threshold', 'base_value': 30, 'min_value': 20, 'max_value': 40, 'step': 5},
        {'name': 'overbought_threshold', 'base_value': 70, 'min_value': 60, 'max_value': 80, 'step': 5}
    ]
}""")

            # Real human message
            real_human_message = HumanMessage(content=f"Backtest strategy: {business_question}")

            # Use fake conversation to prime the model
            messages = [
                system_message,
                fake_human_message,
                fake_ai_response,
                real_human_message
            ]

            response = await model.ainvoke(messages)

            # Debug the raw response
            print_debug(f"🎯 RAW STRATEGY CODE GENERATED", "CodeGen")
            print_debug(f"📝 Response length: {len(response.content)} characters", "CodeGen")
            print_debug(f"🔍 Full generated code:\n{response.content}", "CodeGen")

            state["raw_strategy_code"] = response.content
            print_step(f"Generated {len(response.content)} characters of strategy code", "BacktestStrategyTool: Generate Code", "wheat1")

        except Exception as e:
            print_debug(f"Error generating raw strategy code: {str(e)}", "CodeGen")
            state["raw_strategy_code"] = ""
            print_step(f"Error generating strategy code: {str(e)}", "BacktestStrategyTool: Generate Code", "wheat1")

        return state

    async def parse_strategy_code(self, state: WorkflowState) -> WorkflowState:
        """Parse raw strategy code into structured OptimizedStrategyMethods."""
        print_step("Parsing strategy code into structured format", "BacktestStrategyTool: Parse Code", "wheat1")

        try:
            raw_code = state["raw_strategy_code"]

            llm_connect = get_llm_connect()
            model = await llm_connect.get_llm()
            structured_model = model.with_structured_output(SimpleStrategyMethods)

            # Dynamic parsing system message based on Anthropic best practices with model schema
            system_message = SystemMessage(content=f"""<role>
You are a Python code parser specializing in extracting trading strategy components from raw code with expertise in financial algorithm parsing.
</role>

<task>
Parse the provided raw Python strategy code and extract specific components into the exact structured format defined by the schema below.
</task>

<target_schema>
{SimpleStrategyMethods.model_json_schema()}
</target_schema>

<extraction_requirements>
Extract these exact components following the schema above:
1. calculate_indicators_code: Method body only (without def signature)
2. should_long_code: Method body only (without def signature)
3. should_exit_long_code: Method body only (without def signature)
4. should_short_code: Method body only (without def signature, or "return False" if not present)
5. should_exit_short_code: Method body only (without def signature, or "return False" if not present)
6. indicators_for_chart: Python list of indicator column names as strings
7. optimization_config: Must follow the OptimizationConfig schema with indicators and thresholds arrays
</extraction_requirements>

<parsing_rules>
- Extract only method bodies, never include function signatures
- Preserve exact indentation and code structure
- Include all validation logic and return statements
- For optimization_config: Convert flat parameter dictionaries to proper IndicatorRange/ThresholdRange objects
- If a method is missing, provide appropriate default (e.g., "return False" for optional methods)
- Follow the exact schema structure provided above
</parsing_rules>

<quality_standards>
- Ensure extracted code is syntactically valid Python
- Maintain all technical indicator calculations exactly as written
- Preserve all conditional logic and validation checks
- Extract optimization parameters with correct data types matching the schema
- Ensure optimization_config follows the nested structure with proper IndicatorRange and ThresholdRange objects
</quality_standards>""")

            human_message = HumanMessage(content=f"Parse this strategy code:\n\n{raw_code}")

            response = await structured_model.ainvoke([system_message, human_message])

            # Debug the parsed response
            print_debug(f"🎯 STRATEGY CODE PARSED", "CodeParse")
            print_debug(f"🔍 Calculate indicators length: {len(response.calculate_indicators_code)} chars", "CodeParse")
            print_debug(f"🔍 Should long length: {len(response.should_long_code)} chars", "CodeParse")
            print_debug(f"📈 Indicators for chart: {response.indicators_for_chart}", "CodeParse")

            state["strategy_methods"] = response
            print_step(f"Parsed strategy with {len(response.indicators_for_chart)} indicators for chart", "BacktestStrategyTool: Parse Code", "wheat1")

        except Exception as e:
            print_debug(f"Error parsing strategy code: {str(e)}", "CodeParse")

            # Handle Cerebras model structured output failure by extracting from failed_generation
            error_str = str(e)
            if "failed_generation" in error_str and "SimpleStrategyMethods(" in error_str:
                print_debug("Attempting to extract from failed_generation", "CodeParse")
                try:
                    # Parse the raw code manually since structured output failed
                    raw_code = state["raw_strategy_code"]

                    # Extract method bodies using regex
                    def extract_method_body(method_name: str, code: str) -> str:
                        import re
                        pattern = rf"def {method_name}\(self\):(.*?)(?=def |\Z)"
                        match = re.search(pattern, code, re.DOTALL)
                        if match:
                            body = match.group(1).strip()
                            # Remove the first line break and normalize indentation
                            lines = body.split('\n')
                            if lines and not lines[0].strip():
                                lines = lines[1:]
                            # Remove common leading whitespace
                            if lines:
                                min_indent = min(len(line) - len(line.lstrip()) for line in lines if line.strip())
                                lines = [line[min_indent:] if line.strip() else line for line in lines]
                            return '\n'.join(lines)
                        return "return False"

                    # Extract indicators_for_chart
                    import re
                    indicators_match = re.search(r"indicators_for_chart\s*=\s*\[(.*?)\]", raw_code, re.DOTALL)
                    indicators_for_chart = []
                    if indicators_match:
                        indicators_str = indicators_match.group(1)
                        # Extract quoted strings
                        indicators_for_chart = re.findall(r"'([^']*)'", indicators_str)

                    parsed_methods = SimpleStrategyMethods(
                        calculate_indicators_code=extract_method_body("calculate_indicators", raw_code),
                        should_long_code=extract_method_body("should_long", raw_code),
                        should_exit_long_code=extract_method_body("should_exit_long", raw_code),
                        should_short_code=extract_method_body("should_short", raw_code),
                        should_exit_short_code=extract_method_body("should_exit_short", raw_code),
                        indicators_for_chart=indicators_for_chart
                    )

                    state["strategy_methods"] = parsed_methods
                    print_debug(f"Successfully extracted from failed_generation", "CodeParse")
                    print_debug(f"📈 Indicators for chart: {parsed_methods.indicators_for_chart}", "CodeParse")
                    print_step(f"Extracted strategy with {len(parsed_methods.indicators_for_chart)} indicators from failed_generation", "BacktestStrategyTool: Parse Code", "wheat1")
                    return state

                except Exception as extract_error:
                    print_debug(f"Failed to extract from failed_generation: {str(extract_error)}", "CodeParse")

            # Return default structure on error
            state["strategy_methods"] = SimpleStrategyMethods(
                calculate_indicators_code="return",
                should_long_code="return False",
                should_exit_long_code="return False",
                indicators_for_chart=[]
            )
            print_step(f"Error parsing strategy code: {str(e)}", "BacktestStrategyTool: Parse Code", "wheat1")

        return state

    async def create_strategy_template(self, state: WorkflowState) -> WorkflowState:
        """Create strategy template with parsed methods."""
        print_step("Creating strategy template", "BacktestStrategyTool: Create Template", "wheat1")
        # TODO: Implement strategy template creation
        return state

    async def run_original_backtest(self, state: WorkflowState) -> WorkflowState:
        """Execute original strategy and calculate performance."""
        print_step("Running original backtest", "BacktestStrategyTool: Original Backtest", "wheat1")
        # TODO: Implement original backtest execution
        state["original_performance"] = {}
        return state

    async def run_optimization(self, state: WorkflowState) -> WorkflowState:
        """Run hyperparameter optimization if configured."""
        print_step("Running hyperparameter optimization", "BacktestStrategyTool: Optimization", "wheat1")
        # TODO: Implement optimization
        state["optimization_result"] = None
        return state

    async def create_optimized_chart(self, state: WorkflowState) -> WorkflowState:
        """Generate final chart with results."""
        print_step("Creating optimized chart", "BacktestStrategyTool: Chart Creation", "wheat1")
        # TODO: Implement chart creation
        state["chart_path"] = None
        return state

    async def analyze_results(self, state: WorkflowState) -> WorkflowState:
        """Generate comprehensive markdown analysis report."""
        print_step("Analyzing results and generating report", "BacktestStrategyTool: Analysis", "wheat1")
        # TODO: Implement results analysis
        state["analysis_report"] = "Backtest analysis pending implementation."
        state["formatted_answer"] = state["analysis_report"]
        return state










if __name__ == "__main__":
    import asyncio
    async def main():
        tool = BacktestStrategyTool()
        result = await tool._arun(business_question="Backtest a simple strategy for Apple (AAPL) that buys when the price crosses above the 50-day moving average and sells when it crosses below. Test over 3 months.")
        print(result)

    asyncio.run(main())
from typing import Dict, Any, Optional, Type
from langchain.tools import StructuredTool
from pydantic import Field, BaseModel
from rich.console import Console
from rich.panel import Panel

console = Console()

class BaseTool(StructuredTool):
    """Base class for all tools in the system."""
    
    name: str = Field(description="Name of the tool")
    description: str = Field(description="Description of what the tool does")
    args_schema: Optional[Type[BaseModel]] = None
    category: str = Field(default="general", description="Tool category")
    version: str = Field(default="1.0.0", description="Tool version")
    
    async def _arun(self, **kwargs: Dict[str, Any]) -> Any:
        """Execute the tool's functionality asynchronously."""
        console.print(Panel(
            f"[cyan]Arguments:[/cyan] {kwargs}",
            title=f"[bold]{self.name} Async Execution[/bold]",
            border_style="blue"
        ))
        raise NotImplementedError("Tool must implement _arun method")
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert tool metadata to dictionary format."""
        parameters = {}
        if self.args_schema:
            schema = self.args_schema.schema()
            for field_name, field in schema.get("properties", {}).items():
                parameters[field_name] = {
                    "type": field.get("type", "any"),
                    "description": field.get("description", ""),
                    "required": field_name in schema.get("required", [])
                }
        
        result = {
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "version": self.version,
            "parameters": parameters
        }
        
        console.print(Panel(
            "\n".join([
                f"[cyan]Name:[/cyan] {result['name']}",
                f"[cyan]Category:[/cyan] {result['category']}",
                f"[cyan]Version:[/cyan] {result['version']}",
                f"[cyan]Parameters:[/cyan] {len(parameters)}"
            ]),
            title="[bold]Tool Configuration[/bold]",
            border_style="cyan"
        ))
        
        return result

from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from ai.tools.base_tool import BaseTool
from software.ai.llm.llm_connect import get_llm_connect
from pydantic import BaseModel, Field, ConfigDict
from typing import Type, Any, Dict, Sequence, List, TypedDict
from api.database import get_sync_db
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from main import StockAnalysisApp
from data import registry
from db.report_repository import ReportFactory, ReportRepository

import pandas as pd
import asyncio

#Pydantic models using LLM

class TickerSelection(BaseModel):
    """Schema for TickerSelection input parameters"""
    ticker: str = Field(
        ..., 
        description="The ticker to select from the business question considering the available data sources.",
        examples=["META", "AMZN", "AMD", "JPM", "MSFT"]
    )
    model_config = ConfigDict(from_attributes=True)

class DatasourceSelection(BaseModel):
    """Schema for DatasourceSelection input parameters"""
    datasource: str = Field(
        ...,
        description="The datasource to select from the available datasources.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the datasource from the available datasources."
    )
    model_config = ConfigDict(from_attributes=True)

class FeaturesSelection(BaseModel):
    """Schema for FeaturesSelection input parameters"""
    features: List[str] = Field(
        ...,
        description="The features to select from the available features."
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the features from the available features."
    )
    model_config = ConfigDict(from_attributes=True)

class ModelSelection(BaseModel):
    """Schema for ModelSelection input parameters"""
    model: str = Field(
        ...,
        description="The model to select from the available models. ignore the description after the hyphen, get only the class name ending with 'Model'"
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the model from the available models."
    )
    model_config = ConfigDict(from_attributes=True)

class TargetSelection(BaseModel):
    """Schema for TargetSelection input parameters"""
    target: str = Field(
        ...,
        description="The target to select from the available targets."
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the target from the available targets."
    )
    model_config = ConfigDict(from_attributes=True)

class ForecastHorizon(BaseModel):
    """Schema for ForecastHorizon input parameters"""
    forecast_horizon: int = Field(
        ...,
        description="Number of days to forecast ahead based on datasource interval units, must be a positive integer and greater than 5",
        ge=5
    )
    reasoning: str = Field(
        ...,
        description="Reasoning for selecting this forecast horizon"
    )
    model_config = ConfigDict(from_attributes=True)


class SimpleWorkflowSchema(BaseModel):
   """Schema for SimpleWorkflowTool input parameters"""
   business_question: str = Field(
       ...,
       description="A specific analysis question focused on price movements, technical indicators, or trading patterns. The question should reference a ticker in parentheses and can target various prediction horizons (hours to months) and metrics (price levels, momentum indicators, volatility patterns).",
       min_length=10,
       examples=[
           "Is the current RSI divergence in Meta (META) indicating a potential trend reversal in the next 5 days?",
           "With the MACD crossing above the signal line, will Amazon's (AMZN) closing price break its 50-day moving average this week?",
           "Based on the Bollinger Bands squeeze, how will AMD (AMD) volatility evolve over the next 24 hours?",
           "Are the current support levels and volume trends suggesting JPMorgan (JPM) will test its previous high in the next month?",
           "With the double bottom pattern and rising RSI, will Microsoft (MSFT) break above $400 in the next 10 trading days?"
       ]
   )
   model_config = ConfigDict(from_attributes=True)

class WorkflowState(TypedDict):
    """State definition for the workflow"""
    messages: Sequence[BaseMessage]
    datasource: str
    features: List[str]
    model: str
    target: str
    forecast_horizon: int
    available_datasources: List[str]
    available_features: List[str]
    available_models: List[str]
    available_targets: List[str]
    report_id: str | None
    stage: str
    error: str | None
    ticker: str
    business_question: str
    performance: Dict[str, float]
    predictions: pd.DataFrame
    formatted_answer: str
    # Add fields for reasoning
    datasource_reasoning: str
    features_reasoning: str
    model_reasoning: str
    target_reasoning: str
    forecast_horizon_reasoning: str


class SimpleWorkflowTool(BaseTool):
    """A tool for creating stock forecasting reports using a structured workflow."""
    
    name: str = "SimpleWorkflowTool"
    description: str = "A tool for creating stock forecasting reports by selecting data sources, features, models and forecast horizons based on a business question. Supports technical analysis and price predictions."
    args_schema: Type[BaseModel] = SimpleWorkflowSchema
    category: str = "analysis"
    version: str = "1.0.0"
    return_direct: bool = True

    async def _arun(self, business_question: str) -> Any:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with business_question: {business_question}", "First Run (Async)")

        workflow = StateGraph(WorkflowState)

        workflow.add_node("list_datasources", self.list_datasources)
        workflow.add_node("extract_ticker", self.extract_ticker)
        workflow.add_node("select_datasource", self.select_datasource)
        workflow.add_node("list_features", self.list_features)
        workflow.add_node("select_features", self.select_features)
        workflow.add_node("list_models", self.list_models)
        workflow.add_node("select_model", self.select_model)
        workflow.add_node("list_targets", self.list_targets)
        workflow.add_node("select_target", self.select_target)
        workflow.add_node("select_forecast_horizon", self.select_forecast_horizon)
        workflow.add_node("run_model", self.run_model)
        workflow.add_node("generate_report", self.generate_report)
        workflow.add_node("process_report", self.process_report)

        workflow.add_edge(START, "list_datasources")
        workflow.add_edge("list_datasources", "extract_ticker")
        workflow.add_edge("extract_ticker", "select_datasource")
        workflow.add_edge("select_datasource", "list_features")
        workflow.add_edge("list_features", "select_features")
        workflow.add_edge("select_features", "list_models")
        workflow.add_edge("list_models", "select_model")
        workflow.add_edge("select_model", "list_targets")
        workflow.add_edge("list_targets", "select_target")  
        workflow.add_edge("select_target", "select_forecast_horizon")
        workflow.add_edge("select_forecast_horizon", "run_model")
        workflow.add_edge("run_model", "generate_report")
        workflow.add_edge("generate_report", "process_report")
        workflow.add_edge("process_report", END)

        compiled_graph = workflow.compile()

        # Prepare initial state
        initial_state = {
            "messages": [HumanMessage(content=business_question)],
            "business_question": business_question,
            # Initialize other fields if necessary, e.g., empty lists/dicts
            "datasource": "",
            "features": [],
            "model": "",
            "target": "",
            "forecast_horizon": 0,
            "available_datasources": [],
            "available_features": [],
            "available_models": [],
            "available_targets": [],
            "report_id": None,
            "stage": "",
            "error": None,
            "ticker": "", # You might want to extract the ticker here
            "performance": {},
            "predictions": pd.DataFrame(),
            "formatted_answer": "",
            # Initialize reasoning fields
            "datasource_reasoning": "",
            "features_reasoning": "",
            "model_reasoning": "",
            "target_reasoning": "",
            "forecast_horizon_reasoning": ""
        }
        
        # Invoke the graph asynchronously using ainvoke
        final_state = await compiled_graph.ainvoke(initial_state)
        return final_state['formatted_answer']

    def list_datasources(self, state: WorkflowState) -> WorkflowState:
        """List all available data sources synchronously."""
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        state["available_datasources"] = [ds["name"]+ " (" + ds["description"] + ")" for ds in datasources_cursor]
        print_step(f"Available datasources:\n• {'\n• '.join(state['available_datasources'])}", "SimpleWorkflowTool: List Datasources", "violet")
        return state
    
    async def extract_ticker(self, state: WorkflowState) -> WorkflowState:
        """Extract the ticker from the business question."""
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()

        structured_model = model.with_structured_output(TickerSelection)
        list_datasources = state["available_datasources"]
        business_question = state["business_question"]
        system_message = SystemMessage(content="You are a helpful assistant that extracts the ticker from the business question.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable data sources: {list_datasources}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        ticker = response.ticker
        state["ticker"] = ticker
        print_step(f"Extracted ticker: {ticker}", "SimpleWorkflowTool: Extract Ticker", "violet")
        return state

    async def select_datasource(self, state: WorkflowState) -> WorkflowState:
        """Select the datasource from the available datasources."""
        available_datasources = state["available_datasources"]
        datasource = state["datasource"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DatasourceSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the datasource from the available datasources that is most relevant to the business question. Ignore parantheses description of the datasources.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable datasources: {available_datasources}\nSelect only one datasource from the list")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["datasource"] = response.datasource
        print_step(f"Selected datasource: [bold]{response.datasource}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "SimpleWorkflowTool: Select Datasource", "violet")
        state["datasource_reasoning"] = response.reasoning # Store reasoning
        return state
    
    def list_features(self, state: WorkflowState) -> WorkflowState:
        """List all available features for the selected datasource."""
        features = {}
        for name, feature_class in FEATURE_CLASSES.items():
            description = feature_class.__doc__ or "No description available"
            description = description.strip().split('\n')[0]
            features[name] = description
        # Format the output string correctly
        feature_lines = [f"{name} - {desc}" for name, desc in features.items()]
        print_step(f"Available features: \n• {'\n• '.join(feature_lines)}", "SimpleWorkflowTool: List Features", "violet")
        state["available_features"] = feature_lines
        return state
    

    async def select_features(self, state: WorkflowState) -> WorkflowState:
        """Select the features from the available features."""
        available_features = state["available_features"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(FeaturesSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the features from the available features that are most relevant to the business question. ignore the description after the hyphen.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable features: {available_features}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["features"] = response.features
        print_step(f"Selected features: [bold]{response.features}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "SimpleWorkflowTool: Select Features", "violet")
        state["features_reasoning"] = response.reasoning # Store reasoning
        return state
    
    def list_models(self, state: WorkflowState) -> WorkflowState:
        """List all available models."""
        models = [
            f"{cls.__name__} - {(getattr(cls, 'description', cls.__doc__ or 'No description available')).strip().splitlines()[0]}"
            for cls in MODEL_CLASSES
        ]
        print_step(f"Available models: \n• {'\n• '.join(models)}", "SimpleWorkflowTool: List Models", "violet")
        state["available_models"] = models
        return state
    
    async def select_model(self, state: WorkflowState) -> WorkflowState:
        """Select the model from the available models."""
        available_models = state["available_models"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(ModelSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the model from the available models that is most relevant to the business question. ignore the description after the hyphen, get only the class name ending with 'Model'")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable models: {available_models}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["model"] = response.model
        print_step(f"Selected model: [bold]{response.model}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "SimpleWorkflowTool: Select Model", "violet")
        state["model_reasoning"] = response.reasoning # Store reasoning
        return state
    
    def list_targets(self, state: WorkflowState) -> WorkflowState:
        """List all available numeric targets after loading data and features."""
        loader_class = registry.get_loader_class(state["datasource"])
        numeric_columns = []
        
        if loader_class:
            app = StockAnalysisApp(ticker=state["ticker"])
            app.load_data(loader_class=loader_class, sample_size=10) # Load a small sample
            app.add_features(feature_names=state["features"])
            
            if app.data is not None and not app.data.empty:
                numeric_columns = app.data.select_dtypes(include=['number']).columns.tolist()

        state["available_targets"] = numeric_columns
        print_step(f"Available targets: \n• {'\n• '.join(numeric_columns)}", "SimpleWorkflowTool: List Targets", "violet")
        return state
    
    async def select_target(self, state: WorkflowState) -> WorkflowState:
        """Select the target from the available targets."""
        selected_datasource = state["datasource"]
        selected_features = state["features"]
        selected_model = state["model"]
        available_targets = state["available_targets"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(TargetSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the target from the available targets that is most relevant to the business question.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nSelected datasource: {selected_datasource}\nSelected features: {selected_features}\nSelected model: {selected_model}\nAvailable targets: {available_targets}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["target"] = response.target
        print_step(f"Selected target: [bold]{response.target}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "SimpleWorkflowTool: Select Target", "violet")
        state["target_reasoning"] = response.reasoning # Store reasoning
        return state

    async def select_forecast_horizon(self, state: WorkflowState) -> WorkflowState:
        """Select the forecast horizon from the available forecast horizons."""
        business_question = state["business_question"]
        selected_datasource = state["datasource"]
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        #get the datasource description
        datasource_description = next((ds["description"] for ds in datasources_cursor if ds["name"] == selected_datasource), None)

        selected_features = state["features"]
        selected_model = state["model"]
        selected_target = state["target"]

        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(ForecastHorizon)
        system_message = SystemMessage(content="You are a ML expert selecting forecast horizon for the business question. Forecast the horizon in days based on the datasource interval units.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nSelected datasource: {selected_datasource} ({datasource_description})\nSelected features: {selected_features}\nSelected model: {selected_model}\nSelected target: {selected_target}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)


        state["forecast_horizon"] = response.forecast_horizon
        print_step(f"Selected forecast horizon: [bold]{response.forecast_horizon}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "SimpleWorkflowTool: Select Forecast Horizon", "violet")
        state["forecast_horizon_reasoning"] = response.reasoning # Store reasoning
        
        return state

    def run_model(self, state: WorkflowState) -> WorkflowState:
        """Run the model with the selected parameters."""
        loader_class = registry.get_loader_class(state["datasource"])
        model_class = next((cls for cls in MODEL_CLASSES if cls.__name__ == state["model"]), None)

        if not loader_class or not model_class:
            # Handle error: loader or model not found
            state["error"] = f"Loader {state['datasource']} or Model {state['model']} not found."
            state["predictions"] = pd.DataFrame()
            state["performance"] = {}
            print_step(f"Error: {state['error']}", "SimpleWorkflowTool: Run Model", "red")
            return state

        app = StockAnalysisApp(ticker=state["ticker"])
        # Load full data here, not just a sample
        app.load_data(loader_class=loader_class)
        app.add_features(feature_names=state["features"])

        if app.data is None or app.data.empty:
            state["error"] = "Failed to load data or data is empty before running model."
            state["predictions"] = pd.DataFrame()
            state["performance"] = {}
            print_step(f"Error: {state['error']}", "SimpleWorkflowTool: Run Model", "red")
            return state
        
        # Pass the actual model class object
        result, metrics = app.run_model(model_class=model_class, predict=state["target"], forecast_horizon=state["forecast_horizon"])
        
        state["predictions"] = result
        state["performance"] = metrics
        print_step(f"Performance: {metrics} , Predictions: {result}", "SimpleWorkflowTool: Run Model", "violet") # Avoid printing large dataframes
        return state

    async def generate_report(self, state: WorkflowState) -> WorkflowState:
        """Generate a report from the predictions and performance metrics."""
        report = ReportFactory.create_simple_report(
            ticker=state["ticker"],
            data_loader=state["datasource"],
            features=state["features"],
            model_name=state["model"],
            prediction_column=state["target"],
            forecast_horizon=state["forecast_horizon"],
            data=state["predictions"],
            performance=state["performance"]
        )
        report_repository = await ReportRepository.create() # Use create() to get initialized instance
        report_id = await report_repository.insert_report(report)
        print_step(f"Report ID: {report_id}", "SimpleWorkflowTool: Generate Report", "violet")
        state["report_id"] = report_id
        return state
    
    async def process_report(self, state: WorkflowState) -> WorkflowState:
        """Process the report and return the formatted answer in markdown."""
        report_repository = await ReportRepository.create() # Use create() to get initialized instance
        report = await report_repository.get_report(state["report_id"])

        if report is None:
            state["error"] = f"Report with ID {state['report_id']} not found."
            print_step(f"Error: {state['error']}", "SimpleWorkflowTool: Process Report", "red")
            return state

        forecast_horizon = report["workflow"]["model"]["forecast_horizon"]
        predicted_col = f"{state['target']}_Predicted"
        actual_col = state['target']
        last_actual_idx = state["predictions"].index[-forecast_horizon-1]
        last_actual_data = state["predictions"].loc[last_actual_idx]
        forecast_data = state["predictions"].iloc[-forecast_horizon:]

        #construct the last actual and forecast data
        last_actual = {
            "date": last_actual_idx.strftime('%Y-%m-%d'),
            "value": float(last_actual_data[actual_col])
        }
        forecast = [
            {
                "date": idx.strftime('%Y-%m-%d'),
                "value": float(row[predicted_col])
            }
            for idx, row in forecast_data.iterrows()
        ]

        last_actual_value = float(last_actual_data.get(actual_col, 0))
        final_pred = forecast[-1].get("value", 0)
        pct_change = ((final_pred / last_actual_value) - 1) * 100 if last_actual_value else 0
        direction_text = "Upward" if final_pred > last_actual_value else "Downward"

        # Format available datasources, features, models, targets as markdown lists
        def format_md_list(items):
            return "\n".join([f"- {item}" for item in items])

        # Construct markdown output
        markdown_output = f"""# Investment Analysis Report

## Business Question
{state["business_question"]}

## Ticker Analysis
**Ticker**: {state["ticker"]}

## Data Source Selection
**Selected Data Source**: {state["datasource"]}  
**Reasoning**: {state["datasource_reasoning"]}

### Available Data Sources
{format_md_list(state["available_datasources"])}

## Feature Selection
**Selected Features**:
{format_md_list(state["features"])}

**Reasoning**: {state["features_reasoning"]}

### Available Features
{format_md_list(state["available_features"])}

## Model Selection
**Selected Model**: {state["model"]}  
**Reasoning**: {state["model_reasoning"]}

### Available Models
{format_md_list(state["available_models"])}

## Target Selection
**Selected Target**: {state["target"]}  
**Reasoning**: {state["target_reasoning"]}

### Available Targets
{format_md_list(state["available_targets"])}

## Forecast Horizon Selection
**Selected Horizon**: {state["forecast_horizon"]} days  
**Reasoning**: {state["forecast_horizon_reasoning"]}

## Model Performance
**Performance Metrics**:
{format_md_list([f"{k}: {v:.4f}" for k, v in state["performance"].items()])}

## Forecast Results
**Last Known Value**: {last_actual["value"]:.2f} ({last_actual["date"]})  
**Predicted Value**: {final_pred:.2f} ({forecast[-1]["date"]}, horizon: {state["forecast_horizon"]} days)  
**Direction**: {direction_text}  
**Predicted Change**: {pct_change:.2f}%

**Report ID**: {state["report_id"]}
"""

        state["formatted_answer"] = markdown_output
        
        print_step(f"Report:\\n{markdown_output}", "SimpleWorkflowTool: Process Report", "violet")
        return state

if __name__ == "__main__":
    # Example usage when running the script directly
    tool_instance = SimpleWorkflowTool()
    example_question = "Is the current RSI divergence in Meta (META) indicating a potential trend reversal in the next 5 days?"
    print(f"Running SimpleWorkflowTool with question: '{example_question}'")
    try:
        import asyncio
        result = asyncio.run(tool_instance._arun(business_question=example_question))
        print("\\n--- Final Result ---")
        print(result)
    except Exception as e:
        print(f"An error occurred during execution: {e}")
        import traceback
        traceback.print_exc()


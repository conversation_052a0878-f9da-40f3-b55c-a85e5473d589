"""Tool registry module for managing AI tools and converting them to LangChain format."""
import os
import importlib
import asyncio
from typing import Dict, Any, Optional, Type, List, Set
from ai.tools.base_tool import BaseTool
from langchain.tools import Tool
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

# Define tool file filter once
def _is_tool_file(f: str) -> bool:
    return (f.endswith('.py') 
            and not f.endswith('_.py')  # Exclude disabled tools 
            and not f.startswith('__')
            and f not in ['base_tool.py', 'registry.py'])

class ToolRegistry:
    """Registry for managing tool instances and converting them to LangChain format."""
    
    _instance = None
    _tools: Dict[str, BaseTool] = {}
    _langchain_tools: Optional[List[Tool]] = None
    _initialized: bool = False
    _disabled_tools: Set[str] = set()  # Track disabled tools by name
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the registry without loading tools automatically."""
        if not self._initialized:
            self._initialized = True
    
    async def load_tools(self) -> None:
        """Load tools from directory asynchronously."""
        await self.load_tools_async()
    
    def load_tools_sync(self) -> None:
        """Load tools from directory synchronously."""
        try:
            # Clear existing tools
            self._tools.clear()
            self._langchain_tools = None
            
            # Load tools from directory
            tools_dir = os.path.dirname(__file__)
            tool_files = [f for f in os.listdir(tools_dir) if _is_tool_file(f)]
            
            # Create status table
            status_table = Table(show_header=True, header_style="bold magenta")
            status_table.add_column("Tool Name")
            status_table.add_column("Status")
            status_table.add_column("Type")
            
            # Load tools from files
            for tool_file in tool_files:
                try:
                    module_name = tool_file[:-3]
                    module = importlib.import_module(f"ai.tools.{module_name}")
                    tool_class = getattr(module, module_name)
                    
                    # Create instance and store
                    tool_instance = tool_class()
                    self._tools[tool_class.__name__] = tool_instance
                    
                    status_table.add_row(
                        tool_class.__name__,
                        "✓ Loaded" if tool_class.__name__ not in self._disabled_tools else "✗ Disabled",
                        tool_instance.__class__.__name__
                    )
                    
                except Exception as e:
                    console.print(f"[red]Failed to load tool {tool_file}: {str(e)}[/red]")
                    status_table.add_row(
                        tool_file,
                        "✗ Failed",
                        "Error"
                    )
            
            # Add disabled tools to status table
            disabled_files = [f for f in os.listdir(tools_dir) if f.endswith('_.py') and not f.startswith('__')]
            
            for disabled_file in disabled_files:
                original_name = disabled_file[:-4]  # Remove .py and the underscore
                status_table.add_row(original_name, "⚠ Disabled", "-")
            
            # Display the status table
            console.print(Panel(
                status_table,
                title="[bold]Tool Registry Status[/bold]",
                border_style="blue"
            ))
            
        except Exception as e:
            console.print(Panel(f"[red]Error loading tools: {str(e)}[/red]", title="[bold]Error[/bold]", border_style="red"))
            raise
    
    async def load_tools_async(self) -> None:
        """Load tools from directory asynchronously."""
        try:
            # Clear existing tools
            self._tools.clear()
            self._langchain_tools = None
            
            # Load tools from directory using asyncio.to_thread for blocking operations
            tools_dir = os.path.dirname(__file__)
            tool_files = await asyncio.to_thread(lambda: [f for f in os.listdir(tools_dir) if _is_tool_file(f)])
            
            # Create status table
            status_table = Table(show_header=True, header_style="bold magenta")
            status_table.add_column("Tool Name")
            status_table.add_column("Status")
            status_table.add_column("Type")
            
            # Load tools from files
            for tool_file in tool_files:
                try:
                    module_name = tool_file[:-3]
                    # Import is also a blocking operation so wrap in to_thread
                    module = await asyncio.to_thread(importlib.import_module, f"ai.tools.{module_name}")
                    tool_class = getattr(module, module_name)
                    
                    # Create instance and store
                    tool_instance = tool_class()
                    self._tools[tool_class.__name__] = tool_instance
                    
                    status_table.add_row(
                        tool_class.__name__,
                        "✓ Loaded" if tool_class.__name__ not in self._disabled_tools else "✗ Disabled",
                        tool_instance.__class__.__name__
                    )
                    
                except Exception as e:
                    console.print(f"[red]Failed to load tool {tool_file}: {str(e)}[/red]")
                    status_table.add_row(
                        tool_file,
                        "✗ Failed",
                        "Error"
                    )
            
            # Add disabled tools to status table
            disabled_files = await asyncio.to_thread(lambda: [f for f in os.listdir(tools_dir) if f.endswith('_.py') and not f.startswith('__')])
            
            for disabled_file in disabled_files:
                original_name = disabled_file[:-4]  # Remove .py and the underscore
                status_table.add_row(original_name, "⚠ Disabled", "-")
            
            # Display the status table
            console.print(Panel(
                status_table,
                title="[bold]Tool Registry Status[/bold]",
                border_style="blue"
            ))
            
        except Exception as e:
            console.print(Panel(f"[red]Error loading tools: {str(e)}[/red]", title="[bold]Error[/bold]", border_style="red"))
            raise
    
    async def get_tool_async(self, name: str) -> Optional[BaseTool]:
        """Get a tool instance by name asynchronously."""
        if not self._tools:
            await self.load_tools_async()
        return self._tools.get(name)

    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool instance by name."""
        if not self._tools:
            self.load_tools_sync()
        return self._tools.get(name)

    async def get_all_tools_async(self) -> Dict[str, BaseTool]:
        """Get all registered tools asynchronously."""
        if not self._tools:
            await self.load_tools_async()
        return self._tools

    def get_all_tools(self) -> Dict[str, BaseTool]:
        """Get all registered tools."""
        if not self._tools:
            self.load_tools_sync()
        return self._tools
    
    def is_tool_enabled(self, name: str) -> bool:
        """Check if a tool is enabled."""
        return name not in self._disabled_tools
    
    def enable_tool(self, name: str) -> bool:
        """Enable a tool by name."""
        if name in self._disabled_tools:
            self._disabled_tools.remove(name)
            self._langchain_tools = None  # Reset langchain tools to rebuild
            console.print(f"[green]Tool {name} enabled[/green]")
            return True
        return False
    
    def disable_tool(self, name: str) -> bool:
        """Disable a tool by name."""
        if name in self._tools and name not in self._disabled_tools:
            self._disabled_tools.add(name)
            self._langchain_tools = None  # Reset langchain tools to rebuild
            console.print(f"[yellow]Tool {name} disabled[/yellow]")
            return True
        return False
    
    def toggle_tool(self, name: str) -> bool:
        """Toggle a tool's enabled state."""
        return self.enable_tool(name) if name in self._disabled_tools else self.disable_tool(name)
    
    async def get_all_tool_classes_async(self) -> Dict[str, Type]:
        """Get all tool class names and classes asynchronously."""
        tools_dir = os.path.dirname(__file__)
        tool_files = await asyncio.to_thread(lambda: [f for f in os.listdir(tools_dir) if _is_tool_file(f)])
        tool_classes = {}
        
        for tool_file in tool_files:
            try:
                module_name = tool_file[:-3]
                module = await asyncio.to_thread(importlib.import_module, f"ai.tools.{module_name}")
                tool_class = getattr(module, module_name)
                tool_classes[module_name] = tool_class
            except Exception as e:
                console.print(f"[red]Failed to load tool class {tool_file}: {str(e)}[/red]")
        
        return tool_classes

    def get_all_tool_classes(self) -> Dict[str, Type]:
        """Get all tool class names and classes."""
        tools_dir = os.path.dirname(__file__)
        tool_classes = {}
        
        for tool_file in [f for f in os.listdir(tools_dir) if _is_tool_file(f)]:
            try:
                module_name = tool_file[:-3]
                module = importlib.import_module(f"ai.tools.{module_name}")
                tool_class = getattr(module, module_name)
                tool_classes[module_name] = tool_class
            except Exception as e:
                console.print(f"[red]Failed to load tool class {tool_file}: {str(e)}[/red]")
        
        return tool_classes
    
    async def get_langchain_tools_async(self) -> List[Tool]:
        """Get all tools formatted for LangChain use asynchronously."""
        # Always refresh the tool list to ensure we have current state
        await self.load_tools_async()
        
        # Create a reusable NotImplementedError function
        def async_only(*args, **kwargs):
            raise NotImplementedError("This tool is async-only")
            
        self._langchain_tools = []
        for name, tool in self._tools.items():
            try:
                # Skip disabled tools
                if name in self._disabled_tools:
                    console.print(f"[yellow]Skipping disabled tool: {name}[/yellow]")
                    continue
                
                # Create Tool with coroutine only
                langchain_tool = Tool.from_function(
                    func=async_only,
                    coroutine=tool._arun,
                    name=tool.name,
                    description=tool.description,
                    args_schema=tool.args_schema if hasattr(tool, 'args_schema') else None
                )
                
                # Copy return_direct attribute if present
                if hasattr(tool, 'return_direct'):
                    langchain_tool.return_direct = tool.return_direct
                
                self._langchain_tools.append(langchain_tool)
                console.print(f"[green]✓ Added {tool.name} as async-only LangChain Tool[/green]")
                
            except Exception as e:
                console.print(Panel(
                    f"[red]Failed to add tool {tool.name} to LangChain format: {str(e)}[/red]",
                    title="[bold]Conversion Error[/bold]",
                    border_style="red"
                ))
        
        return self._langchain_tools

    def get_langchain_tools(self) -> List[Tool]:
        """Get all tools formatted for LangChain use."""
        # Always refresh the tool list to ensure we have current state
        self.load_tools_sync()
        
        # Create a reusable NotImplementedError function
        def async_only(*args, **kwargs):
            raise NotImplementedError("This tool is async-only")
            
        self._langchain_tools = []
        for name, tool in self._tools.items():
            try:
                # Skip disabled tools
                if name in self._disabled_tools:
                    console.print(f"[yellow]Skipping disabled tool: {name}[/yellow]")
                    continue
                
                # Create Tool with coroutine only
                langchain_tool = Tool.from_function(
                    func=async_only,
                    coroutine=tool._arun,
                    name=tool.name,
                    description=tool.description,
                    args_schema=tool.args_schema if hasattr(tool, 'args_schema') else None
                )
                
                # Copy return_direct attribute if present
                if hasattr(tool, 'return_direct'):
                    langchain_tool.return_direct = tool.return_direct
                
                self._langchain_tools.append(langchain_tool)
                console.print(f"[green]✓ Added {tool.name} as async-only LangChain Tool[/green]")
                
            except Exception as e:
                console.print(Panel(
                    f"[red]Failed to add tool {tool.name} to LangChain format: {str(e)}[/red]",
                    title="[bold]Conversion Error[/bold]",
                    border_style="red"
                ))
        
        return self._langchain_tools
    
    async def generate_system_message_async(self) -> str:
        """Generate a system message describing available tools and their parameters asynchronously."""
        tools = await self.get_langchain_tools_async()
        
        if not tools:
            return "You are a data analyst that helps analyze financial data. Currently no tools are available."
        
        tool_descriptions = "\n".join([f"- {tool.name} : {tool.description}" for tool in tools])
        
        return f"""Available tools:
{tool_descriptions}"""
    
    async def get_tool_names_async(self) -> List[str]:
        """Get all tool names asynchronously."""
        tools = await self.get_langchain_tools_async()
        return [tool.name for tool in tools]

    def generate_system_message(self) -> str:
        """Generate a system message describing available tools and their parameters."""
        tools = self.get_langchain_tools()
        
        if not tools:
            return "You are a data analyst that helps analyze financial data. Currently no tools are available."
        
        tool_descriptions = "\n".join([f"- {tool.name} : {tool.description}" for tool in tools])
        
        return f"""You are a data analyst solving complex financial questions. You must use multiple tools in combination to produce comprehensive analysis.

Available tools:
{tool_descriptions}

IMPORTANT: Use at least 2 different tools per analysis. Gather data first, then analyze it.
Cross-validate information from multiple sources. Don't rely on meta-tools - explicitly use separate tools for gathering and analyzing data.
Start by stating the current date and time in your analysis."""

    def tool_count(self) -> int:
        """Return the number of loaded tools."""
        return len(self._tools)
    
    async def reload(self) -> None:
        """Reload all tools from directory."""
        self.load_tools_sync() 
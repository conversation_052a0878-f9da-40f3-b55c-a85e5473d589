"""Tools package initialization."""
from .registry import ToolRegistry
import importlib
import sys
from pathlib import Path
from typing import Dict, Type
from .base_tool import BaseTool

# Create singleton registry instance
registry = ToolRegistry()

def reload_tools():
    """Reload all tool modules in the package."""
    tools_dir = Path(__file__).parent
    
    # Clear module cache for all tool modules
    for module_name in list(sys.modules.keys()):
        if module_name.startswith('ai.tools.') and module_name != 'ai.tools.base_tool':
            del sys.modules[module_name]
    
    # Clear the tools package itself
    if 'ai.tools' in sys.modules:
        del sys.modules['ai.tools']
    
    # Invalidate import caches
    importlib.invalidate_caches()
    
    # Import the package again
    importlib.import_module('ai.tools')
    
    # Reset registry state
    registry._tools.clear()
    registry._langchain_tools = None
    
    # Load tools - do not convert to langchain format yet to avoid recursion
    registry.load_tools_sync()
    
    # No need to print here - the registry already logs its status

# Tools are not loaded automatically on import to avoid duplicate loading
# Call registry.load_tools_sync() explicitly when needed

# Export registry and reload_tools
__all__ = ["registry", "reload_tools"] 
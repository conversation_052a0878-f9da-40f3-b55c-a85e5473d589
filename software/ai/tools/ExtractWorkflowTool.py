from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from typing import Dict, Any, Optional, Type, List, Sequence, TypedDict
from datetime import datetime
from software.ai.llm.llm_connect import get_llm_connect
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from api.database import get_sync_db
from data import registry
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from main import StockAnalysisApp
from db.report_repository import ReportFactory, ReportRepository
import pandas as pd

class TickerSelection(BaseModel):
    """Schema for TickerSelection input parameters"""
    ticker: str = Field(
        ...,
        description="The ticker to select from the business question considering the available data sources.",
        examples=["META", "AMZN", "AMD", "JPM", "MSFT"]
    )

class DatasourceSelection(BaseModel):
    """Schema for DatasourceSelection input parameters"""
    datasource: str = Field(
        ...,
        description="The datasource to select from the available datasources.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the datasource from the available datasources."
    )

class FeaturesSelection(BaseModel):
    """Schema for FeaturesSelection input parameters"""
    features: List[str] = Field(
        ...,
        description="The features to select from the available features."
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the features from the available features."
    )

class DateRangeSelection(BaseModel):
    """Schema for DateRangeSelection input parameters"""
    start_date: datetime = Field(
        ...,
        description="The start date to select from the available date ranges."
    )
    end_date: datetime = Field(
        ...,
        description="The end date to select from the available date ranges."
    )
    rows: int = Field(
        ...,
        description="The number of rows to select from the available date ranges.",
        ge=1,
        le=40,
        examples=[10, 20, 30, 40]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the date range from the available date ranges."
    )

class ExtractWorkflowToolSchema(BaseModel):
    """Schema for ExtractWorkflowTool input parameters"""
    business_question: str = Field(
        ...,
        description="A data extraction request specifying a ticker for which to load a small sample of historical data (typically 10-20 rows) and calculate features. The request should include a ticker in parentheses and should be specific about the amount and timeframe of data needed.",
        min_length=10,
        examples=[
            "Extract the most recent 10 trading days of price data for Meta (META) with RSI and MACD indicators",
            "Get the last two weeks (10 trading days) of Amazon (AMZN) data with volume and moving averages",
            "Pull a 15-day snapshot of Tesla (TSLA) price history with technical indicators from June 1-15, 2023",
            "Show me the 12 most recent trading days for Apple (AAPL) with Bollinger Bands and RSI",
            "Extract 5 days before and 5 days after March 15, 2023 for Netflix (NFLX) to analyze price movement around their earnings announcement"
        ]
    )

class WorkflowState(TypedDict):
    """State definition for the workflow"""
    messages: Sequence[BaseMessage]
    datasource: str
    features: List[str]
    available_datasources: List[str]
    available_features: List[str]
    available_models: List[str]
    formatted_answer: str
    # Add fields for reasoning
    datasource_reasoning: str
    features_reasoning: str
    ticker: str
    business_question: str
    start_date: str
    end_date: str
    rows: int
    app: Any
    sample_data: pd.DataFrame
    report_id: str | None

class ExtractWorkflowTool(BaseTool):
    """A tool for extracting historical data and calculating features for a given ticker. This tool cannot forecast data, only extracting a small sample of historical data."""

    name: str = "ExtractWorkflowTool"
    description: str = "A tool for extracting historical data and calculating features for a given ticker. This tool cannot forecast data, only extracting a small sample of historical data."
    category: str = "Data Analysis"
    version: str = "1.0.1"
    args_schema: Type[BaseModel] = ExtractWorkflowToolSchema

    async def _arun(self, business_question: str) -> Dict[str, Any]:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with parameter: {business_question}", "Tool Execution")

        workflow = StateGraph(WorkflowState)

        workflow.add_node("list_datasources", self.list_datasources)
        workflow.add_node("extract_ticker", self.extract_ticker)
        workflow.add_node("select_datasource", self.select_datasource)
        workflow.add_node("list_features", self.list_features)
        workflow.add_node("select_features", self.select_features)
        workflow.add_node("list_date_ranges", self.list_date_ranges)
        workflow.add_node("select_date_range", self.select_date_range)
        workflow.add_node("extract_sample_data", self.extract_sample_data)
        workflow.add_node("list_models", self.list_models)
        workflow.add_node("generate_report", self.generate_report)
        workflow.add_node("final_answer", self.final_answer)

        workflow.add_edge(START, "list_datasources")
        workflow.add_edge("list_datasources", "extract_ticker")
        workflow.add_edge("extract_ticker", "select_datasource")
        workflow.add_edge("select_datasource", "list_features")
        workflow.add_edge("list_features", "select_features")
        workflow.add_edge("select_features", "list_date_ranges")
        workflow.add_edge("list_date_ranges", "select_date_range")
        workflow.add_edge("select_date_range", "extract_sample_data")
        workflow.add_edge("extract_sample_data", "list_models")
        workflow.add_edge("list_models", "generate_report")
        workflow.add_edge("generate_report", "final_answer")
        workflow.add_edge("final_answer", END)

        compiled_graph = workflow.compile()

        initial_state = {
            "messages": [HumanMessage(content=business_question)],
            "business_question": business_question,
            # Initialize other fields if necessary, e.g., empty lists/dicts
            "datasource": "",
            "features": [],
            "formatted_answer": "",
            "datasource_reasoning": "",
            "features_reasoning": ""
        }

        final_state = await compiled_graph.ainvoke(initial_state)
        # Show pretty result summary
        return final_state['formatted_answer']

    def list_datasources(self, state: WorkflowState) -> WorkflowState:
        """List all available data sources synchronously."""
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        state["available_datasources"] = [ds["name"]+ " (" + ds["description"] + ")" for ds in datasources_cursor]
        print_step(f"Available datasources:\n• {'\n• '.join(state['available_datasources'])}", "ExtractWorkflowTool: List Datasources", "medium_spring_green")
        return state

    async def extract_ticker(self, state: WorkflowState) -> WorkflowState:
        """Extract the ticker from the business question."""
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()

        structured_model = model.with_structured_output(TickerSelection)
        list_datasources = state["available_datasources"]
        business_question = state["business_question"]
        system_message = SystemMessage(content="You are a helpful assistant that extracts the ticker from the business question.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable data sources: {list_datasources}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        ticker = response.ticker
        state["ticker"] = ticker
        print_step(f"Extracted ticker: {ticker}", "ExtractWorkflowTool: Extract Ticker", "medium_spring_green")
        return state

    async def select_datasource(self, state: WorkflowState) -> WorkflowState:
        """Select the datasource from the available datasources."""
        available_datasources = state["available_datasources"]
        datasource = state["datasource"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DatasourceSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the datasource from the available datasources that is most relevant to the business question. Ignore parantheses description of the datasources.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable datasources: {available_datasources}\nSelect only one datasource from the list")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["datasource"] = response.datasource
        print_step(f"Selected datasource: [bold]{response.datasource}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "ExtractWorkflowTool: Select Datasource", "medium_spring_green")
        state["datasource_reasoning"] = response.reasoning # Store reasoning
        return state

    def list_features(self, state: WorkflowState) -> WorkflowState:
        """List all available features for the selected datasource."""
        features = {}
        for name, feature_class in FEATURE_CLASSES.items():
            description = feature_class.__doc__ or "No description available"
            description = description.strip().split('\n')[0]
            features[name] = description
        # Format the output string correctly
        feature_lines = [f"{name} - {desc}" for name, desc in features.items()]
        print_step(f"Available features: \n• {'\n• '.join(feature_lines)}", "ExtractWorkflowTool: List Features", "medium_spring_green")
        state["available_features"] = feature_lines
        return state

    async def select_features(self, state: WorkflowState) -> WorkflowState:
        """Select the features from the available features."""
        available_features = state["available_features"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(FeaturesSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the features from the available features that are most relevant to the business question. ignore the description after the hyphen.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable features: {available_features}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["features"] = response.features
        print_step(f"Selected features: [bold]{response.features}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic]", "ExtractWorkflowTool: Select Features", "medium_spring_green")
        state["features_reasoning"] = response.reasoning # Store reasoning
        return state

    async def list_date_ranges(self, state: WorkflowState) -> WorkflowState:
        """List start and end dates available for the selected datasource."""
        loader_class = registry.get_loader_class(state["datasource"])
        if loader_class:
            app = StockAnalysisApp(ticker=state["ticker"])
            app.load_data(loader_class=loader_class) # Load all data
            app.add_features(feature_names=state["features"])

            if app.data is not None and not app.data.empty:
                start_date = app.data.index.min()
                end_date = app.data.index.max()
                rows = app.data.shape[0]
                state["app"] = app
                state["start_date"] = start_date
                state["end_date"] = end_date
                state["rows"] = rows
                print_step(f"Available date ranges: {start_date} to {end_date} with {rows} rows", "ExtractWorkflowTool: List Date Ranges", "medium_spring_green")
        return state

    async def select_date_range(self, state: WorkflowState) -> WorkflowState:
        """Select the date range from the available date ranges."""
        available_start_date = state["start_date"]
        available_end_date = state["end_date"]
        available_rows = state["rows"]
        business_question = state["business_question"]

        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DateRangeSelection)

        system_message = SystemMessage(content=f"""You are a helpful assistant that selects a date range that is within the available date range.
IMPORTANT: You MUST select dates between {available_start_date} and {available_end_date}.
IMPORTANT: You MUST select a number of rows that is between 1 and 40.
If the business question asks for dates outside this range, select dates within this range instead.
If the business question asks for a specific number of days before and after a date, ensure the total rows is correct (days before + target day + days after).
For example, 5 days before and 5 days after a specific date would be 11 rows total.""")

        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable date range: {available_start_date} to {available_end_date} with {available_rows} total rows")
        messages = [system_message, human_message]

        response = await structured_model.ainvoke(messages)

        # Ensure selected dates are within available range
        selected_start = max(response.start_date, available_start_date)
        selected_end = min(response.end_date, available_end_date)

        state["start_date"] = selected_start
        state["end_date"] = selected_end
        state["rows"] = response.rows

        print_step(f"Selected date range: [bold]{selected_start} to {selected_end}[/bold] \\nReasoning: [italic]{response.reasoning}[/italic] \\nNumber of rows: [bold]{response.rows}[/bold]", "ExtractWorkflowTool: Select Date Range", "medium_spring_green")
        return state

    async def extract_sample_data(self, state: WorkflowState) -> WorkflowState:
        """Extract the data from the selected date range."""
        app = state["app"]
        start_date = state["start_date"]
        end_date = state["end_date"]
        rows = state["rows"]

        # Filter existing data by date range instead of reloading
        filtered_data = app.data[
            (app.data.index >= start_date) &
            (app.data.index <= end_date)
        ]

        # Get evenly distributed samples across the date range
        if len(filtered_data) > rows:
            # Calculate indices to sample evenly across the range
            indices = []
            if rows > 1:
                step = len(filtered_data) / rows
                for i in range(rows):
                    indices.append(int(i * step))
            else:
                indices = [0]

            sample_data = filtered_data.iloc[indices]
        else:
            sample_data = filtered_data

        # Sort chronologically for display
        sample_data = sample_data.sort_index(ascending=True)
        state["sample_data"] = sample_data
        print_step(f"Extracted data: {sample_data}", "ExtractWorkflowTool: Extract Data", "medium_spring_green")
        return state

    def list_models(self, state: WorkflowState) -> WorkflowState:
        """List all available models."""
        models = [
            f"{cls.__name__} - {(getattr(cls, 'description', cls.__doc__ or 'No description available')).strip().splitlines()[0]}"
            for cls in MODEL_CLASSES
        ]
        print_step(f"Available models: \n• {'\n• '.join(models)}", "SimpleWorkflowTool: List Models", "violet")
        state["available_models"] = models
        return state

    async def generate_report(self, state: WorkflowState) -> WorkflowState:
        """Generate a report from the extracted data."""
        # Convert datetime objects to string format
        start_date_str = state["start_date"].strftime("%Y-%m-%d") if hasattr(state["start_date"], "strftime") else state["start_date"]
        end_date_str = state["end_date"].strftime("%Y-%m-%d") if hasattr(state["end_date"], "strftime") else state["end_date"]

        report = ReportFactory.create_extract_report(
            ticker=state["ticker"],
            data_loader=state["datasource"],
            data=state["sample_data"],
            features=state["features"],
            start_date=start_date_str,
            end_date=end_date_str
        )
        report_repository = await ReportRepository.create() # Use create() to get initialized instance
        report_id = await report_repository.insert_report(report)
        print_step(f"Report ID: {report_id}", "ExtractWorkflowTool: Generate Report", "medium_spring_green")
        state["report_id"] = report_id
        return state

    def transpose_and_split_data(self, df: pd.DataFrame) -> str:
        """
        Transpose the DataFrame and split it by metric.

        Args:
            df: The DataFrame to transpose and split

        Returns:
            A formatted string with each metric in its own table
        """
        if df is None or df.empty:
            return "No data available"

        # Make a copy of the DataFrame to avoid modifying the original
        df_copy = df.copy()

        # Reset index to make Date a column
        df_copy = df_copy.reset_index()

        # Create a formatted string with each metric in its own table
        result = []

        # Iterate over each column (metric) in the original DataFrame
        for column in df_copy.columns:
            if column == 'Date':
                continue

            # Create a clean table for this metric
            metric_data = df_copy[['Date', column]].copy()

            # Format numeric values to be more readable
            if pd.api.types.is_numeric_dtype(metric_data[column]):
                if metric_data[column].abs().max() > 1000:
                    # Format large numbers with commas
                    metric_data[column] = metric_data[column].map(lambda x: f"{x:,.0f}")
                elif metric_data[column].abs().max() < 0.01:
                    # Format very small numbers in scientific notation
                    metric_data[column] = metric_data[column].map(lambda x: f"{x:.4e}")
                else:
                    # Format regular numbers with 2 decimal places
                    metric_data[column] = metric_data[column].map(lambda x: f"{x:.2f}")

            # Create a clean table string
            table_rows = []
            table_rows.append(f"### {column}")
            table_rows.append("| Date | Value |")
            table_rows.append("|------|-------|")

            for _, row in metric_data.iterrows():
                # Use the original datetime format
                table_rows.append(f"| {row['Date']} | {row[column]} |")

            # Add to result
            result.append("\n".join(table_rows))

        # Join all tables with double newlines
        return "\n\n".join(result)

    def final_answer(self, state: WorkflowState) -> WorkflowState:
        """Final crafted formatted answer for the workflow."""
        ticker = state["ticker"]
        business_question = state["business_question"]

        # Get all the data we need
        datasource = state["datasource"]
        datasource_reasoning = state.get("datasource_reasoning", "Not provided")
        available_datasources = state.get("available_datasources", [])

        features = state["features"]
        features_reasoning = state.get("features_reasoning", "Not provided")
        available_features = state.get("available_features", [])
        available_models = state["available_models"]
        start_date = state["start_date"]
        end_date = state["end_date"]
        rows = state["rows"]
        sample_data = state.get("sample_data", None)

        # Format the sample data as markdown table
        if sample_data is not None:
            data_table = sample_data.reset_index().to_markdown(index=False)
            # Also create transposed and split data
            transposed_data = self.transpose_and_split_data(sample_data)
        else:
            data_table = "No data available"
            transposed_data = "No data available"

        # Format available datasources as bullet list
        datasources_list = "\n".join([f"- {ds}" for ds in available_datasources])

        # Format available features as bullet list
        features_list = "\n".join([f"- {f}" for f in available_features])

        # Format available models as bullet list
        models_list = "\n".join([f"- {m}" for m in available_models])

        # Create a markdown formatted summary
        markdown = f"""# Data Extraction Results for {ticker}

## Business Question
{business_question}

## Data Sources
### Available Data Sources
{datasources_list}

### Selected Data Source
**{datasource}**
**Reasoning**: {datasource_reasoning}

## Features
### Available Features
{features_list}

### Selected Features
**{', '.join(features)}**
**Reasoning**: {features_reasoning}

## Models
### Available Models
{models_list}

## Date Range
**Start Date**: {start_date}
**End Date**: {end_date}
**Number of Metrics**: {rows}

## Sample Data
{transposed_data}

## Report ID
**Report ID**: {state["report_id"]}
"""

        state["formatted_answer"] = markdown
        print_step(f"Report:\\n{markdown}", "ExtractWorkflowTool: Final Answer", "medium_spring_green")
        return state

if __name__ == "__main__":
    import asyncio

    async def main():
        # Create instance of the tool
        tool = ExtractWorkflowTool()

        # Sample business question
        question = "Extract 5 days before and 5 days after March 15, 2025 for Netflix (NFLX) to analyze price movement around their earnings announcement"

        # Run the tool
        print(f"Running ExtractWorkflowTool with query: {question}")
        result = await tool._arun(question)
        print(f"Result: {result}")

    # Run the async main function
    asyncio.run(main())

# AI Tools System

This directory contains all the AI tools used by the system. Each tool is defined as a separate Python file.

## Overview

The tools system is designed to be simple and file-based:

- All tools are Python files in this directory
- Each tool inherits from `BaseTool` defined in `base_tool.py`
- Tools are loaded dynamically from files
- Tools are registered to be used with Lang<PERSON><PERSON><PERSON>

## Creating a New Tool

To create a new tool:

1. Create a new Python file with a name that follows PascalCase and ends with "Tool", e.g., `MyNewTool.py`
2. Inherit from the `BaseTool` class
3. Define required fields like `name`, `description`, etc.
4. Implement the `_run` method (and optionally `_arun` for async support)

You can use the web interface at `/add-tool` to create a new tool.

## Tool Structure

A typical tool looks like this:

```python
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool

class MyNewToolSchema(BaseModel):
    """Schema for MyNewTool input parameters"""
    param1: str = Field(
        description="Description of parameter 1",
    )

class MyNewTool(BaseTool):
    """Tool description"""
    
    name: str = Field("MyNewTool", description="The name of the tool")
    description: str = Field("Description of what the tool does", description="A detailed description")
    category: str = Field(default="general", description="Tool category")
    version: str = Field(default="1.0.0", description="Tool version")
    args_schema: type[BaseModel] = MyNewToolSchema
    
    def _run(self, param1: str) -> str:
        """Execute the tool's functionality."""
        # Your code here
        return f"Result with {param1}"
    
    async def _arun(self, param1: str) -> str:
        """Execute the tool's functionality asynchronously."""
        return self._run(param1=param1)
```

## Registry

The `registry.py` file contains the `ToolRegistry` class, which is responsible for:

- Loading tools from files
- Converting tools to LangChain format
- Providing access to all registered tools

## Testing

You can test the tools system by running:

```
python software/test_tools.py
```

## Viewing Tools

All tools can be viewed in the web interface at `/toolbox`. 
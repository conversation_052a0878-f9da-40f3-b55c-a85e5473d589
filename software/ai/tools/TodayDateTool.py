from datetime import datetime
import pytz
from typing import Dict, Any
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty

class TodayDateSchema(BaseModel):
    """Schema for TodayDateTool input parameters"""
    timezone: str = Field(
        description="Timezone name (required)",
        example="America/New_York"
    )

class TodayDateTool(BaseTool):
    """Tool that provides current date and time."""
    
    name: str = "TodayDateTool"
    description: str = "MENDATORY TOOL TO START THE ANALYSIS: A tool that returns the current date, time, day of week and timezone information. Accepts a timezone name (e.g. 'America/New_York') and returns a dictionary with formatted date, time, day of week and timezone. Falls back to New York timezone if invalid timezone provided."
    args_schema: type[BaseModel] = TodayDateSchema
    version: str = "1.0.2"
    category: str = "Time"
    
    async def _arun(self, timezone: str) -> Dict[str, Any]:
        """Get current date and time in specified timezone asynchronously."""
        try:
            # Get timezone
            try:
                tz = pytz.timezone(timezone)
            except pytz.exceptions.UnknownTimeZoneError:
                print_debug(f"[yellow]Invalid timezone '{timezone}', using New York[/yellow]", "TodayDateTool: Error")
                tz = pytz.timezone("America/New_York")
            
            # Get current time
            current_time = datetime.now(tz)
            
            result = {
                "date": current_time.strftime("%Y-%m-%d"),
                "time": current_time.strftime("%H:%M:%S"),
                "day_of_week": current_time.strftime("%a"),
                "timezone": str(tz)
            }
            
            print_step(f"Current date and time: {result}", "TodayDateTool: Current Date & Time", "royal_blue1")
            
            return result
            
        except Exception as e:
            error_result = {"error": str(e)}
            print_debug(f"Error: {error_result}", "TodayDateTool: Error")
            return error_result

if __name__ == "__main__":
    tool = TodayDateTool()
    import asyncio
    
    # Example usage with required timezone parameter
    result = asyncio.run(tool._arun(timezone="America/New_York"))
    print(f"\nTest result: {result}")
    
    # Another example with different timezone
    result = asyncio.run(tool._arun(timezone="Europe/London"))
    print(f"\nLondon time: {result}")

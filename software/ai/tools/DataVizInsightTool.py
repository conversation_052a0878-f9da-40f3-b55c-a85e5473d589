from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from datetime import datetime
from software.ai.llm.llm_connect import get_llm_connect
from typing import Dict, Any, Optional, Type, List, Sequence, TypedDict
from pydantic import BaseModel, Field
from ai.tools.base_tool import BaseTool
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from api.database import get_sync_db
from data import registry
from features import FEATURE_CLASSES
from main import StockAnalysisApp
import pandas as pd

class DataVizInsightToolSchema(BaseModel):
    """Schema for DataVizInsightTool input parameters"""
    business_question: str = Field(
        description="A specific financial analysis request that will be visualized using advanced charting techniques. Our cutting-edge visualization engine can generate professional-grade technical analysis charts, correlation studies, distribution analyses, and pattern recognition visualizations.",
        min_length=10,
        examples=[
            "Analyze Apple (AAPL) RSI and MACD indicators to identify potential overbought or oversold conditions over the past 24 months.",
            "Create a Bollinger Bands chart for Tesla (TSLA) with 20-day moving average and highlight price breakouts beyond 2 standard deviations.",
            "Generate a correlation heatmap between Microsoft (MSFT) stock price, trading volume, and Average True Range to identify volatility patterns.",
            "Visualize the Fibonacci retracement levels for Amazon (AMZN) from its 52-week high to recent low with support and resistance zones.",
            "Plot the Stochastic Oscillator for Netflix (NFLX) with %K and %D lines to identify momentum shifts and potential reversal points.",
            "Create a candlestick chart for JPMorgan Chase (JPM) with VWAP overlay to analyze institutional buying and selling pressure.",
            "Generate a histogram of daily returns for Google (GOOGL) with a normal distribution overlay to assess return distribution characteristics.",
            "Visualize the On-Balance Volume (OBV) indicator for Nvidia (NVDA) compared to its stock price to identify potential divergences.",
            "Create a chart showing the Commodity Channel Index (CCI) for Coca-Cola (KO) to identify cyclical overbought and oversold conditions.",
            "Analyze the seasonal patterns in Johnson & Johnson (JNJ) stock performance with monthly and quarterly return distributions."
        ]
    )

class WorkflowState(TypedDict):
    """State for the workflow"""
    messages: Sequence[BaseMessage]
    business_question: str
    available_datasources: List[str]
    ticker: str
    datasource: str
    datasource_reasoning: str
    available_features: List[str]
    features: List[str]
    features_reasoning: str
    app: Any
    start_date: str
    end_date: str
    rows: int
    sample_data: pd.DataFrame
    code: str
    parsed_code: str
    chart_result: Optional[str]
    execution_output: Optional[str]
    execution_error: Optional[str]
    chart_interpretation: Optional[str]
    interpretation_error: Optional[str]
    report_id: Optional[str]
    formatted_answer: str

class TickerSelection(BaseModel):
    """Schema for TickerSelection input parameters"""
    ticker: str = Field(
        ...,
        description="The ticker to select from the business question considering the available data sources.",
        examples=["META", "AMZN", "AMD", "JPM", "MSFT"]
    )

class DatasourceSelection(BaseModel):
    """Schema for DatasourceSelection input parameters"""
    datasource: str = Field(
        ...,
        description="The datasource to select from the available datasources.",
        examples=["YahooFinanceLoader", "AlphaVantageLoader", "QuandlLoader"]
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the datasource from the available datasources."
    )

class FeaturesSelection(BaseModel):
    """Schema for FeaturesSelection input parameters"""
    features: List[str] = Field(
        ...,
        description="The features to select from the available features."
    )
    reasoning: str = Field(
        ...,
        description="The reasoning for selecting the features from the available features."
    )

class DataVizInsightTool(BaseTool):
    """A tool for generating data visualizations and insights based on a business question."""

    name: str = "DataVizInsightTool"
    description: str = "A powerful tool that must be used after the ExtractWorkflowTool tool for visual confirmation."
    category: str = "Data Visualization"
    version: str = "1.0.2"
    args_schema: Type[BaseModel] = DataVizInsightToolSchema

    async def _arun(self, business_question: str) -> Dict[str, Any]:
        """Execute the tool's functionality asynchronously."""
        print_debug(f"Running {self.name} with parameter: {business_question}", "Tool Execution")

        workflow = StateGraph(WorkflowState)
        workflow.add_node("list_datasources", self.list_datasources)
        workflow.add_node("extract_ticker", self.extract_ticker)
        workflow.add_node("select_datasource", self.select_datasource)
        workflow.add_node("list_features", self.list_features)
        workflow.add_node("select_features", self.select_features)
        workflow.add_node("list_date_ranges", self.list_date_ranges)
        workflow.add_node("extract_sample_data", self.extract_sample_data)
        workflow.add_node("create_chart", self.create_chart)
        workflow.add_node("parse_chart_code", self.parse_chart_code)
        workflow.add_node("execute_code", self.execute_code)
        workflow.add_node("read_chart", self.read_chart)

        workflow.add_edge(START, "list_datasources")
        workflow.add_edge("list_datasources", "extract_ticker")
        workflow.add_edge("extract_ticker", "select_datasource")
        workflow.add_edge("select_datasource", "list_features")
        workflow.add_edge("list_features", "select_features")
        workflow.add_edge("select_features", "list_date_ranges")
        workflow.add_edge("list_date_ranges", "extract_sample_data")
        workflow.add_edge("extract_sample_data", "create_chart")
        workflow.add_edge("create_chart", "parse_chart_code")
        workflow.add_edge("parse_chart_code", "execute_code")
        workflow.add_edge("execute_code", "read_chart")
        workflow.add_edge("read_chart", END)

        compiled_graph = workflow.compile()
        initial_state = {
            "messages": [HumanMessage(content=business_question)],
            "business_question": business_question,
            "formatted_answer": "Analysis pending. Currently only listing available datasources.",
        }

        final_state = await compiled_graph.ainvoke(initial_state)

        return final_state['formatted_answer']


    def list_datasources(self, state: WorkflowState) -> WorkflowState:
        """List all available data sources synchronously."""
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        state["available_datasources"] = [ds["name"]+ " (" + ds["description"] + ")" for ds in datasources_cursor]
        print_step(f"Available datasources:\n• {'\n• '.join(state['available_datasources'])}", "DataVizInsightTool: List Datasources", "light_coral")
        return state

    async def extract_ticker(self, state: WorkflowState) -> WorkflowState:
        """Extract the ticker from the business question."""
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()

        structured_model = model.with_structured_output(TickerSelection)
        list_datasources = state["available_datasources"]
        business_question = state["business_question"]
        system_message = SystemMessage(content="You are a helpful assistant that extracts the ticker from the business question.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable data sources: {list_datasources}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        ticker = response.ticker
        state["ticker"] = ticker
        print_step(f"Extracted ticker: {ticker}", "ExtractWorkflowTool: Extract Ticker", "light_coral")
        return state

    async def select_datasource(self, state: WorkflowState) -> WorkflowState:
        """Select the datasource from the available datasources."""
        available_datasources = state["available_datasources"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DatasourceSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects a datasource from the available datasources that is most relevant to the business question. Ignore parantheses description of the datasources.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable datasources: {available_datasources}\nSelect only one datasource from the list")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["datasource"] = response.datasource
        print_step(f"Selected datasource: [bold]{response.datasource}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "DataVizInsightTool: Select Datasource", "light_coral")
        state["datasource_reasoning"] = response.reasoning # Store reasoning
        return state

    def list_features(self, state: WorkflowState) -> WorkflowState:
        """List all available features for the selected datasource."""
        features = {}
        for name, feature_class in FEATURE_CLASSES.items():
            description = feature_class.__doc__ or "No description available"
            description = description.strip().split('\n')[0]
            features[name] = description
        # Format the output string correctly
        feature_lines = [f"{name} - {desc}" for name, desc in features.items()]
        print_step(f"Available features: \n• {'\n• '.join(feature_lines)}", "DataVizInsightTool: List Features", "light_coral")
        state["available_features"] = feature_lines
        return state

    async def select_features(self, state: WorkflowState) -> WorkflowState:
        """Select the features from the available features."""
        available_features = state["available_features"]
        business_question = state["business_question"]
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(FeaturesSelection)
        system_message = SystemMessage(content="You are a helpful assistant that selects the features from the available features that are most relevant to the business question. ignore the description after the hyphen.")
        human_message = HumanMessage(content=f"Business Question: {business_question}\nAvailable features: {available_features}")
        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)
        state["features"] = response.features
        print_step(f"Selected features: [bold]{response.features}[/bold] \nReasoning: [italic]{response.reasoning}[/italic]", "DataVizInsightTool: Select Features", "light_coral")
        state["features_reasoning"] = response.reasoning # Store reasoning
        return state

    async def list_date_ranges(self, state: WorkflowState) -> WorkflowState:
        """List start and end dates available for the selected datasource."""
        loader_class = registry.get_loader_class(state["datasource"])
        if loader_class:
            app = StockAnalysisApp(ticker=state["ticker"])
            app.load_data(loader_class=loader_class) # Load all data
            app.add_features(feature_names=state["features"])

            if app.data is not None and not app.data.empty:
                start_date = app.data.index.min()
                end_date = app.data.index.max()
                rows = app.data.shape[0]
                state["app"] = app
                state["start_date"] = start_date
                state["end_date"] = end_date
                state["rows"] = rows
                print_step(f"Available date ranges: {start_date} to {end_date} with {rows} rows", "DataVizInsightTool: List Date Ranges", "light_coral")
        return state

    async def extract_sample_data(self, state: WorkflowState) -> WorkflowState:
        """Extract the data from the selected date range."""
        app = state["app"]
        start_date = state["start_date"]
        end_date = state["end_date"]
        rows = state["rows"]

        # Filter existing data by date range instead of reloading
        filtered_data = app.data[
            (app.data.index >= start_date) &
            (app.data.index <= end_date)
        ]

        # Get evenly distributed samples across the date range
        if len(filtered_data) > rows:
            # Calculate indices to sample evenly across the range
            indices = []
            if rows > 1:
                step = len(filtered_data) / rows
                for i in range(rows):
                    indices.append(int(i * step))
            else:
                indices = [0]

            sample_data = filtered_data.iloc[indices]
        else:
            sample_data = filtered_data

        # Sort chronologically for display
        sample_data = sample_data.sort_index(ascending=True)
        state["sample_data"] = sample_data
        print_step(f"Extracted data: {sample_data}", "DataVizInsightTool: Extract Data", "light_coral")
        return state

    async def create_chart(self, state: WorkflowState) -> WorkflowState:
        """Create visualization charts based on the extracted data.

        This node generates appropriate visualizations based on the sample data
        and the business question.
        """
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")
        system_prompt = '''You are a data visualization expert.
        Your task is to create Python code using matplotlib that creates a chart that will answer the business question.
        Your inputs are the business question(string) and the pandas DataFrame (data), which is illustrated by the sample data.
        The output should be Python code that will create an image that will be saved in `software/library/images` directory.

        Guidance:
        - Create a specific method called `create_chart` that takes a pandas DataFrame as an argument: `def create_chart(data: pd.DataFrame):`
        - IMPORTANT: Use ONLY the data parameter passed to the function. DO NOT create or hardcode any sample data.
        - Work with the actual columns available in the provided sample data. DO NOT assume columns that don't exist.
        - If the sample data doesn't have the exact metrics needed for the business question, derive them from the available data.
        - If metrics cannot be derived, create a chart that clearly indicates which metrics are missing.
        - Do not include validation code, error handling, or example usage outside the create_chart function.
        - Do not include any if __name__ == '__main__' blocks or test code.
        - The chart should be simple and easy to understand.
        - Create a unique output path for the saved image using the chart type and uuid4:
          ```
          import uuid
          chart_type = "appropriate_name"  # Use an appropriate name based on the chart type
          unique_id = str(uuid.uuid4())[:8]  # Use first 8 characters of UUID for brevity
          output_path = f"software/library/images/{chart_type}_{unique_id}.png"
          ```
        - Include a return statement that returns the output_path to the saved image.
        - Do not provide explanation. Only provide the code.
        - Code should be wrapped between ```python and ```'''

        human_prompt = f'''Business Question: {state["business_question"]}
        Sample Data: {state["sample_data"]}'''
        response = await model.ainvoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ])
        print_step(f"Generated chart: {response.content}", "DataVizInsightTool: Create Chart Code", "light_coral")

        # Update the formatted answer with the chart code
        state["code"] = response.content
        return state

    async def parse_chart_code(self, state: WorkflowState) -> WorkflowState:
        """Parse the Python code from the state's code field.

        This node extracts the actual Python code from the markdown code block
        and stores it in a clean format for execution.
        """
        code = state.get("code", "")

        # Extract code between ```python and ``` markers
        if "```python" in code and "```" in code[code.find("```python") + 9:]:
            start_idx = code.find("```python") + 9
            end_idx = code.find("```", start_idx)
            parsed_code = code[start_idx:end_idx].strip()

            # Store the parsed code in the state
            state["parsed_code"] = parsed_code
        else:
            # Handle case where code is not properly formatted
            state["parsed_code"] = code.replace("```python", "").replace("```", "").strip()
        return state

    async def execute_code(self, state: WorkflowState) -> WorkflowState:
        """Execute the parsed chart code with the sample data.

        This node takes the parsed Python code and executes it using the sample data
        to generate the visualization.
        """
        import os
        import sys
        from io import StringIO

        parsed_code = state.get("parsed_code", "")
        sample_data = state.get("sample_data")

        if not parsed_code or sample_data is None or sample_data.empty:
            print_step("Missing code or data for execution", "DataVizInsightTool: Execute Chart Code", "light_coral")
            return state

        # Create directory for images if it doesn't exist
        image_dir = "software/library/images"
        os.makedirs(image_dir, exist_ok=True)

        # Prepare a temporary module to execute the code
        try:
            # Create a namespace for execution
            namespace = {
                "pd": pd,
                "plt": __import__("matplotlib.pyplot").pyplot,
                "np": __import__("numpy"),
                "uuid": __import__("uuid"),
                "df": sample_data,  # Pass the sample data as df
                "data": sample_data  # Also pass as data for flexibility
            }

            # Capture stdout to get any print output
            old_stdout = sys.stdout
            redirected_output = StringIO()
            sys.stdout = redirected_output

            # Execute the code
            exec(parsed_code, namespace)

            # Check if create_chart function was defined
            if "create_chart" in namespace:
                # Call the create_chart function with the sample data
                result = namespace["create_chart"](sample_data)
                state["chart_result"] = str(result)

                # Verify the chart file exists
                if os.path.exists(result):
                    state["chart_result"] = result
                else:
                    abs_result = os.path.abspath(result)
                    if os.path.exists(abs_result):
                        # Update the result to use the absolute path
                        state["chart_result"] = abs_result
                    else:
                        print_step(f"Chart file not found after creation at path: {result} or {abs_result}", "DataVizInsightTool: Execute Chart Code", "light_coral")
            else:
                print_step("No create_chart function found in the code", "DataVizInsightTool: Execute Chart Code", "light_coral")

            # Restore stdout
            sys.stdout = old_stdout
            output = redirected_output.getvalue()

            if output:
                state["execution_output"] = output

        except Exception as e:
            error_msg = f"Error executing chart code: {str(e)}"
            state["execution_error"] = error_msg

        return state

    async def read_chart(self, state: WorkflowState) -> WorkflowState:
        """Interpret the generated chart using LLM vision capabilities.

        This node takes the chart image path and uses an LLM with vision capabilities
        to interpret the chart and answer the business question.
        """
        import os
        import base64

        # Get the chart result (path to the image) and business question
        chart_path = state.get("chart_result", "")
        business_question = state.get("business_question", "")

        # Check if the chart path exists and is valid
        if not chart_path:
            print_step("No chart path found", "DataVizInsightTool: Read Chart", "light_coral")
            return state

        # Convert to absolute path if it's a relative path
        if not os.path.isabs(chart_path):
            abs_chart_path = os.path.abspath(chart_path)
        else:
            abs_chart_path = chart_path

        if not os.path.exists(abs_chart_path):
            print_step(f"Chart file not found at path: {abs_chart_path}", "DataVizInsightTool: Read Chart", "light_coral")
            return state

        try:
            # Get LLM with vision capabilities
            llm_connect = get_llm_connect()
            model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")

            # Create minimalistic and effective prompts
            system_prompt = "Analyze this chart and answer the business question concisely. Focus on patterns, trends, and correlations visible in the visualization."

            # Create a human prompt with the business question and image
            human_prompt = f"Business Question: {business_question}\n\nThe chart is attached. What insights can you provide based on this visualization?"

            # Convert the image to base64

            # Read the image file and convert to base64
            with open(abs_chart_path, "rb") as image_file:
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode("utf-8")

            # Determine the MIME type based on file extension
            mime_type = "image/png"  # Default to PNG
            if abs_chart_path.lower().endswith(".jpg") or abs_chart_path.lower().endswith(".jpeg"):
                mime_type = "image/jpeg"

            # Create the data URL
            data_url = f"data:{mime_type};base64,{base64_image}"

            # Include the image in the message using base64
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=[
                    {"type": "text", "text": human_prompt},
                    {"type": "image_url", "image_url": {"url": data_url}}
                ])
            ]

            # Get the interpretation from the LLM
            response = await model.ainvoke(messages)

            # Store the interpretation in the state
            state["chart_interpretation"] = response.content

            # Create a comprehensive markdown report
            available_datasources = state.get("available_datasources", [])
            datasource = state.get("datasource", "")
            datasource_reasoning = state.get("datasource_reasoning", "")

            # Format available datasources with line breaks
            formatted_datasources = '\n- '.join(available_datasources)

            # Build the markdown report with improved visual presentation
            markdown_report = f"""
# Data Visualization Report

## Business Question
{business_question}

## Chart Analysis
{response.content}

## Data Sources
### Available Data Sources:
- {formatted_datasources}

### Selected Data Source
**{datasource}**

### Selection Reasoning
{datasource_reasoning}
"""

            # Update the formatted answer with the markdown report
            state["formatted_answer"] = markdown_report

            # Print the formatted answer
            print_step(state["formatted_answer"], "DataVizInsightTool: Format Report", "light_coral")

        except Exception as e:
            error_msg = f"Error interpreting chart: {str(e)}"
            state["interpretation_error"] = error_msg

            # Create a markdown report even in case of error
            available_datasources = state.get("available_datasources", [])
            datasource = state.get("datasource", "")
            datasource_reasoning = state.get("datasource_reasoning", "")

            # Format available datasources with line breaks
            formatted_datasources = '\n- '.join(available_datasources)

            # Build the markdown report with error information and improved visual presentation
            markdown_report = f"""
# Data Visualization Report

## Business Question
{business_question}

## Chart Analysis
*Error interpreting chart: {str(e)}*

## Data Sources
### Available Data Sources:
- {formatted_datasources}

### Selected Data Source
**{datasource}**

### Selection Reasoning
{datasource_reasoning}
"""

            # Update the formatted answer with the markdown report
            state["formatted_answer"] = markdown_report

            # Print the formatted answer with error indication
            print_step("Generated visualization report (with errors)", "DataVizInsightTool: Format Report", "light_coral")

        return state


if __name__ == "__main__":

    import asyncio
    def main():
        tool = DataVizInsightTool()
        result = asyncio.run(tool._arun(business_question='''Analyze the seasonal patterns in Johnson & Johnson (JNJ) stock performance with monthly and quarterly return distributions.'''))
        print(result)

    main()
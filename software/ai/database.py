from typing import Dict, Any, Optional
from datetime import datetime
from pymongo import MongoClient
from pymongo.database import Database
import os
import logging

logger = logging.getLogger(__name__)

class DatabaseConnection:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseConnection, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.client = None
            self.db = None
            self._initialized = True
            self.connect()
    
    def connect(self) -> None:
        """Connect to MongoDB using environment variables."""
        try:
            mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017')
            self.client = MongoClient(mongo_uri)
            self.db = self.client.get_database('vero')
            logger.info("Successfully connected to MongoDB")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    
    def get_database(self) -> Optional[Database]:
        """Get the database instance."""
        return self.db if self.db is not None else None
    
    def close(self) -> None:
        """Close the database connection."""
        if self.client is not None:
            self.client.close()
            logger.info("Closed MongoDB connection")

def save_tool(tool_data: Dict[str, Any]) -> bool:
    """Save tool metadata to MongoDB."""
    try:
        db = DatabaseConnection().get_database()
        if db is None:
            logger.error("No database connection available")
            return False
        
        # Prepare tool document
        tool_doc = {
            "name": tool_data["name"],
            "description": tool_data["description"],
            "category": tool_data["category"],
            "version": tool_data["version"],
            "parameters": tool_data.get("parameters", {}),
            "updated_at": datetime.utcnow()
        }
        
        # Update or insert the tool
        result = db.tools.update_one(
            {"name": tool_data["name"]},
            {"$set": tool_doc},
            upsert=True
        )
        
        logger.info(f"Tool {tool_data['name']} saved successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save tool to database: {str(e)}")
        return False

def get_tool(name: str) -> Optional[Dict[str, Any]]:
    """Get tool metadata from MongoDB."""
    try:
        db = DatabaseConnection().get_database()
        if db is None:
            logger.error("No database connection available")
            return None
            
        tool = db.tools.find_one({"name": name})
        return tool
        
    except Exception as e:
        logger.error(f"Failed to get tool from database: {str(e)}")
        return None

def get_all_tools() -> list:
    """Get all tools from MongoDB."""
    try:
        db = DatabaseConnection().get_database()
        if db is None:
            logger.error("No database connection available")
            return []
            
        tools = list(db.tools.find())
        return tools
        
    except Exception as e:
        logger.error(f"Failed to get tools from database: {str(e)}")
        return [] 
from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import print_step, print_debug, print_pretty, StructuredReport
from langgraph.constants import Send
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from typing import List, Dict, Any, TypedDict
from software.ai.tools import registry
from langchain_core.messages import SystemMessage, HumanMessage
from software.db.research_repository import ResearchRepository
from software.ai.graph.search_scope_graph import graph as search_scope_graph

research_repo = ResearchRepository()
MAX_OPERATIONS = 1
# Target exploration/exploitation ratio (exploration % / exploitation %)
# Higher exploration ratio (e.g., 70/30) favors discovering new avenues
# Higher exploitation ratio (e.g., 30/70) favors deepening existing findings
TARGET_EXPLORE_EXPLOIT_RATIO = 90/10

class DeepSearchState(TypedDict, total=False):
    director_id: str
    task_id: str
    business_question: str
    messages: List[Dict[str, Any]]
    tasks: List[str]
    results: List[str]
    summarized_tasks: str
    summarized_results: str
    operations: int
    max_operations: int
    final_report: str
    structured_report: StructuredReport
    tools: List[List[Dict[str, str]]]

async def search_node(state: DeepSearchState):
    """Node that uses LLM to decide tool usage and processes results"""
    print_debug("Entering search node", "search_node")
    await research_repo.update_workflow_stage(state["task_id"], "__start__", "in_progress")
    await research_repo.update_workflow_stage(state["task_id"], "__start__", "completed")
    await research_repo.update_workflow_stage(state["task_id"], "search_scope", "in_progress")
    await research_repo.update_workflow_stage(state["task_id"], "search_scope", "completed")

    return {"business_question": "Should we invest in Nike (NKE) in the next 5 days?"}

async def supervisor(state: DeepSearchState):
    """Node that uses LLM to decide tool usage and processes results"""
    await research_repo.update_workflow_stage(state["task_id"], "supervisor", "in_progress")
    print_debug("Entering supervisor node", "supervisor")
    print_step(f"Step {state.get("operations", 0)}/{state.get("max_operations", MAX_OPERATIONS)}", "Steps", "pale_turquoise1", verbose=True)

    if not state.get("tasks"):
        state["tasks"] = [state["business_question"]]
    if "results" not in state:
        state["results"] = []

    task_description = state["tasks"][-1]


    tools = await registry.get_langchain_tools_async()
    tool_prompt = await registry.generate_system_message_async()
    director_id = state.get("director_id")
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "supervisor")

    supervisor = create_react_agent(
        model=model,
        tools=tools,
        name="supervisor",
        prompt=f"""<role>
You are a financial research supervisor specializing in analyzing investment opportunities and market trends. Your expertise is in using specialized tools to gather and synthesize financial data.
</role>

<context>
The business question requires a comprehensive research plan that will be executed by specialized research agents. Each agent will focus on a specific aspect of the analysis to provide a complete picture.

AVAILABLE TOOLS:
{tool_prompt}
</context>

<task>
Your task is to generate report for: {task_description}
</task>

<strict_tool_usage>
You MUST follow these rules for tool usage:
1. Use EXACTLY ONE tool per task - no exceptions
2. Select the single most appropriate tool for the specific business question
3. Make only ONE call to this tool
4. If the tool fails or returns insufficient data, you may retry ONCE with adjusted parameters
5. Do NOT call multiple tools simultaneously or in sequence
6. If one tool doesn't provide sufficient information, clearly state this in your report
</strict_tool_usage>

<reasoning_steps>
When analyzing:
1. First, carefully analyze the available tools and their specific capabilities
2. Second, identify the single most appropriate tool that best addresses the business question
3. Third, make ONE call to this selected tool with optimal parameters
4. Fourth, after receiving tool output, reformat it according to the required output format
5. Fifth, ensure your final response follows the exact output format specified
</reasoning_steps>

<tool_output_handling>
When you receive output from the tool:
1. Extract the key information from the tool's response
2. Reformat this information according to the required output format
3. NEVER return raw tool output without reformatting it
4. Ensure your final response follows the exact structure specified in the output format
5. Include all relevant information from the tool in your reformatted response
</tool_output_handling>

<error_handling>
If the selected tool fails or cannot be used effectively:
1. Try ONE more time with adjusted parameters if appropriate
2. If the tool still fails or is not applicable, respond with exactly "NULL"
3. NEVER make up information or provide analysis without tool support
4. NEVER ask for human confirmation or intervention
5. Always ensure your final response follows the required output format
</error_handling>

<output_format>
Regardless of which tool you use, your FINAL response must be a markdown-formatted report with exactly these sections:
## Business question
[Restate the business question here]

## Conclusion
[Provide a clear, direct answer to the business question based solely on the tool output]

## Reasoning
[Explain the reasoning behind your conclusion, including key data points from the tool output]

## Tool Optimization
[Suggest ways to improve tool usage or parameters for better results]

IMPORTANT: Do not include any other sections or headers. Do not include the raw tool output. Format your response exactly as shown above.
</output_format>""",
    )


    human_message = HumanMessage(content=f"Analyze this business question: {task_description} and provide a markdown-formatted report.")

    try:
        response = await supervisor.ainvoke({"messages": [human_message]})
        print_debug("Supervisor response received", "supervisor")
    except Exception as e:
        print_debug(f"Error in supervisor: {str(e)}", "Error")
        await research_repo.update_workflow_stage(state["task_id"], "supervisor", "error")
        state["results"].append("NULL")
        return state


    # Process all messages to find tool messages
    tool_outputs = []
    tool_summary = []
    tool_logs = []
    tool_summary.append(f"Processing {len(response['messages'])} messages")

    for i, msg in enumerate(response["messages"]):
        # Check if it's a tool message
        if hasattr(msg, "content") and hasattr(msg, "name") and hasattr(msg, "tool_call_id"):
            status = getattr(msg, "status", None)

            # Add to tool logs regardless of status
            tool_logs.append({
                "tool_name": msg.name,
                "status": status if status else "unknown",
                "tool_output_content": msg.content
            })

            if status != "error":
                tool_summary.append(f"✓ Found tool: {msg.name} (status: {status})")
                tool_outputs.append({
                    "tool": msg.name,
                    "content": msg.content
                })
            else:
                tool_summary.append(f"✗ Error in tool: {msg.name}")

    # Initialize tools list if it doesn't exist
    if "tools" not in state:
        state["tools"] = []

    # Add current iteration's tool logs to the tools list
    if tool_logs:
        state["tools"].append(tool_logs)

    # If we have tool outputs, format them through the LLM
    if tool_outputs:
        tool_summary.append(f"Found {len(tool_outputs)} valid tool outputs")

        # Combine all tool outputs
        combined_tool_output = ""
        for tool_output in tool_outputs:
            combined_tool_output += f"\n\n--- {tool_output['tool']} OUTPUT ---\n{tool_output['content']}"

        # Create a formatting message to send to the LLM
        format_message = HumanMessage(content=f"""I have received the following output from tools analyzing this business question: "{task_description}"

{combined_tool_output}

Please reformat this information into a concise, well-structured report with exactly these sections:
## Business question
## Conclusion
## Reasoning
## Tool Optimization

For the conclusion:
1. Provide a direct, clear answer to the specific business question
2. Include relevant specific data points from the tool outputs
3. When mentioning prices, ALWAYS include the corresponding date in yyyy-mm-dd format (e.g., "price of $150.89 on 2025-05-09")
4. Do NOT make up any values that aren't present in the tool outputs
5. If specific values aren't available, focus on general trends and directions

For the reasoning:
1. Include the most relevant data points that support your conclusion
2. Organize information logically with the strongest evidence first
3. Include both quantitative and qualitative factors if available
4. When referencing historical data or forecasts, always include specific dates in yyyy-mm-dd format
5. Use the name tools, for eg. Using [Tool Name], ...

For the tool optimization:
1. Analyze which tools were most effective and which could have provided better results
2. Suggest specific parameter adjustments that could improve tool outputs (e.g., different time ranges, metrics, or search terms)
3. Identify any missing data points that could be obtained with different tool configurations
4. Recommend additional tools that could complement the current analysis
5. Keep suggestions concise, practical and directly related to the business question
""")

        # Get a formatted response from the LLM
        try:
            tool_summary.append("Formatting tool outputs with LLM")
            format_response = await model.ainvoke([format_message])
            tool_summary.append("Successfully formatted response")
            state["results"].append(format_response.content)
        except Exception as e:
            tool_summary.append(f"Error formatting through LLM: {str(e)}")
            await research_repo.update_workflow_stage(state["task_id"], "supervisor", "failed")
            state["results"].append("NULL")
    else:
        tool_summary.append("No valid tool outputs found")
        await research_repo.update_workflow_stage(state["task_id"], "supervisor", "failed")
        state["results"].append("NULL")

    # Display a single summary of the tool processing
    print_step("\n".join(tool_summary), "Tool Processing Summary", "deep_sky_blue1", verbose=True)

    await research_repo.update_workflow_stage(state["task_id"], "supervisor", "completed")

    return state

async def collect_reports(state: DeepSearchState):
    """Collects all reports and synthesizes them into a final report"""
    await research_repo.update_workflow_stage(state["task_id"], "collect_reports", "in_progress")
    director_id = state["director_id"]
    last_tasks = state["tasks"][-1]
    last_results = state["results"][-1]

    summarized_tasks = state.get("summarized_tasks", "No tasks yet")
    summarized_results = state.get("summarized_results", "No results yet")
    operations = state.get("operations", 0)
    max_operations = state.get("max_operations", MAX_OPERATIONS)

    # Get target exploration/exploitation ratio
    target_ratio = TARGET_EXPLORE_EXPLOIT_RATIO
    target_explore_percentage = int((target_ratio.real / (target_ratio.real + target_ratio.imag)) * 100)
    target_exploit_percentage = 100 - target_explore_percentage

    # Initialize structured_report if it doesn't exist
    if "structured_report" not in state:
        state["structured_report"] = None

    print_step(f"Processing Iteration #{operations+1}/{max_operations}", "Collecting Reports", "medium_purple", verbose=True)

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "collect_reports")

    # System message for tasks summarization
    tasks_system_message = SystemMessage(content=f"""<role>
You are a financial research task manager responsible for tracking all research questions and avenues of investigation.
</role>

<task>
Create a comprehensive summary of all research tasks, maintaining a complete record of the research journey while evaluating task effectiveness and balancing exploration vs. exploitation.
</task>

<context>
You are summarizing research tasks for iteration #{operations+1} of {max_operations}. This summary will be used to track the research path, evaluate task effectiveness, and inform future directions.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete
</context>

<summarization_principles>
1. COMPLETENESS: Maintain a record of all tasks in chronological order
2. ORGANIZATION: Group similar tasks together when possible
3. CLARITY: Ensure all specific metrics, dates, and parameters are preserved
4. FORMATTING: Use consistent bullet points for readability
5. EVALUATION: Assess each task's effectiveness based on the insights it generated
6. BALANCE: Track the balance between exploration (NEW ROUTE) and exploitation (DEEP DIVE)
</summarization_principles>

<task_evaluation_criteria>
For each completed task, evaluate its effectiveness based on available information:
1. RELEVANCE: How directly it addresses the core business question
2. INSIGHT QUALITY: The value and uniqueness of information it provided (based on the task description and summarized results)
3. IMPACT: How significantly it influenced the overall research direction
4. TOOL UTILIZATION: How effectively it leveraged the selected tool (based on tool name and status)
5. STRATEGIC VALUE: How well the task contributes to achieving the target exploration/exploitation ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%) by the end of all {max_operations} iterations

Note: Your evaluation should be based on the summarized information available in the current task summary. For the most recent task, you have access to the latest result summary and tool usage, but for older tasks, rely on your existing summary and the task descriptions.
</task_evaluation_criteria>

<output_format>
Structure your summary with:
- **Updated Summary**:
  - **Task Updates**: List all tasks with their specific parameters and effectiveness ratings
    - **Task #N**: *Task description* [Effectiveness: ⭐⭐⭐⭐⭐/⭐⭐⭐/⭐]
      - **Parameters**: List all relevant parameters
      - **Tool Used**: Tool name and whether it was 🔍 DEEP DIVE or 🧭 NEW ROUTE
      - **Key Insights**: Brief mention of most valuable findings (if completed)
      - **Status**: Current status (🔄 Open/✅ Completed)
  - **Research Balance**: Current ratio of exploration vs. exploitation tasks (e.g., 🧭 40% / 🔍 60%)
  - **Status**: Overall status of the research journey

Use visual indicators to quickly convey status and quality:
- Task effectiveness: ⭐⭐⭐⭐⭐ (Very High), ⭐⭐⭐⭐ (High), ⭐⭐⭐ (Medium), ⭐⭐ (Low), ⭐ (Very Low)
- Task type: 🔍 for exploitation (DEEP DIVE), 🧭 for exploration (NEW ROUTE)
- Status: 🔄 for in-progress tasks, ✅ for completed tasks, ⚠️ for tasks with issues

Use bullet points for clarity and organization. When mentioning dates, ALWAYS use yyyy-mm-dd format (e.g., 2025-05-09) for consistency and proper timeline reference.
</output_format>""")

    # System message for results summarization
    results_system_message = SystemMessage(content=f"""<role>
You are a financial research archivist responsible for maintaining a comprehensive historical record of investment research.
</role>

<task>
Create a balanced summary that preserves historical context while integrating new information. Your summary must maintain a complete record of all key findings across all reports.
</task>

<context>
You are summarizing research results for iteration #{operations+1}. This summary will be used for memory retrieval and to inform future research directions.
</context>

<summarization_principles>
1. HISTORICAL PRESERVATION: Maintain 70-80% of historical information from previous reports
2. NEW INFORMATION: Add 20-30% new information from the current report
3. CHRONOLOGICAL AWARENESS: Clearly indicate which findings came from which iteration number
4. DEDUPLICATION: Remove truly redundant information but preserve similar findings with different nuances
5. CROSS-REFERENCING: Highlight connections between findings across different reports
6. COMPLETENESS: Ensure all key data points are preserved, especially:
   - Ticker symbols
   - Current and forecasted prices
   - Dates of price data and forecasts
   - Recommendations (BUY/SELL/HOLD)
   - Confidence levels
</summarization_principles>

<output_format>
Structure your summary with these sections:
1. **Deduplicated Summary (Updated with Iteration {operations+1}):**
2. **Business Question** (from current report)
3. **Conclusion** (from current report)
4. **Reasoning** (from current report)
5. **Tool Optimization** (from all reports)
6. **Missing Data** (from all reports)
7. **Recommendations** (from all reports)
8. **All Reports Summary** (key findings across all reports)

Use bullet points for clarity and organization. When mentioning dates, ALWAYS use yyyy-mm-dd format (e.g., 2025-05-09) for consistency and proper timeline reference.
</output_format>""")

    try:
        tasks_message_content = f"""Update the tasks summary by adding the latest research task while maintaining a comprehensive record and evaluating task effectiveness.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete
- Remaining iterations: {max_operations - operations}

CURRENT TASKS SUMMARY:
{summarized_tasks}

NEW TASK:
{last_tasks}

PREVIOUS RESULTS:
{["Result #" + str(len(state.get("results", [])) - 1) + " (Latest): " + (state.get("results", ["No results yet"])[-2][:150] + "..." if len(state.get("results", [""])[:-1]) > 0 and len(state.get("results", [""])[-2]) > 150 else state.get("results", ["No previous results yet"])[-2] if len(state.get("results", [])) > 1 else "No previous results yet")] if len(state.get("results", [])) > 1 else ["No previous results yet"]}

TOOL USAGE:
{[{"tool_name": tool.get("tool_name", "unknown"), "status": tool.get("status", "unknown")} for tool in state.get("tools", [])[-1]] if "tools" in state and len(state.get("tools", [])) > 0 else "No tool usage data available"}

INSTRUCTIONS:
1. Maintain all historical tasks in chronological order
2. Add the new task with appropriate formatting
3. Evaluate each task's effectiveness using star ratings (⭐⭐⭐⭐⭐ to ⭐) based on:
   - Relevance to the business question
   - Quality of insights generated
   - Impact on research direction
   - Effective tool utilization
4. Identify whether each task was exploration (🧭 NEW ROUTE) or exploitation (🔍 DEEP DIVE)
5. Calculate the current exploration/exploitation ratio with icons (e.g., 🧭 40% / 🔍 60%) and compare it to the target ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%)
6. Mark task status with appropriate icons (🔄 Open, ✅ Completed, ⚠️ Issues)
7. Group similar tasks together when possible
8. Ensure all specific metrics, dates, and parameters are preserved
9. Format consistently with bullet points for readability

The tasks summary should provide a complete picture of the research journey with effectiveness evaluation and exploration/exploitation balance. Consider how the current balance compares to the target ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%) and how many iterations remain ({max_operations - operations}) to achieve this target. For older tasks, rely on your existing summary; for the newest task, use the latest result and tool usage information provided.
"""
        response_tasks = await model.ainvoke([tasks_system_message, HumanMessage(content=tasks_message_content)])
        if "</think>" in response_tasks.content:
            response_tasks.content = response_tasks.content.split("</think>", 1)[1].strip()
        state["summarized_tasks"] = response_tasks.content
    except Exception as e:
        print_debug(f"Error summarizing tasks: {str(e)}", "collect_reports")
        await research_repo.update_workflow_stage(state["task_id"], "collect_reports", "failed")
    try:
        results_message_content = f"""Update the research summary by integrating new findings from Iteration #{operations+1} while preserving historical context.

CURRENT SUMMARY:
{summarized_results}

NEW FINDINGS FROM ITERATION #{operations+1}:
{last_results}

INSTRUCTIONS:
1. Maintain 70-80% of the historical information from previous iterations
2. Add 20-30% of the most important new information from Iteration #{operations+1}
3. Clearly label which findings came from which iteration number
4. Remove truly redundant information but preserve similar findings with different nuances
5. Highlight connections between findings across different iterations
6. Ensure all key metrics, dates, and numerical values are preserved
7. Update the "All Reports Summary" section to reflect the complete research journey
8. PRESERVE ALL CRITICAL DATA POINTS including:
   - Ticker symbols
   - Current and forecasted prices with dates
   - Recommendations (BUY/SELL/HOLD)
   - Confidence levels

Remember to follow the structured output format with all required sections.
"""
        response_results = await model.ainvoke([results_system_message, HumanMessage(content=results_message_content)])
        if "</think>" in response_results.content:
            response_results.content = response_results.content.split("</think>", 1)[1].strip()

        # Store the summarized results
        state["summarized_results"] = response_results.content
    except Exception as e:
        print_debug(f"Error summarizing results: {str(e)}", "collect_reports")
        await research_repo.update_workflow_stage(state["task_id"], "collect_reports", "failed")

    state["operations"] = operations + 1
    state["max_operations"] = max_operations
    await research_repo.update_workflow_stage(state["task_id"], "collect_reports", "completed")

    if operations >= max_operations:
        state.update({"next": "summary"})
    else:
        state.update({"next": "create_plan"})

    return state



async def create_plan(state: DeepSearchState):
    """Creates a research plan with tasks"""
    await research_repo.update_workflow_stage(state["task_id"], "create_plan", "in_progress")
    print_debug("Creating research plan", "create_plan")
    director_id = state["director_id"]
    summarized_tasks = state.get("summarized_tasks", "No tasks yet")
    summarized_results = state.get("summarized_results", "No results yet")
    last_three_tasks = state["tasks"][-3:]
    print_step(summarized_tasks, "Summarized Tasks",border_style="random", verbose=True)
    print_step(summarized_results, "Summarized Results", border_style="random", verbose=True)

    tool_prompt = await registry.generate_system_message_async()

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "create_plan")
    operations = state.get("operations", 0)
    max_operations = state.get("max_operations", MAX_OPERATIONS)
    target_ratio = TARGET_EXPLORE_EXPLOIT_RATIO

    # Calculate exploration percentage from target ratio
    target_explore_percentage = int((target_ratio.real / (target_ratio.real + target_ratio.imag)) * 100)
    target_exploit_percentage = 100 - target_explore_percentage

    system_message = SystemMessage(content=f"""<role>
You are a senior hedge fund research analyst with 15+ years of experience creating precise, focused business questions for investment analysis. You excel at strategic research planning, balancing exploration of new avenues with exploitation of promising leads to maximize investment insights.
</role>

<context>
The business question requires targeted research that will be executed by specialized research agents. Each question should focus on a single, specific aspect of the analysis.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete

AVAILABLE TOOLS:
{tool_prompt}

LAST THREE TASKS:
{"\n".join(last_three_tasks)}
</context>

<task>
Create one highly focused business question that either explores a new avenue of investigation OR deepens understanding of a promising insight from previous research, strategically balancing exploration and exploitation based on:
1. Current progress through the research timeline ({int((operations/max_operations)*100)}% complete)
2. Target exploration/exploitation ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%)
3. Current task effectiveness patterns
</task>

<research_strategy>
Make a strategic choice between two approaches based on the current research state:

OPTION 1: DEEPEN AN EXISTING BRANCH (🔍 EXPLOITATION)
- Follow a promising lead from a previous high-effectiveness task (⭐⭐⭐⭐+)
- Drill deeper into a specific metric or finding that showed potential
- Use the keyword "🔍 DEEP DIVE" at the beginning of your question
- Prioritize this when:
  - Previous exploitation tasks yielded high-value insights
  - You're in later research stages (>50% of total iterations)
  - Current exploration percentage exceeds the target ({target_explore_percentage}%)

OR

OPTION 2: CREATE A NEW AVENUE (🧭 EXPLORATION)
- Explore a completely different aspect not yet investigated
- Use tools or analysis methods not yet applied to this company
- Use the keyword "🧭 NEW ROUTE" at the beginning of your question
- Prioritize this when:
  - Previous exploration revealed promising new directions
  - Exploitation tasks are showing diminishing returns (decreasing ⭐ ratings)
  - Current exploration percentage is below the target ({target_explore_percentage}%)
  - You're in early research stages (<50% of total iterations)
</research_strategy>

<adaptive_balancing>
Consider these factors when deciding between exploration and exploitation:
1. RESEARCH PROGRESS: Current iteration ({operations+1}) out of total iterations ({max_operations})
   - Early stage (0-33%): Favor exploration to discover promising avenues
   - Middle stage (34-66%): Balance exploration and exploitation based on target ratio
   - Late stage (67-100%): Favor exploitation of the most promising avenues

2. TARGET RATIO: Aim for 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}% by the end of all iterations
   - If current exploration % < {target_explore_percentage}%: Favor exploration
   - If current exploration % > {target_explore_percentage}%: Favor exploitation
   - As you approach the final iterations, prioritize meeting the target ratio

3. EFFECTIVENESS PATTERNS: Analyze which approach has been more effective
   - If exploration tasks average higher star ratings: Favor exploration
   - If exploitation tasks average higher star ratings: Favor exploitation
   - If a specific avenue shows diminishing returns: Switch approaches

4. REMAINING ITERATIONS: Consider how many iterations remain ({max_operations - operations})
   - With many iterations left: You can afford to explore more
   - With few iterations left: Focus on exploiting the most promising leads
</adaptive_balancing>

<output_requirements>
1. Format: "Using [Tool Name] 🔍 DEEP DIVE: [specific question]?" or "Using [Tool Name] 🧭 NEW ROUTE: [specific question]?"
2. Include informative context in [brackets] after the question
3. Context must explain:
   - Which specific finding you're following up on (if 🔍 deepening)
   - What key information the supervisor needs to understand this task
   - Why you chose 🧭 exploration or 🔍 exploitation based on current research state
4. Maximum length: 25 words for the question + 25 words for the context
5. Must include:
   - Exact ticker symbol
   - Specific metrics/indicators with parameters
   - Precise time periods
   - Numerical values from previous results (if applicable)
</output_requirements>

<examples>
<example>
Previous Tasks Summary:
- **Task #1**: Using FinancialNewsTool 🧭 NEW ROUTE: What recent news might impact AAPL stock price in the next week? [Effectiveness: ⭐⭐⭐]
- **Task #2**: Using StockPriceTool 🧭 NEW ROUTE: What is AAPL's current price and 50-day moving average? [Effectiveness: ⭐⭐⭐⭐⭐]

Good Output (Exploitation):
Using TechnicalAnalysisTool 🔍 DEEP DIVE: How has AAPL's price performed relative to its 50-day MA during market corrections? [Following up on Task #2's finding that AAPL is trading 5% above its 50-day MA; exploitation optimal at iteration 3/10 with current 67% exploration]

Good Output (Exploration):
Using CompetitorAnalysisTool 🧭 NEW ROUTE: How does AAPL's P/E ratio compare to MSFT, GOOG in the past 30 days? [Exploring valuation metrics not yet investigated; exploration needed at iteration 3/10 to reach target 90% exploration ratio]
</example>

<example>
Previous Tasks Summary:
- **Task #1**: Using EconomicIndicatorTool 🧭 NEW ROUTE: How do rising interest rates correlate with JPM stock performance? [Effectiveness: ⭐⭐⭐⭐]
- **Task #2**: Using FinancialStatementTool 🔍 DEEP DIVE: What is JPM's net interest margin trend over the past 4 quarters? [Effectiveness: ⭐⭐]

Good Output (Exploitation):
Using EconomicIndicatorTool 🔍 DEEP DIVE: How did JPM perform during the last 3 Fed rate hike cycles from 2015-2018? [Following Task #1's finding of 0.72 correlation between rates and JPM; exploitation needed at iteration 3/5 with only 40% exploitation]

Good Output (Exploration):
Using SentimentAnalysisTool 🧭 NEW ROUTE: What is the current analyst sentiment on JPM compared to other major banks? [Exploring sentiment dimension not yet investigated; exploration optimal at iteration 2/10 with current 50% exploration vs. target 70%]
</example>
</examples>

<error_handling>
If you encounter challenges:
1. If previous tasks show no clear high-value leads, create a question that explores a fundamentally different aspect of the company
2. If the business question is vague, focus on the most financially relevant metrics for the ticker
3. If previous tasks have conflicting effectiveness ratings, prioritize the most recent high-rated task
4. If no tool seems perfectly suited, select the tool that can provide the closest relevant information
5. If you're unsure about specific parameters, use industry-standard defaults (e.g., 14-day RSI, 50/200-day MAs)
</error_handling>

<output_format>
Using [Tool Name] 🔍 DEEP DIVE: [concise question with specific metrics for TICKER]? [Brief context explaining which finding is being followed up on and why exploitation is optimal at iteration {operations+1}/{max_operations} with current exploration/exploitation balance]

OR

Using [Tool Name] 🧭 NEW ROUTE: [concise question with specific metrics for TICKER]? [Brief context explaining why this avenue is valuable and why exploration is optimal at iteration {operations+1}/{max_operations} with current exploration/exploitation balance]
</output_format>""")
    human_message = HumanMessage(content=f"""Create a focused business question based on previous research, task effectiveness, and strategic exploration/exploitation balance.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete
- Remaining iterations: {max_operations - operations}

Previous tasks summary:
{summarized_tasks}

Previous results summary:
{summarized_results}

INSTRUCTIONS:
1. Analyze the current exploration/exploitation ratio (🧭/🔍) in the task summary compared to the target ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%)
   - Calculate the exact current ratio from the task summary
   - Determine how far the current ratio is from the target ratio
   - Identify which approach (exploration or exploitation) needs to be prioritized to reach the target

2. Consider your position in the research timeline:
   - Early stage (0-33% complete): Favor exploration to discover promising avenues
   - Middle stage (34-66% complete): Balance according to target ratio and effectiveness
   - Late stage (67-100% complete): Favor exploitation of the most promising avenues

3. Evaluate task effectiveness patterns:
   - Calculate the average star rating for exploration tasks vs. exploitation tasks
   - Identify which approach has yielded the highest-rated tasks (⭐⭐⭐⭐+)
   - Check if any approach shows diminishing returns (decreasing star ratings over time)
   - Identify the most promising leads from high-rated tasks (⭐⭐⭐⭐+)

4. Make a strategic decision based on:
   - Current research stage ({int((operations/max_operations)*100)}% complete)
   - Gap between current and target exploration/exploitation ratio
   - Effectiveness patterns from previous tasks
   - Number of remaining iterations ({max_operations - operations})
   - Presence of high-potential leads from recent tasks

5. Format your response using the appropriate keyword and icon:
   - For exploitation: "Using [Tool Name] 🔍 DEEP DIVE: [specific question]?"
   - For exploration: "Using [Tool Name] 🧭 NEW ROUTE: [specific question]?"

6. Add brief context in [brackets] after the question that explains:
   - Which specific finding you're following up on (if DEEP DIVE)
   - Why this avenue is valuable to explore (if NEW ROUTE)
   - Why you chose exploration or exploitation at this point in the research
   - Any key information needed to understand this task's purpose

7. Ensure your question includes:
   - Exact ticker symbol
   - Specific metrics/indicators with precise parameters
   - Exact time periods (e.g., "past 30 days" not "recent")
   - Numerical values from previous results when applicable
   - Maximum 25 words for the question + 25 words for the context

8. Select the most appropriate tool based on:
   - The specific information needed for your question
   - The tool's capabilities as described in the available tools section
   - Previous successful tool usage patterns

9. Make the context informative for a supervisor who hasn't seen previous research by:
   - Referencing specific task numbers and their key findings
   - Including exact metrics and values from previous results
   - Explaining your strategic reasoning clearly and concisely""")
    try:
        response = await model.ainvoke([system_message, human_message])
    except Exception as e:
        print_debug(f"Error creating plan: {str(e)}", "create_plan")
        await research_repo.update_workflow_stage(state["task_id"], "create_plan", "failed")
        return state

    if "</think>" in response.content:
        response.content = response.content.split("</think>", 1)[1].strip()
    print_step(response.content, "Created Plan", "random", verbose=True)

    state["tasks"].append(response.content)
    await research_repo.update_workflow_stage(state["task_id"], "create_plan", "completed")
    return state

async def summary(state: DeepSearchState):
    """Summarizes the final report"""
    await research_repo.update_workflow_stage(state["task_id"], "summary", "in_progress")
    print_debug("Summarizing final report", "summary")
    director_id = state["director_id"]
    business_question = state["business_question"]
    summarized_tasks = state.get("summarized_tasks", "No tasks yet")
    summarized_results = state.get("summarized_results", "No results yet")

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "summary")
    system_message = SystemMessage(content=f"""<role>
You are a financial Director providing authoritative investment recommendations based on comprehensive market analysis.
</role>

<context>
The original business question was: {business_question}
You have access to summarized research tasks and results from a comprehensive analysis.
</context>

<task>
Generate a final report that synthesizes all research findings into a clear, actionable recommendation.
</task>

<reasoning_steps>
When creating the final report:
1. First, carefully review all summarized tasks and results
2. Second, identify the key insights and data points relevant to the business question
3. Third, formulate a clear conclusion that directly answers the business question
4. Fourth, provide reasoning that supports your conclusion with specific evidence
</reasoning_steps>

<output_format>
Provide a clean, well-formatted report with these exact sections:
1. Business question
2. Conclusion - MUST follow this exact format:
   "As a Director, I predict [TICKER] will [increase/decrease] from $[CURRENT_PRICE] to $[PREDICTED_PRICE] ([PERCENTAGE]% [up/down]) by [TARGET_DATE]. Confidence: [CONFIDENCE_PERCENTAGE]%."
3. Reasoning

IMPORTANT GUIDELINES:
1. Extract the ticker, current price, predicted price, percentage change, target date, and confidence level ONLY from the research results
2. Do NOT invent or estimate any values that aren't explicitly mentioned in the results
3. If specific values are missing, clearly indicate this in your conclusion with "[VALUE NOT AVAILABLE]"
4. For confidence percentage, use a value that reflects the consistency and strength of evidence in the results
5. If the research results contain conflicting information, acknowledge this and base your conclusion on the most recent or reliable data
6. DO NOT wrap your response in markdown code blocks or any other formatting tags
7. Present the report as plain text with clear section headers
</output_format>""")
    human_message = HumanMessage(content=f"""Generate a final report for the business question: {business_question}

RESEARCH TASKS:
{summarized_tasks}

RESEARCH RESULTS:
{summarized_results}

Remember to format your conclusion exactly as:
"As a Director, I predict [TICKER] will [increase/decrease] from $[CURRENT_PRICE] to $[PREDICTED_PRICE] ([PERCENTAGE]% [up/down]) by [TARGET_DATE]. Confidence: [CONFIDENCE_PERCENTAGE]%."

IMPORTANT:
1. Extract all values (ticker, prices, percentages, dates) ONLY from the research results
2. Do NOT make up or estimate any values that aren't explicitly mentioned in the results
3. If specific values are missing, use ranges or approximations CLEARLY marked as estimates based on available data
4. If critical values are completely absent, acknowledge this limitation in your conclusion
5. For the confidence percentage, base it on the strength and consistency of evidence in the results
6. DO NOT wrap your response in markdown code blocks (```markdown) or any other formatting tags
7. Present the report as plain text with clear section headers""")

    try:
        response = await model.ainvoke([system_message, human_message])
        if "</think>" in response.content:
            response.content = response.content.split("</think>", 1)[1].strip()

        state["final_report"] = response.content
    except Exception as e:
        print_debug(f"Error summarizing final report: {str(e)}", "summary")
        state["final_report"] = "# Error generating final report\n\nDue to an error, the final report could not be generated. Please check the logs for more details."
        await research_repo.update_workflow_stage(state["task_id"], "summary", "failed")
        return state

    print_step(response.content, "Final Report", "gold1", verbose=True)

    await research_repo.update_workflow_stage(state["task_id"], "summary", "completed")
    return state

async def schedule_revisit(state: DeepSearchState):
    """Schedule a revisit for the report in the future"""
    print_debug("Entering schedule revisit", "Schedule Revisit")
    await research_repo.update_workflow_stage(state["task_id"], "schedule_revisit", "in_progress")
    director_id = state["director_id"]
    business_question = state.get("business_question", "")
    final_report = state.get("final_report", "")

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "schedule_revisit")

    structured_model = model.with_structured_output(StructuredReport)
    try:
        # Get the schema directly from the model
        schema_json = StructuredReport.model_json_schema()

        structured_report = await structured_model.ainvoke([
            SystemMessage(content=f"""Extract the ticker, forecasted price, forecasted horizon, last closing price, closing price date, recommendation, and confidence level from the final report.

    IMPORTANT:
    1. For date fields (closing_price_date and forecasted_horizon), ALWAYS use the yyyy-mm-dd format (e.g., 2025-05-09).
    2. Your response must match exactly this JSON schema:

    {schema_json}"""),
            HumanMessage(content=f"""Please extract structured information from this final report:

{final_report}""")
        ])
        state["structured_report"] = structured_report

    except Exception as e:
        print_debug(f"Error scheduling revisit: {str(e)}", "schedule_revisit")
        await research_repo.update_workflow_stage(state["task_id"], "schedule_revisit", "failed")
        return state



    # Schedule a forecast revisit for the day after the forecasted_horizon date
    if structured_report:
        try:
            forecast_data = {
                "ticker": structured_report.ticker,
                "forecasted_price": structured_report.forecasted_price,
                "forecasted_horizon": structured_report.forecasted_horizon.isoformat(),
                "last_closing_price": structured_report.last_closing_price,
                "closing_price_date": structured_report.closing_price_date.isoformat(),
                "recommendation": structured_report.recommendation,
                "confidence_level": structured_report.confidence_level
            }

            revisit_id = await research_repo.schedule_forecast_revisit(state["task_id"], forecast_data)
            print_debug(f"Scheduled forecast revisit with ID: {revisit_id}", "Schedule Revisit")
        except Exception as e:
            print_debug(f"Error scheduling forecast revisit: {str(e)}", "Schedule Revisit")
            await research_repo.update_workflow_stage(state["task_id"], "schedule_revisit", "failed")
            return state

    # Complete the workflow stages
    print_step(f"Workflow completed for question: {business_question}", "Schedule Revisit")
    await research_repo.update_workflow_stage(state["task_id"], "schedule_revisit", "completed")
    await research_repo.update_workflow_stage(state["task_id"], "END", "in_progress")
    await research_repo.update_workflow_stage(state["task_id"], "END", "completed")
    return state


def create_graph():
    """Creates the deep research graph"""
    builder = StateGraph(DeepSearchState)


    builder.add_node("search_scope", search_node)
    builder.add_node("supervisor", supervisor)
    builder.add_node("collect_reports", collect_reports)
    builder.add_node("create_plan", create_plan)
    builder.add_node("summary", summary)
    builder.add_node("schedule_revisit", schedule_revisit)

    builder.add_edge(START, "search_scope")
    builder.add_edge("search_scope", "supervisor")
    builder.add_edge("supervisor", "collect_reports")
    builder.add_conditional_edges(
        "collect_reports",
        lambda state: state["next"],
        {
            "summary": "summary",
            "create_plan": "create_plan"
        }
    )
    builder.add_edge("create_plan", "supervisor")
    builder.add_edge("summary", "schedule_revisit")
    builder.add_edge("schedule_revisit", END)


    return builder.compile()

graph = create_graph()

__all__ = ['graph']

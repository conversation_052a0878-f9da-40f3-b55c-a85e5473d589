from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    SystemMessage,
    HumanMessage,
    BaseMessage,
    AIMessage
)
from typing import Dict, Any, Optional, List, Sequence, TypedDict, Set, Union, Literal, Tuple
from software.ai.llm.llm_connect import get_llm_connect
from pydantic import BaseModel, Field
from datetime import datetime
import json
import re
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from software.db.research_director_repository import ResearchDirectorRepository
research_director_repo = ResearchDirectorRepository()


class AgentProfile(BaseModel):
    """Schema for research director agent profile"""
    name: str = Field(
        ...,
        description="The name of the research director agent",
        examples=["Dr. <PERSON>", "<PERSON>", "<PERSON>"]
    )
    title: str = Field(
        ...,
        description="The professional title of the agent",
        examples=["Hedge Fund Manager & Market Analyst", "Senior Investment Strategist", "Chief Economist"]
    )
    experience_years: int = Field(
        ...,
        description="Years of professional experience in the field",
        ge=1,
        le=50,
        examples=[25, 15, 30]
    )
    expertise: List[str] = Field(
        ...,
        description="Areas of expertise or specialization",
        examples=[
            ["Value Investing", "Market Bubbles", "Contrarian Strategy"],
            ["Technical Analysis", "Emerging Markets", "Commodities"]
        ]
    )
    analysis_style: str = Field(
        ...,
        description="The agent's approach to market analysis",
        examples=[
            "Deeply analytical and contrarian approach. Examines market fundamentals with intense focus on data.",
            "Technical analysis focused with emphasis on chart patterns and market psychology."
        ]
    )
    background: str = Field(
        ...,
        description="Professional background and education",
        examples=[
            "MD from Vanderbilt University School of Medicine. Founded Scion Capital hedge fund.",
            "MBA from Harvard Business School with 15 years at Goldman Sachs."
        ]
    )
    personality: str = Field(
        ...,
        description="The agent's personality traits and communication style",
        examples=[
            "Intensely private and focused. Highly detail-oriented with exceptional pattern recognition abilities.",
            "Outgoing and collaborative. Communicates complex ideas in accessible language."
        ]
    )

class AgentBuilderState(TypedDict):
    """State definition for the agent builder workflow"""
    messages: Sequence[BaseMessage]
    user_request: str
    agent_profile: Optional[Union[AgentProfile, Dict[str, Any]]]
    result: Optional[Dict[str, Any]]

def create_agent_builder_graph() -> StateGraph:
    """Create the graph for the agent builder workflow."""
    workflow = StateGraph(AgentBuilderState)

    # Add nodes
    workflow.add_node("analyze_agent_profile", analyze_agent_profile)

    # Add edges
    workflow.add_edge(START, "analyze_agent_profile")
    workflow.add_edge("analyze_agent_profile", END)

    return workflow


async def analyze_agent_profile(state: AgentBuilderState) -> AgentBuilderState:
    """Analyze the agent profile from the business question."""
    user_request = state.get("user_request", "")

    #get last 3
    all_directors = await research_director_repo.list_directors()
    directors = all_directors[-3:] if len(all_directors) >= 3 else all_directors
    print_pretty(directors)

    # Create enhanced director profiles with more comprehensive information
    directors_profiles = []
    for director in directors:
        profile = f"""
DIRECTOR: {director['name']}
TITLE: {director['title']}
EXPERIENCE: {director['experience_years']} years
EXPERTISE: {', '.join(director['expertise'])}
ANALYSIS STYLE: {director['analysis_style']}
BACKGROUND: {director['background']}
PERSONALITY: {director['personality']}
"""
        directors_profiles.append(profile)

    directors_profiles = "\n".join(directors_profiles)

    print_pretty(directors_profiles)


    if not user_request:
        error_message = "No user request provided"
        print_debug(error_message, "Agent Builder: Input Error")
        state["result"] = {
            "error": "Missing required input",
            "details": error_message
        }
        return state

    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(AgentProfile)

        system_message = SystemMessage(content=f"""You are an AI expert specializing in creating exceptional hedge fund director agent profiles.
Your task is to analyze the user request and create a world-class agent profile that would be best equipped to answer it.

Create an extraordinary profile with the following characteristics:
1. Impressive educational background from elite institutions (Harvard, Stanford, MIT, Wharton, etc.)
2. Exceptional professional experience (20+ years) at prestigious firms (Goldman Sachs, JP Morgan, BlackRock, etc.)
3. Remarkable track record of successful investments and market predictions
4. Deep expertise in multiple relevant domains (at least 4-5 specialized areas)
5. Unique analytical approach that combines quantitative and qualitative methods
6. Published research, books, or notable media appearances
7. Distinctive personality traits that make them stand out in their field
8. Specific accomplishments that demonstrate their exceptional abilities (e.g., predicting market crashes, identifying unicorn startups early)

The profile should be highly impressive yet believable, with specific details that showcase their expertise.
Make sure the expertise areas are directly relevant to the business domain and question context.

IMPORTANT GUIDELINES:
1. Create a profile that is MORE impressive than the current profiles shown below
2. Focus on a SPECIFIC industry or sector expertise (e.g., biotech, renewable energy, fintech) rather than a wide range
3. Ensure the expertise is DIFFERENT from those in the current profiles - avoid overlap in specialization areas
4. The director should be a recognized authority in their specific domain with deep specialized knowledge

Current profiles:
{directors_profiles}

""")

        human_message = HumanMessage(content=f"""
Based on this business question, what would be an appropriate research director agent profile?

User Request: {user_request}

Please create a complete agent profile with name, title, experience years, expertise areas, analysis style,
professional background, and personality traits.
""")

        messages = [system_message, human_message]
        agent_profile = await structured_model.ainvoke(messages)

        # Print a summary of the profile
        profile_summary = f"""Agent Profile Analysis Results:
Name: {agent_profile.name}
Title: {agent_profile.title}
Experience: {agent_profile.experience_years} years
Expertise: {', '.join(agent_profile.expertise)}
Analysis Style: {agent_profile.analysis_style[:100]}...
Background: {agent_profile.background[:100]}...
Personality: {agent_profile.personality[:100]}..."""

        print_step(profile_summary, "Agent Builder: Profile Created", "cyan")

        # Convert the Pydantic model to a dictionary before storing it in the state
        # This ensures it can be properly serialized when returned from the graph
        state["agent_profile"] = agent_profile.model_dump()

        return state
    except Exception as e:
        error_message = f"Error analyzing agent profile: {str(e)}"

        state["result"] = {
            "error": "Failed to analyze agent profile",
            "details": error_message
        }
        return state


graph = create_agent_builder_graph().compile()

__all__ = ['graph', 'AgentProfile']
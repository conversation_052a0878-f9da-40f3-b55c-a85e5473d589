from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    SystemMessage,
    HumanMessage
)
from software.ai.graph.director_state import DirectorState, StructuredReport, print_step, print_debug, print_pretty
from software.ai.graph.input_memory_graph import graph as input_memory_graph
from software.ai.graph.output_memory_graph import graph as output_memory_graph
from software.ai.llm.llm_connect import get_llm_connect
from software.db.research_repository import ResearchRepository
research_repo = ResearchRepository()

async def get_structured_report(state: DirectorState) -> DirectorState:
    """Generate a structured FinalReport using the LLM model"""
    await research_repo.update_workflow_stage(state["task_id"], "delegate", "completed")
    await research_repo.update_workflow_stage(state["task_id"], "summarize_report", "in_progress")
    print_step("Entering get structured report", "Get Structured Report")
    business_question = state.get("business_question", "")

    # Extract each AnalysisReport and format as markdown
    analysis_reports = ""
    for report in state.get("completed_reports", []):
        # No tools section since successful_tools was removed from AnalysisReport
        tools_md = ""

        report_md = f"""
## Analysis Report ID: {report.analysis_report_id}
- **Business Question**: {report.business_question}
- **Business Answer**: {report.business_answer}
- **Target Column**: {report.target_column}
- **Confidence**: {report.confidence}%
- **Conclusion**: {report.conclusion}
- **Current Value**: {report.current_target_value}
- **Predicted Value**: {report.predicted_target_value}
- **Current Date**: {report.current_date}
- **Horizon Date**: {report.horizon_date}

### Observation
{report.observation}

### Arguments For
{report.argument_for_conclusion}

### Arguments Against
{report.argument_against_conclusion}

{tools_md}
"""
        analysis_reports += report_md
    print_step(analysis_reports, "Get Structured Report")
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "get_structured_report")
    structured_model = model.with_structured_output(StructuredReport)

    # Get the schema directly from the model
    schema_json = StructuredReport.model_json_schema()

    # Simplified prompt for structured FinalReport
    conclusion_prompt = f"""
    Fill StructuredReport JSON from BUSINESS QUESTION and ANALYST REPORTS. Discard unrealistic predictions beyond reasonable market ranges. Return only JSON matching StructuredReport model.

    IMPORTANT:
    1. For date fields (closing_price_date and forecasted_horizon), ALWAYS use the yyyy-mm-dd format (e.g., 2025-05-09).
    2. Your response must match exactly this JSON schema:

    {schema_json}
    """

    system_message = SystemMessage(content=conclusion_prompt)
    human_message = HumanMessage(content=f"BUSINESS QUESTION: {business_question}\n\nANALYST REPORTS:{analysis_reports}")
    response = await structured_model.ainvoke([system_message, human_message])

    print_debug(f"Structured Report: {response}", "Get Structured Report")

    return {"structured_report": response}

async def write_final_report(state: DirectorState) -> DirectorState:
    """Format the structured report into a readable markdown document"""
    print_step("Entering write final report", "Write Final Report")
    fr: StructuredReport = state.get("structured_report")
    # Build report sections
    plan_items = state["plan"].tasks
    analysts = [f"{a.name_analyst} ({a.title_analyst})" for a in state["analysts"].analysts]

    conclusion = (
        f"As a Director, I predict {fr.ticker} will "
        f"{'increase' if fr.forecasted_price > fr.last_closing_price else 'decrease'} "
        f"from ${fr.last_closing_price} to ${fr.forecasted_price} "
        f"({abs((fr.forecasted_price / fr.last_closing_price - 1) * 100):.2f}% "
        f"{'up' if fr.forecasted_price > fr.last_closing_price else 'down'}) "
        f"by {fr.forecasted_horizon}. Confidence: {fr.confidence_level}%."
    )

    final_md = f"""# {state.get('business_question')}

## Plan
- {'\n- '.join(plan_items)}

## Analysts
- {'\n- '.join(analysts)}

## Conclusion
{conclusion}
"""
    print_step(final_md, "Final Report")
    return {"final_report": final_md}

async def schedule_revisit(state: DirectorState) -> DirectorState:
    """Schedule a revisit for the report in the future"""
    print_step("Entering schedule revisit", "Schedule Revisit")
    business_question = state.get("business_question", "")
    structured_report = state.get("structured_report")

    # Schedule a forecast revisit for the day after the forecasted_horizon date
    if structured_report:
        try:
            forecast_data = {
                "ticker": structured_report.ticker,
                "forecasted_price": structured_report.forecasted_price,
                "forecasted_horizon": structured_report.forecasted_horizon.isoformat(),
                "last_closing_price": structured_report.last_closing_price,
                "closing_price_date": structured_report.closing_price_date.isoformat(),
                "recommendation": structured_report.recommendation,
                "confidence_level": structured_report.confidence_level
            }

            revisit_id = await research_repo.schedule_forecast_revisit(state["task_id"], forecast_data)
            print_step(f"Scheduled forecast revisit with ID: {revisit_id}", "Schedule Revisit")
        except Exception as e:
            print_step(f"Error scheduling forecast revisit: {str(e)}", "Schedule Revisit")

    # Complete the workflow stages
    print_step(f"Workflow completed for question: {business_question}", "Schedule Revisit")
    await research_repo.update_workflow_stage(state["task_id"], "summarize_report", "completed")
    await research_repo.update_workflow_stage(state["task_id"], "END", "in_progress")
    await research_repo.update_workflow_stage(state["task_id"], "END", "completed")
    return {"messages": "Revisit scheduled", "workflow_status": "completed"}

def create_summarize_report_subgraph() -> StateGraph:
    """Subgraph for report summarization"""
    subgraph = StateGraph(DirectorState)
    subgraph.add_node("output_memory", output_memory_graph)
    subgraph.add_node("get_structured_report", get_structured_report)
    subgraph.add_node("write_final_report", write_final_report)
    subgraph.add_node("schedule_revisit", schedule_revisit)
    subgraph.add_edge(START, "output_memory")
    subgraph.add_edge("output_memory", "get_structured_report")
    subgraph.add_edge("get_structured_report", "write_final_report")
    subgraph.add_edge("write_final_report", "schedule_revisit")
    subgraph.add_edge("schedule_revisit", END)
    return subgraph.compile()

graph = create_summarize_report_subgraph()

__all__ = ['graph']
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    SystemMessage,
    HumanMessage,
    BaseMessage,
    AIMessage,
    ToolMessage
)
from typing import Dict, Any, Optional, List, Sequence, TypedDict, Set, Union, Literal, Tuple
from software.ai.llm.llm_connect import get_llm_connect
from pydantic import BaseModel, Field
from datetime import datetime
import pandas as pd
import os
import importlib
import json
import re
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_react_agent
from langchain_community.tools import WikipediaQueryRun
from langchain_community.utilities import WikipediaAP<PERSON>Wrapper
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_core.tools import Tool
from langchain.agents.format_scratchpad.tools import format_to_tool_messages
from langchain.agents.output_parsers.tools import ToolsAgentOutputParser
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from api.database import get_sync_db
from data import registry
from dotenv import load_dotenv
import traceback
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from langchain import hub
import aiohttp

class DataLoaderDescription(BaseModel):
    """Schema for data loader description analysis"""
    class_name: str = Field(
        ...,
        description="The class name for the data loader that follows Python naming conventions",
        examples=["StockPriceLoader", "CompanyNewsLoader", "AlphaVantageDataLoader"]
    )
    description: str = Field(
        ...,
        description="A concise description of what the data loader does"
    )
    file_name: str = Field(
        ...,
        description="The file name for the data loader (should be the class name)",
        examples=["StockPriceLoader.py", "CompanyNewsLoader.py", "AlphaVantageDataLoader.py"]
    )
    purpose: str = Field(
        ...,
        description="The main purpose of this data loader"
    )
    data_format: str = Field(
        ...,
        description="The expected format of the data that will be loaded (CSV, JSON, API response, etc.)"
    )
    api_name: str = Field(
        ...,
        description="The name of the API or library used (e.g., 'yfinance', 'alpha_vantage', 'finnhub')",
        examples=["yfinance", "alpaca", "alpha_vantage", "finnhub"]
    )

class ApiKeyInfo(BaseModel):
    """Schema for API key information"""
    api_keys: List[Dict[str, str]] = Field(
        ...,
        description="List of API keys needed for this data loader",
        examples=[[
            {"name": "FINN_HUB", "usage": "Used to access the Finnhub API for financial data"},
            {"name": "ALPHA_VANTAGE", "usage": "Used to access the Alpha Vantage API for stock data"}
        ]]
    )

class ApiKeyRequirement(BaseModel):
    """Schema for determining API key requirements"""
    requires_api_key: bool = Field(
        ...,
        description="Whether the API requires authentication via API keys"
    )
    key_fields: List[Dict[str, str]] = Field(
        default_factory=list,
        description="List of required API key fields (empty if no API key is required)",
        examples=[[
            {"name": "API_KEY", "description": "The main API key for authentication"},
            {"name": "API_SECRET", "description": "The API secret for secure access"}
        ]]
    )
    reasoning: str = Field(
        ...,
        description="Explanation for why API keys are or are not required based on the documentation",
        examples=["The documentation explicitly states that an API key is required for authentication.",
                 "No API key requirement is mentioned in the documentation, and examples show direct usage without authentication."]
    )

class RelevantLoaders(BaseModel):
    """Schema for selecting relevant data loaders"""
    loaders: List[str] = Field(
        ...,
        description="Names of the most relevant data loaders to use as reference (up to 3)",
        examples=[["YahooFinanceLoader", "AlphaVantageLoader", "StockPrice10YrDaily"]]
    )
    reasoning: str = Field(
        ...,
        description="Why these loaders were selected as references"
    )

class DataLoaderCode(BaseModel):
    """Schema for generating data loader code"""
    code: str = Field(
        ...,
        description="Complete Python code for the data loader class that inherits from BaseDataLoader",
    )
    explanation: str = Field(
        ...,
        description="Explanation of how the code works and meets the requirements"
    )

class DataLoaderEnhancedDescription(BaseModel):
    """Schema for enhanced data loader description"""
    enhanced_description: str = Field(
        ...,
        description="A detailed, production-ready description of the data loader",
    )
    inputs: str = Field(
        ...,
        description="Description of the inputs required by the data loader"
    )
    outputs: str = Field(
        ...,
        description="Description of the outputs produced by the data loader"
    )
    requirements: str = Field(
        ...,
        description="Any dependencies or API requirements"
    )

class WebSearchResults(BaseModel):
    """Schema for web search results about the API"""
    api_documentation: str = Field(
        ...,
        description="The main documentation or usage info for the API"
    )
    installation: str = Field(
        ...,
        description="How to install the API library"
    )
    authentication: str = Field(
        ...,
        description="Information about authentication requirements"
    )
    key_methods: str = Field(
        ...,
        description="Key methods or functions relevant to loading data"
    )
    data_format: str = Field(
        ...,
        description="Information about the data format returned by the API"
    )

class ApiKeyDetection(BaseModel):
    """Schema for API key detection in generated code"""
    api_keys_found: List[Dict[str, str]] = Field(
        default_factory=list,
        description="List of API keys found in the code",
        examples=[[
            {"class_name": "StockPrice10YrDaily", "api_key_name": "FMP_API_KEY"},
            {"class_name": "AlphaVantageLoader", "api_key_name": "ALPHA_VANTAGE_API_KEY"}
        ]]
    )
    requires_api_keys: bool = Field(
        default=False,
        description="Whether the code requires API keys to function"
    )

class HumanFeedbackState(TypedDict):
    """Schema for state requiring human feedback"""
    human_input_required: bool
    input_type: str
    prompt: str
    resumed: bool
    api_key_fields: List[Dict[str, str]]
    api_keys_provided: List[Dict[str, str]]

class WorkflowState(TypedDict):
    """State definition for the data loader builder workflow"""
    messages: Sequence[BaseMessage]
    description: str
    api_keys: List[Dict[str, str]]
    available_datasources: List[Dict[str, Any]]
    selected_loaders: List[str]
    loader_code_samples: Dict[str, str]
    data_loader_info: Optional[Dict[str, Any]]
    generated_code: Optional[str]
    test_cases: Optional[str]
    result: Optional[Dict[str, Any]]
    api_docs: Optional[Dict[str, str]]
    human_feedback: Optional[HumanFeedbackState]

class PythonScript(BaseModel):
    """Schema for extracted Python code from documentation"""
    code: str = Field(
        ...,
        description="Clean Python script without comments or markdown tags"
    )

class TestCaseRequirements(BaseModel):
    """Schema for test case and base loader requirements"""
    base_requirements: List[str] = Field(
        ...,
        description="List of requirements extracted from the base loader class",
        examples=[
            "Must inherit from BaseDataLoader",
            "Must implement the load_historical_data method",
            "Must call self._ensure_timezone_naive(df) before returning the DataFrame"
        ]
    )
    test_requirements: List[str] = Field(
        ...,
        description="List of requirements extracted from test cases",
        examples=[
            "DataFrame must have columns: Open, High, Low, Close",
            "Index must be DatetimeIndex without timezone",
            "Must handle errors by returning empty DataFrame"
        ]
    )
    data_structure: Dict[str, str] = Field(
        default_factory=dict,
        description="Required data structure extracted from test cases",
        examples=[{
            "index_type": "DatetimeIndex",
            "required_columns": "Open, High, Low, Close, Volume",
            "expected_frequency": "daily"
        }]
    )

def create_data_loader_builder_graph() -> StateGraph:
    """Create the graph for the data loader builder workflow."""
    workflow = StateGraph(WorkflowState)

    # Add all nodes
    workflow.add_node("get_description", get_description)
    workflow.add_node("web_search", web_search)
    workflow.add_node("sample_code_tavily_search", sample_code_tavily_search)
    workflow.add_node("check_api_key_requirements", check_api_key_requirements)
    workflow.add_node("detect_api_keys", detect_api_keys)
    workflow.add_node("get_api_keys", get_api_keys)
    workflow.add_node("list_datasources", list_datasources)
    workflow.add_node("select_relevant_loaders", select_relevant_loaders)
    workflow.add_node("load_data_loader_samples", load_data_loader_samples)
    workflow.add_node("load_test_cases", load_test_cases)
    workflow.add_node("create_test_cases_requirements", create_test_cases_requirements)
    workflow.add_node("generate_code", generate_code)
    workflow.add_node("enhance_description", enhance_description)
    workflow.add_node("final_output", final_output)

    # Add the edges between nodes
    workflow.add_edge(START, "get_description")
    workflow.add_edge("get_description", "web_search")
    workflow.add_edge("web_search", "sample_code_tavily_search")
    workflow.add_edge("sample_code_tavily_search", "check_api_key_requirements")
    workflow.add_edge("check_api_key_requirements", "list_datasources")
    workflow.add_edge("list_datasources", "select_relevant_loaders")
    workflow.add_edge("select_relevant_loaders", "load_data_loader_samples")
    workflow.add_edge("load_data_loader_samples", "load_test_cases")
    workflow.add_edge("load_test_cases", "create_test_cases_requirements")
    workflow.add_edge("create_test_cases_requirements", "generate_code")
    workflow.add_edge("generate_code", "detect_api_keys")
    workflow.add_edge("detect_api_keys", "get_api_keys")
    workflow.add_edge("get_api_keys", "enhance_description")
    workflow.add_edge("enhance_description", "final_output")
    workflow.add_edge("final_output", END)

    return workflow

async def get_description(state: WorkflowState) -> WorkflowState:
    """Extract information from the data loader description."""
    description = state.get("description", "")

    if not description:
        error_message = "No data loader description provided"
        print_debug(error_message, "Data Loader Builder: Description Input Error")
        state["result"] = {
            "error": "Missing required input",
            "details": error_message
        }
        return state

    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DataLoaderDescription)

        system_message = SystemMessage(content="""You are a Python code architect specializing in data loaders.
Your task is to analyze a description and identify key details for creating a data loader class.
For class names, use a specific format based on the data type and time period:
- For stock market data: StockMarket{TimePeriod}{Interval} (e.g., StockMarket1Y1D for 1 year daily data)
- For news/sentiment: Media{TimePeriod} (e.g., Media1Y for 1 year of news)
- For macroeconomics: Macro{TimePeriod} (e.g., Macro20Y for 20 years of macro data)
- For company data: Company{DataType} (e.g., CompanyFinancials for financial statements)

The file name should match the class name with .py extension.
Focus on the core functionality and be specific about the data type and time period.""")

        human_message = HumanMessage(content=f"""
Based on this description, what would be the appropriate class name, file name, and details for a data loader?

Description: {description}

Please extract a concise description, the main purpose, data format, and API name (if any).
""")

        messages = [system_message, human_message]
        analysis = await structured_model.ainvoke(messages)

        state["data_loader_info"] = {
            "class_name": analysis.class_name,
            "file_name": analysis.file_name,
            "description": analysis.description,
            "purpose": analysis.purpose,
            "data_format": analysis.data_format,
            "api_name": analysis.api_name if hasattr(analysis, 'api_name') else ""
        }

        details = f"""Class Name: {analysis.class_name}
File Name: {analysis.file_name}
Description: {analysis.description}
Purpose: {analysis.purpose}
Data Format: {analysis.data_format}
API: {getattr(analysis, 'api_name', 'Not specified')}"""

        print_step(f"""Data Loader Analysis Results:
{details}""", "Data Loader Builder: Analyzed Description", "cyan")

        return state
    except Exception as e:
        error_message = f"Error analyzing description: {str(e)}"
        print_debug(error_message, "Data Loader Builder: Description Analysis Error")

        state["result"] = {
            "error": "Failed to analyze description",
            "details": error_message
        }
        return state

async def web_search(state: WorkflowState) -> WorkflowState:
    """Perform web search to get API documentation and usage examples."""
    data_loader_info = state.get("data_loader_info", {})
    api_name = data_loader_info.get("api_name", "")

    if not api_name:
        print_debug("API name not found in data loader info", "Data Loader Builder: Web Search Error")
        return state

    # Tavily API key validation
    tavily_api_key = os.getenv("TAVILY_API_KEY")
    if not tavily_api_key:
        error_message = "TAVILY_API_KEY not found in environment variables, web search will be limited"
        print_debug(error_message, "Data Loader Builder: Tavily API Error")
        state["web_search_error"] = "Tavily API key missing"
        return state

    try:
        search_query = f"{api_name} python library documentation installation guide authentication"

        # Create the Tavily search tool
        tools = [
            TavilySearchResults(api_key=tavily_api_key, max_results=5)
        ]

        print_debug(f"Setting up React agent for {api_name} search", "Web Search Debug")

        llm_connect = get_llm_connect()
        llm = await llm_connect.get_llm()

        # Set up a ReAct style prompt with the proper format
        prompt = hub.pull("hwchase17/react")

        # Create the agent with ReAct prompt
        agent = create_react_agent(llm, tools, prompt)

        print_debug("Creating agent executor with verbose=True", "Web Search Debug")
        agent_executor = AgentExecutor(
            agent=agent,
            tools=tools,
            verbose=True,
            handle_parsing_errors=True,
        )

        print_debug(f"Executing search for {api_name}", "Web Search Debug")

        # Execute the search
        try:
            search_input = f"Find comprehensive documentation about the {api_name} Python library including: installation guide, authentication requirements, key methods for data loading, and the data format it returns."
            print_debug(f"Search input: {search_input}", "Web Search Debug")

            result = await agent_executor.ainvoke({
                "input": search_input
            })

            print_debug(f"Search result received, length: {len(str(result.get('output', '')))}", "Web Search Debug")

            # Parse agent output
            output_text = result.get("output", "")

        except Exception as e:
            error_message = f"Error executing search: {str(e)}"
            print_debug(error_message, "Data Loader Builder: Web Search Error")
            print_debug(f"Exception details: {traceback.format_exc()}", "Web Search Error Details")
            state["web_search_error"] = str(e)

            print_step(f"""Web Search Error:
API: {api_name}
Error: {str(e)}
Details: {traceback.format_exc()}
Note: Continuing with limited information""", "Data Loader Builder: Web Search Error", "red")

            return state

        # Extract structured information using proper message format
        llm_structured = llm.with_structured_output(WebSearchResults)

        system_message = SystemMessage(content=f"""You are an API documentation expert.
Your task is to extract structured information from search results about the {api_name} Python library.
Organize the information into categories for documentation, installation, authentication, key methods, and data format.""")

        human_message = HumanMessage(content=f"""
Based on this search result about the {api_name} Python library, extract structured information:

{output_text}

Please organize the information into these categories:
1. API Documentation: Main usage and documentation
2. Installation: How to install the library
3. Authentication: Information about API keys or authentication
4. Key Methods: Important functions for loading data
5. Data Format: Information about the returned data structure
""")

        # Pass as list of messages
        messages = [system_message, human_message]
        docs_result = await llm_structured.ainvoke(messages)

        # Store the results
        api_docs = {
            "documentation": docs_result.api_documentation,
            "installation": docs_result.installation,
            "authentication": docs_result.authentication,
            "key_methods": docs_result.key_methods,
            "data_format": docs_result.data_format,
        }

        state["api_docs"] = api_docs

        # Create a readable summary for the step output
        api_docs_summary = f"""Web Search Results for {api_name}:
Documentation found: {len(docs_result.api_documentation)} characters
Search query: "{search_query}"
Search method: ReAct agent with Tavily search
Status: Successful

Agent Thought Process:

{output_text}"""

        print_step(api_docs_summary, "Data Loader Builder: Web Search", "cyan")

        return state
    except Exception as e:
        error_message = f"Error performing web search: {str(e)}"
        print_debug(error_message, "Data Loader Builder: Web Search Error")
        print_debug(f"Exception traceback: {traceback.format_exc()}", "Web Search Error Full Traceback")
        state["web_search_error"] = str(e)

        print_step(f"""Web Search Error:
API: {api_name}
Error: {str(e)}
Details: {traceback.format_exc()}
Note: Continuing with limited information""", "Data Loader Builder: Web Search Error", "red")

        return state

async def check_api_key_requirements(state: WorkflowState) -> WorkflowState:
    """Check if the API requires any authentication keys based on the documentation."""
    data_loader_info = state.get("data_loader_info", {})
    api_docs = state.get("api_docs", {})
    api_name = data_loader_info.get("api_name", "Unknown")
    
    print_step(f"Checking API key requirements for {api_name}", "Data Loader Builder: API Keys", "blue")
    print_debug(f"API documentation available: {bool(api_docs)}", "Data Loader Builder: API Keys")
    
    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(ApiKeyRequirement)
        
        # Documentation to analyze
        documentation = api_docs.get("documentation", "")
        authentication = api_docs.get("authentication", "")
        
        combined_docs = f"""
        API NAME: {api_name}
        DOCUMENTATION:
        {documentation}
        
        AUTHENTICATION INFO:
        {authentication}
        """
        
        # Truncate if too long
        if len(combined_docs) > 15000:
            combined_docs = combined_docs[:15000] + "... [truncated]"
        
        print_debug(f"Combined docs length: {len(combined_docs)}", "Data Loader Builder: API Keys")
        
        system_message = SystemMessage(content="You are an API authentication analyzer. Your job is to determine if an API requires authentication keys based on its documentation.")
        
        human_message = HumanMessage(content=f"""
        Analyze the following documentation for the {api_name} API and determine:
        1. Whether it requires an API key for authentication
        2. What specific key fields are needed (e.g., API_KEY, CLIENT_ID, SECRET_KEY)
        3. Your reasoning for this determination
        
        {combined_docs}
        """)
        
        messages = [system_message, human_message]
        result = await structured_model.ainvoke(messages)
        
        requires_api_key = result.requires_api_key
        key_fields = result.key_fields
        reasoning = result.reasoning
        
        print_debug(f"API key requirement determined: {requires_api_key}", "Data Loader Builder: API Keys")
        print_debug(f"Required key fields: {key_fields}", "Data Loader Builder: API Keys")
        print_debug(f"Reasoning: {reasoning}", "Data Loader Builder: API Keys")
        
        # Update state with API key requirements
        data_loader_info["requires_api_key"] = requires_api_key
        data_loader_info["api_key_fields"] = key_fields
        data_loader_info["api_key_reasoning"] = reasoning
        state["data_loader_info"] = data_loader_info
        
        # Print step information
        status_message = f"""API Key Requirements Check for {api_name}:
Requires API key: {requires_api_key}
Key fields: {[field.get('name') for field in key_fields]}
Reasoning: {reasoning}"""

        print_step(status_message, "Data Loader Builder: API Keys", "blue" if requires_api_key else "green")
        
        # Update state for API key requirements
        if requires_api_key and key_fields:
            # Configure state with API key requirements
            data_loader_info["requires_api_key"] = True
            data_loader_info["api_key_fields"] = key_fields
            state["data_loader_info"] = data_loader_info
            
            print_step(f"""API Keys Required:
API: {api_name}
Required fields: {[field.get('name') for field in key_fields]}
Action: Tracking API key requirements for code generation""", "Data Loader Builder: API Keys", "blue")
        else:
            print_step(f"""No API Keys Required:
API: {api_name}
Reasoning: {reasoning[:100]}...
Action: Continuing without API key requirements""", "Data Loader Builder: API Keys", "green")
        
        return state
    except Exception as e:
        error_message = f"Error checking API key requirements: {str(e)}"
        print_debug(error_message, "Data Loader Builder: API Key Check Error")
        print_debug(f"Exception details: {traceback.format_exc()}", "API Key Check Error Details")

        print_step(f"""API Key Requirement Check Error:
API: {api_name}
Error: {str(e)}
Detail: Could not determine if API keys are required
Action: Assuming no keys required and continuing""", "Data Loader Builder: API Keys", "yellow")

        return state

async def detect_api_keys(state: WorkflowState) -> WorkflowState:
    """Detect API key usage in the generated code"""
    generated_code = state.get("generated_code", "")
    data_loader_info = state.get("data_loader_info", {})
    class_name = data_loader_info.get("class_name", "")
    requires_api_key = data_loader_info.get("requires_api_key", False)
    
    if not generated_code:
        print_step("No generated code to analyze for API keys", "Data Loader Builder: API Keys", "yellow")
        return state
    
    try:
        # Regular expression to find API key patterns
        # Pattern: self.api_key = self.get_api_key("ClassName", "API_KEY_NAME")
        pattern = r'self\.(?:[\w_]+)\s*=\s*self\.get_api_key\(\s*["\']([^"\']+)["\']\s*,\s*["\']([^"\']+)["\']\s*\)'
        
        # Find all matches
        matches = re.findall(pattern, generated_code)
        
        api_keys_found = []
        for class_name_in_code, api_key_name in matches:
            api_keys_found.append({
                "class_name": class_name_in_code,
                "api_key_name": api_key_name
            })
        
        # Check if API keys are required but no API key retrieval is found
        if requires_api_key and not api_keys_found:
            # Need to flag that the implementation didn't add API key retrieval
            print_debug("API keys are required but no API key retrieval code was found", "Data Loader Builder: API Keys")
            
            # Get API key fields
            api_key_fields = data_loader_info.get("api_key_fields", [])
            key_names = [field.get('name', '') for field in api_key_fields if field.get('name')]
            
            # Add this info to the state
            state["api_key_detection"] = {
                "api_keys_found": [],
                "requires_api_keys": True,
                "missing_implementation": True,
                "required_key_names": key_names
            }
            
            # Print warning
            missing_keys = ", ".join(key_names)
            print_step(f"""API Key Implementation Missing:
Class Name: {class_name}
Required API Keys: {missing_keys}
Status: API keys required but implementation missing
Action: Code needs to be updated to include API key retrieval""", "Data Loader Builder: API Keys", "yellow")
        else:
            # Update state with detected API keys
            state["api_key_detection"] = {
                "api_keys_found": api_keys_found,
                "requires_api_keys": len(api_keys_found) > 0,
                "missing_implementation": False
            }
            
            # Store API keys required for the data_loader_info
            if api_keys_found:
                data_loader_info["api_key_fields"] = [{"name": key["api_key_name"]} for key in api_keys_found]
                data_loader_info["requires_api_key"] = True
                state["data_loader_info"] = data_loader_info
            
            # Print step information
            if api_keys_found:
                keys_list = "\n".join([f"• {key['api_key_name']} (for {key['class_name']})" for key in api_keys_found])
                print_step(f"""API Keys Detection:
Class Name: {class_name}
API Keys Found: {len(api_keys_found)}
Required Keys:
{keys_list}
Status: API keys detected in code""", "Data Loader Builder: API Keys", "blue")
            else:
                print_step(f"""API Keys Detection:
Class Name: {class_name}
API Keys Found: None
Status: No API keys detected in code
Note: Code does not appear to require API keys""", "Data Loader Builder: API Keys", "green")
        
        return state
    except Exception as e:
        error_message = f"Error detecting API keys: {str(e)}"
        print_debug(error_message, "Data Loader Builder: API Key Detection Error")
        
        print_step(f"""API Key Detection Error:
Class Name: {class_name}
Error: {str(e)}
Status: Could not detect API keys
Action: Continuing without API key information""", "Data Loader Builder: API Keys", "yellow")
        
        return state

async def get_api_keys(state: WorkflowState) -> WorkflowState:
    """Process API keys required by the code"""
    api_key_detection = state.get("api_key_detection", {})
    data_loader_info = state.get("data_loader_info", {})
    class_name = data_loader_info.get("class_name", "")
    api_keys_found = api_key_detection.get("api_keys_found", [])
    requires_api_keys = api_key_detection.get("requires_api_keys", False)
    missing_implementation = api_key_detection.get("missing_implementation", False)
    
    if missing_implementation:
        # Handle case where API keys are required but not implemented
        required_key_names = api_key_detection.get("required_key_names", [])
        
        # Format API keys for code generation guidance
        api_key_info = {}
        for key_name in required_key_names:
            api_key_info[key_name] = {
                "value": f"os.getenv('{key_name}')",
                "usage": f"self.get_api_key(\"{class_name}\", \"{key_name}\")"
            }
        
        state["api_key_info"] = api_key_info
        
        # Print step information
        keys_list = ", ".join(required_key_names)
        print_step(f"""API Keys Processing:
Class Name: {class_name}
Required Keys: {keys_list}
Status: API keys required but not implemented in code
Note: Provide implementation guidance with self.get_api_key("{class_name}", "KEY_NAME")""", "Data Loader Builder: API Keys Processing", "yellow")
        
        return state
    
    if not api_keys_found and not requires_api_keys:
        print_step(f"""API Keys Processing:
Class Name: {class_name}
Status: No API keys required
Note: Continuing without API key integration""", "Data Loader Builder: API Keys Processing", "green")
        return state
    
    # Format API keys for code generation
    api_key_info = {}
    for key in api_keys_found:
        api_key_name = key.get("api_key_name", "")
        if api_key_name:
            # Store the key with proper formatting for code generation
            api_key_info[api_key_name] = {
                "value": f"os.getenv('{api_key_name}')",
                "usage": f"self.get_api_key(\"{class_name}\", \"{api_key_name}\")"
            }
    
    state["api_key_info"] = api_key_info
    
    # Print step information
    keys_list = ", ".join(api_key_info.keys())
    print_step(f"""API Keys Processing:
Class Name: {class_name}
Required Keys: {keys_list}
Total Keys: {len(api_key_info)}
Status: API keys identified for code generation
Note: These keys will need to be set in environment variables""", "Data Loader Builder: API Keys Processing", "blue")
    
    return state

def list_datasources(state: WorkflowState) -> WorkflowState:
    """List all available data sources from MongoDB and data directory"""
    # Get data loaders from MongoDB
    try:
        datasources_cursor = get_sync_db().data_loaders.find({"is_active": True})
        all_loaders = []

        for ds in datasources_cursor:
            all_loaders.append({
                "name": ds["name"],
                "description": ds["description"],
                "source": "database"
            })

        state["available_datasources"] = all_loaders

        loader_list = "\n".join([f"• {ds['name']} - {ds['description']}" for ds in all_loaders])
        print_step(f"""Available Data Loaders:
Total Loaders Found: {len(all_loaders)}
Data Loaders:
{loader_list}""", "Data Loader Builder: Available Data Loaders", "cyan")
    except Exception as e:
        state["available_datasources"] = []
        print_debug(f"Error fetching datasources: {str(e)}", "Data Loader Builder: Datasources Error")
        print_step("""Available Data Loaders:
Total Loaders Found: 0
Status: Error fetching data loaders from database
Note: Proceeding with empty loader list""", "Data Loader Builder: Available Data Loaders", "yellow")

    return state

async def select_relevant_loaders(state: WorkflowState) -> WorkflowState:
    """Select up to 3 most relevant data loaders based on the description"""
    data_loader_info = state.get("data_loader_info", {})
    available_datasources = state.get("available_datasources", [])

    if not data_loader_info or not available_datasources:
        state["selected_loaders"] = []
        print_step("""Relevant Loaders Selection:
Status: No data loader info or available sources
Result: No loaders selected
Note: Will proceed without reference loaders""", "Data Loader Builder: Selected Loaders", "yellow")
        return state

    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(RelevantLoaders)

        loader_description = f"Purpose: {data_loader_info['purpose']}\nData Format: {data_loader_info['data_format']}"
        available_loaders = [f"{ds['name']} - {ds['description']}" for ds in available_datasources]

        system_message = SystemMessage(content="""You are a Python expert specialized in data loaders.
Your task is to select up to 3 most relevant existing data loaders that could serve as references
for creating a new data loader based on the given description.""")

        human_message = HumanMessage(content=f"""
Description of the new data loader:
{loader_description}

Available data loaders:
{chr(10).join(['- ' + loader for loader in available_loaders])}

Select up to 3 most relevant data loaders from the list above that could serve as references.
""")

        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)

        state["selected_loaders"] = response.loaders

        loader_list = "\n".join([f"• {loader}" for loader in response.loaders])
        print_step(f"""Relevant Loaders Selection:
New Loader Purpose: {data_loader_info.get('purpose', 'Unknown')}
Total Available Loaders: {len(available_datasources)}
Selected Loaders ({len(response.loaders)}):
{loader_list}

Selection Reasoning: {response.reasoning}""", "Data Loader Builder: Selected Loaders", "cyan")

        return state
    except Exception as e:
        state["selected_loaders"] = []
        print_debug(f"Error selecting relevant loaders: {str(e)}", "Data Loader Builder: Loader Selection Error")
        print_step(f"""Relevant Loaders Selection:
Status: Error during selection process
Result: No loaders selected
Error: {str(e)}
Note: Will proceed without reference loaders""", "Data Loader Builder: Selected Loaders", "yellow")
        return state

async def load_data_loader_samples(state: WorkflowState) -> WorkflowState:
    """Load code samples from selected data loaders"""
    selected_loaders = state.get("selected_loaders", [])

    if not selected_loaders:
        state["loader_code_samples"] = {}
        print_step("""Code Sample Loading:
Status: No loaders selected
Result: No code samples loaded
Note: Will proceed with empty sample set""", "Data Loader Builder: Code Samples", "yellow")
        return state

    code_samples = {}
    loaded_samples = []
    missing_samples = []

    # Get samples from the data directory
    try:
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")

        for loader_name in selected_loaders:
            # Try to get the file from the data directory
            file_path = os.path.join(data_dir, f"{loader_name}.py")

            if os.path.exists(file_path):
                # Read the file
                with open(file_path, 'r') as f:
                    code_samples[loader_name] = f.read()

                loaded_samples.append(loader_name)
            else:
                missing_samples.append(loader_name)
    except Exception as e:
        print_debug(f"Error loading code samples: {str(e)}", "Data Loader Builder: Code Sample Error")

    state["loader_code_samples"] = code_samples

    loaded_list = "\n".join([f"• {sample}" for sample in loaded_samples])
    missing_list = "\n".join([f"• {sample}" for sample in missing_samples]) if missing_samples else "None"

    print_step(f"""Code Sample Loading:
Selected Loaders: {len(selected_loaders)}
Successfully Loaded Samples ({len(loaded_samples)}):
{loaded_list}

Missing Samples ({len(missing_samples)}):
{missing_list}

Total Code Sample Size: {sum(len(sample) for sample in code_samples.values())} characters""", "Data Loader Builder: Code Samples", "cyan")

    return state

async def load_test_cases(state: WorkflowState) -> WorkflowState:
    """Load test case content and base data loader for context"""
    try:
        print_debug("Starting load_test_cases function", "Data Loader Builder: Function Start")
        
        # Get paths for the test and base loader files
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        test_file_path = os.path.join(root_dir, "tests", "test_data.py")
        base_loader_path = os.path.join(root_dir, "data", "base_data.py")

        print_debug(f"Root directory: {root_dir}", "Data Loader Builder: File Paths")
        print_debug(f"Test file path: {test_file_path}", "Data Loader Builder: File Paths")
        print_debug(f"Base loader path: {base_loader_path}", "Data Loader Builder: File Paths")
        print_debug(f"Files exist - test: {os.path.exists(test_file_path)}, base: {os.path.exists(base_loader_path)}", "Data Loader Builder: File Check")

        test_content = ""
        base_loader_content = ""

        # Load test_data.py
        if os.path.exists(test_file_path):
            with open(test_file_path, 'r') as f:
                test_content = f.read()
            print_debug(f"Test file exists and read. Content length: {len(test_content)}", "Data Loader Builder: File Reading")
            print_debug(f"Test file first 100 chars: {test_content[:100]}", "Data Loader Builder: File Content")
            print_step(f"""Test Case Loading:
Test Case File: {os.path.basename(test_file_path)}
Test Case Size: {len(test_content)} characters
Status: Successfully loaded test cases""", "Data Loader Builder: Test Cases", "cyan")
        else:
            print_debug(f"Test file not found at {test_file_path}", "Data Loader Builder: Test File Not Found")
            print_step(f"""Test Cases Loading:
Test Case File: {os.path.basename(test_file_path)}
Status: File not found
Note: Proceeding without test cases""", "Data Loader Builder: Test Cases", "yellow")

        # Load base_data.py
        if os.path.exists(base_loader_path):
            with open(base_loader_path, 'r') as f:
                base_loader_content = f.read()
            print_debug(f"Base loader file exists and read. Content length: {len(base_loader_content)}", "Data Loader Builder: File Reading")
            print_debug(f"Base loader first 100 chars: {base_loader_content[:100]}", "Data Loader Builder: File Content")
            print_step(f"""Base Loader Loading:
Base Loader File: {os.path.basename(base_loader_path)}
Base Loader Size: {len(base_loader_content)} characters
Status: Successfully loaded base loader template""", "Data Loader Builder: Base Loader", "cyan")
        else:
            # Try an alternative path
            alt_path = os.path.join(root_dir, "data", "base_data_loader.py")
            print_debug(f"Base loader not found at {base_loader_path}, trying alternative path: {alt_path}", "Data Loader Builder: Base Loader Not Found")
            print_debug(f"Alternative path exists: {os.path.exists(alt_path)}", "Data Loader Builder: File Check")
            
            if os.path.exists(alt_path):
                with open(alt_path, 'r') as f:
                    base_loader_content = f.read()
                print_debug(f"Base loader found at alternative path. Content length: {len(base_loader_content)}", "Data Loader Builder: File Reading")
                print_debug(f"Base loader first 100 chars: {base_loader_content[:100]}", "Data Loader Builder: File Content")
                print_step(f"""Base Loader Loading:
Base Loader File: {os.path.basename(alt_path)}
Base Loader Size: {len(base_loader_content)} characters
Status: Successfully loaded base loader from alternative path""", "Data Loader Builder: Base Loader", "cyan")
            else:
                print_debug(f"Base loader file not found at {base_loader_path} or {alt_path}", "Data Loader Builder: Base Loader Not Found")
                print_step(f"""Base Loader Loading:
Base Loader File: {os.path.basename(base_loader_path)}
Status: File not found
Note: Proceeding without base loader template""", "Data Loader Builder: Base Loader", "yellow")

        # Store the content in the state
        state["test_cases"] = test_content
        state["base_loader"] = base_loader_content

        # Add explicit length checks to state for debugging
        print_debug(f"State object id: {id(state)}", "Data Loader Builder: State Object")
        print_debug(f"State type: {type(state)}", "Data Loader Builder: State Type")
        print_debug(f"State keys before return: {state.keys()}", "Data Loader Builder: State Keys")
        print_debug(f"State test_cases length: {len(state.get('test_cases', ''))}", "Data Loader Builder: State Check")
        print_debug(f"State base_loader length: {len(state.get('base_loader', ''))}", "Data Loader Builder: State Check")
        print_debug(f"State base_loader type: {type(state.get('base_loader', ''))}", "Data Loader Builder: State Type")
        
        print_debug("Finishing load_test_cases function", "Data Loader Builder: Function End")
        return state
    except Exception as e:
        print_debug(f"Error loading reference files: {str(e)}", "Data Loader Builder: Reference Files Error")
        print_debug(f"Exception details: {traceback.format_exc()}", "Reference Files Error Details")
        
        state["test_cases"] = ""
        state["base_loader"] = ""
        
        print_step(f"""Reference Files Loading:
Status: Error loading reference files
Error: {str(e)}
Note: Proceeding without reference files""", "Data Loader Builder: Reference Files", "yellow")

        return state

async def generate_code(state: WorkflowState) -> WorkflowState:
    """Generate the code for the data loader"""
    data_loader_info = state.get("data_loader_info", {})
    loader_code_samples = state.get("loader_code_samples", {})
    test_requirements = state.get("test_requirements", {})
    api_docs = state.get("api_docs", {})
    sample_code = state.get("sample_code", "")

    if not data_loader_info:
        state["generated_code"] = ""
        print_step("""Code Generation:
Status: No data loader information available
Result: No code generated""", "Data Loader Builder: Code Generation", "yellow")
        return state
    
    print_step(f"Generating code for {data_loader_info.get('class_name', 'Unknown')}", "Data Loader Builder: Code Generation", "blue")
    
    api_name = data_loader_info.get("api_name", "")
    requires_api_key = data_loader_info.get("requires_api_key", False)
    api_key_fields = data_loader_info.get("api_key_fields", [])
    
    try:
        # Get LLM
        llm_connect = get_llm_connect()
        llm = await llm_connect.get_llm_by_id("67f92772d2826d263e751e2c")
        structured_model = llm.with_structured_output(PythonScript)
        
        # Create examples from existing loaders
        examples = []
        for loader_name, code in loader_code_samples.items():
            examples.append(f"LOADER: {loader_name}\n```python\n{code}\n```\n")
        
        # Combine examples with length limitation
        examples_text = "\n".join(examples)
        if len(examples_text) > 8000:
            examples_text = examples_text[:8000] + "\n... [more examples truncated]"
        
        # Process test requirements
        base_requirements = test_requirements.get("base_requirements", [])
        test_requirements_list = test_requirements.get("test_requirements", [])
        data_structure = test_requirements.get("data_structure", {})
        
        base_requirements_text = "\n".join([f"- {req}" for req in base_requirements])
        test_requirements_text = "\n".join([f"- {req}" for req in test_requirements_list])
        data_structure_text = "\n".join([f"- {k}: {v}" for k, v in data_structure.items()])
        
        # API key requirements information
        api_key_info = ""
        if requires_api_key and api_key_fields:
            api_key_names = [field.get('name', '') for field in api_key_fields if field.get('name')]
            api_key_info = f"""
IMPORTANT: The {api_name} API requires authentication.
Required API key fields: {api_key_names}

You MUST use: self.api_key = self.get_api_key("{data_loader_info.get('class_name', 'MyDataLoader')}", "{api_key_names[0] if api_key_names else 'API_KEY'}")
"""

        # Add API documentation sample code if available
        api_sample_code = ""
        if sample_code:
            api_sample_code = f"""
API SAMPLE CODE:
```python
{sample_code}
```
"""
        
        system_message = SystemMessage(content=f"""You are creating a data loader class for {data_loader_info.get('class_name', '')}.

IMPORTANT: The existing code samples represent PERFECTLY WORKING implementations. Your primary approach should be to:
1. Study the provided sample loaders carefully
2. Use their structure, patterns, and error handling approaches as your main reference
3. Adapt these patterns to the current requirements with minimal changes

REQUIREMENTS:
1. Base Class Requirements:
{base_requirements_text}

2. Test Requirements:
{test_requirements_text}

3. Data Structure Requirements:
{data_structure_text}

{api_key_info if requires_api_key else ""}
Your code MUST satisfy ALL these requirements to pass the tests while staying as close as possible to the working samples.""")
        
        human_message = HumanMessage(content=f"""Create data loader class {data_loader_info.get('class_name', '')} for {data_loader_info.get('description', '')}.

PURPOSE: {data_loader_info.get('purpose', '')}
API: {api_name}
DATA FORMAT: {data_loader_info.get('data_format', '')}
{api_sample_code}
SAMPLE WORKING LOADERS:
{examples_text}

Provide ONLY the Python code for the data loader class as clean code. No explanations, no comments, no markdown tags.
The code MUST satisfy ALL the requirements to pass the tests.""")
        
        # Get structured response
        result = await structured_model.ainvoke([system_message, human_message])
        generated_code = result.code
        
        # Store the generated code
        state["generated_code"] = generated_code
        
        print_step(f"""Code Generation:
Class Name: {data_loader_info.get('class_name', 'Unknown')}
Status: Successfully generated code
Code Size: {len(generated_code)} characters
API: {api_name}
Sample Code Used: {"Yes" if sample_code else "No"}
Test Requirements: {len(base_requirements) + len(test_requirements_list)} requirements applied
Structured Output: Yes
Result: Code ready for review""", "Data Loader Builder: Code Generation", "green")
        
        return state
    except Exception as e:
        error_message = f"Error generating code: {str(e)}"
        print_debug(error_message, "Data Loader Builder: Code Generation Error")
        print_debug(f"Exception details: {traceback.format_exc()}", "Code Generation Error Details")
        state["generated_code"] = ""

        print_step(f"""Code Generation:
Class Name: {data_loader_info.get('class_name', 'Unknown')}
Status: Error during code generation
Error: {str(e)}
Result: No code generated""", "Data Loader Builder: Code Generation", "red")
        return state

async def enhance_description(state: WorkflowState) -> WorkflowState:
    """Enhance the data loader description to make it more production-ready"""
    data_loader_info = state.get("data_loader_info", {})
    generated_code = state.get("generated_code", "")

    if not data_loader_info or not generated_code:
        print_step("""Documentation Enhancement:
Status: Missing data loader info or generated code
Result: No documentation enhancement performed""", "Data Loader Builder: Documentation", "yellow")
        return state

    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(DataLoaderEnhancedDescription)

        original_description = data_loader_info.get("description", "")
        purpose = data_loader_info.get("purpose", "")
        class_name = data_loader_info.get("class_name", "")

        system_message = SystemMessage(content="""You are a technical documentation expert.
Your task is to create a concise, one-sentence description for a data loader class.
The description should be clear and include the main functionality in a single sentence.""")

        human_message = HumanMessage(content=f"""
Based on the following information, create a concise, one-sentence description for a data loader:

Class Name: {class_name}
Original Description: {original_description}
Purpose: {purpose}

Generated Code:
```python
{generated_code}
```

Please provide a single, clear sentence that describes what this data loader does.
""")

        messages = [system_message, human_message]
        response = await structured_model.ainvoke(messages)

        data_loader_info["enhanced_description"] = response.enhanced_description
        state["data_loader_info"] = data_loader_info

        print_step(f"""Documentation Enhancement:
Class Name: {class_name}
Original Description Length: {len(original_description)} characters
Enhanced Description Length: {len(response.enhanced_description)} characters
Status: Documentation enhanced successfully""", "Data Loader Builder: Enhanced Description", "green")

        return state
    except Exception as e:
        error_message = f"Error enhancing description: {str(e)}"
        print_debug(error_message, "Data Loader Builder: Documentation Error")

        print_step(f"""Documentation Enhancement:
Class Name: {data_loader_info.get('class_name', 'Unknown')}
Status: Error during documentation enhancement
Error: {str(e)}
Result: Using original description without enhancement""", "Data Loader Builder: Enhanced Description", "yellow")
        return state

async def final_output(state: WorkflowState) -> WorkflowState:
    """Finalize the output of the workflow"""
    data_loader_info = state.get("data_loader_info", {})
    generated_code = state.get("generated_code", "")

    if data_loader_info and generated_code:
        # Use enhanced description if available, otherwise use original
        description = data_loader_info.get("enhanced_description", data_loader_info.get("description", ""))

        result = {
            "class_name": data_loader_info.get("class_name", ""),
            "file_name": data_loader_info.get("file_name", ""),
            "code": generated_code,
            "description": description,
            "inputs": data_loader_info.get("inputs", ""),
            "outputs": data_loader_info.get("outputs", ""),
            "requirements": data_loader_info.get("requirements", "")
        }

        print_step(f"""Data Loader Creation Complete:
Class Name: {data_loader_info.get('class_name', '')}
File Name: {data_loader_info.get('file_name', '')}
Code Size: {len(generated_code)} characters
Description Size: {len(description)} characters
Status: Successfully completed data loader creation
Result: Ready for integration into the system""", "Data Loader Builder: Complete", "green")
    else:
        result = {
            "error": "Failed to generate data loader code",
            "details": "Missing required information"
        }

        print_step("""Data Loader Creation Failed:
Error: Missing required information
Details: Could not find data loader info or generated code
Status: Failed to create data loader""", "Data Loader Builder: Error", "red")

    state["result"] = result
    return state

async def sample_code_tavily_search(state: WorkflowState) -> WorkflowState:
    """Extract clean Python code samples from Tavily search results."""
    api_docs = state.get("api_docs", {})
    data_loader_info = state.get("data_loader_info", {})
    api_name = data_loader_info.get("api_name", "")
    description = data_loader_info.get("description", "")
    purpose = data_loader_info.get("purpose", "")
    
    if not api_docs or not api_name:
        print_debug("No API documentation or API name found", "Data Loader Builder: Sample Code Extraction")
        return state
    
    try:
        documentation = api_docs.get("documentation", "")
        key_methods = api_docs.get("key_methods", "")
        combined_docs = f"{documentation}\n\n{key_methods}"
        
        llm_connect = get_llm_connect()
        llm = await llm_connect.get_llm_by_id("67f92772d2826d263e751e2c")
        structured_model = llm.with_structured_output(PythonScript)
        
        # Extract timeframe from description or purpose
        timeframe_info = ""
        if "32 months" in description or "32 months" in purpose:
            timeframe_info = "32 months with daily interval"
        elif "months" in description or "months" in purpose:
            timeframe_info = "several months with daily interval"
        else:
            timeframe_info = "appropriate timeframe based on requirements"
        
        system_message = SystemMessage(content=f"""Extract or create a clean, working Python code sample for {api_name} that specifically demonstrates:
1. Loading {timeframe_info} of stock market data
2. Properly handling the data with pandas
3. Include error handling and data validation
4. Implement best practices for the {api_name} library

The code must be complete and functional, not just fragments.""")
        
        human_message = HumanMessage(content=f"""I need a Python code sample for {api_name} that implements:
- Loading stock data for {timeframe_info}
- Purpose: {purpose}
- Description: {description}

Here is documentation about the {api_name} Python library:
{combined_docs}

Please provide ONLY a complete, working Python code sample without comments or markdown tags that I can use as a reference.""")
        
        messages = [system_message, human_message]
        result = await structured_model.ainvoke(messages)
        
        code_sample = result.code
        
        # Store the code sample
        state["sample_code"] = code_sample
        
        # Create a code snippet for display (up to 20 lines)
        code_lines = code_sample.strip().split('\n')
        display_lines = code_lines[:20]
        
        # Add ellipsis if code was truncated
        if len(code_lines) > 20:
            display_lines.append("...")
        
        display_code = '\n'.join(display_lines)
        
        print_step(f"""Sample Code Extraction:
API: {api_name}
Timeframe: {timeframe_info}
Code Size: {len(code_sample)} characters
Status: Successfully extracted code samples

Extracted Code Sample:
```python
{display_code}
```
""", "Data Loader Builder: Sample Code", "green")
        
        return state
    except Exception as e:
        error_message = f"Error extracting sample code: {str(e)}"
        print_debug(error_message, "Data Loader Builder: Sample Code Error")
        
        print_step(f"""Sample Code Extraction:
API: {api_name}
Status: Error extracting code samples
Error: {str(e)}""", "Data Loader Builder: Sample Code", "yellow")
        
        return state

async def create_test_cases_requirements(state: WorkflowState) -> WorkflowState:
    """Extract structured requirements from test cases and base loader code"""
    test_cases = state.get("test_cases", "")
    base_loader = state.get("base_loader", "")
    data_loader_info = state.get("data_loader_info", {})
    class_name = data_loader_info.get("class_name", "")
    
    # Enhanced debugging to identify the issue
    print_debug(f"State keys: {state.keys()}", "Data Loader Builder: State Keys Debug")
    print_debug(f"Test cases length: {len(test_cases)}", "Data Loader Builder: Test Requirements Debug")
    print_debug(f"Base loader length: {len(base_loader)}", "Data Loader Builder: Test Requirements Debug")
    print_debug(f"Base loader type: {type(base_loader)}", "Data Loader Builder: Test Requirements Debug")
    
    # If base_loader is empty, try to load it directly
    if not base_loader:
        try:
            print_debug("Base loader missing from state, attempting to load directly", "Data Loader Builder: Recovery")
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            base_loader_path = os.path.join(root_dir, "data", "base_data.py")
            
            if os.path.exists(base_loader_path):
                with open(base_loader_path, 'r') as f:
                    base_loader = f.read()
                print_debug(f"Successfully loaded base_loader directly, length: {len(base_loader)}", "Data Loader Builder: Recovery")
            else:
                alt_path = os.path.join(root_dir, "data", "base_data_loader.py")
                if os.path.exists(alt_path):
                    with open(alt_path, 'r') as f:
                        base_loader = f.read()
                    print_debug(f"Successfully loaded base_loader from alt path, length: {len(base_loader)}", "Data Loader Builder: Recovery")
        except Exception as e:
            print_debug(f"Failed to recover base_loader: {str(e)}", "Data Loader Builder: Recovery Error")
    
    if not test_cases:
        print_debug("Test cases missing from state", "Data Loader Builder: Test Requirements Error")
        print_step("""Test Requirements Extraction:
Status: Missing test cases
Result: No requirements extracted""", "Data Loader Builder: Test Requirements", "yellow")
        return state
    
    if not base_loader:
        print_debug("Base loader content missing and could not be loaded directly", "Data Loader Builder: Test Requirements Error")
        print_step("""Test Requirements Extraction:
Status: Missing base loader code
Result: No requirements extracted
Action: Proceeding with limited code generation""", "Data Loader Builder: Test Requirements", "yellow")
        
        # Create empty test requirements to allow workflow to continue
        state["test_requirements"] = {
            "base_requirements": [],
            "test_requirements": [],
            "data_structure": {}
        }
        return state
    
    # Store the recovered base_loader in the state
    state["base_loader"] = base_loader
    
    # Get LLM and prepare for structured output
    llm_connect = get_llm_connect()
    llm = await llm_connect.get_llm()
    structured_model = llm.with_structured_output(TestCaseRequirements)
    
    system_message = SystemMessage(content="""Extract specific requirements from test cases and base loader code.
Identify exactly what the data loader must do to pass all tests.
Format requirements as clear, actionable statements that a developer would follow.
Identify required data structure, columns, index type, and error handling.""")
    
    human_message = HumanMessage(content=f"""I need to create a data loader class named {class_name} that passes all tests.
Extract clear requirements from these test cases and base loader code.

BASE LOADER CODE:
```python
{base_loader}
```

TEST CASES:
```python
{test_cases}
```

Please analyze both and extract:
1. Base requirements - what I must implement from the base class
2. Test requirements - what my implementation must do to pass tests
3. Data structure - required columns, index type, and data format

Be specific and thorough so I can write code that passes all tests.""")
    
    messages = [system_message, human_message]
    
    try:
        result = await structured_model.ainvoke(messages)
        
        # Store the requirements
        state["test_requirements"] = result.dict()
        
        # Prepare display of requirements
        base_reqs = "\n".join([f"• {req}" for req in result.base_requirements])
        test_reqs = "\n".join([f"• {req}" for req in result.test_requirements])
        data_struct = "\n".join([f"• {k}: {v}" for k, v in result.data_structure.items()])
        
        print_step(f"""Test Requirements Extraction:
Class Name: {class_name}
Status: Successfully extracted requirements

Base Requirements:
{base_reqs}

Test Requirements:
{test_reqs}

Data Structure Requirements:
{data_struct}
""", "Data Loader Builder: Test Requirements", "violet")
    
    except Exception as e:
        error_message = f"Error extracting test requirements: {str(e)}"
        print_debug(error_message, "Data Loader Builder: Test Requirements Error")
        print_debug(f"Exception details: {traceback.format_exc()}", "Test Requirements Error Details")
        
        # Create empty test requirements to allow workflow to continue
        state["test_requirements"] = {
            "base_requirements": [],
            "test_requirements": [],
            "data_structure": {}
        }
        
        print_step(f"""Test Requirements Extraction:
Class Name: {class_name}
Status: Error during requirement extraction
Error: {str(e)}
Result: Creating minimal requirements to continue workflow""", "Data Loader Builder: Test Requirements", "yellow")
    
    return state

# Create and compile the graph
graph = create_data_loader_builder_graph().compile()

__all__ = ['graph']
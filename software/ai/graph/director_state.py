from langgraph.graph.message import add_messages
from langchain_core.messages import (
    BaseMessage,
)
from typing import Annotated, List, TypedDict, Optional, Literal, Any, Dict
from pydantic import BaseModel, Field
import uuid
from datetime import date

import rich
from rich.panel import Panel
from rich.pretty import Pretty
from rich.color import ANSI_COLOR_NAMES
import random

from software.db.research_repository import ResearchRepository

# ┌────────────────────────────────────────────────────────────────────┐
# │                       CONSOLE UTILITIES                             │
# └────────────────────────────────────────────────────────────────────┘
console = rich.get_console()
research_repo = ResearchRepository()
VERBOSE = True

def print_step(message: str, title: str, border_style: str = "random", verbose: bool = VERBOSE):
    if not verbose:
        return
    if border_style == "random":
        border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
        while "red" in border_style:
            border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
    console.print(Panel(message, title="STEP", title_align="left", subtitle=title, border_style=border_style))

def print_pretty(object: object):
    border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
    console.print(Panel(Pretty(object), title="PRETTY", title_align="left", border_style=border_style))

def print_debug(message: str, title: str = str(uuid.uuid4()), verbose: bool = VERBOSE):
    if not verbose:
        return
    border_style = "bright_red"
    console.print(Panel(message, title="DEBUG", title_align="left", subtitle=title, border_style=border_style))

# ┌────────────────────────────────────────────────────────────────────┐
# │                          SEARCH SCOPE                               │
# └────────────────────────────────────────────────────────────────────┘

class Plan(BaseModel):
    tasks: List[str] = Field(default_factory=list, description="The tasks to be completed by the analyst")
    model_config = {"extra": "forbid"}

# ┌────────────────────────────────────────────────────────────────────┐
# │                         DELEGATE MODELS                             │
# └────────────────────────────────────────────────────────────────────┘

class AnalystState(BaseModel):
    """State class for the analyst workflow."""
    messages: Annotated[List[BaseMessage], add_messages]
    business_question: str = Field(description="The business question the analyst needs to answer")
    name_analyst: str = Field(description="The name of the analyst")
    age_analyst: int = Field(description="The age of the analyst")
    title_analyst: str = Field(description="The title of the analyst")
    background_analyst: str = Field(description="The background/expertise/education of the analyst")
    successful_tools: dict[str, dict[str, Any]] = Field(default_factory=dict, description="The tools used by the analyst")
    director_id: Optional[str] = Field(None, description="The ID of the director overseeing this analyst.")
    task_id: Optional[str] = Field(None, description="The ID of the overall task this analyst is contributing to.")
    retrieved_context: Optional[str] = None
    draft_report_vp: Optional[str] = None
    rag_answer: Optional[str] = None

class AnalystList(BaseModel):
    analysts: List[AnalystState] = Field(default_factory=list, description="A list of analysts with their assigned tasks and personas.")

    model_config = {"extra": "forbid"}

class FeatureRequest(BaseModel):
    feature_request: str = Field(description="The feature request to be added to the list")
    feature_type: Literal["data", "feature"] = Field(description="The type of feature request")

    model_config = {"extra": "forbid"}

class FeatureRequestList(BaseModel):
    feature_requests: List[FeatureRequest] = Field(description="A list of feature requests")

    model_config = {"extra": "forbid"}

class AnalysisReport(BaseModel):
    analysis_report_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique identifier for the report")
    business_question: str = Field(description="The business question the analyst needs to answer")
    business_answer: str = Field(description="The business answer to the question")
    target_column: str = Field(description="The target column of the report", examples=["closing price", "volume", "RSI"])
    confidence: int = Field(description="The confidence level of the report", ge=0, le=100)
    conclusion: Literal["BUY", "SELL", "HOLD"] = Field(description="The conclusion of the report", examples=["BUY", "SELL", "HOLD"])
    observation: str = Field(description="The observation of the report")
    current_target_value: float = Field(description="The current target value of the stock")
    predicted_target_value: float = Field(description="The predicted target value of the stock. If no prediction is available, this will be the same as current_target_value")
    current_date: str = Field(description="The current date")
    horizon_date: str = Field(description="The horizon date")
    argument_for_conclusion: str = Field(description="The arguments for the conclusion")
    argument_against_conclusion: str = Field(description="The arguments against the conclusion")

class ReferenceList(BaseModel):
    references: List[str] = Field(
        description="A list of references",
        examples=['67f701257d751f9f7924abc7', '67f701257d751f9f7924abc8']
    )

    model_config = {"extra": "forbid"}

# ┌────────────────────────────────────────────────────────────────────┐
# │                    SUMMARIZE REPORT MODELS                          │
# └────────────────────────────────────────────────────────────────────┘

class StructuredReport(BaseModel):
    ticker: str = Field(...,
        description="The stock ticker symbol",
        examples=["AAPL", "MSFT", "TSLA"])

    last_closing_price: float = Field(...,
        description="The last recorded closing price of the stock",
        examples=[150.23, 325.75])

    closing_price_date: date = Field(...,
        description="The date of the last closing price",
        examples=["2023-11-15", "2023-12-01"])

    forecasted_price: float = Field(...,
        description="The forecasted price target by the director",
        examples=[175.50, 310.25])

    forecasted_horizon: date = Field(...,
        description="The target date for the forecasted price",
        examples=["2023-12-15", "2024-01-31"])

    recommendation: Literal["BUY", "SELL", "HOLD"] = Field(...,
        description="The director's investment recommendation",
        examples=["BUY", "SELL", "HOLD"])

    confidence_level: int = Field(...,
        description="Confidence level in the prediction (0-100)",
        ge=0, le=100,
        examples=[85, 70])



    model_config = {"extra": "forbid"}

# ┌────────────────────────────────────────────────────────────────────┐
# │                        MAIN STATE MODEL                             │
# └────────────────────────────────────────────────────────────────────┘

class DirectorState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    director_id: str
    task_id: str
    business_question: str
    critical_thinking_search: str
    critical_thinking_planning_result: str
    director_profile: Dict[str, Any]
    plan: Plan
    analysts: AnalystList
    # Use a simple lambda that leverages the analysis_report_id field for uniqueness
    completed_reports: Annotated[list[AnalysisReport], lambda x, y: list({report.analysis_report_id: report for report in x + y}.values())]
    embeddings_analysis_report: dict[str, list[float]]
    structured_report: StructuredReport
    final_report: str

# ┌────────────────────────────────────────────────────────────────────┐
# │                   GRAPH OUTPUTS AND EXPORTS                         │
# └────────────────────────────────────────────────────────────────────┘

class AnalystOutput(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    completed_reports: Annotated[list[AnalysisReport], lambda x, y: list({report.analysis_report_id: report for report in x + y}.values())]
    retrieved_context: Optional[str] = None
    rag_answer: Optional[str] = None

__all__ = ['DirectorState', 'AnalystState', 'AnalystList', 'AnalystOutput',
           'Plan',
           'FeatureRequest', 'FeatureRequestList', 'ReferenceList', 'AnalysisReport',
           'StructuredReport',
           'print_step', 'print_pretty', 'print_debug', 'research_repo', 'console']
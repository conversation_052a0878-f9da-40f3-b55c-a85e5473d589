from typing import List
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.graph.director_state import DirectorState, Plan, AnalystList, console, print_step, print_debug, print_pretty, AnalystState
from software.ai.graph.output_memory_graph import graph as output_memory_graph
from software.ai.graph.simple_agent_tool_graph import graph as simple_agent_tool_graph
from software.ai.graph.feature_requestor_graph import graph as feature_requestor_graph
from software.ai.tools import registry

from langgraph.types import Send
from software.ai.llm.llm_connect import get_llm_connect
from software.db.research_repository import ResearchRepository
from software.db.research_director_repository import ResearchDirectorRepository

research_repo = ResearchRepository()
research_director_repo = ResearchDirectorRepository()

async def init_graph(state: DirectorState) -> DirectorState:
    print_step("Entering init graph", "Init Graph")

    # Preserve all existing state keys
    result_state = {k: v for k, v in state.items()}

    director_id = state.get("director_id", "")
    letta_agent_id = await research_director_repo.get_letta_agent_id(director_id)
    reset_letta_agent_messages = await research_director_repo.reset_letta_agent_messages(letta_agent_id)
    business_question = state.get("business_question", "")

    init_message = HumanMessage(content=f'''I have a plan to answer the following business question: {business_question}

Can you search anything that can help to jumpstart and brainstorm critical things I should consider and things I should avoid? if I don't have anything just say "Nothing in memory", avoid creating memories.''')

    # Update state with new values while preserving other keys
    result_state.update({
        'messages': [init_message]
    })

    return result_state

async def critical_thinking_planning(state: DirectorState) -> DirectorState:
    print_step("Entering critical thinking planning", "Critical Thinking Planning")
    messages = state.get("messages", [])
    memory_block = messages[-1].content if messages else ""
    business_question = state.get("business_question", "")
    director_id = state.get("director_id", "")

    # Get Letta memory block
    letta_agent_id = await research_director_repo.get_letta_agent_id(director_id)
    memory_block = await research_director_repo.get_memory_block(letta_agent_id, "research_planning")
    print_pretty(memory_block['content'])

    # Get LLM for critical thinking
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "critical_thinking_planning")

    # Create enhanced system message for critical thinking
    system_message = SystemMessage(content=f"""<role>
You are a strategic hedge fund research director analyzing a business question to create an optimal research plan.
Your expertise is in breaking down complex financial questions into actionable research tasks.
</role>

<context>
BUSINESS QUESTION: {business_question}

HISTORICAL INSIGHTS:
{memory_block['content']}
</context>

<task>
Analyze the business question and provide strategic insights to guide the research planning process.
Focus on identifying the most effective approach based on past successful analyses.
</task>

<output_format>
Provide a concise analysis with:
1. Key companies/tickers that should be researched
2. Critical financial metrics to focus on
3. Optimal timeframe considerations
4. Most effective tool combinations based on past successes
5. Potential risks or blind spots to address
</output_format>""")

    # Create human message with the task
    human_message = HumanMessage(content=f"Analyze this investment question strategically: {business_question}")

    # Get critical analysis from LLM
    response = await model.ainvoke([
        system_message,
        human_message
    ])

    critical_thinking_planning_result = response.content
    print_step(critical_thinking_planning_result, "Critical Thinking Planning", "light_coral")
    return {'critical_thinking_planning_result': critical_thinking_planning_result}


async def create_plan(state: DirectorState) -> DirectorState:
    console.print("[orange1]Entering create plan[/orange1]")

    await research_repo.update_workflow_stage(state["task_id"], "delegate", "in_progress")
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "create_plan")

    # Get critical thinking analysis
    critical_thinking_planning = state.get("critical_thinking_planning_result", "")

    # Create structured output model
    structured_model = model.with_structured_output(Plan)
    json_plan = Plan().model_dump_json()
    tool_prompt = await registry.generate_system_message_async()
    tool_names = await registry.get_tool_names_async()
    tool_names_comma = ', '.join(tool_names)
    fin_lang = f"""
    <critical_thinking_section>
    {critical_thinking_planning}
    </critical_thinking_section>

    ROLE -> HEDGE_FUND_MANAGER
    GOAL -> DESIGN(RESEARCH_PLAN) FOR(ANALYST)
    INPUT -> BUSINESS_QUESTION

    RULES:
    COUNT(TASKS) = 7
    FORMAT(TASK) = TASK([List of tools], "company_name/s", "objective", "timeframe")
    CONSTRAINTS:
        -- TOOLS
            - TOOLS(NAME-DESCRIPTION) = {tool_prompt}
            - TOOLS = [List of tools]
            - TOOLS ∈ {tool_names_comma}
            - TOOLS = CAN BE REPEATED, ORDER MATTERS
            - COUNT(TOOLS) = 3-6

        -- COMPANY_NAME/S
            - COMPANY_NAME/S = "company_name/s"
            - COMPANY_NAME/S = CANNOT BE REPEATED, ORDER MATTERS
            - COUNT(COMPANY_NAME/S) = 1-2

        -- OBJECTIVE
            - OBJECTIVE ∈ {"closing price", "volume", "RSI",...}

        -- TIMEFRAME
            - TIMEFRAME = M days
            - RANGE(TIMEFRAME) = 1-14 days
            - TIME: PAST, PRESENT, FUTURE

        -- CONSTRAINTS
            - NO(JUSTIFICATION)
            - NO(EXPLANATION)
            - EACH TASK -> ACTIONABLE
    CRITICAL: FINAL OUTPUT MUST BE IN FIN_LANG FORMAT, DO NOT OUTPUT ANYTHING ELSE
    OUTPUT -> [TASK(),...]

    Your format ultimately must be a valid JSON matching this schema:
    {json_plan}

    IMPORTANT: You must return a valid list of tasks in the 'tasks' field. The list cannot be empty.
    Example output format: {{
      "tasks": ["TASK([{tool_names_comma}], \"AVGO\", \"closing price\", \"next 7 days\")"]
    }}
    """
    human_message = HumanMessage(content="Please create a research plan for the following business question: " + state["business_question"])
    print_step(state["business_question"], "Business Question", "pale_turquoise1", verbose=True)

    # Use structured output model
    plan = await structured_model.ainvoke([
        SystemMessage(content=fin_lang),
        human_message
    ])

    print_pretty(plan)
    return {"plan": plan}

async def create_personas(state: DirectorState) -> DirectorState:
    console.print("[purple3]Entering create personas[/purple3]")
    messages = state.get("messages", [])
    memory_block = messages[-1].content if messages else ""
    print_step(memory_block, "Memory Block")

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "create_personas")

    # Create structured output model
    structured_model = model.with_structured_output(AnalystList)

    original_business_question = state["business_question"]

    # Create a sample AnalystList for the example with tool order in business question
    sample_analyst = AnalystState(
        messages=[],
        business_question="using TodayDateTool, SimpleWorkflowTool, DataVizInsightTool forecast AVGO closing price next 7 days",
        name_analyst="Jane Smith",
        age_analyst=35,
        title_analyst="Vice President of Semiconductor Equity Research",
        background_analyst="Ph.D. in Financial Economics with 15 years of specialized experience in semiconductor industry analysis. Expert in price forecasting models for tech stocks with particular focus on Broadcom (AVGO) and related chipmakers. Developed proprietary algorithms for predicting semiconductor stock movements based on market trends, supply chain data, and technical indicators."
    )
    sample_analyst_list = AnalystList(analysts=[sample_analyst])
    sample_json = sample_analyst_list.model_dump_json()

    system_message = SystemMessage(content=f"""You are an HR manager responsible for assigning research tasks to analysts.
You will be given a research plan (a list of tasks) from a Hedge Fund Manager.
Your goal is to create a list of analysts, assigning ONE task from the plan to EACH analyst.

<memory>
{memory_block}
</memory>

**Instructions:**

1.  **Analyze the Plan:** Understand each task defined in the provided plan JSON.
2.  **Create Analyst Profiles:** For EACH task in the plan, create ONE analyst profile.
3.  **Construct Business Question:** For each analyst, formulate a `business_question` that includes:
    * First, the exact tool sequence from the task in the format: "using Tool1, Tool2, Tool3"
    * Then, the action verb, company name, objective, and timeframe
    * Format: "using [exact tool sequence] [action_verb] [company_name] [objective] [timeframe]"
    * Example: "using TodayDateTool, SimpleWorkflowTool, DataVizInsightTool forecast HPE closing price next 4 days"
    * This question should be specific to the task and differ from the original overall business question: '{original_business_question}'.
    * IMPORTANT: The tool sequence MUST match the exact order from the task definition.
4.  **Generate Analyst Details:** For each analyst, generate detailed and substantive values for the following fields:
    *   `name_analyst` (e.g., "John Doe")
    *   `age_analyst` (e.g., 30-65)
    *   `title_analyst` - Make this specific and impressive (e.g., "Director of Quantitative Trading Strategies" rather than just "Analyst")
    *   `background_analyst` - This should be comprehensive (3-5 sentences) and include:
        - Educational background (degrees, fields of study)
        - Years of relevant experience
        - Specific expertise related to the business question
        - Technical skills relevant to the tools being used
        - Notable achievements or specializations in the relevant sector
5.  **Output Format:** Structure your entire output as a list of analyst objects conforming *exactly* to the `AnalystList` tool schema. Ensure ALL required fields (`business_question`, `name_analyst`, `age_analyst`, `title_analyst`, `background_analyst`) are present for every analyst. Do NOT generate values for 'director_id' or 'task_id'.

**CRITICAL INSTRUCTIONS:**
- The tool sequence in the business question MUST match the exact order from the task definition
- Extract tools from the task format: TASK([Tool1, Tool2, Tool3], "company", "objective", "timeframe")
- Include ALL tools in the exact order they appear in the task
- Your final output MUST be a valid list of analyst objects ready for the `AnalystList` tool
- Adhere strictly to the schema provided
- Do not add any explanations or introductory text outside the structured list

**EXAMPLE:**
For a task like: TASK([TodayDateTool, SimpleWorkflowTool, DataVizInsightTool], "HPE", "closing price", "next 4 days")

The analyst profile should include:
- business_question: "using TodayDateTool, SimpleWorkflowTool, DataVizInsightTool forecast HPE closing price next 4 days"
- name_analyst: "Michael Chen"
- age_analyst: 42
- title_analyst: "Head of Enterprise Technology Equity Research"
- background_analyst: "MBA from Wharton and MS in Computer Science from Stanford. 18 years of experience analyzing enterprise hardware companies with specialized focus on HPE, Dell, and IBM. Developed and implemented advanced forecasting models using technical indicators and machine learning algorithms. Previously led the technology research division at Goldman Sachs, where he built a reputation for accurate price predictions in the enterprise hardware sector. Expert in data visualization techniques for communicating complex financial trends to institutional investors."

**EXAMPLE OUTPUT FORMAT:**
{sample_json}
""")

    # Get the plan from state
    plan_obj = state.get("plan")
    if plan_obj is None:
        print_debug("Plan is None in create_personas", "Error")
        return {"analysts": AnalystList(analysts=[])}

    plan = HumanMessage(content=plan_obj.model_dump_json())

    # Use structured output model
    analysts = await structured_model.ainvoke([
        system_message,
        plan
    ])

    print_pretty(analysts)
    return {"analysts": analysts}


async def call_asyn_agent(state: DirectorState) -> List[Send]:
    print_step("Send API calls to analyst tool agent", "Send API")
    analyst_states = []
    for analyst in state["analysts"].analysts:
        # Ensure the state passed matches AnalystState
        analyst_state_data = analyst.model_dump()
        # Pass necessary parts of DirectorState if needed by analyst_tool_agent
        analyst_state_data["director_id"] = state.get("director_id")
        analyst_state_data["task_id"] = state.get("task_id")
        analyst_state_data["successful_tools"] = {}
        # Pass the messages field as is
        analyst_state_data["messages"] = []
        analyst_states.append(analyst_state_data)
    # print how many analysts are being sent to the analyst tool agent
    print_step(f"Analysts being sent to analyst tool agent: {len(analyst_states)}", "Analysts being sent to analyst tool agent", "pale_turquoise1", verbose=True)
    return [
        Send("analyst_tool_agent", analyst_state)
        for analyst_state in analyst_states
    ]

def collect_reports(state: DirectorState):
    # completed_reports is now automatically aggregated by the state definition
    # This node can be used for any final processing after aggregation, if needed.
    print_pretty(state.get("completed_reports", []))
    print_pretty(len(state.get("completed_reports", [])))
    # print_pretty(state.get("completed_reports", [])) # Print the collected reports
    # Return an empty dict or specific updates if further processing happens here
    return {}

def send_to_feature_requestor(state: DirectorState):
    print_step("Send to feature requestor", "Send to Feature Requestor")

    # Add this line for debugging:
    print_debug(f"Checking completed_reports: {state.get('completed_reports', [])}", "State Check")
    print_pretty(len(state.get("completed_reports", [])))

    # if feature_requestor contains we need to return feature_requestor
    for report in state.get("completed_reports", []):
        if "<feature_requestor>" in report: #check if feature_requestor is one of the reports
            print_debug("Feature requestor detected, continuing to feature requestor node", "Feature requestor detection")
            return "feature_requestor"

    # This part is reached ONLY if the loop completes without finding the tag in ANY report
    print_debug("No feature requestor detected, continuing to summarize workflow node", "Feature requestor detection")
    return "summarize_workflow"

def summarize_workflow(state: DirectorState) -> DirectorState:
    print_step("Summarize workflow", "Summarize Workflow")

    # Preserve all existing state keys
    result_state = {k: v for k, v in state.items()}

    # Get necessary state values
    business_question = state.get("business_question", "")
    plan = state.get("plan")
    analysts = state.get("analysts")
    completed_reports = state.get("completed_reports", [])

    # Extract task information from plan if available
    tasks = plan.tasks if plan else []
    task_details = "\n".join([f"- {task}" for task in tasks]) if tasks else "None"

    # Extract analyst information if available
    analyst_details = ""
    if analysts and hasattr(analysts, 'analysts'):
        for i, analyst in enumerate(analysts.analysts):
            analyst_details += f"\nANALYST {i+1}:\n"
            analyst_details += f"- Name: {analyst.name_analyst}\n"
            analyst_details += f"- Title: {analyst.title_analyst}\n"
            analyst_details += f"- Background: {analyst.background_analyst}\n"
            analyst_details += f"- Task: {analyst.business_question}\n"

    # Extract report information if available
    report_details = ""
    conclusions = []
    confidence_levels = []

    for i, report in enumerate(completed_reports):
        report_details += f"\nREPORT {i+1}:\n"
        report_details += f"- Question: {report.business_question}\n"
        report_details += f"- Conclusion: {report.conclusion}\n"
        report_details += f"- Confidence: {report.confidence}%\n"
        report_details += f"- Target: {report.target_column}\n"
        report_details += f"- Current Value: {report.current_target_value}\n"
        report_details += f"- Predicted Value: {report.predicted_target_value}\n"

        conclusions.append(report.conclusion)
        confidence_levels.append(report.confidence)

    # Calculate average confidence if reports exist
    avg_confidence = sum(confidence_levels) / len(confidence_levels) if confidence_levels else 0

    # Get most common conclusion if reports exist
    most_common_conclusion = max(set(conclusions), key=conclusions.count) if conclusions else "N/A"

    summarize_message = HumanMessage(content=f'''Please Remember:
BUSINESS QUESTION: {business_question}

PLAN DETAILS:
{task_details}

ANALYST PERSONAS:
{analyst_details}

REPORT OUTCOMES:
{report_details}

PERFORMANCE METRICS:
- Tasks Created: {len(tasks)}
- Analysts Assigned: {len(analysts.analysts) if analysts and hasattr(analysts, 'analysts') else 0}
- Reports Completed: {len(completed_reports)}
- Average Confidence: {avg_confidence:.1f}%
- Dominant Conclusion: {most_common_conclusion}

SELF-IMPROVEMENT DIRECTIVES:
1. Evaluate which specific task types yielded highest-confidence predictions
2. Identify which analyst backgrounds were most effective for this company/sector
3. Compare task structure effectiveness (e.g., forecast vs. compare vs. read)
4. Suggest 3 concrete improvements to task definitions for similar companies
5. Recommend optimal analyst persona compositions for this sector

Store this detailed workflow analysis to optimize future research strategies.''')

    # Update state with new values while preserving other keys
    result_state.update({
        'messages': [summarize_message]
    })

    return result_state

def create_delegate_subgraph() -> StateGraph:
    """Subgraph for delegation operations"""
    subgraph = StateGraph(DirectorState)
    subgraph.add_node("init_graph", init_graph)
    subgraph.add_node("output_memory", output_memory_graph)
    subgraph.add_node("critical_thinking_planning", critical_thinking_planning)
    subgraph.add_node("create_plan", create_plan)
    subgraph.add_node("create_personas", create_personas)
    subgraph.add_node("analyst_tool_agent", simple_agent_tool_graph)
    subgraph.add_node("collect_reports", collect_reports)
    subgraph.add_node("feature_requestor", feature_requestor_graph)
    subgraph.add_node("summarize_workflow", summarize_workflow)

    # Define the flow of the graph
    subgraph.add_edge(START, "init_graph")
    subgraph.add_edge("init_graph", "output_memory")
    subgraph.add_edge("output_memory", "critical_thinking_planning")
    subgraph.add_edge("critical_thinking_planning", "create_plan")
    subgraph.add_edge("create_plan", "create_personas")
    # Change this edge to a conditional edge using call_asyn_agent as the decider
    subgraph.add_conditional_edges(
        "create_personas",
        call_asyn_agent, # This function now returns List[Send]
        ["analyst_tool_agent"] # The target node for the Send commands
    )
    # The analyst_tool_agent node now implicitly goes to collect_reports
    # when all parallel branches launched by Send complete.
    subgraph.add_edge("analyst_tool_agent", "collect_reports")
    subgraph.add_conditional_edges(
        "collect_reports",
        send_to_feature_requestor,
        {"feature_requestor": "feature_requestor", "summarize_workflow": "summarize_workflow"}
    )
    subgraph.add_edge("feature_requestor", "summarize_workflow")
    subgraph.add_edge("summarize_workflow", END)
    return subgraph.compile()

graph = create_delegate_subgraph()

__all__ = ['graph']
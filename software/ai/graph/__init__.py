"""Graph package initialization"""
import os
import importlib
from typing import Dict, Any

_loaded_graphs = None

def load_graphs() -> Dict[str, Any]:
    """Dynamically load all graph modules in the directory"""
    graphs = {}
    current_dir = os.path.dirname(__file__)
    
    for filename in os.listdir(current_dir):
        if filename.endswith('_graph.py'):
            module_name = filename[:-3]  # Remove .py extension
            full_module_name = f"ai.graph.{module_name}"
            
            try:
                module = importlib.import_module(full_module_name)
                graph_name = module_name.replace('_', ' ').title()
                graphs[module_name] = {
                    "name": graph_name,
                    "module": module
                }
            except ImportError as e:
                print(f"Error loading graph module {module_name}: {e}")
                
    return graphs

def get_available_graphs() -> Dict[str, Any]:
    """Get available graphs, loading them if not already loaded"""
    global _loaded_graphs
    if _loaded_graphs is None:
        _loaded_graphs = load_graphs()
    return _loaded_graphs 
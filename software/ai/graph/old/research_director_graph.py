from typing import Dict, Any, List, TypedDict, Sequence, Union, Optional, Literal, Annotated
import asyncio
from rich.console import Console
import os
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langgraph.graph import StateGraph, END
from langgraph.constants import <PERSON><PERSON>
from dotenv import load_dotenv
from typing_extensions import TypedDict
from pydantic import BaseModel, Field, ValidationError
from langchain.output_parsers import PydanticOutputParser
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph.message import add_messages
from langgraph.pregel import RetryPolicy
import time
import re
from tavily import TavilyClient
from db.research_repository import ResearchRepository
from db.research_director_repository import ResearchDirectorRepository
from db.data_loader_repository import DataLoaderRepository
from ai.llm.llm_connect import get_llm_connect
from langchain_core.language_models.chat_models import BaseChatModel
from datetime import datetime
import json
from ai.tools import registry
from features import FEATURE_CLASSES

# Load environment variables
load_dotenv()
tavily = TavilyClient(api_key=os.getenv("TAVILY_API_KEY"))

console = Console()
director_repo = ResearchDirectorRepository()
research_repo = ResearchRepository()

# Constants
MAX_TOOL_ITERATIONS = 2
MAX_TOOL_RETRIES = 2
MAX_NODE_RETRIES = 3
MAX_ANALYSTS = 1
MAX_RETRIES = 3
ANALYSTS_PER_BATCH = 5

# Get tools from registry
tools = registry.get_langchain_tools()

# Create tool node with loaded tools
tool_node = ToolNode(tools=tools)

# Define state schemas
class HypothesisVerdict(BaseModel):
    decision: str = Field(
        description="REJECT or NOT REJECT",
        pattern="^(REJECT|NOT REJECT)$"
    )
    confidence_level: str = Field(
        description="HIGH, MEDIUM, or LOW",
        pattern="^(HIGH|MEDIUM|LOW)$"
    )
    supporting_evidence: List[str] = Field(
        description="List of evidence points supporting the decision"
    )
    statistical_significance: Optional[float] = Field(
        description="P-value or statistical measure if applicable",
        default=None
    )

class DataSufficiencyEvaluation(BaseModel):
    is_sufficient: bool = Field(
        description="Indicates whether the collected data is sufficient to test the hypothesis."
    )
    missing_data: List[str] = Field(
        default_factory=list,
        description="A list of missing data points needed to test the hypothesis."
    )
    reasoning: str = Field(
        description="An explanation of why the data is or is not sufficient."
    )

class ToolState(BaseModel):
    """Track the state of a tool's usage"""
    attempts: int = 0
    last_args: Dict[str, Any] = {}
    success: bool = False
    last_error: Optional[str] = None

class ResearchAnalyst(BaseModel):
    name: str = Field(
        description="Full name of the analyst"
    )
    age: int = Field(
        description="Age of the analyst, typically between 30-65",
        ge=30,
        le=65
    )
    affiliation: str = Field(
        description="Current institution or firm affiliation"
    )
    education: str = Field(
        description="Highest degree and field of study"
    )
    expertise: str = Field(
        description="Primary area of expertise relevant to the research topic"
    )
    experience_years: int = Field(
        description="Years of experience in financial analysis",
        ge=5
    )
    analysis_style: str = Field(
        description="Unique approach to analysis and decision making"
    )
    risk_tolerance: str = Field(
        description="Attitude towards risk (conservative, moderate, aggressive)"
    )
    
    @property
    def persona(self) -> str:
        return f"""
Name: {self.name}
Age: {self.age}
Affiliation: {self.affiliation}
Education: {self.education}
Expertise: {self.expertise}
Experience: {self.experience_years} years
Analysis Style: {self.analysis_style}
Risk Tolerance: {self.risk_tolerance}
"""

class ResearchDirectorState(TypedDict):
    messages: List[Any]
    topic: str
    findings: List[Dict[str, Any]]
    final_analysis: str | None
    director_id: str | None
    task_id: str | None
    assigned_analysts: List[ResearchAnalyst]
    analyst_findings: List[Dict[str, Any]]
    collected_conclusions: str | None
    final_report: str | None
    analysis_complete: bool
    pending_analysts: int
    verdicts: List[Dict[str, Any]]

async def get_director_llm(state: Dict[str, Any], stage: str) -> Optional[BaseChatModel]:
    """Get the LLM instance configured for a specific stage in the director workflow"""
    try:
        console.print(f"\n[magenta]========== GET DIRECTOR LLM ==========[/magenta]")
        console.print(f"[magenta]Stage: {stage}[/magenta]")
        console.print(f"[magenta]State: {state}[/magenta]")
        
        llm_connect = get_llm_connect()
        
        # Get director_id from state
        director_id = state.get('director_id')
        console.print(f"[magenta]Director ID: {director_id}[/magenta]")
        
        if not director_id:
            console.print("[yellow]Warning: No director_id in state, using default LLM[/yellow]")
            return await llm_connect.get_llm()
            
        # Use the llm_connect's get_llm_for_stage method
        llm = await llm_connect.get_llm_for_stage(director_id, stage)
        
        if not llm:
            console.print(f"[yellow]Warning: No LLM configured for director {director_id} and stage {stage}, using default LLM[/yellow]")
            return await llm_connect.get_llm()
            
        return llm
        
    except Exception as e:
        console.print(f"[red]Error getting director LLM: {str(e)}[/red]")
        # Try to get default LLM as fallback
        try:
            return await llm_connect.get_llm()
        except Exception as e2:
            console.print(f"[red]Failed to get default LLM: {str(e2)}[/red]")
            raise ValueError(f"Could not get any LLM instance: {str(e)} / {str(e2)}")

async def generate_analysts(business_question: str, state: Dict[str, Any]) -> List[ResearchAnalyst]:
    """Generate a diverse team of research analysts based on the business question"""
    model = await get_director_llm(state, "assign_analysts")
    if not model:
        raise ValueError("Failed to get LLM model for analyst generation")
        
    # Extract company and ticker from business question
    company_match = re.search(r'invest in ([^(]+) \(([^)]+)\)', business_question)
    if not company_match:
        raise ValueError(f"Could not extract company info from question: {business_question}")
        
    company_name = company_match.group(1).strip()
    ticker = company_match.group(2).strip()
    
    # Create structured output parser
    parser = PydanticOutputParser(pydantic_object=ResearchAnalyst)
    
    system_prompt = f"""As a senior investment director, create {MAX_ANALYSTS} diverse research analysts to evaluate {company_name} ({ticker}).
    Each analyst should have:
    1. Different expertise areas relevant to {company_name}'s business
    2. Varied analysis styles and risk tolerances
    3. Complementary educational backgrounds
    4. Different years of experience (5-40 years)
    5. Unique perspectives on the company
    
    {parser.get_format_instructions()}
    
    The output MUST be a valid JSON object matching the ResearchAnalyst schema exactly."""
    
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"""Create a research analyst to analyze {company_name} ({ticker}).
        The response must be a valid JSON object that can be parsed into a ResearchAnalyst.
        Follow the format instructions exactly.""")
    ]
    
    response = await model.ainvoke(messages)
    analyst = parser.parse(response.content)
    return [analyst]

async def get_director_historical_questions(director_id: str) -> List[Dict[str, Any]]:
    """Get the last 10 business questions for a director"""
    try:
        # Get director's report IDs
        director_repo = ResearchDirectorRepository()
        director = await director_repo.get_director(director_id)
        if not director:
            return []
            
        report_ids = director.get("report_ids", [])[-10:]  # Get last 10 report IDs
        
        # Get reports for these IDs
        research_repo = ResearchRepository()
        historical_questions = []
        
        for report_id in report_ids:
            report = await research_repo.get_analysis(report_id)
            if report and report.get("business_question"):
                historical_questions.append({
                    "date": report.get("started_at"),
                    "question": report.get("business_question"),
                    "ticker": report.get("ticker")
                })
                
        # Sort by date descending
        historical_questions.sort(key=lambda x: x["date"], reverse=True)
        
        # Print historical questions in color
        console.print("\n[sky_blue1]========== HISTORICAL QUESTIONS ==========[/sky_blue1]")
        for idx, q in enumerate(historical_questions, 1):
            console.print(f"\n[sky_blue1]{idx}. Date: {q['date']}[/sky_blue1]")
            console.print(f"[sky_blue1]   Ticker: {q['ticker']}[/sky_blue1]")
            console.print(f"[sky_blue1]   Question: {q['question']}[/sky_blue1]")
        console.print("\n[sky_blue1]========== END HISTORICAL QUESTIONS ==========[/sky_blue1]\n")
        
        return historical_questions
            
    except Exception as e:
        console.print(f"[red]Error getting historical questions: {str(e)}[/red]")
        return []

async def formulate_question_node(state: ResearchDirectorState) -> ResearchDirectorState:
    """Generate the business question to be analyzed using Tavily search"""
    try:
        # Debug print at the start
        console.print(f"[cyan]DEBUG: Initial state in formulate_question_node:[/cyan]")
        console.print(f"[cyan]{state}[/cyan]")
        
        model = await get_director_llm(state, "formulate_question")
        if not model:
            raise ValueError("Failed to get LLM model - check API keys and configuration")
            
        console.print("\n[bold cyan]========== QUESTION FORMULATION ==========[/bold cyan]")
        
        # Update workflow stage - starting Tavily search
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "tavily_search", "in_progress")
            
            # Get director's expertise areas and historical questions
            director_repo = ResearchDirectorRepository()
            director = await director_repo.get_director(state.get("director_id"))
            if not director:
                raise ValueError("Director not found")
            
            # Add report ID to director's document
            await director_repo.add_report_id(state.get("director_id"), state["task_id"])
            
            # Get historical questions
            historical_questions = await get_director_historical_questions(state.get("director_id"))
            
            # Extract previously analyzed tickers and their frequencies
            ticker_frequencies = {}
            for q in historical_questions:
                ticker = q.get("ticker")
                if ticker:
                    ticker_frequencies[ticker] = ticker_frequencies.get(ticker, 0) + 1
            
            # Print ticker frequencies in yellow
            console.print("\n[yellow]Previously Analyzed Companies:[/yellow]")
            for ticker, freq in sorted(ticker_frequencies.items(), key=lambda x: x[1], reverse=True):
                console.print(f"[yellow]{ticker}: analyzed {freq} times[/yellow]")
            
            # Use director's expertise areas for search
            expertise_areas = director.get("expertise", [])
            expertise_query = " ".join(expertise_areas)
            
            # Get market news from Tavily using director's expertise
            news_results = await search_market_news(expertise_query)
        else:
            # Fallback to general market news if no director_id
            news_results = await search_market_news("market trends investment opportunities")
            historical_questions = []
            ticker_frequencies = {}
        
        # Update workflow stage - Tavily search complete
        if state.get("task_id"):
            await repo.update_workflow_stage(state["task_id"], "tavily_search", "completed")
        
        # Format news for the model
        news_summary = "\n".join([
            f"- {item.get('title', '')}: {item.get('content', '')[:200]}..."
            for item in news_results
        ])

        # Print Tavily context in pink
        console.print("\n[bright_magenta]========== TAVILY SEARCH CONTEXT ==========[/bright_magenta]")
        console.print("[bright_magenta]News articles being used for analysis:[/bright_magenta]")
        for idx, item in enumerate(news_results, 1):
            console.print(f"\n[bright_magenta]{idx}. Title: {item.get('title', 'No title')}[/bright_magenta]")
            console.print(f"[bright_magenta]   URL: {item.get('url', 'No URL')}[/bright_magenta]")
            console.print(f"[bright_magenta]   Content: {item.get('content', 'No content')[:200]}...[/bright_magenta]")
        console.print("\n[bright_magenta]========== END TAVILY SEARCH CONTEXT ==========[/bright_magenta]\n")

        # Format historical questions for context
        historical_context = ""
        if historical_questions:
            historical_context = "\nYour previous 10 research questions:\n" + "\n".join([
                f"{idx}. {q['date'].strftime('%Y-%m-%d')}: {q['question']}"
                for idx, q in enumerate(historical_questions, 1)
            ])

        # Example messages to fine-tune the output format
        example_messages = [
            HumanMessage(content="Based on market news, suggest a company to analyze for investment."),
            AIMessage(content="Should we invest in Apple (AAPL)?")
        ]

        # First, have the LLM analyze the articles
        analysis_prompt = f"""Based on the recent market news articles and your historical research questions, identify ONE interesting company that could be a good investment opportunity.

Historical Context:
{historical_context}

Previously Analyzed Companies (AVOID these unless there's a VERY compelling reason):
{', '.join(f'{ticker} ({freq}x)' for ticker, freq in ticker_frequencies.items())}

REQUIREMENTS:
- Choose a company mentioned in the news articles
- Must be publicly traded
- Market cap > $10B
- High trading volume
- Avoid obvious/overvalued companies
- Look for unique opportunities
- DO NOT choose companies you've analyzed frequently (especially {', '.join(ticker for ticker, freq in ticker_frequencies.items() if freq > 1)})
- If you must reanalyze a previous company, provide a STRONG justification based on significant new developments

Your response MUST follow this EXACT format with no additional text:
"Should we invest in Company Name (TICKER)?"

Example valid responses:
"Should we invest in Apple (AAPL)?"
"Should we invest in Microsoft (MSFT)?"

DO NOT include any explanation or additional text. ONLY the question in the exact format shown."""

        messages = [
            SystemMessage(content="""You are a senior investment analyst specializing in identifying unique investment opportunities.
Your task is to avoid repeating analysis of the same companies unless there are EXCEPTIONAL circumstances.
You MUST respond with ONLY the question in this exact format: "Should we invest in Company Name (TICKER)?"
DO NOT include any other text, explanations, or thinking process.
DO NOT use XML tags or other formatting.
Just output the exact question format."""),
            *example_messages,  # Add example messages for fine-tuning
            HumanMessage(content=f"""Here are the recent market news articles:

{news_summary}

{analysis_prompt}""")
        ]
        
        # Get question from the LLM
        question_response = await model.ainvoke(messages)
        if not question_response or not question_response.content:
            raise ValueError("Failed to get investment question from LLM")
            
        question = question_response.content.strip()
        if not question:
            raise ValueError("Generated question is empty")
            
        # Validate question format
        import re
        if not re.match(r'^Should we invest in [A-Za-z\s\.]+ \([A-Z]+\)\?$', question):
            raise ValueError(f"Generated question does not match required format: {question}")
        
        # Add task_id to the question if available
        if state.get("task_id"):
            question = f"{question} (task_id:{state['task_id']})"
        
        console.print(f"[bold blue]Business Question:[/bold blue] {question}")
        
        # Debug print before creating new state
        console.print(f"[cyan]DEBUG: Generated question: {question}[/cyan]")
        
        # Create new state with all required fields
        new_state = {
            **state,
            "business_question": question,  # Add the business question to the state
            "assigned_analysts": [],
            "analyst_findings": [],
            "collected_conclusions": None,
            "final_report": None,
            "analysis_complete": False,
            "pending_analysts": 0,
            "verdicts": []
        }
        
        # Debug print after creating new state
        console.print(f"[cyan]DEBUG: New state after question formulation:[/cyan]")
        console.print(f"[cyan]{new_state}[/cyan]")
        console.print(f"[cyan]DEBUG: business_question in new state: {new_state.get('business_question', 'NOT FOUND')}[/cyan]")
        
        return new_state
        
    except Exception as e:
        console.print(f"[red]Critical error in question formulation: {str(e)}[/red]")
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "tavily_search", "failed")
            await repo.update_analysis_error(state["task_id"], str(e))
        # Kill the graph by raising the exception
        raise ValueError(f"Question formulation failed: {str(e)}")

async def assign_analysts_node(state: ResearchDirectorState) -> Dict:
    """Generate and assign research personas to analyze the question"""
    try:
        model = await get_director_llm(state, "assign_analysts")
        console.print("\n[bold yellow]========== ANALYST ASSIGNMENT ==========[/bold yellow]")
        
        # Debug prints
        console.print(f"[cyan]DEBUG: Full state in assign_analysts_node:[/cyan]")
        console.print(f"[cyan]{state}[/cyan]")
        
        if "business_question" not in state:
            console.print("[red]ERROR: business_question not found in state[/red]")
            console.print(f"[red]Available keys in state: {list(state.keys())}[/red]")
            raise ValueError("business_question not found in state")
            
        console.print(f"[cyan]DEBUG: Business Question: {state.get('business_question', 'NOT FOUND')}[/cyan]")
        
        # Update workflow stage - starting analyst creation
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "creating_analysts", "in_progress")
        
        # Generate dynamic analysts based on the business question
        analysts = await generate_analysts(state["business_question"], state)
        state["assigned_analysts"] = analysts
        state["pending_analysts"] = len(analysts)
        
        for analyst in analysts:
            console.print(f"[yellow]Assigned Analyst:[/yellow]")
            console.print(analyst.persona)
        
        # Update workflow stage - analyst creation complete
        if state.get("task_id"):
            await repo.update_workflow_stage(state["task_id"], "creating_analysts", "completed")
        
        return state
        
    except Exception as e:
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "creating_analysts", "failed")
        console.print(f"[red]Error in analyst assignment: {str(e)}[/red]")
        raise

async def search_market_news(expertise_query: str) -> List[Dict[str, Any]]:
    """Search for recent market news in specific expertise areas using Tavily"""
    try:
        console.print("\n[bright_blue]=== Starting Tavily Search ===[/bright_blue]")
        
        # Craft query that prioritizes expertise and adds some market context
        search_modifiers = [
            "investment opportunities analysis",
            "market analysis trends",
            "stock analysis research",
            "company analysis insights"
        ]
        
        # Rotate through modifiers to add slight variation while keeping expertise primary
        current_modifier = search_modifiers[int(time.time()) % len(search_modifiers)]
        
        # Put expertise first in the query to prioritize it
        query = f"{expertise_query} {current_modifier}"
        console.print(f"[bright_blue]Query: {query}[/bright_blue]")
        
        console.print("[bright_blue]Executing Tavily search...[/bright_blue]")
        results = await asyncio.to_thread(
            tavily.search,
            query=query,
            search_depth="advanced",
            max_results=8,
            include_answer=False,
            include_raw_content=True,
            sort_by="relevance" if int(time.time()) % 2 == 0 else "date",
            include_domains=[
                "seekingalpha.com",
                "finance.yahoo.com",
                "investors.com",
                "fool.com",
                "bloomberg.com",
                "reuters.com",
                "marketwatch.com",
                "barrons.com",
                "ft.com",
                "wsj.com",
                "cnbc.com",
                "investing.com"
            ]
        )
        console.print("[bright_blue]✓ Tavily search completed successfully[/bright_blue]")
        
        # Extract and format results
        news_items = results.get("results", [])
        console.print(f"\n[bright_blue]Found {len(news_items)} news items from Tavily:[/bright_blue]")
        for idx, item in enumerate(news_items):
            console.print(f"\n[bright_blue]{idx + 1}. {item.get('title', 'No title')}[/bright_blue]")
            console.print(f"[bright_blue]   URL: {item.get('url', 'No URL')}[/bright_blue]")
            console.print(f"[bright_blue]   Content length: {len(item.get('content', ''))}[/bright_blue]")
        
        return news_items
            
    except Exception as e:
        console.print(f"\n[red]✗ Search failed: {str(e)}[/red]")
        return [{
            "title": "Market Analysis Required",
            "content": f"Current market conditions in {expertise_query} require careful analysis.",
            "url": None
        }]

async def run_analysis_node(state: ResearchDirectorState) -> Dict:
    """Run individual analyst research in parallel"""
    try:
        console.print("\n[bold magenta]========== RUNNING ANALYSIS ==========[/bold magenta]")
        
        # Update workflow stage - starting research
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "researching", "in_progress")
        
        # Create a semaphore to limit concurrent API calls
        sem = asyncio.Semaphore(5)  # Allow up to 5 concurrent analysts
        
        async def run_analyst_subgraph_with_error_handling(analyst):
            """Run analysis for a single analyst with rate limiting and retries"""
            async with sem:
                try:
                    console.print(f"\n[magenta]Starting analysis for {analyst.name}[/magenta]")
                    
                    result = await run_analyst_subgraph(
                        analyst_info=analyst.dict(),
                        task_id=state.get("task_id"),
                        director_id=state.get("director_id")
                    )
                    
                    if not result:
                        raise ValueError("No result returned from analyst subgraph")
                    
                    return {
                        "analyst": analyst.name,
                        "analysis": result
                    }
                    
                except Exception as e:
                    console.print(f"[red]Error in analysis for {analyst.name}: {str(e)}[/red]")
                    console.print(f"[red]DEBUG: Full error details: {repr(e)}[/red]")
                    return {
                        "analyst": analyst.name,
                        "analysis": {
                            "error": str(e),
                            "final_analysis": f"Analysis failed: {str(e)}",
                            "verdict": {
                                "decision": "REJECT",
                                "confidence_level": "LOW",
                                "supporting_evidence": [f"Analysis failed due to error: {str(e)}"],
                                "statistical_significance": None
                            },
                            "findings": [],
                            "messages": []
                        }
                    }
        
        # Run all analyst analyses in parallel
        analysis_tasks = [
            run_analyst_subgraph_with_error_handling(analyst) 
            for analyst in state["assigned_analysts"]
        ]
        
        analysis_results = await asyncio.gather(*analysis_tasks)
        
        state["analyst_findings"] = analysis_results
        state["pending_analysts"] = 0
        state["analysis_complete"] = True
        
        # Update workflow stage - research complete
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "researching", "completed")
        else:
            console.print("[yellow]No task_id found, skipping workflow stage update[/yellow]")
        
        return state
        
    except Exception as e:
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "researching", "failed")
        console.print(f"[red]Error in analysis execution: {str(e)}[/red]")
        raise

def should_continue(state: ResearchDirectorState) -> Union[str, Sequence[str]]:
    """Determine if all analyses are complete"""
    if state["analysis_complete"]:
        return "collect_conclusions"
    return "run_analysis"

async def collect_conclusions_node(state: ResearchDirectorState) -> Dict:
    """Collect and organize conclusions from all analysts"""
    try:
        model = await get_director_llm(state, "collect_conclusions")
        console.print("\n[bold green]========== COLLECTING CONCLUSIONS ==========[/bold green]")
        
        # Update workflow stage - starting collecting findings
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "collecting_findings", "in_progress")
        
        # Extract verdicts from analyst findings
        verdicts = []
        for finding in state["analyst_findings"]:
            if "verdict" in finding["analysis"]:
                verdicts.append({
                    "analyst": finding["analyst"],
                    "verdict": finding["analysis"]["verdict"]
                })
        state["verdicts"] = verdicts
        
        system_prompt = """As a senior investment manager, review the analyses and verdicts from different research analysts.
        For each analyst:
        1. Identify their key findings
        2. Note their investment recommendation
        3. Review their hypothesis verdict (REJECT/NOT REJECT)
        4. Highlight unique insights from their perspective
        
        Organize the information clearly by analyst."""
        
        findings_text = "\n\n".join([
            f"""Analyst: {finding['analyst']}
            Analysis: {finding['analysis']['final_analysis']}
            Verdict: {finding['analysis'].get('verdict', 'No verdict provided')}"""
            for finding in state["analyst_findings"]
        ])
        
        messages = [
            HumanMessage(content=f"""{system_prompt}
            
            Business Question: {state['business_question']}
            
            Analyst Findings:
            {findings_text}""")
        ]
        
        response = await retry_with_backoff(model.ainvoke, messages)
        collected_conclusions = response.content.strip()
        
        console.print("[bold green]Conclusions collected and organized[/bold green]")
        console.print(collected_conclusions)
        
        state["collected_conclusions"] = collected_conclusions
        
        # Update workflow stage - collecting findings complete
        if state.get("task_id"):
            await repo.update_workflow_stage(state["task_id"], "collecting_findings", "completed")
        
        return state
        
    except Exception as e:
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "collecting_findings", "failed")
        console.print(f"[red]Error in collecting conclusions: {str(e)}[/red]")
        raise

async def summarize_report_node(state: ResearchDirectorState) -> Dict:
    """Generate final summary report with investment recommendation"""
    try:
        model = await get_director_llm(state, "summarize_report")
        console.print("\n[bold blue]========== FINAL REPORT ==========[/bold blue]")
        
        # Initialize repo at the start of the function
        repo = ResearchRepository()
        
        system_prompt = """As a senior investment manager, synthesize the various analyst perspectives into a final recommendation.
        
        Your report should include:
        1. Executive Summary
        2. Key Points of Agreement/Disagreement
        3. Risk Factors
        4. Final Investment Recommendation
        5. Confidence Level (High/Medium/Low)"""
        
        messages = [
            HumanMessage(content=f"""{system_prompt}
            
            Business Question: {state['business_question']}
            
            Collected Analysis:
            {state.get('collected_conclusions', 'No conclusions collected')}""")
        ]
        
        response = await retry_with_backoff(model.ainvoke, messages)
        final_report = response.content.strip()
        
        console.print("[bold blue]Final Investment Report:[/bold blue]")
        console.print(final_report)
        
        state["final_report"] = final_report
        
        # Update workflow stage - writing report complete
        if state.get("task_id"):
            await repo.update_workflow_stage(state["task_id"], "writing_report", "completed")
        
        return state
        
    except Exception as e:
        if state.get("task_id"):
            repo = ResearchRepository()
            await repo.update_workflow_stage(state["task_id"], "writing_report", "failed")
        console.print(f"[red]Error in report summarization: {str(e)}[/red]")
        raise

async def save_final_report_node(state: ResearchDirectorState) -> ResearchDirectorState:
    """Save final research report to MongoDB"""
    try:
        console.print("\n[bold green]========== FINAL REPORT ==========[/bold green]")
        console.print("[bold green]Final Investment Report:[/bold green]")
        
        # Extract task_id from business_question if available
        import re
        task_id = None
        task_id_match = re.search(r'task_id:(\w+)', state['business_question'])
        if task_id_match:
            task_id = task_id_match.group(1)
        else:
            task_id_match = re.search(r'\(task:\s*(\w+)\)', state['business_question'])
            if task_id_match:
                task_id = task_id_match.group(1)
        
        console.print(f"\n[bold yellow]DEBUG: Extracted task_id: {task_id}[/bold yellow]")
        
        if task_id:
            # Update workflow stage - starting completion
            repo = ResearchRepository()
            await repo.update_task(state["task_id"], {
                "status": "failed",
                "completed_at": datetime.now(),
                "error": str(e)
            })
            
            # Prepare result data
            result_data = {
                "business_question": state['business_question'],
                "conclusions": state['collected_conclusions'],
                "individual_analyses": state['analyst_findings'],
                "verdicts": state['verdicts']  # Include structured verdicts
            }
            
            # Save final results
            await repo.update_task(
                task_id=task_id,
                update_data={
                    "status": "completed",
                    "completed_at": datetime.now(),
                    "metadata": {
                        "completion_time": datetime.now()
                    }
                }
            )
            
            console.print("[green]Final report saved to MongoDB![/green]")
        else:
            console.print("[yellow]Warning: No task_id found in business_question, skipping MongoDB save[/yellow]")
            console.print("[yellow]Business question content:[/yellow]")
            console.print(state['business_question'])
        
        return state
        
    except Exception as e:
        if task_id:
            repo = ResearchRepository()
            await repo.update_task(task_id, {
                "status": "failed",
                "completed_at": datetime.now(),
                "error": str(e)
            })
        console.print(f"[red]Error in saving final report: {str(e)}[/red]")
        console.print(f"[red]Full error details: {repr(e)}[/red]")
        raise

async def retry_with_backoff(func, *args, max_retries=3, initial_delay=1):
    """Retry an async function with exponential backoff"""
    delay = initial_delay
    last_exception = None
    
    for attempt in range(max_retries):
        try:
            return await func(*args)
        except Exception as e:
            last_exception = e
            if attempt < max_retries - 1:
                await asyncio.sleep(delay)
                delay *= 2
                continue
            raise last_exception 

# Build the graph
workflow = StateGraph(ResearchDirectorState)

# Add nodes
workflow.add_node("formulate_question", formulate_question_node)
workflow.add_node("assign_analysts", assign_analysts_node)
workflow.add_node("run_analysis", run_analysis_node)
workflow.add_node("collect_conclusions", collect_conclusions_node)
workflow.add_node("summarize_report", summarize_report_node)
workflow.add_node("save_final_report", save_final_report_node)

# Set entry point
workflow.set_entry_point("formulate_question")

# Add edges
workflow.add_edge("formulate_question", "assign_analysts")
workflow.add_edge("assign_analysts", "run_analysis")
workflow.add_conditional_edges(
    "run_analysis",
    should_continue,
    {
        "run_analysis": "run_analysis",
        "collect_conclusions": "collect_conclusions"
    }
)
workflow.add_edge("collect_conclusions", "summarize_report")
workflow.add_edge("summarize_report", "save_final_report")
workflow.add_edge("save_final_report", END)

# Compile the graph
graph = workflow.compile()

# Export only the graph
__all__ = ['graph'] 
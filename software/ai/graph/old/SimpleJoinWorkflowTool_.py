from ai.tools.base_tool import BaseTool
from typing import Dict, Any, List, Optional
from pydantic import Field, BaseModel, validator
import pandas as pd
import asyncio
import datetime
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from main import StockAnalysisApp
from data import registry
from db.report_repository import ReportFactory, ReportRepository
from rich.console import Console
from rich.panel import Panel
from api.database import loader_collection
import json
import traceback
import numpy as np
import re

console = Console()

class JoinWorkflowSchema(BaseModel):
    """Schema for SimpleJoinWorkflowTool input parameters"""
    business_question: str = Field(
        ...,
        description="A specific analysis question that requires joining data from two sources. The question should reference the primary ticker in parentheses. It can either involve comparing two different tickers or analyzing a single ticker using two distinct data sources (e.g., price and sentiment, or data with different time granularities).",
        min_length=10,
        examples=[
            "How does the news sentiment for Tesla (TSLA) affect its stock price movements over the next week?", # Example of single ticker, multiple sources
            "How does the VIX index influence Apple's (AAPL) stock price volatility?", # Example comparing different entities
            "How does Apple's (AAPL) quarterly revenue announcements impact its daily stock price?", # Example of single ticker, different granularities (daily vs quarterly)
            "Using Microsoft's (MSFT) price trends, can we predict Amazon's (AMZN) future performance for the next 5 days?",
            "Does the trading volume of AMD (AMD) have any leading correlation with NVIDIA's (NVDA) price movements?"
        ]
    )

class FeatureSelection(BaseModel):
    """Schema for feature selection"""
    selected_features: List[str] = Field(
        default_factory=list,
        description="List of selected features for analysis"
    )

class DataSourceJoinSelection(BaseModel):
    """Schema for LLM output when selecting data sources and join type.

    Attributes:
        main_loader: The name of the selected primary data loader.
        auxiliary_loader: The name of the selected secondary data loader.
        join_type: The selected join type (e.g., 'inner', 'left', 'forward_fill').
        reasoning: The LLM's reasoning for the selection.
    """
    main_loader: str = Field(..., description="Selected primary data loader name.")
    auxiliary_loader: str = Field(..., description="Selected secondary data loader name.")
    join_type: str = Field(
        ...,
        description="Selected join type (must be one of 'inner', 'left', 'right', 'outer', 'forward_fill')."
    )
    reasoning: str = Field(..., description="Reasoning for selecting these loaders and join type.")

    @validator('join_type')
    def check_join_type(cls, v):
        valid_types = ['inner', 'left', 'right', 'outer', 'forward_fill']
        if v not in valid_types:
            raise ValueError(f"Invalid join type: {v}. Must be one of {valid_types}")
        return v

class JoinTypeSelection(BaseModel):
    """Schema for join type selection"""
    join_type: str = Field(
        default="inner",
        description="Type of join to perform"
    )
    reasoning: str = Field(
        default="",
        description="Explanation for join type selection"
    )

class JoinColumnSelection(BaseModel):
    """Schema for join column selection"""
    join_columns: List[str] = Field(
        default_factory=list,
        description="List of columns to join on"
    )
    reasoning: str = Field(
        default="",
        description="Explanation for join column selection"
    )

class SimpleJoinWorkflowTool(BaseTool):
    """A tool for creating advanced stock forecasting reports using joined data from multiple sources.
    This tool allows combining data from different sources (e.g., price data with sentiment data)
    or analyzing relationships between different stocks."""
    
    name: str = Field("SimpleJoinWorkflowTool", description="The name of the tool")
    description: str = Field(
        "A tool for creating stock forecasting reports by joining multiple data sources. Supports combining price data, "
        "technical indicators, sentiment analysis and cross-company analysis to identify relationships and patterns. "
        "Uses structured workflow to select data sources, join types, and features based on business questions.",
        description="A detailed description of the tool's functionality"
    )
    category: str = Field(default="analysis", description="Tool category")
    version: str = Field(default="1.0.0", description="Tool version")
    args_schema: type = JoinWorkflowSchema
    
    async def get_llm(self, stage: str) -> Optional[BaseChatModel]:
        """Get LLM instance for a specific stage"""
        try:
            from ai.llm.llm_connect import get_llm_connect
            llm_connect = get_llm_connect()
            model = await llm_connect.get_llm()
            if not model:
                raise ValueError(f"Failed to get LLM for stage {stage}")
            return model
        except Exception as e:
            raise

    async def get_active_data_loaders(self) -> Dict[str, str]:
        """Get list of active data loaders and their descriptions from MongoDB"""
        try:
            console.print(Panel(
                "[blue]Querying active data loaders from database...[/blue]",
                title="SimpleJoinWorkflowTool: Database Query",
                border_style="gold1"
            ))
            
            cursor = loader_collection.find({"is_active": True})
            data_loaders = {}
            
            async for loader in cursor:
                if "name" in loader and "description" in loader:
                    data_loaders[loader["name"]] = loader["description"]
                    
            if not data_loaders:
                raise ValueError("No active data loaders found. Please check database configuration.")
            
            # Show available loaders
            console.print(Panel(
                "[bold blue]Found Data Loaders:[/bold blue]\n" +
                "\n".join([f"• [cyan]{name}[/cyan]" for name in data_loaders.keys()]),
                title="SimpleJoinWorkflowTool: Available Loaders",
                border_style="gold1"
            ))
                
            return data_loaders
            
        except Exception as e:
            console.print(Panel(
                f"[bold red]Error:[/bold red] {str(e)}",
                title="SimpleJoinWorkflowTool: Data Loader Error",
                border_style="gold1"
            ))
            raise ValueError(f"Failed to fetch data loaders: {str(e)}")

    async def extract_tickers(self, business_question: str) -> tuple[str, str]:
        """Extract ticker symbols from the business question. If only one ticker is found,
        intelligently determine a related ticker for comparison."""
        try:
            # Use regex to extract tickers from parentheses
            ticker_matches = re.findall(r'\(([A-Za-z]+)\)', business_question)
            
            if not ticker_matches:
                raise ValueError("No ticker symbols found. Please include at least one ticker in parentheses.")
            
            main_ticker = ticker_matches[0]
            
            # If only one ticker is found, determine a related ticker
            if len(ticker_matches) == 1:
                console.print(Panel(
                    f"[bold blue]Single Ticker Detected:[/bold blue] [cyan]{main_ticker}[/cyan]\n"
                    f"[yellow]Determining appropriate join operation...[/yellow]",
                    title="SimpleJoinWorkflowTool: Join Planning",
                    border_style="gold1"
                ))
                
                # Determine if we should use same ticker with different data sources
                # or find a related ticker in the same industry
                try:
                    auxiliary_ticker = await self.determine_related_ticker(main_ticker, business_question)
                except Exception as e:
                    # Default to same ticker if error finding related ticker
                    auxiliary_ticker = main_ticker
                    console.print(f"[yellow]Using same ticker for different data sources: {main_ticker}[/yellow]")
            else:
                # Use the second ticker found if multiple are provided
                auxiliary_ticker = ticker_matches[1]
            
            # Show results
            console.print(Panel(
                f"[bold blue]Ticker Configuration:[/bold blue] "
                f"Main: [cyan]{main_ticker}[/cyan], Auxiliary: [cyan]{auxiliary_ticker}[/cyan]",
                title="SimpleJoinWorkflowTool: Ticker Results",
                border_style="gold1"
            ))
            
            return main_ticker, auxiliary_ticker
            
        except Exception as e:
            console.print(Panel(
                f"[bold red]Error:[/bold red] {str(e)}",
                title="SimpleJoinWorkflowTool: Ticker Extraction Error",
                border_style="gold1"
            ))
            raise ValueError(f"Failed to extract tickers: {str(e)}")

    async def determine_related_ticker(self, main_ticker: str, business_question: str) -> str:
        """Determine a related ticker for comparison based on the main ticker and business question."""
        try:
            # Get industry mapping from a database or use a model to determine industry relationships
            # This is just for demonstration - in a real implementation, you would use a more sophisticated approach
            model = await self.get_llm("ticker_selection")
            
            messages = [
                SystemMessage(content=f"""Analyze the business question and determine the most appropriate ticker to compare with {main_ticker}.
                
                Consider:
                1. Industry competitors
                2. Supply chain relationships
                3. Market correlations
                
                Return ONLY the ticker symbol (e.g., AAPL, MSFT) without any explanation.
                The ticker should be relevant to answering the business question."""),
                HumanMessage(content=f"""Business Question: {business_question}
                Main Ticker: {main_ticker}
                
                What is the most appropriate ticker to compare with {main_ticker}?""")
            ]
            
            response = await model.ainvoke(messages)
            related_ticker = response.content.strip().upper()
            
            # Validate that we got something that looks like a ticker
            if not re.match(r'^[A-Z]{1,5}$', related_ticker):
                # Fall back to a default industry competitor if invalid
                if main_ticker == "TSLA":
                    related_ticker = "F"  # Ford as Tesla competitor
                elif main_ticker == "AAPL":
                    related_ticker = "MSFT"  # Microsoft as Apple competitor
                else:
                    related_ticker = main_ticker  # Fall back to same ticker
            
            console.print(Panel(
                f"[bold blue]Related Ticker Analysis:[/bold blue]\n"
                f"• Selected [cyan]{related_ticker}[/cyan] to compare with [cyan]{main_ticker}[/cyan]\n"
                f"• This enables cross-company analysis relevant to the business question",
                title="SimpleJoinWorkflowTool: Ticker Relationship",
                border_style="gold1"
            ))
            
            return related_ticker
            
        except Exception as e:
            console.print(f"[yellow]Error determining related ticker: {str(e)}. Using same ticker.[/yellow]")
            return main_ticker

    async def select_data_sources(self, active_loaders: Dict[str, str], main_ticker: str, auxiliary_ticker: str, business_question: str) -> DataSourceJoinSelection:
        """Select data sources and join type using LLM with structured output."""
        try:
            if not active_loaders:
                raise ValueError("No active data loaders available")

            # 1. Print available loaders and descriptions
            loader_list_str = "\n".join([f"• [cyan]{name}[/cyan]: {desc}" for name, desc in active_loaders.items()])
            console.print(Panel(
                f"[bold blue]Available Data Loaders:[/bold blue]\n{loader_list_str}",
                title="SimpleJoinWorkflowTool: Loader Options",
                border_style="gold1"
            ))

            # Prepare prompt for LLM
            loader_options_str = "\n".join([f"- {name}: {desc}" for name, desc in active_loaders.items()])
            valid_join_types = ['inner', 'left', 'right', 'outer', 'forward_fill']
            join_options_str = "\n".join([f"- {jt}" for jt in valid_join_types])

            prompt = f"""You need to select the optimal main data loader, auxiliary data loader, and join type for the given business question and tickers.

Business Question: {business_question}
Main Ticker: {main_ticker}
Auxiliary Ticker: {auxiliary_ticker}

Available Data Loaders:
{loader_options_str}

Available Join Types:
{join_options_str}

Considerations:
- If main_ticker and auxiliary_ticker are the same, select two *different* loaders if possible, choosing loaders whose descriptions suggest different data types or granularities (e.g., daily price vs. sentiment, daily vs. yearly).
- If tickers are different, often selecting the *same* loader type for both is best for comparison, unless the question implies using different data types.
- Choose 'forward_fill' join type primarily when joining data for the *same ticker* from sources with different time frequencies (e.g., daily prices and quarterly fundamentals, daily prices and hourly news sentiment). The loader descriptions might hint at the frequency.
- For standard joins (inner, left, right, outer), consider which dataset should be primary (left) based on the question.

Output ONLY a JSON object matching the following schema:
{{ "main_loader": "string", "auxiliary_loader": "string", "join_type": "string", "reasoning": "string" }}
"""

            # Get LLM and structured model
            model = await self.get_llm("select_data_sources")
            structured_model = model.with_structured_output(DataSourceJoinSelection)

            console.print(Panel(
                f"[bold blue]Selecting data sources and join type for:[/bold blue]\n" 
                f"• Main: {main_ticker}\n" 
                f"• Auxiliary: {auxiliary_ticker}\n" 
                f"• Question: {business_question}",
                title="SimpleJoinWorkflowTool: LLM Selection Task",
                border_style="cyan"
            ))

            # Invoke LLM
            response = await structured_model.ainvoke([SystemMessage(content=prompt)])

            # 2. Print the selected configuration and reasoning
            console.print(Panel(
                f"[bold blue]LLM Selection Results:[/bold blue]\n"
                f"• Main Loader: [cyan]{response.main_loader}[/cyan] (for {main_ticker})\n"
                f"• Auxiliary Loader: [cyan]{response.auxiliary_loader}[/cyan] (for {auxiliary_ticker})\n"
                f"• Join Type: [cyan]{response.join_type}[/cyan]\n"
                f"[bold blue]Reasoning:[/bold blue] {response.reasoning}",
                title="SimpleJoinWorkflowTool: Selected Configuration",
                border_style="gold1"
            ))

            return response

        except Exception as e:
            console.print(Panel(
                f"[bold red]Error:[/bold red] {str(e)}",
                title="SimpleJoinWorkflowTool: Data Source Selection Error",
                border_style="gold1"
            ))
            raise ValueError(f"Failed to select data sources: {str(e)}")

    async def select_join_columns(self, df_main, df_aux):
        """
        Select the columns to join on based on the structure of the datasets.
        Uses LLM to determine the best columns to join on.
        """
        # Find common columns between the two datasets
        main_columns = df_main.columns.tolist()
        aux_columns = df_aux.columns.tolist()
        common_columns = [col for col in main_columns if col in aux_columns]
        
        # Print available columns
        console.print(Panel(
            f"[bold blue]Available Columns:[/bold blue]\n"
            f"• Main Dataset: [cyan]{', '.join(main_columns)}[/cyan]\n"
            f"• Auxiliary Dataset: [cyan]{', '.join(aux_columns)}[/cyan]\n"
            f"• Common Columns: [yellow]{', '.join(common_columns)}[/yellow]",
            title="SimpleJoinWorkflowTool: Column Analysis for Join",
            border_style="gold1"
        ))

        if not common_columns:
            raise ValueError("No common columns found between the main and auxiliary datasets. Cannot perform join.")
        
        # Get sample data for the LLM to analyze
        sample_main = df_main.head(3)
        sample_aux = df_aux.head(3)
        
        # Only show first few columns to avoid overwhelming the context
        max_cols = 5
        sample_main_str = sample_main.iloc[:, :min(max_cols, len(main_columns))].to_string()
        sample_aux_str = sample_aux.iloc[:, :min(max_cols, len(aux_columns))].to_string()
        
        # If datasets have more columns than shown in sample, indicate that
        main_cols_note = f"(showing {min(max_cols, len(main_columns))} of {len(main_columns)} columns)" if len(main_columns) > max_cols else ""
        aux_cols_note = f"(showing {min(max_cols, len(aux_columns))} of {len(aux_columns)} columns)" if len(aux_columns) > max_cols else ""
        
        # Create prompt for LLM
        prompt = f"""You need to select the most appropriate column(s) to join two financial datasets.

Main Dataset Sample {main_cols_note}:
{sample_main_str}

Auxiliary Dataset Sample {aux_cols_note}:
{sample_aux_str}

Common Columns (available in both datasets): {common_columns}

Please select the best column(s) to join these datasets on, considering:
1. The column should exist in both datasets
2. The column should uniquely identify records (like a date, ID, or key)
3. The values should be comparable (same data type and scale)
4. For time series data, date-related columns are usually good join keys

Respond with a JSON object containing:
1. "join_columns": Array of column names to join on (must exist in common_columns)
2. "reasoning": Your detailed reasoning for selecting these columns

JSON FORMAT ONLY - NO OTHER TEXT:
{{
  "join_columns": ["column_name"],
  "reasoning": "Your reasoning here"
}}
"""
        
        console.print(f"[bold cyan]Selecting join columns for datasets...[/bold cyan]")
        
        # Get LLM response
        model = await self.get_llm("join_column_selection")
        messages = [
            SystemMessage(content=prompt),
            HumanMessage(content="Select the most appropriate columns to join these datasets.")
        ]
        response = await model.ainvoke(messages)
        response_text = response.content.strip()
        
        # Print raw response for debugging
        console.print(f"[dim]LLM Raw Response: {response_text}[/dim]")
        
        # Parse the response to extract the join columns and reasoning
        try:
            # Try to directly parse as JSON
            content = json.loads(response_text)
            
            # Validate that we have the expected keys
            if not all(key in content for key in ["join_columns", "reasoning"]):
                raise ValueError("LLM response missing required keys: join_columns and reasoning")
            
            # Validate that the join columns exist in both datasets
            join_columns = content["join_columns"]
            if not isinstance(join_columns, list):
                join_columns = [join_columns]
            
            # Validate each selected column exists in common columns
            for col in join_columns:
                if col not in common_columns:
                    raise ValueError(f"Selected join column '{col}' is not present in both datasets.")
            
            reasoning = content["reasoning"]
            
            # Display the selection and reasoning
            console.print(Panel(
                f"[bold blue]Join Column Selection:[/bold blue]\n"
                f"• Selected Column(s): [cyan]{', '.join(join_columns)}[/cyan]\n"
                f"[bold blue]Reasoning:[/bold blue] {reasoning}",
                title="SimpleJoinWorkflowTool: LLM Join Column Decision",
                border_style="gold1"
            ))
            
            return join_columns[0] if len(join_columns) == 1 else join_columns
            
        except json.JSONDecodeError:
            raise ValueError(f"Failed to parse LLM response as JSON: {response_text}")
        except Exception as e:
            raise ValueError(f"Error selecting join columns: {str(e)}")

    async def select_join_type(self, main_ds_name: str, aux_ds_name: str) -> str:
        """
        Selects an appropriate join type based on the datasets being joined.
        Uses LLM to determine the best join type.
        """
        # Available join types
        valid_join_types = ["inner", "left", "right", "outer", "forward_fill"]
        
        prompt = f"""You need to select the appropriate join type to use when joining two datasets:
1. Main dataset: {main_ds_name}
2. Auxiliary dataset: {aux_ds_name}

Please analyze the dataset names and determine which join type would be most appropriate.

Available join types:
- inner: Returns records that have matching values in both tables
- left: Returns all records from the left table, and the matched records from the right table
- right: Returns all records from the right table, and the matched records from the left table
- outer: Returns all records when there is a match in either left or right table
- forward_fill: Time-series join. For each row in the left table, matches the most recent row from the right table whose join key (usually Date) is less than or equal to the left's join key. Fills forward data from the auxiliary source onto the main source based on the nearest preceding timestamp. Ideal for merging time-series data with different frequencies.

Consider using 'forward_fill' when analyzing the same ticker with data from two sources that have different time intervals (e.g., daily price data joined with hourly sentiment data).

Respond with a JSON object containing:
1. "join_type": The selected join type (must be one of: {', '.join(valid_join_types)})
2. "reasoning": Your reasoning for selecting this join type

JSON FORMAT ONLY - NO OTHER TEXT:
{{
  "join_type": "selected_join_type",
  "reasoning": "Your reasoning here"
}}
"""
        console.print(f"[bold cyan]Selecting join type for {main_ds_name} and {aux_ds_name}...[/bold cyan]")
        
        # Get LLM response
        model = await self.get_llm("join_type_selection")
        messages = [
            SystemMessage(content=prompt),
            HumanMessage(content="Select the most appropriate join type for these datasets.")
        ]
        response = await model.ainvoke(messages)
        response_text = response.content.strip()
        
        # Print raw response for debugging
        console.print(f"[dim]LLM Raw Response: {response_text}[/dim]")
        
        # Parse the response to extract the join type and reasoning
        try:
            # Try to directly parse as JSON
            content = json.loads(response_text)
            
            # Validate that we have the expected keys
            if not all(key in content for key in ["join_type", "reasoning"]):
                raise ValueError("LLM response missing required keys: join_type and reasoning")
            
            # Validate that the join type is valid
            join_type = content["join_type"].lower()
            if join_type not in valid_join_types:
                raise ValueError(f"Invalid join type '{join_type}'. Must be one of: {', '.join(valid_join_types)}")
            
            reasoning = content["reasoning"]
            
            # Display the selection and reasoning
            console.print(f"[green]Selected join type: [bold]{join_type}[/bold][/green]")
            console.print(f"[green]Reasoning: {reasoning}[/green]")
            
            return join_type
            
        except json.JSONDecodeError:
            raise ValueError(f"Failed to parse LLM response as JSON: {response_text}")
        except Exception as e:
            raise ValueError(f"Error selecting join type: {str(e)}")

    async def perform_join(self, left_loader: str, right_loader: str, main_ticker: str, auxiliary_ticker: str, 
                         join_type: str) -> pd.DataFrame:
        """Perform the join operation between two data sources."""
        try:
            # Validate inputs
            if not main_ticker or not auxiliary_ticker:
                raise ValueError(f"Invalid tickers: {main_ticker}, {auxiliary_ticker}. Valid stock tickers are required.")
            
            # Initialize loaders and get data
            main_loader_class = registry.get_loader_class(left_loader)
            auxiliary_loader_class = registry.get_loader_class(right_loader)
            
            if not main_loader_class or not auxiliary_loader_class:
                raise ValueError(f"One or more data loaders not found: {left_loader}, {right_loader}")
            
            # Load data for main and auxiliary tickers
            try:
                main_data = main_loader_class(main_ticker).load_historical_data()
                auxiliary_data = auxiliary_loader_class(auxiliary_ticker).load_historical_data()
                
                if main_data is None or main_data.empty:
                    raise ValueError(f"No data available for {main_ticker} with {left_loader}")
                if auxiliary_data is None or auxiliary_data.empty:
                    raise ValueError(f"No data available for {auxiliary_ticker} with {right_loader}")
            except Exception as e:
                raise ValueError(f"Failed to load data: {str(e)}")
            
            # Print brief data structure summary
            console.print(Panel(
                f"[bold blue]Data Summary:[/bold blue]\n"
                f"• {main_ticker}: {main_data.shape[0]} rows, {main_data.shape[1]} columns\n"
                f"• {auxiliary_ticker}: {auxiliary_data.shape[0]} rows, {auxiliary_data.shape[1]} columns",
                title="SimpleJoinWorkflowTool: Data Overview",
                border_style="gold1"
            ))
            
            # Reset index to columns if DatetimeIndex
            if isinstance(main_data.index, pd.DatetimeIndex):
                main_data = main_data.reset_index(names=['Date'])
            if isinstance(auxiliary_data.index, pd.DatetimeIndex):
                auxiliary_data = auxiliary_data.reset_index(names=['Date'])
            
            if join_type == "forward_fill":
                console.print(Panel(
                    f"[bold blue]Performing Forward Fill Join (merge_asof)...[/bold blue]",
                    title="SimpleJoinWorkflowTool: Join Operation",
                    border_style="gold1"
                ))
                # Ensure Date column exists and is datetime, then set as index
                try:
                    for df, name in [(main_data, 'main_data'), (auxiliary_data, 'auxiliary_data')]:
                        if 'Date' not in df.columns:
                            raise ValueError(f"'Date' column missing in {name}")
                        df['Date'] = pd.to_datetime(df['Date'])
                        df.set_index('Date', inplace=True)
                except Exception as e:
                    raise ValueError(f"Error preparing Date index for merge_asof: {e}")

                # Sort both dataframes by index (Date)
                try:
                    main_data = main_data.sort_index()
                    auxiliary_data = auxiliary_data.sort_index()
                except Exception as e:
                    raise ValueError(f"Error sorting data by date for merge_asof: {e}")

                # Perform as-of merge
                try:
                    joined_data = pd.merge_asof(
                        main_data,
                        auxiliary_data,
                        left_index=True,
                        right_index=True,
                        direction='forward', # Forward fill
                        suffixes=('', '_sub') # Suffix for overlapping columns from right_df
                    )
                    joined_data = joined_data.reset_index() # Reset index to keep Date as column
                    join_columns = ['Date'] # Explicitly set join_columns for reporting
                except Exception as e:
                    raise ValueError(f"Error performing merge_asof: {e}")
            else:
                # Standard merge
                console.print(Panel(
                    f"[bold blue]Performing Standard Join ({join_type})...[/bold blue]",
                    title="SimpleJoinWorkflowTool: Join Operation",
                    border_style="gold1"
                ))
                # Use LLM to select join columns ONLY for standard joins
                join_columns = await self.select_join_columns(main_data, auxiliary_data)

                # Perform join operation with selected columns and type
                joined_data = pd.merge(
                    main_data,
                    auxiliary_data,
                    on=join_columns,
                    how=join_type,
                    suffixes=('', '_sub')  # Use '' for left (main) and '_sub' for right
                )

            # Verify join result
            if joined_data.empty:
                raise ValueError("Join operation resulted in empty DataFrame. Make sure both datasets have overlapping values in join columns or check join type.")

            console.print(Panel(
                f"[bold green]Join Result:[/bold green] {len(joined_data)} rows using join type: {join_type}",
                # f"{', '.join(join_columns if isinstance(join_columns, list) else [join_columns])} and join type: {join_type}",
                title="SimpleJoinWorkflowTool: Join Operation Success",
                border_style="gold1"
            ))

            # Return the final joined data and the columns used (or ['Date'] for forward_fill)
            return joined_data, join_columns

        except Exception as e:
            console.print(Panel(
                f"[bold red]Error:[/bold red] {str(e)}",
                title="SimpleJoinWorkflowTool: Join Error",
                border_style="gold1"
            ))
            raise ValueError(f"Failed to perform join: {str(e)}")

    async def select_features_and_target(self, business_question: str, joined_columns: List[str]) -> tuple[List[str], str]:
        """Select appropriate features and target variable."""
        try:
            model = await self.get_llm("select_features")
            
            messages = [
                SystemMessage(content=f"""Select 2-4 features and one target variable based on the analysis question.
                
                Available columns: {', '.join(joined_columns)}
                Available features: {', '.join(FEATURE_CLASSES.keys())}
                
                Return ONLY a JSON object with 'features' (list) and 'target' (string).
                Example: {{"features": ["RSI", "MACD"], "target": "Close"}}
                
                IMPORTANT: Features must be from the feature list, target must be from available columns."""),
                HumanMessage(content=business_question)
            ]
            
            response = await model.ainvoke(messages)
            import json
            result = json.loads(response.content)
            return result["features"], result["target"]
            
        except Exception as e:
            raise ValueError(f"Failed to select features and target: {str(e)}")

    async def select_features(self, business_question: str, features_with_desc: Dict[str, str]) -> List[str]:
        """Select appropriate features based on the business question."""
        try:
            # Display available features
            console.print(Panel(
                "[bold blue]Available Technical Indicators:[/bold blue]\n" +
                "\n".join([f"• [cyan]{name}[/cyan]: {desc[:50]}..." for name, desc in features_with_desc.items()]),
                title="SimpleJoinWorkflowTool: Feature Options",
                border_style="gold1"
            ))

            model = await self.get_llm("select_features")
            
            messages = [
                SystemMessage(content=f"""Select the most appropriate technical indicators based on the business question.
                
                Available Features:
                {chr(10).join([f"- {name}: {desc}" for name, desc in features_with_desc.items()])}
                
                Business Question:
                {business_question}
                
                IMPORTANT:
                You MUST respond with ONLY a valid JSON object containing a "selected_features" array.
                Example: {{"selected_features": ["RSI", "BollingerBands"]}}
                
                Choose features that would best help the model make accurate predictions.
                Select only from the available feature names listed above."""),
                HumanMessage(content=f"""Select the most appropriate technical indicators for this analysis and return as a JSON object.""")
            ]
            
            response = await model.ainvoke(messages)
            
            # Extract and validate selection
            try:
                # Parse response content
                content = response.content.strip()
                
                # Log the raw response for debugging
                console.print(Panel(
                    f"[cyan]Raw LLM response:[/cyan]\n{content}",
                    title="Feature Selection Debug",
                    border_style="yellow"
                ))
                
                # Extract JSON if embedded in code blocks or text
                if content.startswith('```') and content.endswith('```'):
                    content = content[3:-3].strip()
                if content.startswith('```json'):
                    content = content[7:].strip()
                if content.endswith('```'):
                    content = content[:-3].strip()
                
                # Try to find JSON object if not properly formatted
                if not content.strip().startswith('{'):
                    import re
                    json_match = re.search(r'({.*})', content)
                    if json_match:
                        content = json_match.group(1)
                
                # Parse JSON
                try:
                    selection_data = json.loads(content)
                    
                    # Handle different JSON structures
                    if "selected_features" not in selection_data:
                        if isinstance(selection_data, dict) and any(k in features_with_desc for k in selection_data.keys()):
                            # Keys might be feature names
                            selected = [k for k in selection_data.keys() if k in features_with_desc]
                            selection_data = {"selected_features": selected}
                        elif isinstance(selection_data, list) and all(isinstance(item, str) for item in selection_data):
                            # Direct list of features
                            selection_data = {"selected_features": selection_data}
                    
                    # Create FeatureSelection model
                    selection = FeatureSelection(**selection_data)
                except (json.JSONDecodeError, TypeError):
                    # Fallback: parse text for feature names
                    content_lower = content.lower()
                    mentioned_features = [f for f in features_with_desc.keys() 
                                          if f.lower() in content_lower]
                    
                    if mentioned_features:
                        selection = FeatureSelection(selected_features=mentioned_features)
                        console.print(Panel(
                            f"[yellow]Extracted features from text: {', '.join(mentioned_features)}[/yellow]",
                            title="Feature Selection Recovery",
                            border_style="yellow"
                        ))
                    else:
                        # If parsing failed and no features identified, fall back to default
                        raise ValueError("Could not parse feature selection")
                
                # Validate selected features are in available list
                available_features = list(features_with_desc.keys())
                invalid_features = [f for f in selection.selected_features if f not in available_features]
                
                if invalid_features:
                    # Generate suggestions for invalid features
                    suggestions = []
                    corrected_features = []
                    
                    for feature in selection.selected_features:
                        if feature in available_features:
                            corrected_features.append(feature)
                        else:
                            possible_matches = [f for f in available_features if feature.lower() in f.lower() 
                                              or f.lower() in feature.lower()]
                            if possible_matches:
                                suggestions.append(f"Instead of '{feature}', did you mean: {', '.join(possible_matches)}?")
                                corrected_features.append(possible_matches[0])
                            else:
                                suggestions.append(f"'{feature}' is not a valid feature name.")
                    
                    # Use corrected features if found, otherwise keep invalid selection
                    if corrected_features:
                        console.print(Panel(
                            f"[yellow]Invalid features were selected: {', '.join(invalid_features)}\n"
                            f"Using corrected features: {', '.join(corrected_features)}[/yellow]",
                            title="Feature Selection Auto-correction",
                            border_style="yellow"
                        ))
                        selection.selected_features = corrected_features
                    else:
                        # Fall back to default features if no corrections possible
                        selection.selected_features = list(features_with_desc.keys())[:3]
                        console.print(f"[yellow]Invalid features with no corrections, using defaults: {', '.join(selection.selected_features)}[/yellow]")
                
                # If we ended up with empty selection, use default
                if not selection.selected_features:
                    selection.selected_features = list(features_with_desc.keys())[:3]
                    console.print(f"[yellow]Empty selection, using defaults: {', '.join(selection.selected_features)}[/yellow]")
                
                selected_features = selection.selected_features
                
            except Exception as e:
                # Fall back to default features
                console.print(f"[yellow]Error processing feature selection: {str(e)}. Using defaults.[/yellow]")
                selected_features = list(features_with_desc.keys())[:3]
            
            # Display selected features with count
            feature_count = len(selected_features)
            console.print(Panel(
                f"[bold blue]Selected {feature_count} Features:[/bold blue]\n" +
                "\n".join([f"• [cyan]{feature}[/cyan]" for feature in selected_features]),
                title="SimpleJoinWorkflowTool: Feature Selection Results",
                border_style="gold1"
            ))
                
            return selected_features
            
        except Exception as e:
            console.print(Panel(
                f"[bold red]Error in feature selection:[/bold red] {str(e)}\n[dim]Using default features[/dim]",
                title="SimpleJoinWorkflowTool: Feature Selection Error",
                border_style="red"
            ))
            # Fall back to default features in case of error
            return list(features_with_desc.keys())[:3]

    async def select_model(self, business_question: str, available_models: List[str]) -> str:
        """Select appropriate model based on the business question."""
        try:
            # Try to get descriptions from models
            model_descriptions = {}
            
            # Try dynamic approach first
            try:
                from models import get_model_descriptions
                model_descriptions = get_model_descriptions()
            except (ImportError, AttributeError):
                # Fallback to direct access
                model_descriptions = {cls.__name__: getattr(cls, 'description', cls.__doc__ or "No description") 
                                     for cls in MODEL_CLASSES}
            
            # If still empty, use hardcoded fallbacks
            if not model_descriptions:
                model_descriptions = {
                    "InteractionModel": "Captures interactions between features",
                    "MLModel": "Traditional machine learning based on ensemble methods",
                    "CandleModel": "Technical analysis based on candlestick patterns",
                    "SimpleModel": "Basic statistical model with trend detection"
                }

            # Display available models
            console.print(Panel(
                "[bold blue]Available Models:[/bold blue]\n" +
                "\n".join([f"• [cyan]{model}[/cyan]: {model_descriptions.get(model, 'No description')}" 
                          for model in available_models]),
                title="SimpleJoinWorkflowTool: Model Options",
                border_style="gold1"
            ))

            # Default to InteractionModel if available, otherwise use first model
            default_model = "InteractionModel" if "InteractionModel" in available_models else available_models[0]
            
            model = await self.get_llm("select_model")
            
            messages = [
                SystemMessage(content=f"""Select ONE forecasting model for the business question.
                
                Available Models:
                {chr(10).join([f"- {model}: {model_descriptions.get(model, 'No description')}" for model in available_models])}
                
                Business Question:
                {business_question}
                
                Return ONLY the model name that best fits the analysis."""),
                HumanMessage(content=f"""Select the most appropriate model.""")
            ]
            
            response = await model.ainvoke(messages)
            selected_model = response.content.strip()
            
            if selected_model not in available_models:
                selected_model = default_model
                console.print(f"[yellow]Invalid model selection, using default: {selected_model}[/yellow]")

            # Display selected model
            console.print(Panel(
                f"[bold blue]Selected Model:[/bold blue]\n"
                f"• [cyan]{selected_model}[/cyan]: {model_descriptions.get(selected_model, 'No description')}",
                title="SimpleJoinWorkflowTool: Model Selection Result",
                border_style="gold1"
            ))
                
            return selected_model
            
        except Exception as e:
            console.print(f"[yellow]Error selecting model: {str(e)}. Using default.[/yellow]")
            # Fall back to default model in case of error
            return "InteractionModel" if "InteractionModel" in available_models else available_models[0]

    async def select_target(self, business_question: str, available_targets: List[str]) -> str:
        """Select appropriate target variable based on the business question."""
        try:
            # Display available targets
            console.print(Panel(
                "[bold blue]Available Target Variables:[/bold blue]\n" +
                "\n".join([f"• [cyan]{target}[/cyan]" for target in available_targets]),
                title="SimpleJoinWorkflowTool: Target Options",
                border_style="gold1"
            ))

            # Prioritize 'Close' if available, otherwise use first numeric column
            default_target = "Close" if "Close" in available_targets else available_targets[0]
            
            model = await self.get_llm("select_target")
            
            messages = [
                SystemMessage(content=f"""Select ONE target variable based on the business question.
                
                Available Target Variables:
                {', '.join(available_targets)}
                
                Business Question:
                {business_question}
                
                Return ONLY the variable name that best matches the business question."""),
                HumanMessage(content=f"""Select ONE target variable.""")
            ]
            
            response = await model.ainvoke(messages)
            selected_target = response.content.strip()
            
            if selected_target not in available_targets:
                selected_target = default_target
                console.print(f"[yellow]Invalid target selection, using default: {selected_target}[/yellow]")

            # Display selected target
            console.print(Panel(
                f"[bold blue]Selected Target:[/bold blue]\n"
                f"• [cyan]{selected_target}[/cyan]",
                title="SimpleJoinWorkflowTool: Target Selection Result",
                border_style="gold1"
            ))
                
            return selected_target
            
        except Exception as e:
            console.print(f"[yellow]Error selecting target: {str(e)}. Using default.[/yellow]")
            # Fall back to default target in case of error
            return "Close" if "Close" in available_targets else available_targets[0]

    async def select_forecast_horizon(self, business_question: str) -> int:
        """Select appropriate forecast horizon based on the business question."""
        try:
            model = await self.get_llm("select_forecast_horizon")
            
            messages = [
                SystemMessage(content=f"""Determine the appropriate forecast horizon (in days) from the business question.
                
                Business Question:
                {business_question}
                
                Return ONLY a positive integer between 1-30. 
                For short-term: 1-5 days
                For medium-term: 5-20 days
                For long-term: 20-30 days"""),
                HumanMessage(content="""Select the most appropriate forecast horizon (days).""")
            ]
            
            response = await model.ainvoke(messages)
            
            # Try to parse as integer, default to 10 days if not possible
            try:
                days = int(response.content.strip())
                days = max(1, min(30, days))  # Clamp between 1-30 days
            except:
                days = 10
                console.print("[yellow]Could not parse forecast horizon, using default: 10 days[/yellow]")

            # Display selected horizon
            horizon_category = "Short term (1-5 days)" if days <= 5 else "Medium term (5-20 days)" if days <= 20 else "Long term (20-30 days)"
            console.print(Panel(
                f"[bold blue]Selected Forecast Horizon:[/bold blue]\n"
                f"• [cyan]{days} days[/cyan] ({horizon_category})",
                title="SimpleJoinWorkflowTool: Horizon Selection Result",
                border_style="gold1"
            ))
                
            return days
            
        except Exception as e:
            console.print(f"[yellow]Error selecting forecast horizon: {str(e)}. Using default: 10 days[/yellow]")
            return 10  # Default to 10 days in case of error

    async def get_available_features(self) -> Dict[str, str]:
        """Get list of available technical analysis features and their descriptions"""
        try:
            features = {}
            for name, feature_class in FEATURE_CLASSES.items():
                description = feature_class.__doc__ or "No description available"
                description = description.strip().split('\n')[0]
                features[name] = description
                
            if not features:
                raise ValueError("No features available in registry")
                
            return features
            
        except Exception as e:
            raise ValueError(f"Failed to get features from registry: {str(e)}")

    async def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            models = [cls.__name__ for cls in MODEL_CLASSES]
            if not models:
                raise ValueError("No models available")
            return models
        except Exception as e:
            raise ValueError(f"Failed to get models: {str(e)}")

    def _run(self, business_question: str) -> Any:
        """Execute the tool synchronously."""
        try:
            return asyncio.run(self._arun(business_question=business_question))
        except Exception as e:
            console.print(f"[red]Error in synchronous execution:[/red] {str(e)}")
            return {"error": str(e)}

    async def _arun(self, business_question: str) -> Dict[str, Any]:
        """Execute the workflow tool asynchronously."""
        try:
            # Stage 0: Display Analysis Plan
            console.print(Panel(
                f"[bold blue]Business Question:[/bold blue] {business_question}",
                title="SimpleJoinWorkflowTool: Analysis Planning",
                border_style="gold1"
            ))

            # Stage 1: Extract Tickers
            main_ticker, auxiliary_ticker = await self.extract_tickers(business_question)

            # Stage 2: Data Source Selection and Join Configuration
            active_loaders = await self.get_active_data_loaders()
            selected_config = await self.select_data_sources(active_loaders, main_ticker, auxiliary_ticker, business_question)

            # Stage 3: Perform Join
            joined_df, selected_join_columns = await self.perform_join(
                selected_config.main_loader, selected_config.auxiliary_loader,
                main_ticker, auxiliary_ticker,
                selected_config.join_type
            )

            # IMPORTANT: No need to handle join_columns anymore as they're determined during the join process
            
            # Stage 4: Feature Selection
            features_with_desc = await self.get_available_features()
            selected_features = await self.select_features(business_question, features_with_desc)

            # Stage 5-7: Model, Target, and Horizon Selection
            models = await self.get_available_models()
            selected_model = await self.select_model(business_question, models)
            
            available_targets = joined_df.columns.tolist()
            selected_target = await self.select_target(business_question, available_targets)
            
            forecast_horizon = await self.select_forecast_horizon(business_question)

            # Stage 8: Initialize StockAnalysisApp
            app = StockAnalysisApp(main_ticker)
            app.data = joined_df
            app.data_loader_name = f"{selected_config.main_loader}+{selected_config.auxiliary_loader}"
            app.ticker = main_ticker
            
            # Apply features
            console.print(Panel(
                f"[bold cyan]Applying Features:[/bold cyan]\n"
                f"• Selected Features: [cyan]{', '.join(selected_features)}[/cyan]",
                title="SimpleJoinWorkflowTool: Feature Application",
                border_style="gold1"
            ))
            
            app.rerun_features(selected_features)
            
            # Sanitize data before model execution
            app.data = await self.sanitize_dataframe(app.data)
            
            # Model execution
            console.print(Panel(
                f"[bold blue]Executing Model:[/bold blue]\n"
                f"• Model: [cyan]{selected_model}[/cyan]\n"
                f"• Target: [cyan]{selected_target}[/cyan]\n"
                f"• Horizon: [cyan]{forecast_horizon} days[/cyan]",
                title="SimpleJoinWorkflowTool: Model Execution",
                border_style="gold1"
            ))
            
            # Get model class and run model
            model_class = next((cls for cls in MODEL_CLASSES if cls.__name__ == selected_model), None)
            if not model_class:
                raise ValueError(f"Model {selected_model} not found")
            
            result, metrics = app.run_model(
                model_class, 
                predict=selected_target, 
                forecast_horizon=forecast_horizon
            )
            
            if result.empty:
                raise ValueError("Model execution produced no results")
            
            # Sanitize result DataFrame
            result = await self.sanitize_dataframe(result)
            
            console.print(Panel(
                f"[green]Model Results:[/green]\n"
                f"• Result rows: {len(result)}\n"
                f"• Metrics: {', '.join([f'{k}: {v:.4f}' for k, v in metrics.items()])}\n",
                title="SimpleJoinWorkflowTool: Model Results",
                border_style="green_yellow"
            ))
            
            # Create forecast summary
            last_actual_idx = None
            forecast_data = None
            forecast_summary = {}
            
            try:
                if result.empty:
                    raise ValueError("Cannot generate forecast summary from empty results")
                
                # Make a copy to avoid modifying the original
                result_copy = result.copy()
                
                # Ensure Date column exists or create from index
                if isinstance(result_copy.index, pd.DatetimeIndex) and 'Date' not in result_copy.columns:
                    result_copy = result_copy.reset_index(names=['Date'])
                elif not isinstance(result_copy.index, pd.DatetimeIndex) and 'Date' not in result_copy.columns:
                    raise ValueError("Results must contain a Date column or DatetimeIndex")
                
                # Ensure Date is proper datetime
                if 'Date' in result_copy.columns:
                    # Force to datetime with error handling
                    result_copy['Date'] = pd.to_datetime(result_copy['Date'], errors='coerce')
                    # Drop rows with invalid dates
                    result_copy = result_copy.dropna(subset=['Date'])
                    if result_copy.empty:
                        raise ValueError("No valid dates in results after conversion")
                    # Sort by date
                    result_copy = result_copy.sort_values('Date')
                
                # Prepare forecast data
                split_idx = len(result_copy) - forecast_horizon
                if split_idx <= 0:
                    raise ValueError(f"Not enough data for forecast. Need at least {forecast_horizon+1} rows.")
                
                # Get last actual data
                last_actual_data = result_copy.iloc[split_idx - 1]
                last_actual_date = result_copy.iloc[split_idx - 1]['Date']
                
                # Get forecast data
                forecast_data = result_copy.iloc[-forecast_horizon:]
                
                # Format dates properly
                date_format = '%Y-%m-%d'
                
                forecast_summary = {
                    "last_actual": {
                        "date": last_actual_date.strftime(date_format),
                        "value": float(last_actual_data[selected_target])
                    },
                    "forecast": []
                }
                
                # Format forecast entries
                for _, row in forecast_data.iterrows():
                    forecast_date = row['Date']
                    if pd.isna(forecast_date):
                        continue
                        
                    forecast_summary["forecast"].append({
                        "date": forecast_date.strftime(date_format),
                        "value": float(row.get(f"{selected_target}_Predicted", 0))
                    })
                
                if not forecast_summary["forecast"]:
                    raise ValueError("No valid forecast dates found")
                    
            except Exception as e:
                console.print(f"[red]Error generating forecast summary: {str(e)}[/red]")
                console.print(f"[dim]Exception details: {traceback.format_exc()}[/dim]")
                raise ValueError(f"Failed to generate forecast summary: {str(e)}")
                
            # Stage 9: Report Generation
            # Get the necessary data for report creation
            # In the perform_join method we used main_data and auxiliary_data before joining
            main_data = None
            auxiliary_data = None
            
            # Initialize data loaders to get the original data again
            try:
                main_loader_class = registry.get_loader_class(selected_config.main_loader)
                auxiliary_loader_class = registry.get_loader_class(selected_config.auxiliary_loader)
                
                if main_loader_class and auxiliary_loader_class:
                    main_data = main_loader_class(main_ticker).load_historical_data()
                    auxiliary_data = auxiliary_loader_class(auxiliary_ticker).load_historical_data()
            except Exception as e:
                console.print(f"[yellow]Warning: Could not load original datasets for report: {str(e)}[/yellow]")
                # Fall back to using joined data for both
                main_data = joined_df
                auxiliary_data = joined_df
            
            # Create a complete report parameters dictionary with explicit report_type
            report_params = {
                "main_df": main_data if main_data is not None else joined_df,
                "aux_df": auxiliary_data if auxiliary_data is not None else joined_df,
                "target_df": result,
                "main_datasource_id": selected_config.main_loader,
                "aux_datasource_id": selected_config.auxiliary_loader,
                "join_column": selected_join_columns,
                "join_type": selected_config.join_type,
                "report_type": "joined",
                "type": "join",
                "ticker": main_ticker,
                "features": selected_features,
                "model_name": selected_model,
                "prediction_column": selected_target,
                "forecast_horizon": forecast_horizon,
                "performance": metrics or {}
            }
            
            # Debug print report parameters
            console.print(Panel(
                f"[bold blue]Report Parameters:[/bold blue]\n" +
                "\n".join([f"• [cyan]{k}[/cyan]: {str(v)[:50]}{'...' if len(str(v)) > 50 else ''}" for k, v in report_params.items()]),
                title="SimpleJoinWorkflowTool: Report Creation Debug",
                border_style="red"
            ))
            
            # Create the report data using the factory
            report_data = ReportFactory.create_join_report(report_params)
            
            # Debug print the returned report_data
            console.print(Panel(
                f"[bold blue]Report Data (Returned from Factory):[/bold blue]\n" +
                "\n".join([f"• [cyan]{k}[/cyan]: {str(v)[:50]}{'...' if len(str(v)) > 50 else ''}" for k, v in report_data.items()]),
                title="SimpleJoinWorkflowTool: Report Factory Result",
                border_style="magenta"
            ))
            
            # Ensure type field exists for database validation
            if "type" not in report_data and "report_type" in report_data:
                report_data["type"] = "join"
                console.print(Panel(
                    f"[bold yellow]Adding missing 'type' field. Set to: [cyan]join[/cyan][/bold yellow]",
                    title="SimpleJoinWorkflowTool: Report Field Fix",
                    border_style="yellow"
                ))
            
            # Add required additional fields if missing
            required_fields = {
                "ticker": main_ticker,
                "workflow": {
                    "data_loader": f"{selected_config.main_loader}+{selected_config.auxiliary_loader}",
                    "features": selected_features,
                    "left_loader": selected_config.main_loader,
                    "right_loader": selected_config.auxiliary_loader,
                    "left_ticker": main_ticker,
                    "right_ticker": auxiliary_ticker,
                    "join_type": selected_config.join_type,
                    "model": {
                        "name": selected_model,
                        "prediction_column": selected_target,
                        "forecast_horizon": forecast_horizon
                    },
                    "start_date": "",  # Will be populated below
                    "end_date": ""     # Will be populated below
                },
                "performance": metrics or {},
                "data": {
                    "timeseries": result.reset_index().to_dict('records') if hasattr(result, 'reset_index') else []
                },
                "status": "completed",
                "type": "join"  # Ensure type is set at top level
            }
            
            # Get proper date range from the result DataFrame
            try:
                console.print(Panel(
                    f"[bold cyan]Debug DataFrame Date Structure:[/bold cyan]\n"
                    f"• DataFrame Shape: {result.shape}\n"
                    f"• DataFrame Columns: {list(result.columns)}\n"
                    f"• DataFrame Index Type: {type(result.index)}\n"
                    f"• First few rows:\n{result.head(3).to_string()}",
                    title="SimpleJoinWorkflowTool: Date Debug Info",
                    border_style="red"
                ))
                
                # Check for Date column
                if 'Date' in result.columns:
                    # Extract dates directly from the Date column
                    result_dates = pd.to_datetime(result['Date'])
                    if not result_dates.empty:
                        start_date = result_dates.min().strftime("%Y-%m-%d")
                        end_date = result_dates.max().strftime("%Y-%m-%d")
                        console.print(f"[green]Using dates from 'Date' column: {start_date} to {end_date}[/green]")
                    else:
                        raise ValueError("Date column exists but contains no valid dates")
                # Check for DatetimeIndex
                elif hasattr(result, 'index') and isinstance(result.index, pd.DatetimeIndex):
                    start_date = result.index.min().strftime("%Y-%m-%d")
                    end_date = result.index.max().strftime("%Y-%m-%d")
                    console.print(f"[green]Using dates from DatetimeIndex: {start_date} to {end_date}[/green]")
                # Try parsing from other date-like columns
                else:
                    # Look for any column that might contain dates
                    date_candidates = [col for col in result.columns if any(
                        date_term in col.lower() for date_term in ['date', 'time', 'day', 'month', 'year'])]
                    
                    if date_candidates:
                        for col in date_candidates:
                            try:
                                date_col = pd.to_datetime(result[col], errors='coerce')
                                if not date_col.isna().all():
                                    start_date = date_col.min().strftime("%Y-%m-%d")
                                    end_date = date_col.max().strftime("%Y-%m-%d")
                                    console.print(f"[green]Using dates from '{col}' column: {start_date} to {end_date}[/green]")
                                    break
                            except:
                                continue
                        else:  # If no break occurred in the loop
                            raise ValueError("Could not extract valid dates from any date-like columns")
                    else:
                        raise ValueError("No date-like columns found in the result DataFrame")
                
                # If we get here, we should have valid start_date and end_date
                console.print(Panel(
                    f"[bold cyan]Final Date Range:[/bold cyan]\n"
                    f"• Start Date: [green]{start_date}[/green]\n"
                    f"• End Date: [green]{end_date}[/green]",
                    title="SimpleJoinWorkflowTool: Selected Date Range",
                    border_style="green"
                ))
            except Exception as e:
                console.print(Panel(
                    f"[bold red]Error extracting dates:[/bold red]\n{str(e)}\n"
                    f"[dim]Falling back to current date range[/dim]",
                    title="SimpleJoinWorkflowTool: Date Extraction Error",
                    border_style="red"
                ))
                # Set to a guaranteed valid date range - today and tomorrow
                today = datetime.datetime.now()
                start_date = today.strftime("%Y-%m-%d")
                end_date = (today + datetime.timedelta(days=30)).strftime("%Y-%m-%d")
                console.print(f"[yellow]Using fallback date range: {start_date} to {end_date}[/yellow]")
            
            # Set dates directly in the required fields
            required_fields["workflow"]["start_date"] = start_date
            required_fields["workflow"]["end_date"] = end_date
            required_fields["workflow"]["left_loader"] = selected_config.main_loader
            required_fields["workflow"]["right_loader"] = selected_config.auxiliary_loader
            required_fields["workflow"]["join_type"] = selected_config.join_type
            required_fields["workflow"]["data_loader"] = f"{selected_config.main_loader}+{selected_config.auxiliary_loader}"

            # Convert DataFrame to records correctly without default index
            timeseries_records = []
            if not result.empty:
                # If result has a DatetimeIndex, first convert to Date column
                if isinstance(result.index, pd.DatetimeIndex):
                    result = result.reset_index()
                    if 'index' in result.columns:
                        result.rename(columns={'index': 'Date'}, inplace=True)
                
                # Explicitly reset index and then drop the index column to prevent it from appearing in records
                record_df = result.reset_index(drop=True)
                
                # Debug the conversion
                console.print(Panel(
                    f"[bold cyan]Debug DataFrame Conversion:[/bold cyan]\n"
                    f"• Original Columns: {list(result.columns)}\n"
                    f"• Prepared Columns: {list(record_df.columns)}\n"
                    f"• First row as dict: {record_df.iloc[0].to_dict() if len(record_df) > 0 else 'Empty DataFrame'}",
                    title="SimpleJoinWorkflowTool: Records Conversion Debug",
                    border_style="yellow"
                ))
                
                # Convert to records without index
                timeseries_records = record_df.to_dict('records')
            
            # Create a NEW report_data dictionary from scratch with all required fields
            complete_report_data = {
                "type": "join",  # Primary report type for database schema
                "ticker": main_ticker,
                "workflow": required_fields["workflow"],
                "performance": metrics or {},
                "data": {
                    "timeseries": timeseries_records  # Use our carefully constructed records
                },
                "status": "completed"
            }
            
            # Debug print the final structure
            console.print(Panel(
                f"[bold green]Complete Report Data Structure:[/bold green]\n" +
                f"• ticker: {complete_report_data['ticker']}\n" +
                f"• type: {complete_report_data['type']}\n" + 
                f"• workflow.start_date: {complete_report_data['workflow']['start_date']}\n" +
                f"• workflow.end_date: {complete_report_data['workflow']['end_date']}\n" +
                f"• data.timeseries count: {len(complete_report_data['data']['timeseries']) if complete_report_data['data']['timeseries'] else 0}\n" +
                f"• First record keys: {list(complete_report_data['data']['timeseries'][0].keys()) if complete_report_data['data']['timeseries'] else 'No records'}",
                title="SimpleJoinWorkflowTool: Final Database Submission",
                border_style="green"
            ))
            
            # Now insert the report into the database
            repo = await ReportRepository.create()
            report_id = await repo.insert_report(complete_report_data)
            
            # Create formatted answer
            # Generate strings for available options
            available_loaders_str = "\n".join([f"- {name}: {desc}" for name, desc in active_loaders.items()])
            available_features_str = "\n".join([f"- {name}: {desc}" for name, desc in features_with_desc.items()])

            formatted_answer = f"""### Investment Analysis Report

**Investment Question:**
{business_question}

**Available Data Loaders Considered:**
{available_loaders_str}

**Data Configuration:**
- Main Ticker: {main_ticker} (using {selected_config.main_loader})
- Auxiliary Ticker: {auxiliary_ticker} (using {selected_config.auxiliary_loader})
- Join Type: {selected_config.join_type}

**Analysis Configuration:**
**Available Features Considered:**
{available_features_str}

- Selected Features: {', '.join(selected_features)}
- Model: {selected_model}
- Target Variable: {selected_target}
- Forecast Horizon: {forecast_horizon} days

**Performance Metrics:**"""

            # Add detailed metrics to the formatted answer
            formatted_answer += "\n**Performance Metrics:**\n" + "\n".join([f"- {k}: {v:.4f}" for k, v in metrics.items()])
            
            # Add forecast summary if available
            try:
                if forecast_summary and "last_actual" in forecast_summary and "forecast" in forecast_summary:
                    # Use the previously created forecast summary
                    last_actual = forecast_summary["last_actual"]
                    last_forecast = forecast_summary["forecast"][-1] if forecast_summary["forecast"] else None
                    
                    if last_actual and last_forecast:
                        direction = "📈 Upward" if last_forecast["value"] > last_actual["value"] else "📉 Downward"
                        change_pct = ((last_forecast["value"] / last_actual["value"]) - 1) * 100
                        
                        forecast_summary_str = f"""
**Forecast Summary:**
- Last Known {selected_target}: ${last_actual["value"]:.2f} ({last_actual["date"]})
- Predicted {selected_target} after {forecast_horizon} days: ${last_forecast["value"]:.2f} ({last_forecast["date"]})
- Direction: {direction}
- Change: {change_pct:.2f}%
"""
                        formatted_answer += forecast_summary_str
                else:
                    formatted_answer += "\n**Forecast Summary:**\nUnable to generate detailed forecast summary.\n"
            except Exception as e:
                console.print(f"[yellow]Warning: Could not format forecast summary: {str(e)}[/yellow]")
                formatted_answer += "\n**Forecast Summary:**\nUnable to generate detailed forecast summary.\n"

            formatted_answer += f"\n**Report ID:** {report_id}"
            # Display final report
            console.print(Panel(
                "[bold green]Analysis Complete![/bold green]\n\n" + formatted_answer,
                title="SimpleJoinWorkflowTool: Final Report",
                border_style="gold1"
            ))
            
            return {
                "formatted_answer": formatted_answer,
                "task_id": report_id
            }

        except Exception as e:
            error_msg = f"""### Error in Analysis

**Investment Question:**
{business_question}

**Error:**
{str(e)}
"""
            console.print(Panel(
                f"[bold red]Error:[/bold red] {str(e)}\n"
                "[dim]Analysis workflow encountered an error. Please check the details above.[/dim]",
                title="SimpleJoinWorkflowTool: Error",
                border_style="gold1"
            ))
            return {
                "error": str(e),
                "formatted_answer": error_msg
            }

    async def sanitize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean DataFrame for analysis by handling NaN values, dates, etc."""
        try:
            # Store original row count
            original_rows = len(df) if df is not None and not df.empty else 0
            
            # Check if DataFrame is empty
            if df is None or df.empty:
                return pd.DataFrame()
                
            # Make a copy to avoid modifying original
            cleaned_df = df.copy()
            
            # Handle Date column if it exists
            if 'Date' in cleaned_df.columns:
                # Convert to datetime and ensure no timezone info
                cleaned_df['Date'] = pd.to_datetime(cleaned_df['Date']).dt.tz_localize(None)
                
                # Sort data by Date to ensure chronological order
                cleaned_df = cleaned_df.sort_values('Date')
            
            # Add Date_ordinal for certain models
            if 'Date' in cleaned_df.columns:
                cleaned_df['Date_ordinal'] = cleaned_df['Date'].map(datetime.datetime.toordinal)
            
            # Handle NaNs - fill instead of drop
            nan_count = cleaned_df.isna().sum().sum()
            if nan_count > 0:
                # Fill NaNs with appropriate values for each column
                numeric_cols = cleaned_df.select_dtypes(include=['number']).columns
                cleaned_df[numeric_cols] = cleaned_df[numeric_cols].fillna(cleaned_df[numeric_cols].mean())
                
                # For non-numeric columns, forward fill then backward fill
                non_numeric_cols = cleaned_df.select_dtypes(exclude=['number']).columns
                cleaned_df[non_numeric_cols] = cleaned_df[non_numeric_cols].fillna(method='ffill').fillna(method='bfill')
                
                console.print(f"[yellow]{nan_count} NaN values filled in the dataset[/yellow]")
            
            # Ensure the index is sorted if it's a DatetimeIndex
            if isinstance(cleaned_df.index, pd.DatetimeIndex):
                cleaned_df = cleaned_df.sort_index()
            
            console.print(Panel(
                f"[bold green]Data Sanitization Complete:[/bold green]\n"
                f"• Final row count: {len(cleaned_df)}\n"
                f"• Date column cleaned and sorted\n"
                f"• NaN values filled with appropriate values",
                title="SimpleJoinWorkflowTool: Data Preparation",
                border_style="gold1"
            ))
            
            return cleaned_df
            
        except Exception as e:
            console.print(f"[yellow]Warning: Error during data sanitization: {str(e)}[/yellow]")
            # Return original DataFrame if cleaning fails rather than empty DataFrame
            return df

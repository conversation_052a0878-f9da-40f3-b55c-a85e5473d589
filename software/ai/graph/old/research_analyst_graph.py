from typing import Dict, Any, List, Union, Optional
from rich.console import Console
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import StateGraph, END
from pydantic import BaseModel, Field
from ai.llm.llm_connect import get_llm_connect
from ai.tools import registry
from db.research_director_repository import ResearchDirectorRepository
from db.research_repository import ResearchRepository

console = Console()
director_repo = ResearchDirectorRepository()
research_repo = ResearchRepository()

# Get tools from registry
tools = registry.get_langchain_tools()

class AnalystState(BaseModel):
    messages: List[Any] = Field(default_factory=list)
    topic: str
    findings: List[Dict[str, Any]] = Field(default_factory=list)
    final_analysis: Optional[str] = None
    director_id: Optional[str] = None
    task_id: Optional[str] = None

async def analyze_data(state: Dict) -> Dict:
    """Analyze data using available tools"""
    try:
        console.print("\n[green]========== ANALYZE DATA ==========[/green]")
        console.print(f"[green]State: {state}[/green]")
        
        # Get LLM
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(state['director_id'], "analysis")
        console.print(f"[green]Retrieved model: {model}[/green]")
        
        if not model:
            console.print("[yellow]No model found, using default LLM[/yellow]")
            model = await llm_connect.get_llm()
        
        # Bind tools to model
        model = model.bind_tools(tools)
        console.print("[green]Bound tools to model[/green]")
        
        # Run analysis
        messages = [
            SystemMessage(content="You are an AI analyst. Use the available tools to analyze the topic."),
            HumanMessage(content=f"Analyze this topic: {state['topic']}")
        ]
        
        response = await model.ainvoke(messages)
        console.print(f"[green]Model response: {response}[/green]")
        
        # Update state with findings
        state['findings'].append({"data": response.content})
        state['messages'].append(response)
        
        return state
        
    except Exception as e:
        console.print(f"[red]Error in analysis: {str(e)}[/red]")
        raise

async def summarize_findings(state: Dict) -> Dict:
    """Summarize findings into final analysis"""
    try:
        # Get LLM
        llm_connect = get_llm_connect()
        model = await llm_connect.get_director_llm(state['director_id'], "summary")
        if not model:
            model = await llm_connect.get_llm()
        
        # Create summary
        findings_text = "\n".join([f"- {f['data']}" for f in state['findings']])
        messages = [
            SystemMessage(content="You are an AI analyst. Summarize the research findings."),
            HumanMessage(content=f"""
Topic: {state['topic']}

Findings:
{findings_text}

Provide a concise summary of these findings.""")
        ]
        
        response = await model.ainvoke(messages)
        
        # Update state
        state['final_analysis'] = response.content
        state['messages'].append(response)
        
        return state
        
    except Exception as e:
        console.print(f"[red]Error in summary: {str(e)}[/red]")
        return state

def should_continue(state: Dict) -> Union[str, END]:
    """Determine next step in workflow"""
    if len(state['findings']) >= 3:  # Limit to 3 analysis iterations
        return "summarize"
    return "analyze"

# Build graph
workflow = StateGraph(AnalystState)

# Add nodes
workflow.add_node("analyze", analyze_data)
workflow.add_node("summarize", summarize_findings)

# Set entry point
workflow.set_entry_point("analyze")

# Add edges
workflow.add_conditional_edges(
    "analyze",
    should_continue,
    {
        "analyze": "analyze",
        "summarize": "summarize"
    }
)
workflow.add_edge("summarize", END)

# Compile graph
graph = workflow.compile()

# Export only the graph
__all__ = ['graph'] 
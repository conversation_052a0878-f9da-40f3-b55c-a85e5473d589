from typing import Dict, List, Any, TypedDict
from langchain_core.messages import AIMessage, SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END
from ai.llm.llm_connect import get_llm_connect
import rich
from db.research_repository import ResearchRepository

console = rich.get_console()
research_repo = ResearchRepository()

class State(TypedDict):
    """Track the state of the sample graph"""
    messages: List[Any]
    director_id: str | None
    task_id: str | None

async def call_model(state: State) -> Dict[str, Any]:
    """Single-node LLM call"""
    try:
        await research_repo.update_workflow_stage(state["task_id"], "__start__", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "call_model", "in_progress")
        
        director_id = state.get("director_id")
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(director_id, "call_model")
        
        messages = [
            SystemMessage(content="You investigate investment opportunities. Generate a single clear investment question in this format: 'Should we invest in [Company] ([Ticker])?'"),
            HumanMessage(content=f"Create one focused research question for ONE company about their market position, growth, or competitive advantage.")
        ]
        response = await model.ainvoke(messages)
        
        await research_repo.update_workflow_stage(state["task_id"], "call_model", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "END", "in_progress")
        await research_repo.update_workflow_stage(state["task_id"], "END", "completed")
        
        return {
            "messages": [response]
        }
    except Exception as e:
        await research_repo.update_workflow_stage(state["task_id"], "call_model", "failed")
        raise e

# Create and compile the graph
graph = (StateGraph(State)
    .add_node("call_model", call_model)
    .set_entry_point("call_model")
    .add_edge("call_model", END)
    .compile()
)

__all__ = ['graph']
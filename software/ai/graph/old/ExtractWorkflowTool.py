import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
from typing import Any, Dict, List, Optional, Type, Union
from langchain.tools import StructuredTool
from pydantic import BaseModel, Field, ConfigDict
import re
from rich.console import Console
from rich.panel import Panel
from pydantic import field_validator

# Add the project root to Python path
project_root = str(Path(__file__).parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from data import registry
from features import FEATURE_CLASSES
from main import StockAnalysisApp

console = Console()

class ExtractWorkflowSchema(BaseModel):
    """Schema for ExtractWorkflowTool input parameters"""
    business_question: str = Field(
        ...,
        description="A COMPLETE question (NOT just the ticker) that includes a ticker symbol in parentheses. The full question must be at least 10 characters.",
        min_length=10,
        examples=[
            "Show me the trading data for Apple (AAPL)",
            "What are the RSI values for NVIDIA (NVDA)?",
            "Get me the price and volume data for Tesla (TSLA)",
            "Extract the trading data for Microsoft (MSFT)"
        ]
    )
    analysis_date: str = Field(
        ...,
        description="The date to analyze in YYYY-MM-DD format.",
        examples=["2023-05-15", "2023-06-20", "2023-07-01", "2023-04-10"]
    )
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator("business_question")
    def validate_business_question(cls, v):
        if len(v) < 10:
            raise ValueError("Business question must be at least 10 characters")
        return v
        
    @field_validator("analysis_date")
    def validate_analysis_date(cls, v):
        try:
            pd.to_datetime(v)
        except:
            raise ValueError(f"Invalid date format: {v}. Please use YYYY-MM-DD format.")
        return v

class ExtractWorkflowTool(StructuredTool):
    """A tool for extracting and analyzing financial data snapshots.
    This tool processes a business question to extract relevant data points and features.
    """
    
    name: str = "ExtractWorkflowTool"
    description: str = "A tool for extracting historical financial data snapshots for a specific date. Processes a business question to extract ticker symbols and retrieves data from available loaders. Requires a business question with ticker in parentheses (e.g. 'Show me data for Apple (AAPL)') and analysis date in YYYY-MM-DD format."
    args_schema: Type[BaseModel] = ExtractWorkflowSchema
    return_direct: bool = True
    
    def __init__(self, **data):
        super().__init__(**data)
        # Ensure args_schema is set correctly for StructuredTool
        self.args_schema = ExtractWorkflowSchema

    def _run(self, business_question: str, analysis_date: str, **kwargs) -> Any:
        """Execute the tool synchronously."""
        try:
            # Arguments are now passed directly

            # Validate we have the required arguments (redundant due to schema but good practice)
            if not business_question:
                raise ValueError("Missing required argument: business_question")

            if not analysis_date:
                raise ValueError("Missing required argument: analysis_date")

            console.print(Panel(f"[cyan]Processing question:[/cyan] {business_question}\n[cyan]Analysis date:[/cyan] {analysis_date}", title="ExtractWorkflowTool: Input", border_style="blue"))
            
            # Extract ticker from question
            ticker_match = re.search(r'\(([^)]+)\)', business_question)
            if not ticker_match:
                raise ValueError("No ticker symbol found in question. Please include ticker in parentheses, e.g., (AAPL)")
            
            ticker = ticker_match.group(1)
            console.print(Panel(f"[green]Extracted ticker:[/green] {ticker}", title="ExtractWorkflowTool: Ticker", border_style="green"))
            
            # Validate date format
            try:
                target_date = pd.to_datetime(analysis_date)
                console.print(Panel(f"[green]Validated date:[/green] {target_date.date()}", title="ExtractWorkflowTool: Date", border_style="green"))
            except:
                raise ValueError(f"Invalid date format: {analysis_date}. Please use YYYY-MM-DD format.")
            
            # Get data loaders from registry
            data_loaders = registry.get_all_loader_names()
            if not data_loaders:
                raise ValueError("No data loaders available")
            
            datasource = data_loaders[0]  # Use first loader
            console.print(Panel(f"[cyan]Selected data source:[/cyan] {datasource}", title="ExtractWorkflowTool: Data Source", border_style="cyan"))
            
            # Get loader class
            loader_class = registry.get_loader_class(datasource)
            if not loader_class:
                raise ValueError(f"Data loader {datasource} not found")
            
            # Initialize loader with ticker
            data_loader = loader_class(ticker)
            df = data_loader.load_historical_data()
            
            if df is None or df.empty:
                raise ValueError(f"No data available for ticker {ticker} with loader {datasource}")
            
            console.print(Panel(
                f"[green]Data loaded successfully:[/green]\n" +
                f"- Date range: {df.index.min().date()} to {df.index.max().date()}\n" +
                f"- Data points: {len(df)}\n" +
                f"- Columns: {', '.join(df.columns)}",
                title="ExtractWorkflowTool: Data Overview", 
                border_style="green"
            ))
            
            # Initialize StockAnalysisApp with the loaded data
            app = StockAnalysisApp(ticker)
            app.data = df
            app.data_loader_name = datasource
            
            # Calculate some default features
            default_features = ["RSI", "MACD", "BollingerBands"]
            features = []
            
            try:
                console.print(Panel(f"[cyan]Calculating features:[/cyan] {', '.join(default_features)}", title="ExtractWorkflowTool: Features", border_style="cyan"))
                # Use app.rerun_features instead of calculating each feature individually
                app.rerun_features(default_features)
                features = list(app.features.keys())
                if features:
                    console.print(Panel(f"[green]Successfully calculated features:[/green] {', '.join(features)}", title="ExtractWorkflowTool: Features", border_style="green"))
                else:
                    console.print(Panel("[yellow]No features were successfully calculated[/yellow]", title="ExtractWorkflowTool: Warning", border_style="yellow"))
            except Exception as e:
                console.print(Panel(f"[yellow]Warning:[/yellow] Failed to calculate features: {str(e)}", title="ExtractWorkflowTool: Warning", border_style="yellow"))
            
            # Check if we still have data after feature calculation
            if app.data.empty:
                console.print(Panel("[yellow]Data was empty after feature calculation. Using original data.[/yellow]", title="ExtractWorkflowTool: Warning", border_style="yellow"))
                app.data = df.copy()  # Use original data if all features failed
            
            # Get date range for context window around target date
            min_date = app.data.index.min()
            max_date = app.data.index.max() 
            
            # Check if target date is within range
            if target_date < min_date:
                console.print(Panel(f"[yellow]Target date {target_date.date()} is before earliest available date {min_date.date()}. Using earliest date.[/yellow]", title="ExtractWorkflowTool: Warning", border_style="yellow"))
                target_date = min_date
            elif target_date > max_date:
                console.print(Panel(f"[yellow]Target date {target_date.date()} is after latest available date {max_date.date()}. Using latest date.[/yellow]", title="ExtractWorkflowTool: Warning", border_style="yellow"))
                target_date = max_date
            
            # Find nearest available date (trading days may not be continuous)
            if target_date not in app.data.index:
                # Get closest date
                console.print(Panel(f"[yellow]Exact date {target_date.date()} not found in data. Finding nearest date.[/yellow]", title="ExtractWorkflowTool: Warning", border_style="yellow"))
                idx = app.data.index.get_indexer([target_date], method='nearest')[0]
                if idx >= 0:  # Valid index
                    target_date = app.data.index[idx]
                    console.print(Panel(f"[green]Found nearest date:[/green] {target_date.date()}", title="ExtractWorkflowTool: Date", border_style="green"))
                else:
                    raise ValueError(f"Could not find a suitable date near {target_date.date()}")
            
            # Get a window of days around the target date for context
            context_window = 5  # ±5 days
            
            # Find indices for window
            target_idx = app.data.index.get_loc(target_date)
            start_idx = max(0, target_idx - context_window)
            end_idx = min(len(app.data.index) - 1, target_idx + context_window)
            
            # Get context data using iloc to avoid index issues
            context_data = app.data.iloc[start_idx:end_idx + 1]
            
            # Store start and end dates
            start_date = str(context_data.index[0].date())
            end_date = str(context_data.index[-1].date())
            
            console.print(Panel(
                f"[green]Context window:[/green] {start_date} to {end_date}\n" +
                f"[green]Target date:[/green] {target_date.date()}\n" +
                f"[green]Data points in window:[/green] {len(context_data)}",
                title="ExtractWorkflowTool: Analysis Window", 
                border_style="green"
            ))
            
            # Format answer in markdown
            question_lower = business_question.lower()
            target_column = None
            
            # Map common terms to column names
            metric_mapping = {
                'closing': 'Close',
                'close': 'Close',
                'opening': 'Open',
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'volume': 'Volume'
            }
            
            for term, column in metric_mapping.items():
                if term in question_lower and column in app.data.columns:
                    target_column = column
                    break
            
            if not target_column and 'Close' in app.data.columns:
                target_column = 'Close'  # Default to Close if no specific column identified
            elif not target_column:
                target_column = app.data.columns[0]  # Fall back to first column
            
            console.print(Panel(f"[green]Selected column for analysis:[/green] {target_column}", title="ExtractWorkflowTool: Column", border_style="green"))
            
            # Get target value
            target_value = None
            try:
                target_value = context_data.loc[target_date, target_column]
            except:
                target_row = context_data.iloc[context_data.index.get_indexer([target_date], method='nearest')[0]]
                target_value = target_row[target_column]
            
            console.print(Panel(f"[green]Target value:[/green] {target_value}", title="ExtractWorkflowTool: Value", border_style="green"))
            
            # Format value nicely for display
            if isinstance(target_value, (float, int)):
                formatted_value = f"${target_value:.2f}" if target_column in ['Open', 'High', 'Low', 'Close'] else f"{target_value:.2f}"
            else:
                formatted_value = str(target_value)
            
            # Format a comprehensive answer
            answer = f"""
### Data Analysis Result

**Question:** {business_question}

**Answer:** The {target_column.lower()} value for {ticker} on {target_date.date()} was {formatted_value}

**Context (±{context_window} days around {target_date.date()}):**
```
{context_data[[target_column]].to_markdown() if target_column in context_data.columns else context_data.head().to_markdown()}
```

**Additional Information:**
- Data Source: {datasource}
- Features Calculated: {', '.join(features)}
- Total Available Date Range: {app.data.index.min().date()} to {app.data.index.max().date()}
"""
            
            console.print(Panel(answer, title="ExtractWorkflowTool: Analysis Result", border_style="green"))
            
            return {
                "ticker": ticker,
                "start_date": start_date,
                "end_date": end_date,
                "data_source": datasource,
                "formatted_answer": answer
            }
            
        except Exception as e:
            error_msg = str(e)
            console.print(Panel(f"[red]Error in _run:[/red] {error_msg}", title="ExtractWorkflowTool: Error", border_style="red"))
            return {
                "error": error_msg,
                "ticker": ticker if 'ticker' in locals() else "",
                "start_date": "",
                "end_date": "",
                "data_source": "",
                "formatted_answer": f"Error occurred while processing your request: {error_msg}"
            }
    
    async def _arun(self, business_question: str, analysis_date: str, **kwargs) -> Dict[str, Any]:
        """Execute the workflow tool asynchronously."""
        console.print(Panel("[cyan]Calling _arun method with named arguments[/cyan]", title="ExtractWorkflowTool: Async", border_style="blue"))
        # Pass named arguments directly to _run
        # Note: Since _run is synchronous, this async wrapper might not be strictly necessary
        # unless _run itself involved async operations internally, which it doesn't currently.
        # However, keeping it consistent with the pattern for now.
        return self._run(business_question=business_question, analysis_date=analysis_date, **kwargs)

if __name__ == "__main__":
    import asyncio
    from dotenv import load_dotenv
    
    # Load environment variables
    load_dotenv(os.path.join(project_root, '.env'))
    
    async def main():
        try:
            console.print(Panel("[bold]Testing ExtractWorkflowTool[/bold]", title="ExtractWorkflowTool: Test", border_style="thistle1"))
            
            # Create tool instance
            tool = ExtractWorkflowTool()
            
            # Test cases
            test_questions = [
                "Show me the trading data for Apple (AAPL)",
                "What are the RSI values for NVIDIA (NVDA)?",
                "Get me the price and volume data for Tesla (TSLA)"
            ]
            
            test_dates = [
                "2023-05-15", 
                "2023-06-20", 
                "2023-07-01"
            ]
            
            for i, question in enumerate(test_questions):
                console.print(Panel(f"[yellow]Testing question:[/yellow] {question}\n[yellow]Date:[/yellow] {test_dates[i]}", title="ExtractWorkflowTool: Test Case", border_style="thistle1"))
                result = await tool._arun(business_question=question, analysis_date=test_dates[i])
                if "error" in result:
                    console.print(Panel(f"[red]Error:[/red] {result['error']}", title="ExtractWorkflowTool: Error", border_style="dark_orange"))
                else:
                    console.print(Panel("[green]Success![/green]", title="ExtractWorkflowTool: Result", border_style="thistle1"))
                    
        except Exception as e:
            console.print(Panel(f"[red]Test error: {str(e)}[/red]", title="ExtractWorkflowTool: Error", border_style="dark_orange"))

    # Run the async main function
    asyncio.run(main()) 
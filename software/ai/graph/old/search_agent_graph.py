from typing import Dict, List, TypedDict, Annotated, Union, Sequence, Any
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage
)
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from ai.llm.llm_connect import get_llm_connect
import rich
from rich.panel import Panel
from rich.console import Console
from rich.layout import Layout
from rich.table import Table
from rich.style import Style
from rich.markdown import Markdown
from pydantic import BaseModel, Field
from db.research_repository import ResearchRepository
from db.research_director_repository import ResearchDirectorRepository
from langchain_community.tools import DuckDuckGoSearchResults
from langchain_community.utilities import DuckDuckGoSearchAPIWrapper
from langchain.output_parsers import PydanticOutputParser
from time import sleep
import random
from duckduckgo_search.exceptions import DuckDuckGoSearchException

# Pydantic model for search query generation
class SearchQuery(BaseModel):
    query: str = Field(
        description="The search query to be used for DuckDuckGo search"
    )
    context: str = Field(
        description="Brief explanation of why this query was chosen based on director's expertise"
    )
    focus_areas: List[str] = Field(
        description="Key areas from director's expertise that influenced this query",
        max_items=3
    )
    sources: List[str] = Field(
        description="Target financial news sources for the search",
        default=["bloomberg.com", "finance.yahoo.com", "ft.com", "reuters.com", "wsj.com"]
    )

# Global search configuration
SEARCH_CONFIG = {
    "max_results": 5,  # Increased from 10 to get more results
    "time_period": "w",  # Changed from 'm' to 'y' to get a full year of results
    "region": "wt-wt",  # World-wide results
}

# Financial sources configuration
FINANCIAL_SOURCES = [
    "bloomberg.com",
    "finance.yahoo.com", 
    "ft.com",
    "reuters.com",
    "wsj.com",
    "marketwatch.com",
    "cnbc.com",
    "barrons.com",
    "seekingalpha.com",
    "investing.com",
    "fool.com",
    "investors.com",
    "thestreet.com",
    "benzinga.com",
    "zacks.com",
    "morningstar.com",
    "tradingview.com",
    "finviz.com"
]

# Rich styling configuration
STYLE_CONFIG = {
    "url_style": "dim blue",
    "source_style": "italic yellow",
    "separator": "─" * 100,
}

console = rich.get_console()
research_repo = ResearchRepository()
director_repo = ResearchDirectorRepository()

class State(TypedDict):
    """Track the state of the search agent graph"""
    messages: Annotated[List[BaseMessage], add_messages]
    director_id: str
    task_id: str
    search_results: List[Dict[str, str]]  # Updated type hint to match the new structure

async def generate_search_query(director: Dict[str, Any], model) -> SearchQuery:
    """Generate a search query based on director's expertise and personal investment style"""
    
    # Create a personalized system prompt based on director's profile
    system_prompt = f"""You are now embodying the investment mindset and analytical approach of {director['name']}.
    
    YOUR ROLE AND BACKGROUND:
    You are a renowned {director['title']} with {director['experience_years']} years of experience.
    Your investment philosophy is deeply rooted in {director['analysis_style']}.
    You have a proven track record in {', '.join(director['expertise'])}.
    
    YOUR CURRENT TASK:
    As {director['name']}, you are conducting market research to identify potential investment opportunities
    or market risks that align with your analytical framework. You need to craft a search query that will
    help you uncover the kind of market information you typically look for.
    
    YOUR THOUGHT PROCESS:
    1. You approach markets with {director['analysis_style']}
    2. You're known for {director['personality']}
    3. Your expertise areas are {', '.join(director['expertise'])}
    
    SEARCH QUERY REQUIREMENTS:
    - Create a search query that YOU would use to find relevant market information
    - Focus on terms and concepts that align with your investment philosophy
    - Look for patterns and trends that match your analytical framework
    - Consider both obvious and contrarian viewpoints
    - Target high-quality financial sources that you would trust
    
    Remember: You're not just creating a search query - you're thinking like {director['name']}, looking
    for the kind of market information that has made you successful in your career."""
    
    # Create a personalized context message
    context_msg = f"""CURRENT RESEARCH CONTEXT:
    
    As {director['name']}, you're currently researching market conditions through your unique lens:
    
    YOUR EXPERTISE LENS:
    {', '.join(director['expertise'])}
    
    YOUR ANALYTICAL FRAMEWORK:
    {director['analysis_style']}
    
    YOUR BACKGROUND CONTEXT:
    {director['background']}
    
    YOUR RESEARCH APPROACH:
    {director['personality']}
    
    TARGET SOURCES:
    You typically rely on high-quality financial sources like: {', '.join(FINANCIAL_SOURCES[:5])}
    
    QUERY GUIDELINES:
    1. Think about what specific market patterns or trends would catch YOUR attention
    2. Consider what others might be overlooking (given your contrarian approach)
    3. Focus on indicators that have historically validated your investment thesis
    4. Look for information that could challenge or confirm your current market views
    
    Remember: The query should reflect YOUR unique investment philosophy and analytical approach.
    Instead of generic terms, use specific concepts that YOU would look for in your analysis.
    """
    
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=context_msg)
    ]
    
    # Set up the parser with personalized instructions
    parser = PydanticOutputParser(pydantic_object=SearchQuery)
    format_instructions = f"""Given your role as {director['name']}, generate a search query that reflects your investment philosophy.
    
    {parser.get_format_instructions()}
    
    The query should embody your analytical style and focus on the market patterns you typically look for."""
    
    # Add format instructions to the messages
    messages.append(HumanMessage(content=format_instructions))
    
    # Get response from model
    response = await model.ainvoke(messages)
    
    # Parse the response into SearchQuery object
    try:
        search_query = parser.parse(response.content)
        # Append site: operators for financial sources - using more sources
        source_filters = ' OR '.join(f'site:{source}' for source in search_query.sources[:5])
        search_query.query = f"({search_query.query}) ({source_filters})"
        return search_query
    except Exception as e:
        console.print(Panel(f"[red]Error parsing search query: {str(e)}[/red]", title="[DEBUG] Query Parser Error"))
        # Fallback to a personalized query based on expertise
        fallback_query = SearchQuery(
            query=f"(market inefficiency analysis {director['expertise'][0]}) (site:bloomberg.com OR site:reuters.com OR site:wsj.com OR site:ft.com)",
            context=f"Fallback query based on {director['name']}'s primary expertise in {director['expertise'][0]}",
            focus_areas=[director['expertise'][0]],
            sources=["bloomberg.com", "reuters.com", "wsj.com", "ft.com"]
        )
        return fallback_query

async def search_with_retry(search_wrapper, query, max_retries=3, initial_delay=2):
    """Execute search with retry logic for rate limits"""
    for attempt in range(max_retries):
        try:
            return search_wrapper.results(query, max_results=SEARCH_CONFIG["max_results"])
        except DuckDuckGoSearchException as e:
            if "Ratelimit" in str(e) and attempt < max_retries - 1:
                delay = initial_delay * (2 ** attempt) + random.uniform(0, 1)  # Exponential backoff with jitter
                console.print(Panel(
                    f"[yellow]Rate limited by DuckDuckGo. Waiting {delay:.1f} seconds before retry {attempt + 1}/{max_retries}[/yellow]",
                    title="[DEBUG] Rate Limit"
                ))
                sleep(delay)
                continue
            raise

async def search_node(state: State) -> Dict[str, Any]:
    """Node that performs search operations"""
    try:
        console.print(Panel("[cyan]Starting search node execution[/cyan]", title="[DEBUG] Entry"))
        
        console.print(Panel("[cyan]Retrieving director information[/cyan]", title="[DEBUG] Director"))
        # Get director information
        director = await director_repo.get_director(state["director_id"])
        if not director:
            raise ValueError("Director not found")
            
        console.print(Panel(f"[cyan]Found director: {director['name']}[/cyan]", title="[DEBUG] Director Info"))
        
        # Create director info table
        director_table = Table.grid(padding=(0, 1))
        director_table.add_row("[bold]Name:[/bold]", f"[cyan]{director['name']}[/cyan]")
        director_table.add_row("[bold]Title:[/bold]", f"[cyan]{director['title']}[/cyan]")
        director_table.add_row("[bold]Experience:[/bold]", f"[cyan]{director['experience_years']} years[/cyan]")
        director_table.add_row("[bold]Analysis Style:[/bold]", f"[dim cyan]{director['analysis_style']}[/dim cyan]")
        
        # Create expertise table
        expertise_table = Table.grid(padding=(0, 1))
        for expertise in director['expertise']:
            expertise_table.add_row("•", f"[italic cyan]{expertise}[/italic cyan]")
        
        # Display director information
        console.print("\n")
        console.print(Panel(
            director_table,
            title="[bold blue]👤 Director Profile[/bold blue]",
            subtitle="[dim]Research Director Information[/dim]"
        ))
        
        console.print(Panel(
            expertise_table,
            title="[bold blue]🎯 Areas of Expertise[/bold blue]",
            subtitle="[dim]Specialized Knowledge Domains[/dim]"
        ))
        
        # Display background information
        console.print(Panel(
            Markdown(director['background']),
            title="[bold blue]📚 Background[/bold blue]",
            subtitle="[dim]Professional History[/dim]"
        ))
        
        # Display personality traits
        console.print(Panel(
            Markdown(director['personality']),
            title="[bold blue]🧠 Analysis Approach[/bold blue]",
            subtitle="[dim]Investment Philosophy & Methodology[/dim]"
        ))
        
        # Update workflow stage
        console.print(Panel("[cyan]Updating workflow stages[/cyan]", title="[DEBUG] Workflow"))
        await research_repo.update_workflow_stage(state["task_id"], "__start__", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "search", "in_progress")
        
        # Create metadata table
        metadata_table = Table.grid(padding=(0, 1))
        metadata_table.add_row("[bold]Task ID:[/bold]", state["task_id"])
        metadata_table.add_row("[bold]Director ID:[/bold]", state["director_id"])
        metadata_table.add_row("[bold]Stage:[/bold]", "[blue]Search in progress[/blue]")
        
        console.print("\n")
        console.print(Panel(
            metadata_table,
            title="[bold blue]🔍 Search Node[/bold blue]",
            subtitle="[dim]Processing search request...[/dim]"
        ))
        
        console.print(Panel("[cyan]Getting LLM model[/cyan]", title="[DEBUG] LLM"))
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(state["director_id"], "search")
        
        # Generate search query based on director's expertise
        console.print(Panel("[cyan]Generating search query[/cyan]", title="[DEBUG] Query Generation"))
        search_query_obj = await generate_search_query(director, model)
        
        console.print(Panel(f"[cyan]Generated query: {search_query_obj.query}[/cyan]", title="[DEBUG] Query"))
        
        # Display generated query information
        query_info_table = Table.grid(padding=(0, 1))
        query_info_table.add_row("[bold]Generated Query:[/bold]", f"[cyan]{search_query_obj.query}[/cyan]")
        query_info_table.add_row("[bold]Context:[/bold]", f"[dim]{search_query_obj.context}[/dim]")
        query_info_table.add_row("[bold]Focus Areas:[/bold]", f"[italic cyan]{', '.join(search_query_obj.focus_areas)}[/italic cyan]")
        
        console.print(Panel(
            query_info_table,
            title="[bold magenta]🎯 Generated Search Query[/bold magenta]",
            subtitle="[dim]Based on Director's Expertise[/dim]"
        ))
        
        # Initialize DuckDuckGo search with custom configuration
        console.print(Panel("[cyan]Initializing DuckDuckGo search[/cyan]", title="[DEBUG] DDG Init"))
        search_wrapper = DuckDuckGoSearchAPIWrapper(
            max_results=SEARCH_CONFIG["max_results"],
            time=SEARCH_CONFIG["time_period"],
            region=SEARCH_CONFIG["region"]
        )
        
        # Get raw search results using generated query with retry logic
        console.print(Panel("[cyan]Fetching search results[/cyan]", title="[DEBUG] DDG Search"))
        try:
            raw_results = await search_with_retry(
                search_wrapper,
                search_query_obj.query,
                max_retries=3,
                initial_delay=2
            )
            console.print(Panel(f"[green]Got {len(raw_results)} results[/green]", title="[DEBUG] DDG Results"))
        except DuckDuckGoSearchException as search_error:
            if "Ratelimit" in str(search_error):
                console.print(Panel(
                    "[red]DuckDuckGo rate limit reached. Please wait a few minutes before trying again.[/red]\n"
                    "[dim]This usually happens when too many searches are performed in a short time.[/dim]",
                    title="[DEBUG] Rate Limit Error"
                ))
            else:
                console.print(Panel(f"[red]Search error: {str(search_error)}[/red]", title="[DEBUG] DDG Error"))
            raise
        
        # Create search info table
        search_info = Table.grid(padding=(0, 1))
        search_info.add_row("[bold]Search Query:[/bold]", f"[cyan]{search_query_obj.query}[/cyan]")
        search_info.add_row("[bold]Time Period:[/bold]", f"Past {SEARCH_CONFIG['time_period']}")
        search_info.add_row("[bold]Results Found:[/bold]", str(len(raw_results)))
        
        console.print(Panel(
            search_info,
            title="[bold magenta]🔎 Search Parameters[/bold magenta]",
            subtitle="[dim]DuckDuckGo search configuration[/dim]"
        ))
        
        # Create results table with enhanced information
        console.print(Panel("[cyan]Processing search results[/cyan]", title="[DEBUG] Results Processing"))
        results_table = Table.grid(padding=(0, 2))
        for idx, result in enumerate(raw_results, 1):
            console.print(Panel(f"[cyan]Processing result {idx}[/cyan]", title="[DEBUG] Result"))
            console.print(Panel(str(result), title=f"[DEBUG] Raw Result {idx}"))
            
            results_table.add_row(
                f"[bold blue]{idx}.[/bold blue]",
                f"[bold]{result['title']}[/bold]\n"
                f"[dim]{result['snippet']}[/dim]\n"
                f"[{STYLE_CONFIG['url_style']}]→ {result['link']}[/{STYLE_CONFIG['url_style']}]"
            )
            if idx < len(raw_results):
                results_table.add_row("", f"[dim]{STYLE_CONFIG['separator']}[/dim]")
        
        console.print(Panel(
            results_table,
            title="[bold magenta]🌐 Web Search Results[/bold magenta]",
            subtitle=f"[dim]Found {len(raw_results)} results from DuckDuckGo[/dim]"
        ))
        
        # Store enhanced results in state
        state["search_results"] = raw_results
        
        # Display completion status
        status_table = Table.grid(padding=(0, 1))
        status_table.add_row("[bold]Status:[/bold]", "[green]Completed[/green]")
        status_table.add_row("[bold]Results processed:[/bold]", str(len(state["search_results"])))
        
        console.print(Panel(
            status_table,
            title="[bold green]✓ Search Complete[/bold green]",
            subtitle="[dim]Ready for summarization[/dim]"
        ))
        
        console.print(Panel("[green]Search node completed successfully[/green]", title="[DEBUG] Exit"))
        
        await research_repo.update_workflow_stage(state["task_id"], "search", "completed")
        
        # Convert search query to a message for the state
        search_message = HumanMessage(content=f"""Search Query: {search_query_obj.query}
Context: {search_query_obj.context}
Focus Areas: {', '.join(search_query_obj.focus_areas)}
Sources: {', '.join(search_query_obj.sources[:5])}""")
        
        return {
            "messages": [search_message],
            "search_results": state["search_results"]
        }
        
    except Exception as e:
        console.print(Panel(
            f"[bold red]{str(e)}[/bold red]\n\n[dim]Check logs for more details[/dim]",
            title="[bold red]❌ Error in Search Node[/bold red]",
            subtitle="[dim]Operation failed[/dim]"
        ))
        raise

async def summarize_node(state: State) -> Dict[str, Any]:
    """Node that summarizes search results focusing on companies mentioned by sector"""
    try:
        # Update workflow stage
        await research_repo.update_workflow_stage(state["task_id"], "summarize", "in_progress")
        
        # Create metadata table
        metadata_table = Table.grid(padding=(0, 1))
        metadata_table.add_row("[bold]Task ID:[/bold]", state["task_id"])
        metadata_table.add_row("[bold]Director ID:[/bold]", state["director_id"])
        metadata_table.add_row("[bold]Results to process:[/bold]", str(len(state["search_results"])))
        
        console.print("\n")
        console.print(Panel(
            metadata_table,
            title="[bold yellow]📝 Summarize Node[/bold yellow]",
            subtitle="[dim]Generating summary...[/dim]"
        ))
        
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(state["director_id"], "summarize")
        
        # Create a focused summary prompt using XML format
        summary_prompt = """<summary_format>
    <title>Provide a clear, descriptive title for the overall market news</title>
    <overview>Write one concise sentence summarizing the key market insight or trend</overview>
    <companies>List all mentioned companies with their tickers in parentheses, using bullet points</companies>
</summary_format>

Format the output exactly like this:
<title>Your Title Here</title>
<overview>Your one-sentence summary here.</overview>
<companies>
• Company Name (TICKER)
• Company Name (TICKER)
</companies>

Search Results:
"""
        
        for idx, result in enumerate(state["search_results"], 1):
            summary_prompt += f"{idx}. {result['title']}\n"
            summary_prompt += f"   {result['snippet']}\n"
            summary_prompt += f"   Source: {result['link']}\n\n"
        
        state["messages"].append(HumanMessage(content=summary_prompt))
        response = await model.ainvoke(state["messages"])
        
        # Display summary as markdown for better formatting
        console.print(Panel(
            Markdown(response.content),
            title="[bold]📊 Market Summary[/bold]",
            subtitle="[dim]Key Companies and Market Insights[/dim]"
        ))
        
        # Display completion status
        status_table = Table.grid(padding=(0, 1))
        status_table.add_row("[bold]Status:[/bold]", "[green]Completed[/green]")
        status_table.add_row("[bold]Sources analyzed:[/bold]", str(len(state["search_results"])))
        
        console.print(Panel(
            status_table,
            title="[bold green]✓ Summary Complete[/bold green]",
            subtitle="[dim]Task finished successfully[/dim]"
        ))
        
        await research_repo.update_workflow_stage(state["task_id"], "summarize", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "END", "in_progress")
        await research_repo.update_workflow_stage(state["task_id"], "END", "completed")
        
        return {"messages": [response]}
    except Exception as e:
        console.print(Panel(
            f"[bold red]{str(e)}[/bold red]\n\n[dim]Check logs for more details[/dim]",
            title="[bold red]❌ Error in Summarize Node[/bold red]",
            subtitle="[dim]Operation failed[/dim]"
        ))
        raise

# Create the graph
workflow = StateGraph(State)

# Add nodes
workflow.add_node("search", search_node)
workflow.add_node("summarize", summarize_node)

# Set entry point
workflow.set_entry_point("search")

# Add edges
workflow.add_edge("search", "summarize")
workflow.add_edge("summarize", END)

# Compile the graph
graph = workflow.compile()

__all__ = ['graph'] 
from typing import Dict, List, Any, TypedDict
from langchain_core.messages import AIMessage, SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END
from ai.llm.llm_connect import get_llm_connect
from software.ai.graph.old.tool_agent_graph import graph as tool_agent_graph
import rich
from db.research_repository import ResearchRepository

console = rich.get_console()
research_repo = ResearchRepository()

class State(TypedDict):
    """Track the state of the sample graph"""
    messages: List[Any]
    director_id: str | None
    task_id: str | None

async def call_model(state: State) -> Dict[str, Any]:
    """Single-node LLM call"""
    try:
        await research_repo.update_workflow_stage(state["task_id"], "__start__", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "call_model", "in_progress")
        
        director_id = state.get("director_id")
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(director_id, "call_model")
        
        messages = [
            SystemMessage(content="Generate an investment question in the exact format: 'Should we invest in [Company] ([Ticker])?'"),
            HumanMessage(content="Create just one simple question following the specified format about whether to invest in a company.")
        ]
        response = await model.ainvoke(messages)

        console.print(f"[green]Generated question:[/green] {response.content}")
        
        await research_repo.update_workflow_stage(state["task_id"], "call_model", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "tool_agent", "in_progress")
        
        return {
            "messages": [response]
        }
    except Exception as e:
        await research_repo.update_workflow_stage(state["task_id"], "call_model", "failed")
        raise e

# Create and compile the graph
graph = (StateGraph(State)
    .add_node("call_model", call_model)
    .add_node("tool_agent", tool_agent_graph)
    .set_entry_point("call_model")
    .add_edge("call_model", "tool_agent")
    .add_edge("tool_agent", END)
    .compile()
)

__all__ = ['graph']
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.graph.director_state import AnalystState, AnalystOutput, print_step, print_pretty, print_debug, add_messages
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.tools import registry
from typing import List, Dict, Any, Iterable, TypedDict, Optional, Annotated
import json




async def analyst_agent(state: AnalystState) -> Dict[str, Any]:
    print_step(f"Analyst {state.name_analyst}: {state}", "Analyst State to be sent to Analyst Tool Agent", "pale_turquoise1", verbose=True)

    tools = await registry.get_langchain_tools_async()
    director_id = state.director_id

    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(director_id, "analyst_agent")
        if not model:
            raise ValueError("Failed to get LLM for agent stage")
        model_with_tools = model.bind_tools(tools)
    except Exception as e:
        print_debug(str(e), "LLM Error")
        error_message = HumanMessage(content=f"Error: {str(e)}. The LLM failed to generate a response. Please try again or contact support.")
        return {"messages": [error_message]}

    #add analyst persona details to the system message
    system_static_message = f"""Here is the business question you need to address:

    <business_question>
    {state.business_question}
    </business_question>

    You are an advanced AI data analyst with the following characteristics:

    <analyst_name>{state.name_analyst}</analyst_name>
    <analyst_title>{state.title_analyst}</analyst_title>
    <analyst_age>{state.age_analyst}</analyst_age>
    <analyst_background>{state.background_analyst}</analyst_background>

    Your task is to provide accurate, concise answers to specific business questions using various analytical tools and data sources. Follow these steps to address the given business question:

    1. Carefully analyze the business question provided.

    2. In your analysis:
    a. List out key parameters from your analyst characteristics that might influence the analysis.
    b. Consider potential biases based on your background.
    c. Break down the question into key components and write down key terms.
    d. List potential data sources for each key term.
    e. Brainstorm multiple approaches to answering the question.
    f. Identify which tools are needed for each component.
    g. List required parameters for each tool and check their availability.
    h. Estimate the time required for each part of the analysis.
    i. Use appropriate tools to gather necessary data.
    j. If a tool doesn't provide useful results, refine your approach and try once more.
    k. If refinement fails, record it as a feature request in the Missing Data section.
    l. Combine data from multiple tools if needed for comprehensive analysis.
    m. Consider limitations and assumptions in your analysis.
    n. Evaluate the confidence level of your conclusion based on available data.

    3. Wrap your analysis in <business_analysis> tags, showing your thought process, tool usage, data compilation, and evaluation of limitations and confidence level. It's OK for this section to be quite long.

    4. After your analysis, prepare a final report with the following structure:

    <final_report>
    **Business Question:**
    [Quote the exact business question]

    **Conclusion:**
    [Provide a short, direct answer to the business question with PRECISE NUMERIC DATA including:
    - EXACT current values (e.g., "current stock price is $120.45")
    - SPECIFIC predicted values (e.g., "expected to reach $135.78")
    - CALCULATED percentage changes (e.g., "12.73% increase")
    - CONCRETE dates (e.g., "by May 15, 2025")
    - QUANTIFIED metrics (e.g., "revenue of $2.3B, profit margin of 8.7%")
    - MEASURABLE trends (e.g., "average daily volume increased by 15,000 shares")
    - TIME-BOUND predictions (e.g., "within the next 45 days")]

    **Confidence Level:**
    [State the confidence level of your conclusion with NUMERIC PROBABILITY if possible (e.g., "70% confidence")]

    **References:**
    - [List the tools and their Report IDs]

    **Limitations and Assumptions:**
    - [List any limitations or assumptions in your analysis with SPECIFIC METRICS where applicable]

    <feature_requestor>
    [List any feature requests for missing data or features]
    </feature_requestor>
    </final_report>

    5. OPTIONAL: Only if you identify a need for new components that would improve the analysis, include feature requests wrapped in <feature_requestor> tags. Feature requests will be listed as bullet points in the final report. They can be for:
       - Data loaders - for new data sources such as stock market data of specific time frame, period, interval, or Macro Economic data, or sentiment news, or other data sources that could have been used to improve the analysis.
       - Features - for new data processing methods for example news sentiment calculation, RSI calculation, VWAP calculation, MACD calculation, or other technical indicators calculation using existing data.
       - IMPORTANT: ONLY create feature requests when a tool call was attempted but failed due to missing data or features - do NOT request features that already exist.
       Be specific about what is missing if is missing data or features, while a tool encountered an error due to missing data or features, or a tool limitaion of existing data.
       Use the prefix "Missing Data:" or "Missing Features:" in the feature request.
       Be specific about the data or features you need, and the format you need it in, for example:
       - Missing Data: Missing hourly stock price data for the last 30 days.
       - Missing Features: Missing VWAP calculation for the stock price data.
       - Missing Data: Missing CPI data for the last 36 months that could have been used to calculate the inflation rate in joining with the stock price data.
       - Missing Features: Missing news sentiment calculation for the news data.
       - Missing Data: Missing weather data for the last 30 days that could have been used to calculate the weather impact on the stock price.
       Remember:
       - Feature requests should be listed as bullet points in the final report.
       - Feature requests should be only correspinsing to the business question requested, not general or vague or something wasn't discussed.
       - If you haven't identified any feature requests, just return the final report without the <feature_requestor> tags.

    CRITICAL REQUIREMENTS FOR NUMERIC DATA:
    1. NEVER use placeholder values like 0.00% or generic statements like "slight increase" - always provide SPECIFIC numeric values based on your analysis
    2. ALWAYS include the following in your conclusion:
       - Current values with exact figures (e.g., "$120.45" not "around $120")
       - Predicted future values with exact figures (e.g., "$135.78" not "approximately $136")
       - Precise percentage changes calculated to 2 decimal places (e.g., "12.73%" not "about 13%")
       - Specific dates for predictions (e.g., "by May 15, 2025" not "in the next few months")
       - Quantified metrics relevant to the business question (e.g., "revenue of $2.3B" not "increased revenue")
    3. ENSURE all numeric predictions are realistic and supported by your analysis:
       - Stock movements: Daily (±1-3%), Monthly (±5-15%), Annual (±10-30%)
       - Revenue growth: Quarterly (±1-10%), Annual (±5-25%)
       - Profit margins: Industry-specific but typically 5-20%
    4. INCLUDE any key technical indicators with exact values (e.g., "RSI of 68.5" not "overbought RSI")
    5. SPECIFY time horizons with exact dates (e.g., "by June 30, 2025" not "in Q2")

    Remember:
    - Ensure your conclusion directly answers the business question with PRECISE numeric data.
    - List all Tools and Their Report ID for reference.
    - Clearly state any limitations or assumptions in your analysis.
    - Be concise and accurate in your reporting.
    - Each conclusion must start with "As an Analyst, today is [EXACT DATE], using all the references below, my recommendation is: ..."

    Now, please proceed with your analysis and final report."""

    fin_lang = f"""You are an autonomous financial analyst AI named <analyst_name>{state.name_analyst}</analyst_name> with a background in <analyst_background>{state.background_analyst}</analyst_background>.

You answer business questions using `FinLang`, a symbolic reasoning language designed for efficient tool usage, hypothesis tracking, and summarization. Your outputs follow a structured logic-then-language pipeline.

---

<business_question>
{state.business_question}
</business_question>

---

Use the following syntax rules:

TOOL(tool_name) -> RESULT
PRED(ticker, %change, timeframe)
JOIN[method, [inputs]] -> output
OBS: observation or pattern
CONFLICT: contradicting signals
RISK: LOW|MEDIUM|HIGH
CONF: 0.00–1.00
MISSING: [data/feature/tool]
META: self-reflection, issues, suggestions
CONC -> BUY|SELL|HOLD
FINAL_SUMMARY: (natural English summary)

You may invent additional verbs/modules as needed if their structure is clean and logical.

---

Workflow:
1. Use FinLang statements to extract, reason, and combine tool outputs.
2. Evaluate conflicts, missing data, or edge cases using `CONFLICT`, `MISSING`, and `META`.
3. Assign confidence, risk level, and final recommendation.
4. Translate your symbolic reasoning into a natural English summary at the end using `FINAL_SUMMARY`.

Your outputs will be parsed, summarized, and reused by other agents. Be logical and efficient. Avoid verbose natural language except at `FINAL_SUMMARY`.
"""
    system_message_content = await registry.generate_system_message_async()
    system_message = SystemMessage(content= fin_lang + "\n\n" + system_message_content)
    human_message = HumanMessage(content=state.business_question)

    if not state.messages:
        messages_for_llm = [system_message, human_message]
    else:
        messages_for_llm = [system_message, human_message] + [msg for msg in state.messages if not isinstance(msg, (SystemMessage, HumanMessage))]

    # print_pretty(messages_for_llm)
    response = await model_with_tools.ainvoke(messages_for_llm)

    # Debug the response and tool calls
    if hasattr(response, 'tool_calls') and response.tool_calls:
        tool_calls_debug = "\n".join([
            f"[yellow]Tool:[/yellow] {call['name']}\n" +
            f"[yellow]Arguments:[/yellow] {json.dumps(call['args'], indent=2)}"
            for call in response.tool_calls
        ])
        print_debug(tool_calls_debug, "AGENT NODE: Tool Calls from LLM")
        # Return ONLY the messages update if there are tool calls
        return {"messages": [response]}

    print_debug(f"[green]Model Response:[/green]\n{response.content}", "AGENT NODE: LLM Response")
    # If no tool calls, return the message AND the completed report update
    return {"messages": [response], "completed_reports": [response.content]}

async def tools(state: AnalystState) -> Dict[str, Any]:
    print_step("Entering tools", "Tools")
    tools = await registry.get_langchain_tools_async()
    tool_executor = ToolNode(tools=tools)
    try:
        # ToolNode expects the full state containing the message with tool_calls
        print_step("State", "########State########", "pale_turquoise1",verbose=True)
        print_pretty(state.messages)
        tool_result = await tool_executor.ainvoke(state.messages)
        
        # Debug the actual format of tool_result
        print_debug(f"Tool result type: {type(tool_result)}", "Tool result debug")
        print_debug(f"Tool result content: {tool_result}", "Tool result debug")
        
        # ToolNode returns the full state merged with ToolMessages. We only want the messages.
        # Ensure tool_result is a dict and contains 'messages'
        if isinstance(tool_result, dict) and "messages" in tool_result:
             # Find the ToolMessage(s) added by the ToolNode
             last_message = state.messages[-1]
             tool_messages = [msg for msg in tool_result["messages"] if isinstance(msg, ToolMessage) and msg.tool_call_id in [tc['id'] for tc in last_message.tool_calls]]
             #if ToolNode doesn't return error, add the tool name into the tool_used list
             if tool_messages and (not isinstance(tool_result, dict) or "error" not in tool_result):
                state.tool_used.append(tool_messages[0].name)
             return {"messages": tool_messages}
        # Handle case when tool_result is a list of messages directly
        elif isinstance(tool_result, list) and all(isinstance(msg, BaseMessage) for msg in tool_result):
            tool_messages = [msg for msg in tool_result if isinstance(msg, ToolMessage)]
            if tool_messages:
                state.tool_used.append(tool_messages[0].name)
            return {"messages": tool_messages}
        # If ToolNode returns just a single message
        elif isinstance(tool_result, BaseMessage) and isinstance(tool_result, ToolMessage):
            state.tool_used.append(tool_result.name)
            return {"messages": [tool_result]}
        else:
             # Fallback or error handling if ToolNode output is unexpected
             print_debug(f"Unexpected tool_result format: {tool_result}", "Tool execution warning")
             return {"messages": [ToolMessage(content={"error": "Unexpected tool result format"}, name="error", tool_call_id="error")]}

    except Exception as e:
        print_debug(str(e), "Tool execution error")
        # Return only the message update for this branch
        return {"messages": [ToolMessage(content={"error": str(e)}, name="error", tool_call_id="error")]}

def tool_calls(state: AnalystState) -> str:
    # Use attribute access for messages
    if not state.messages:
        # Handle case where messages list might be empty
        return END
    last_message = state.messages[-1]
    if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        print_debug("Tool calls detected, continuing to tools node", "Tool detection")
        return "tools"
    return END


def create_tool_agent_subgraph() -> StateGraph:
    """Subgraph for tool agent operations"""
    # Use AnalystState as input and AnalystOutput as output
    subgraph = StateGraph(AnalystState, output=AnalystOutput)

    # Add nodes
    subgraph.add_node("analyst_agent", analyst_agent)
    subgraph.add_node("tools", tools)

    subgraph.add_edge(START, "analyst_agent")
    subgraph.add_conditional_edges(
        "analyst_agent",
        tool_calls,
        {"tools": "tools", END: END}
    )

    # Tools within each branch go back to the analyst_agent
    subgraph.add_edge("tools", "analyst_agent")


    return subgraph.compile()

graph = create_tool_agent_subgraph()

__all__ = ['graph']
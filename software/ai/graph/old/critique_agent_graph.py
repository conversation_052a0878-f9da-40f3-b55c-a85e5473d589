from typing import List, Dict, Any
from langchain_core.messages import AIMessage, SystemMessage, HumanMessage, BaseMessage, ToolMessage
from langgraph.graph import StateGraph, END, MessagesState
import json
from ai.tools import registry
from ai.llm.llm_connect import get_llm_connect
from db.research_repository import ResearchRepository

# Initialize repositories
research_repo = ResearchRepository()

# Default configuration
DEFAULT_CONFIG = {
    "score_threshold": 7,
    "cycles": 7,
    "default_question": "Should we invest in Nvidia (NVDA)? First, check today's date using TodayDateTool, then analyze current financial data."
}

class AnalystState(MessagesState):
    """State class for the analyst workflow."""
    critique_score: int = 0
    loop_count: int = 0
    business_question: str = ""
    tools_used: List[str] = []
    tool_categories_used: List[str] = []
    critique_data: Dict[str, Any] = {}
    task_id: str = ""
    final_report: str = ""

async def agent_node(state: AnalystState) -> Dict:
    """Generate next research steps based on state and feedback"""
    # Extract key data from state
    business_question = state.get("business_question", DEFAULT_CONFIG["default_question"]) if isinstance(state, dict) else getattr(state, "business_question", DEFAULT_CONFIG["default_question"])
    loop_count = state.get("loop_count", 0) if isinstance(state, dict) else getattr(state, "loop_count", 0)
    messages = state.get("messages", []) if isinstance(state, dict) else getattr(state, "messages", [])
    tools_used = state.get("tools_used", []) if isinstance(state, dict) else getattr(state, "tools_used", [])
    tool_categories_used = state.get("tool_categories_used", []) if isinstance(state, dict) else getattr(state, "tool_categories_used", [])
    task_id = state.get("task_id", "") if isinstance(state, dict) else getattr(state, "task_id", "")
    
    # Update task status if needed
    if task_id:
        if loop_count == 0 and not messages:
            await research_repo.update_workflow_stage(task_id, "__start__", "completed")
        await research_repo.update_workflow_stage(task_id, "agent", "in_progress")
    
    # Get tools and LLM
    tools = registry.get_langchain_tools()
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    if not model:
        raise ValueError("Failed to get LLM")
    
    # Bind tools
    model_with_tools = model.bind_tools(tools, tool_choice="auto")
    
    # Initialize messages with system prompt if needed
    if not messages:
        system_content = registry.generate_system_message()
        system_content += "\n\nIMPORTANT: For complete analysis, you MUST use multiple tools including date verification, financial data, forecasting, risk analysis, and sentiment analysis."
        
        new_messages = [
            SystemMessage(content=system_content),
            HumanMessage(content=business_question)
        ]
        
        return {
            "messages": new_messages,
            "business_question": business_question
        }
    
    # Process any critique feedback
    critique_data = state.get("critique_data", {}) if isinstance(state, dict) else getattr(state, "critique_data", {})
    if loop_count > 0 and critique_data:
        critique_feedback = format_critique_feedback(critique_data)
        new_message = HumanMessage(content=critique_feedback)
        return {"messages": messages + [new_message]}
    
    # Normalize message format
    formatted_messages = [msg for msg in messages if isinstance(msg, BaseMessage)]
    
    # Execute model with tools
    response = await model_with_tools.ainvoke(formatted_messages)
    
    # Return updated state
    return {
        "messages": [response],
        "tools_used": tools_used,
        "tool_categories_used": tool_categories_used,
        "loop_count": loop_count,
        "business_question": business_question,
        "task_id": task_id
    }

async def tool_node(state: AnalystState) -> Dict:
    """Execute tools requested by the agent"""
    # Extract essential data from state
    messages = state.get("messages", []) if isinstance(state, dict) else getattr(state, "messages", [])
    tools_used = state.get("tools_used", []) if isinstance(state, dict) else getattr(state, "tools_used", [])
    tool_categories_used = state.get("tool_categories_used", []) if isinstance(state, dict) else getattr(state, "tool_categories_used", [])
    task_id = state.get("task_id", "") if isinstance(state, dict) else getattr(state, "task_id", "")
    
    if task_id:
        await research_repo.update_workflow_stage(task_id, "agent", "completed")
        await research_repo.update_workflow_stage(task_id, "tools", "in_progress")
    
    # Validate message state
    if not messages or len(messages) == 0:
        return {}
        
    last_message = messages[-1]
    if not isinstance(last_message, AIMessage):
        return {}
    
    # Extract tool calls
    tool_calls = []
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        tool_calls = last_message.tool_calls
    elif hasattr(last_message, "additional_kwargs") and "tool_calls" in last_message.additional_kwargs:
        tool_calls = last_message.additional_kwargs.get("tool_calls", [])
    
    if not tool_calls:
        return {}
    
    # Create tool dictionary for efficient lookup
    available_tools = registry.get_langchain_tools()
    tool_dict = {tool.name: tool for tool in available_tools}
    
    # Process all tool calls
    tool_messages = []
    for tool_call in tool_calls:
        try:
            # Extract tool details
            tool_name = None
            tool_id = None
            args = {}
            
            # Handle object-style tool calls
            if hasattr(tool_call, "function") and hasattr(tool_call.function, "name"):
                tool_name = tool_call.function.name
                tool_id = getattr(tool_call, "id", "")
                args_str = getattr(tool_call.function, "arguments", "{}")
                
                # Parse arguments
                if isinstance(args_str, str):
                    try:
                        args = json.loads(args_str)
                    except json.JSONDecodeError:
                        args = {}
                else:
                    args = args_str
                    
            # Handle dictionary-style tool calls
            elif isinstance(tool_call, dict):
                if "function" in tool_call:
                    tool_name = tool_call.get("function", {}).get("name")
                    args_str = tool_call.get("function", {}).get("arguments", "{}")
                else:
                    tool_name = tool_call.get("name")
                    args_str = tool_call.get("args", "{}")
                    
                tool_id = tool_call.get("id", "")
                
                # Parse arguments
                if isinstance(args_str, str):
                    try:
                        args = json.loads(args_str)
                    except json.JSONDecodeError:
                        args = {}
                else:
                    args = args_str
            
            # Skip if no tool name
            if not tool_name:
                continue
            
            # Track tool usage
            if tool_name not in tools_used:
                tools_used.append(tool_name)
                
            # Track tool category
            category = categorize_tool(tool_name)
            if category and category not in tool_categories_used:
                tool_categories_used.append(category)
            
            # Execute the tool
            if tool_name in tool_dict:
                tool = tool_dict[tool_name]
                result = await tool.ainvoke(args)
                
                # Create tool message
                tool_message = ToolMessage(
                    content=str(result),
                    tool_call_id=tool_id,
                    name=tool_name
                )
                
                tool_messages.append(tool_message)
            else:
                # Tool not found
                tool_messages.append(ToolMessage(
                    content=f"Error: Tool '{tool_name}' not found",
                    tool_call_id=tool_id,
                    name=tool_name
                ))
        
        except Exception as e:
            # Extract tool name and ID if possible
            tool_name = getattr(tool_call, "name", None)
            if not tool_name and hasattr(tool_call, "function"):
                tool_name = getattr(tool_call.function, "name", "unknown")
            if not tool_name and isinstance(tool_call, dict):
                tool_name = tool_call.get("name") or tool_call.get("function", {}).get("name", "unknown")
            
            tool_id = getattr(tool_call, "id", "")
            if not tool_id and isinstance(tool_call, dict):
                tool_id = tool_call.get("id", "")
            
            # Add error message
            tool_messages.append(ToolMessage(
                content=f"Error executing tool {tool_name}: {str(e)}",
                tool_call_id=tool_id,
                name=tool_name if tool_name else "unknown"
            ))
    
    # Update task status
    if task_id:
        await research_repo.update_workflow_stage(task_id, "tools", "completed")
    
    # Return updated state
    return {
        "messages": tool_messages,
        "tools_used": tools_used,
        "tool_categories_used": tool_categories_used
    }

async def critique_node(state: AnalystState) -> Dict:
    """Evaluate research quality and suggest improvements"""
    # Extract key data from state
    messages = state.get("messages", []) if isinstance(state, dict) else getattr(state, "messages", [])
    loop_count = state.get("loop_count", 0) if isinstance(state, dict) else getattr(state, "loop_count", 0)
    tools_used = state.get("tools_used", []) if isinstance(state, dict) else getattr(state, "tools_used", [])
    tool_categories_used = state.get("tool_categories_used", []) if isinstance(state, dict) else getattr(state, "tool_categories_used", [])
    business_question = state.get("business_question", DEFAULT_CONFIG["default_question"]) if isinstance(state, dict) else getattr(state, "business_question", DEFAULT_CONFIG["default_question"])
    task_id = state.get("task_id", "") if isinstance(state, dict) else getattr(state, "task_id", "")
    score_threshold = DEFAULT_CONFIG.get("score_threshold")
    cycles = DEFAULT_CONFIG.get("cycles")
    
    # Update task status
    if task_id:
        try:
            if any(isinstance(msg, ToolMessage) for msg in messages[-3:]):
                await research_repo.update_workflow_stage(task_id, "tools", "completed")
            else:
                await research_repo.update_workflow_stage(task_id, "agent", "completed")
        except Exception:
            pass
            
        await research_repo.update_workflow_stage(task_id, "critique", "in_progress")
    
    # Get LLM
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    if not model:
        raise ValueError("Failed to get LLM for critique")
    
    # Find the latest AI response
    latest_findings = ""
    for msg in reversed(messages):
        if isinstance(msg, AIMessage) and msg.content and msg.content.strip():
            latest_findings = msg.content
            break
    
    if not latest_findings:
        # No content to critique
        ticker = extract_ticker(business_question)
        improved_question = f"Analyze the investment case for {ticker}. Use diverse tools including date verification, financial data gathering, forecasting, risk assessment, and sentiment analysis."
        
        return {
            "messages": [msg for msg in messages if isinstance(msg, SystemMessage)],
            "critique_score": 0,
            "loop_count": loop_count,
            "business_question": improved_question,
            "critique_data": {
                "score": 0,
                "strengths": [],
                "weaknesses": ["No analysis to evaluate"],
                "improved_question": improved_question
            }
        }
    
    # Create critique prompt
    critique_prompt = create_critique_prompt(latest_findings, tools_used, tool_categories_used)
    
    # Generate critique
    critique_response = await model.ainvoke([HumanMessage(content=critique_prompt)])
    critique_text = critique_response.content
    
    # Parse JSON response
    try:
        # Find JSON in the response
        json_start = critique_text.find("{")
        json_end = critique_text.rfind("}") + 1
        
        if json_start >= 0 and json_end > json_start:
            critique_data = json.loads(critique_text[json_start:json_end])
            
            # Extract critique elements
            score = int(critique_data.get("score", 0))
            strengths = critique_data.get("strengths", [])
            weaknesses = critique_data.get("weaknesses", [])
            improved_question = critique_data.get("improved_question", "")
            
            # Increment loop count if score is good enough
            new_loop_count = loop_count + 1 if score >= score_threshold else loop_count
            
            # Ensure tool diversity
            if len(tool_categories_used) < 3 and "tools" not in improved_question.lower():
                missing_categories = {"date_time", "forecast", "financial", "risk", "sentiment"} - set(tool_categories_used)
                missing_cats_str = ", ".join(missing_categories)
                improved_question += f" Be sure to use more diverse tools, especially for {missing_cats_str}."
            
            # Update workflow stage
            if task_id:
                await research_repo.update_workflow_stage(task_id, "critique", "completed")
                
                if score >= score_threshold or new_loop_count >= cycles:
                    await research_repo.update_workflow_stage(task_id, "summarize", "in_progress")
                else:
                    await research_repo.update_workflow_stage(task_id, "agent", "in_progress")
            
            # Return updated state
            return {
                "critique_score": score,
                "loop_count": new_loop_count,
                "business_question": improved_question if improved_question else business_question,
                "critique_data": {
                    "score": score,
                    "strengths": strengths,
                    "weaknesses": weaknesses,
                    "improved_question": improved_question
                }
            }
        else:
            raise ValueError("Failed to extract JSON from critique response")
            
    except Exception as e:
        # Fallback critique data
        score = 3
        ticker = extract_ticker(business_question)
        improved_question = f"Analyze the investment potential of {ticker} using current financial data. Use multiple tools including date verification, financial data gathering, forecasting, risk analysis, and sentiment analysis."
        
        return {
            "critique_score": score,
            "loop_count": loop_count,
            "business_question": improved_question,
            "critique_data": {
                "score": score,
                "strengths": [],
                "weaknesses": ["Analysis needs improvement in data quality and tool diversity"],
                "improved_question": improved_question
            }
        }

async def summarize_node(state: AnalystState) -> Dict:
    """Create a comprehensive final report based on gathered evidence"""
    # Extract key data
    messages = state.get("messages", []) if isinstance(state, dict) else getattr(state, "messages", [])
    tools_used = state.get("tools_used", []) if isinstance(state, dict) else getattr(state, "tools_used", [])
    tool_categories_used = state.get("tool_categories_used", []) if isinstance(state, dict) else getattr(state, "tool_categories_used", [])
    business_question = state.get("business_question", DEFAULT_CONFIG["default_question"]) if isinstance(state, dict) else getattr(state, "business_question", DEFAULT_CONFIG["default_question"])
    task_id = state.get("task_id", "") if isinstance(state, dict) else getattr(state, "task_id", "")
    
    # Update task status
    if task_id:
        await research_repo.update_workflow_stage(task_id, "summarize", "in_progress")
    
    # Get LLM
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    if not model:
        raise ValueError("Failed to get LLM for summary")
    
    # Extract AI responses and tool results
    ai_findings = []
    tool_results = []
    
    for msg in messages:
        if isinstance(msg, AIMessage) and msg.content:
            if not hasattr(msg, "tool_calls") or not msg.tool_calls:
                ai_findings.append(msg.content)
        elif isinstance(msg, ToolMessage):
            tool_results.append({
                "tool": msg.name,
                "result": msg.content
            })
    
    # Calculate tool usage metrics
    tool_diversity_score = min(len(set(tool_categories_used)) * 2, 10)
    missing_categories = list({"date_time", "forecast", "financial", "risk", "sentiment"} - set(tool_categories_used))
    
    # Create summary prompt
    summary_prompt = create_summary_prompt(
        business_question, 
        tools_used,
        tool_categories_used,
        tool_diversity_score,
        missing_categories,
        tool_results,
        ai_findings
    )
    
    # Generate the summary
    summary_response = await model.ainvoke([HumanMessage(content=summary_prompt)])
    final_report = summary_response.content
    
    # Update task status
    if task_id:
        await research_repo.update_workflow_stage(task_id, "summarize", "completed")
        await research_repo.update_workflow_stage(task_id, "END", "in_progress")
        await research_repo.update_workflow_stage(task_id, "END", "completed")
    
    # Return the final state with report
    return {
        "final_report": final_report
    }

def route_from_agent(state: AnalystState) -> str:
    """Determine whether to route to tools or critique after agent execution"""
    messages = state.get("messages", []) if isinstance(state, dict) else getattr(state, "messages", [])
    
    if not messages:
        return "critique"
        
    last_message = messages[-1] if messages else None
    has_tool_calls = False
    
    if isinstance(last_message, AIMessage):
        if hasattr(last_message, "tool_calls") and last_message.tool_calls:
            has_tool_calls = True
        elif hasattr(last_message, "additional_kwargs") and "tool_calls" in last_message.additional_kwargs:
            has_tool_calls = True
    
    return "tools" if has_tool_calls else "critique"

def route_from_critique(state: AnalystState) -> str:
    """Determine whether to continue research or generate final report"""
    critique_score = state.get("critique_score", 0) if isinstance(state, dict) else getattr(state, "critique_score", 0)
    loop_count = state.get("loop_count", 0) if isinstance(state, dict) else getattr(state, "loop_count", 0)
    
    score_threshold = DEFAULT_CONFIG.get("score_threshold", 7)
    cycles = DEFAULT_CONFIG.get("cycles", 7)
    
    if critique_score >= score_threshold or loop_count >= cycles:
        return "summarize"
    else:
        return "agent"

def format_critique_feedback(critique_data: Dict) -> str:
    """Format critique feedback for user consumption"""
    score = critique_data.get("score", 0)
    strengths = critique_data.get("strengths", [])
    weaknesses = critique_data.get("weaknesses", [])
    improved_question = critique_data.get("improved_question", "")
    
    strengths_text = "\n".join([f"- {s}" for s in strengths]) if strengths else "- None identified"
    weaknesses_text = "\n".join([f"- {w}" for w in weaknesses]) if weaknesses else "- None identified"
    
    feedback = (
        f"**Research Quality Assessment**\n\n"
        f"**Score**: {score}/10\n\n"
        f"**Strengths**:\n{strengths_text}\n\n"
        f"**Areas for Improvement**:\n{weaknesses_text}\n\n"
    )
    
    if improved_question:
        feedback += f"**Improved Question**:\n{improved_question}\n\n"
    
    feedback += (
        f"**Action Required**:\n"
        f"Please address the identified weaknesses in your next analysis. "
        f"Use multiple tools including date verification, financial data, forecasting, "
        f"risk analysis, and sentiment analysis for comprehensive research."
    )
    
    return feedback

def categorize_tool(tool_name: str) -> str:
    """Categorize tool based on its name"""
    tool_name = tool_name.lower() if tool_name else ""
    
    categories = {
        "date_time": ["date", "time", "calendar", "today"],
        "forecast": ["forecast", "predict", "workflow", "join", "future"],
        "financial": ["financial", "stock", "market", "price", "extract", "economic"],
        "risk": ["risk", "volatility", "uncertainty", "downside"],
        "sentiment": ["news", "sentiment", "opinion", "social", "media"]
    }
    
    for category, keywords in categories.items():
        if any(keyword in tool_name for keyword in keywords):
            return category
    
    return "other"

def extract_ticker(business_question: str) -> str:
    """Extract ticker symbol from business question"""
    if "(" in business_question and ")" in business_question:
        start = business_question.find("(") + 1
        end = business_question.find(")")
        return business_question[start:end]
    return "NVDA"  # Default

def create_critique_prompt(latest_findings: str, tools_used: List[str], tool_categories_used: List[str]) -> str:
    """Create a prompt for critiquing the research"""
    return f"""
    INVESTMENT ANALYSIS EVALUATION

    Research findings:
    {latest_findings}

    Tools used: {', '.join(tools_used) if tools_used else "None"}
    Tool categories used: {', '.join(tool_categories_used) if tool_categories_used else "None"}

    Evaluate this investment analysis on a scale of 1-10:

    1. Evidence-based: Are claims supported by specific data and facts?
    2. Risk assessment: Are potential downsides clearly identified?
    3. Actionable advice: Does it provide clear investment recommendations?
    4. Tool diversity: Does it use multiple different tool categories for comprehensive analysis?
       (date verification, financial data, forecasting, risk assessment, sentiment analysis)
    5. Objectivity: Is the analysis free from bias and balanced?

    Respond with a JSON object containing:
    {{
        "score": <number 1-10>,
        "strengths": [<key strengths, 1-3 bullet points>],
        "weaknesses": [<improvement areas, 1-3 bullet points>],
        "improved_question": <focused follow-up question that emphasizes using diverse tools>
    }}
    """

def create_summary_prompt(
    business_question: str,
    tools_used: List[str],
    tool_categories_used: List[str],
    tool_diversity_score: int,
    missing_categories: List[str],
    tool_results: List[Dict],
    ai_findings: List[str]
) -> str:
    """Create a prompt for summarizing the research"""
    return f"""
    INVESTMENT REPORT

    Create a comprehensive investment analysis report for: "{business_question}"
    
    FORMAT:
    1. EXECUTIVE SUMMARY: A concise overview with clear investment recommendation
    2. METHODOLOGY: Detailed explanation of the tools and data sources used
    3. FINANCIAL ANALYSIS: Key financial metrics and performance indicators
    4. RISK ASSESSMENT: Identified risks with quantitative measures
    5. MARKET POSITION: Competitive landscape and market trends
    6. INVESTMENT RECOMMENDATION: Clear Buy/Sell/Hold recommendation with confidence level
    7. REFERENCES: Data sources and tools used
    
    AVAILABLE DATA:
    - Tools used: {tools_used}
    - Tool categories: {tool_categories_used}
    - Tool diversity score: {tool_diversity_score}/10
    - Missing tool categories: {missing_categories}
    
    TOOL RESULTS:
    {json.dumps(tool_results, indent=2)}
    
    AI ANALYSIS:
    {json.dumps(ai_findings[-2:] if len(ai_findings) > 2 else ai_findings, indent=2)}
    
    IMPORTANT GUIDELINES:
    - Be objective and data-driven in your analysis
    - Acknowledge any limitations in the data or analysis
    - Make a clear investment recommendation with justification
    - Reference specific tools and data sources when making claims
    """

def create_workflow_graph():
    """Build and compile the analyst graph with the specified structure"""
    # Create graph with state definition
    workflow = StateGraph(AnalystState)
    
    # Add core nodes
    workflow.add_node("agent", agent_node)
    workflow.add_node("tools", tool_node)
    workflow.add_node("critique", critique_node)
    workflow.add_node("summarize", summarize_node)
    
    # Set entry point
    workflow.set_entry_point("agent")
    
    # Connect nodes according to the diagram
    # Agent -> Tools or Critique
    workflow.add_conditional_edges(
        "agent",
        lambda state: route_from_agent(state),
        {
            "tools": "tools",
            "critique": "critique"
        }
    )
    
    # Tools always returns to Agent
    workflow.add_edge("tools", "agent")
    
    # Critique -> Agent or Summarize
    workflow.add_conditional_edges(
        "critique",
        lambda state: route_from_critique(state),
        {
            "agent": "agent",
            "summarize": "summarize"
        }
    )
    
    # Summarize -> END
    workflow.add_edge("summarize", END)
    
    # Compile the graph without memory parameter (using updated API)
    return workflow.compile()

# Create the graph
graph = create_workflow_graph()

# Export only the graph
__all__ = ['graph'] 
from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import print_step, print_debug, print_pretty
from langgraph.constants import Send
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from typing import List, Dict, Any, TypedDict, Annotated
from typing_extensions import Annotated
import operator
from software.ai.tools import registry
from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from software.db.research_repository import ResearchRepository
from software.ai.graph.search_scope_graph import graph as search_scope_graph

research_repo = ResearchRepository()

class Task(TypedDict):
    id: int
    description: str
    status: str

class ResearchPlan(BaseModel):
    tasks: List[str] = Field(default_factory=list, description="The tasks to be completed by the research agents")
    model_config = {"extra": "forbid"}

class DeepResearchState(TypedDict, total=False):
    business_question: str
    tasks: List[Task]
    current_task_index: int
    messages: Annotated[List[Dict[str, Any]], operator.add]
    results: Annotated[List[Dict[str, Any]], operator.add]
    director_id: str
    task_id: str

async def create_plan(state: DeepResearchState):
    """Creates a research plan with tasks"""
    print_debug("Creating research plan", "create_plan")

    # Update workflow stage - starting plan creation
    await research_repo.update_workflow_stage(state["task_id"], "__start__", "in_progress")
    await research_repo.update_workflow_stage(state["task_id"], "__start__", "completed")
    await research_repo.update_workflow_stage(state["task_id"], "create_plan", "in_progress")

    business_question = state.get("business_question", "")
    tool_prompt = await registry.generate_system_message_async()

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()

    # Create structured output model
    structured_model = model.with_structured_output(ResearchPlan)

    # Create system message
    system_message = SystemMessage(content=f"""<role>
You are a strategic financial research director with expertise in breaking down complex investment questions into actionable research tasks. Your analytical approach combines technical analysis, fundamental research, and market sentiment evaluation.
</role>

<context>
The business question requires a comprehensive research plan that will be executed by specialized research agents. Each agent will focus on a specific aspect of the analysis to provide a complete picture.

AVAILABLE TOOLS:
{tool_prompt}
</context>

<task>
Create a structured research plan by breaking down the business question into distinct research tasks that directly leverage the capabilities of the available tools.
</task>

<reasoning_steps>
When creating a research plan:
1. First, carefully analyze the available tools and their specific capabilities
2. Next, identify the core components of the business question that align with these tool capabilities
3. Then, create tasks that can be directly executed using the available tools
4. Finally, ensure the tasks collectively provide a comprehensive answer to the business question
</reasoning_steps>

<output_format>
Your output must be a valid JSON matching this schema:
{ResearchPlan().model_dump_json()}
</output_format>

<instructions>
- Return a list of tasks in the 'tasks' field
- Each task should be a clear, actionable instruction
- Each task should be specified the name of the tool to use, followed with the task.
- Each task must include the names of the companies to analyze - don't leave it to the tool.
- The list must be in small footsteps, we can always add more tasks later.
- The list cannot be empty
- Do not include task numbers or bullet points in the task text
- Each task MUST be directly executable using the specific capabilities of the available tools
- Do NOT create tasks that require tools not listed in the available tools
- Tailor each task to match the exact functionality described in the tool descriptions
</instructions>

<error_handling>
If you encounter challenges:
1. If the business question doesn't align with tool capabilities, create tasks that use the tools in the most relevant way possible
2. If a tool has limited functionality, focus tasks on what the tool CAN do rather than creating tasks it cannot perform
3. If multiple interpretations are possible, prioritize tasks that match the explicit capabilities of the available tools
</error_handling>

<success_criteria>
A successful research plan will:
- Directly address the business question using only the available tools
- Fully leverage the specific capabilities described in each tool
- Provide clear, executable instructions that match tool functionality
- Ensure each task can be completed with the available tools
- Collectively build toward a comprehensive investment recommendation
</success_criteria>
""")

    # Create user message
    user_message = HumanMessage(content=f"""I need a research plan to answer this investment question:

{business_question}

Please create research tasks that directly utilize the capabilities of our available tools. Focus on what our tools can actually do rather than creating tasks they cannot perform.""")

    # Get plan from model
    plan = await structured_model.ainvoke([system_message, user_message])
    print_pretty([system_message, user_message])
    print_step(f"Created {len(plan.tasks)} research tasks", "create_plan")
    print_pretty(plan)

    # Convert to Task format
    tasks = []
    for i, task_description in enumerate(plan.tasks):
        tasks.append({
            "id": i + 1,
            "description": task_description,
            "status": "pending"
        })

    # Ensure we have at least one task
    if not tasks:
        tasks = [{
            "id": 1,
            "description": f"Analyze {business_question}",
            "status": "pending"
        }]

    print_debug(f"Created {len(tasks)} research tasks", "create_plan")

    # Update workflow stage - plan creation completed
    await research_repo.update_workflow_stage(state["task_id"], "create_plan", "completed")

    return {
        "tasks": tasks,
        "current_task_index": 0
    }

async def coordinator(state: DeepResearchState):
    """Coordinates the research process by sending tasks to supervisors"""
    print_debug("Coordinator processing", "coordinator")

    # Update workflow stage - coordinator processing
    await research_repo.update_workflow_stage(state["task_id"], "coordinator", "in_progress")

    tasks = state.get("tasks", [])
    current_index = state.get("current_task_index", 0)

    # Check if we've processed all tasks
    if current_index >= len(tasks):
        print_debug("All tasks completed, moving to synthesis", "coordinator")
        await research_repo.update_workflow_stage(state["task_id"], "coordinator", "completed")
        return {"current_task_index": current_index}

    # Get the current task
    current_task = tasks[current_index]

    # Mark task as in progress
    current_task["status"] = "in_progress"

    # Create isolated message context for this supervisor
    task_message = HumanMessage(content=current_task["description"])

    # Create completely isolated state for the supervisor
    # Only pass the necessary information, don't include any existing messages
    supervisor_state = {
        "messages": [task_message],  # Start with a fresh message list containing only the current task
        "task_id": current_task["id"],
        "business_question": state.get("business_question", ""),
    }

    print_debug(f"Sending task {current_task['id']} to supervisor", "coordinator")

    # Update workflow stage - coordinator completed for this task
    await research_repo.update_workflow_stage(state["task_id"], "coordinator", "completed")

    # Send to supervisor with isolated state
    return Send("supervisor", supervisor_state)

async def create_supervisor_node(_):
    """Creates a supervisor research agent with tools"""
    print_debug("Creating supervisor node", "create_supervisor_node")

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()

    tools = await registry.get_langchain_tools_async()

    # Create a fresh supervisor agent for each task to avoid state accumulation
    supervisor = create_react_agent(
        model=model,
        tools=tools,
        name="supervisor",
        prompt="""<role>
You are a financial research supervisor specializing in analyzing investment opportunities and market trends. Your expertise is in using specialized tools to gather and synthesize financial data.
</role>

<task>
Analyze the specific financial research task you've been assigned using the available tools. Provide a clear, concise summary of your findings.
</task>

<workflow>
1. Carefully analyze the task to determine which tools are needed
2. Use the appropriate tools to gather relevant data
3. Synthesize the tool outputs into a comprehensive summary
4. Present only factual information derived from tool outputs
</workflow>

<error_handling>
If tools fail or cannot be used effectively:
1. Try ONE more time with adjusted parameters if appropriate
2. If tools still fail or are not applicable, respond with exactly "NO_TOOLS"
3. NEVER make up information or provide analysis without tool support
4. NEVER ask for human confirmation or intervention
</error_handling>

<success_criteria>
A successful response will:
1. Directly address the assigned task using tool-generated data
2. Present a clear, concise summary of findings
3. Include only factual information from tools
4. Maintain a professional, analytical tone
5. Return "NO_TOOLS" if tools cannot provide the necessary information
</success_criteria>"""
    )

    print_debug("Created supervisor research agent", "create_supervisor_node")
    return supervisor

async def process_results(state: DeepResearchState):
    """Processes results from the supervisor and updates task status"""
    print_debug("Processing supervisor results", "process_results")

    # Update workflow stage - processing results
    await research_repo.update_workflow_stage(state["task_id"], "process_results", "in_progress")

    # Extract the last assistant message
    messages = state.get("messages", [])
    print_pretty(messages)

    # Find AI messages (could be AIMessage objects or dicts with role=assistant)
    assistant_messages = []
    for msg in messages:
        if hasattr(msg, "type") and msg.type == "ai":
            assistant_messages.append(msg)
        elif hasattr(msg, "content") and hasattr(msg, "name") and msg.name == "supervisor":
            assistant_messages.append(msg)
        elif isinstance(msg, dict) and msg.get("role") == "assistant":
            assistant_messages.append(msg)

    if not assistant_messages:
        print_debug("No assistant messages found", "process_results")
        await research_repo.update_workflow_stage(state["task_id"], "process_results", "failed")
        return {"messages": []}  # Return empty messages to clear state

    # Get the last message content
    if isinstance(assistant_messages[-1], dict):
        result = assistant_messages[-1].get("content", "")
    else:
        result = assistant_messages[-1].content

    # Store the result
    task_id = state.get("task_id")
    task_result = {
        "task_id": task_id,
        "result": result
    }

    # Update workflow stage - results processed
    await research_repo.update_workflow_stage(state["task_id"], "process_results", "completed")
    # Also update supervisor stage as completed
    await research_repo.update_workflow_stage(state["task_id"], "supervisor", "completed")

    # Return the results to be added to the main state and explicitly clear messages
    # This prevents message accumulation between supervisor invocations
    return {
        "results": [task_result],
        "messages": []  # Explicitly clear all messages to prevent accumulation
    }

async def check_completion(state: DeepResearchState):
    """Checks if all tasks are completed and decides next steps"""
    print_debug("Checking completion status", "check_completion")

    # Task ID is available in state

    tasks = state.get("tasks", [])
    current_index = state.get("current_task_index", 0)

    # Update the current task status to completed
    if current_index < len(tasks):
        tasks[current_index]["status"] = "completed"

    # Increment the task index
    next_index = current_index + 1

    # Check if we've completed all tasks
    if next_index >= len(tasks):
        print_debug("All tasks completed, moving to synthesis", "check_completion")
        await research_repo.update_workflow_stage(state["task_id"], "check_completion", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "synthesize_findings", "in_progress")
        return "synthesize"

    # Continue with the next task
    print_debug(f"Moving to next task {next_index + 1}/{len(tasks)}", "check_completion")
    await research_repo.update_workflow_stage(state["task_id"], "check_completion", "completed")
    return "continue"

async def synthesize_findings(state: DeepResearchState):
    """Synthesizes all findings into a final recommendation"""
    print_debug("Synthesizing findings", "synthesize_findings")

    # Update workflow stage - synthesizing findings
    await research_repo.update_workflow_stage(state["task_id"], "synthesize_findings", "in_progress")

    business_question = state.get("business_question", "")
    results = state.get("results", [])

    # Format results
    findings_text = "\n\n".join([f"Task {r.get('task_id')}: {r.get('result', '')}" for r in results])

    final_message = f"""
    Based on our analysis of {business_question}, here is a summary:

    {findings_text}
    """

    print_step(final_message, "Final Recommendation", "gold1", verbose=True)

    # Update workflow stage - synthesis completed and mark END as in_progress then completed
    await research_repo.update_workflow_stage(state["task_id"], "synthesize_findings", "completed")
    await research_repo.update_workflow_stage(state["task_id"], "END", "in_progress")
    await research_repo.update_workflow_stage(state["task_id"], "END", "completed")

    return {
        "messages": [{"role": "assistant", "content": final_message}]
    }

async def update_task_index(state: DeepResearchState):
    """Updates the task index to move to the next task"""
    await research_repo.update_workflow_stage(state["task_id"], "update_task_index", "in_progress")
    current_index = state.get("current_task_index", 0)
    await research_repo.update_workflow_stage(state["task_id"], "update_task_index", "completed")
    return {"current_task_index": current_index + 1}



def create_graph():
    """Creates the deep research graph"""
    builder = StateGraph(DeepResearchState)

    # Add nodes
    builder.add_node("search_scope", search_scope_graph)
    builder.add_node("create_plan", create_plan)
    builder.add_node("coordinator", coordinator)
    builder.add_node("supervisor", create_supervisor_node)
    builder.add_node("process_results", process_results)
    builder.add_node("update_task_index", update_task_index)
    builder.add_node("synthesize_findings", synthesize_findings)

    # Define edges
    builder.add_edge(START, "search_scope")
    builder.add_edge("search_scope", "create_plan")
    builder.add_edge("create_plan", "coordinator")
    builder.add_edge("coordinator", "supervisor")
    builder.add_edge("supervisor", "process_results")

    # Add conditional edges for task completion
    builder.add_conditional_edges(
        "process_results",
        check_completion,
        {
            "continue": "update_task_index",
            "synthesize": "synthesize_findings"
        }
    )

    builder.add_edge("update_task_index", "coordinator")
    builder.add_edge("synthesize_findings", END)

    # Compile with increased recursion limit to prevent errors
    return builder.compile()

graph = create_graph()

__all__ = ['graph']

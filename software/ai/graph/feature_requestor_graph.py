from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import Director<PERSON><PERSON>, FeatureRequestList, print_step, print_debug, print_pretty
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.llm.llm_connect import get_llm_connect

async def list_feature_requests(state: DirectorState) -> DirectorState:
    print_step("Entering feature requestor", "Feature Requestor")
    completed_reports = state.get("completed_reports", [])
    unique_requests = set()
    print_debug(f"Completed reports: {completed_reports}", "Show all reports")

    #parse only between <feature_requestor> and </feature_requestor>    
    for report in completed_reports:
        if "<feature_requestor>" in report and "</feature_requestor>" in report:
            feature_requests = report.split("<feature_requestor>")[1].split("</feature_requestor>")[0]
            unique_requests.add(feature_requests)
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "list_feature_requests")
    structured_model = model.with_structured_output(FeatureRequestList)

    system_message = SystemMessage(content="""You are a professional Pydantic Parser. You are given a list of feature requests and you need to break down the list into a list of feature requests.
    
For each feature request:
1. Extract the actual request text
2. Classify it as either "data" (if it's requesting missing data) or "feature" (if it's requesting new functionality)

Each output item must have both feature_request and feature_type fields.""")
    
    # Convert set to string content for the HumanMessage
    request_content = "\n\n".join(unique_requests)
    human_message = HumanMessage(content=request_content)
    
    # Invoke model with proper message sequence
    response = await structured_model.ainvoke([system_message, human_message])
    
    print_pretty(response)
    return {"feature_requests": response.feature_requests}

async def production_ready_requests(state: DirectorState) -> DirectorState:
    print_step("Refining feature requests for production", "Production Ready Requests")
    
    feature_requests = state.get("feature_requests", [])
    if not feature_requests:
        return {"feature_requests": []}
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "production_ready_requests")
    structured_model = model.with_structured_output(FeatureRequestList)
    
    # Dynamically load content from base files
    base_files = {
        "base_data": "software/data/base_data.py",
        "base_feature": "software/features/base_feature.py",
        "test_data": "software/tests/test_data.py",
        "test_features": "software/tests/test_features.py",
    }
    
    file_contents = {}
    for name, path in base_files.items():
        try:
            with open(path, 'r') as file:
                file_contents[name] = file.read()
            print_debug(f"Successfully loaded {name} from {path}", "File Loading")
        except Exception as e:
            print_debug(f"Error loading {name} from {path}: {str(e)}", "File Loading Error")
            file_contents[name] = f"# Error loading file: {str(e)}"
    
    # Build dynamic system prompt with appropriate excerpts based on file size
    try:
        test_data_excerpt = file_contents["test_data"][:2000] if len(file_contents["test_data"]) > 2000 else file_contents["test_data"]
        test_features_excerpt = file_contents["test_features"] if len(file_contents["test_features"]) < 5000 else file_contents["test_features"][:5000]
        
        system_prompt = """You are a software engineering expert tasked with transforming general feature requests into specific, actionable development tasks.

Below is the content of our base classes and tests to help you understand our implementation requirements:

====== BASE DATA LOADER ======
```python
{base_data}
```

====== BASE FEATURE ======
```python
{base_feature}
```

====== TESTS FOR DATA LOADERS ======
```python
# Excerpts from test_data.py
{test_data_excerpt}
```

====== TESTS FOR FEATURES ======
```python
# Excerpts from test_features.py
{test_features_excerpt}
```

Based on the above code:

Review the incoming feature requests and transform them into production-ready specifications following these strict guidelines:

CRITICAL RULES:
1. SPLIT bundled requests - if a request mentions multiple indicators or features, create SEPARATE production-ready requests for EACH one
2. Features are CALCULATIONS only - not ML models or forecasting systems
3. Data loaders must be GENERIC - never specific to individual tickers/symbols
4. ALWAYS specify time interval for data loaders (daily, hourly, minute)
5. Class names must be descriptive and follow naming conventions

EXACT REQUIREMENTS FOR PRODUCTION-READY REQUESTS:

For data loaders:
- Class name MUST end with "Loader" and include interval (e.g., "DailyStockPriceLoader", "HourlyOptionDataLoader")
- Class names MUST be GENERIC and NOT ticker-specific ("StockPriceLoader" not "NikeStockPriceLoader")
- Must specify EXACT initialization parameters including interval (interval="daily", interval="hourly")
- Must specify time period parameter (period="30d", period="1y", etc.)
- Must list expected output columns (e.g., "Returns DataFrame with columns: Open, High, Low, Close")
- Must note any specific validation requirements (e.g., "requires minimum 100 data points")

For features:
- Each feature must be a SINGLE calculation (one indicator per feature)
- Class name should be CamelCase and clearly indicate purpose (e.g., "RelativeVolume", "RSICalculator")
- Must include explicit calculate() method parameters (e.g., "calculate(data, window=14)")
- Must specify exact return type (e.g., "returns pd.Series of momentum values")
- Must define calculation logic concisely (e.g., "calculates 14-day RSI using exponential average")
- Must note data requirements (e.g., "requires 'Close' column in input data")

EXAMPLES OF WELL-FORMED REQUESTS:

DATA example: 
"Create DailyStockPriceLoader class that loads historical stock price data with daily intervals. Should accept parameters (ticker, start_date, end_date, period='1y'). Returns DataFrame with columns: Date, Open, High, Low, Close, Volume, Adj Close. Validates for minimum 20 data points and chronological order."

DATA example:
"Create HourlyOptionDataLoader class that loads hourly option chain data. Should accept parameters (ticker, expiration_date, interval='hourly', period='30d'). Returns DataFrame with columns: Date, Strike, Bid, Ask, Volume, Open Interest, Implied Volatility. Validates data completeness for each interval."

FEATURE example:
"Create RSICalculator class that calculates Relative Strength Index. Implements calculate(data, window=14) method that computes RSI using ratio of average gains to average losses over the specified window. Returns a pandas Series. Requires 'Close' column in input data."

FEATURE example:
"Create BollingerBands class that calculates upper and lower bands based on price volatility. Implements calculate(data, window=20, std_dev=2) that computes moving average and standard deviation bands. Returns DataFrame with columns: 'Middle', 'Upper', 'Lower'. Requires 'Close' column in input data."

BAD TO GOOD TRANSFORMATION EXAMPLES:

BAD: "Add multiple technical indicators such as RSI, MACD, and Bollinger Bands"
GOOD: [Split into three separate requests]
1. "Create RSICalculator class that calculates Relative Strength Index. Implements calculate(data, window=14) method that returns a pandas Series of RSI values. Requires 'Close' column in input data."
2. "Create MACDCalculator class that calculates Moving Average Convergence Divergence. Implements calculate(data, fast=12, slow=26, signal=9) method that returns a DataFrame with 'MACD', 'Signal', and 'Histogram' columns. Requires 'Close' column in input data."
3. "Create BollingerBands class that calculates Bollinger Bands. Implements calculate(data, window=20, std_dev=2) method that returns a DataFrame with 'Middle', 'Upper', and 'Lower' band columns. Requires 'Close' column in input data."

BAD: "Create SalesDataLoader for Nike and Adidas"
GOOD: "Create QuarterlySalesLoader class that loads quarterly sales data for any company. Should accept parameters (ticker, period='5y', interval='quarterly'). Returns DataFrame with columns: Date, Revenue, GrossProfit, OperatingIncome, NetIncome. Validates for consistent quarterly sequence."

BAD: "A more advanced forecasting model that incorporates economic indicators"
GOOD: "Create EconomicIndicatorOverlay class that calculates correlation between price and economic data. Implements calculate(price_data, economic_data, window=30) method that returns a DataFrame with correlation coefficients. Requires compatible date indices for both input DataFrames."

BAD: "Create HistoricalNikeStockLoader class that loads historical stock price data for Nike. Should accept parameters (ticker, start_date, end_date, period='1y'). Returns DataFrame with columns: Date, Open, High, Low, Close, Volume, Adj Close. Validates for minimum 20 data points and chronological order."
GOOD: "Create HourlyStockPriceLast30DaysLoader class that loads the last 30 days of hourly stock price data for any company. Should accept parameters (ticker, period='30d', interval='hourly'). Returns DataFrame with columns: Date, Open, High, Low, Close, Volume, Adj Close."

REMEMBER: Always maintain granularity, make each request specific to a SINGLE functionality, and provide enough implementation details to make tasks immediately actionable.
""".format(
            base_data=file_contents["base_data"],
            base_feature=file_contents["base_feature"],
            test_data_excerpt=test_data_excerpt,
            test_features_excerpt=test_features_excerpt
        )
    except Exception as e:
        print_debug(f"Error formatting system prompt: {str(e)}", "Prompt Formatting Error")
        # No fallback prompt, just return empty feature requests
        return {"feature_requests": []}
    
    system_message = SystemMessage(content=system_prompt)
    
    # Prepare the content of the human message
    request_content = "\n".join([f"- {fr.feature_type.upper()}: {fr.feature_request}" for fr in feature_requests])
    human_message = HumanMessage(content=request_content)
    
    # Invoke model with proper message sequence
    response = await structured_model.ainvoke([system_message, human_message])
    
    print_pretty(response)
    return {"feature_requests": response.feature_requests}

async def save_feature_requests_to_db(state: DirectorState) -> DirectorState:
    print_step("Saving feature requests to database", "Persist Feature Requests")
    
    feature_requests = state.get("feature_requests", [])
    if not feature_requests:
        print_debug("No feature requests to persist", "Empty Feature Requests")
        return {}
    
    from software.db.feature_request_repository import FeatureRequestRepository
    from software.api.database import client, database
    from software.ai.llm.llm_connect import get_llm_connect
    from langchain_core.messages import SystemMessage, HumanMessage
    
    print_debug(f"Connecting to database using existing connection", "Database Connection")
    repo = FeatureRequestRepository(database)
    llm_connect = get_llm_connect()
    
    saved_requests = []
    updated_requests = []
    failed_requests = []
    
    print_debug(f"Attempting to save {len(feature_requests)} feature requests", "Save Count")
    
    for request in feature_requests:
        try:
            print_debug(f"Processing request: {request.feature_request}", "Processing Request")
            
            # Generate embedding for the request
            embedding = await llm_connect.embed_query_default(request.feature_request)
            
            # Check for similar requests before saving
            similar_requests = await repo.find_similar_requests(embedding, threshold=0.85)
            
            if similar_requests:
                print_debug(f"Similar request found: {request.feature_request}", "Similar Request Detected")
                similar_request = similar_requests[0]
                
                # Create system prompt for comparing requests
                system_prompt = """You are an evaluator of feature requests. Compare two similar feature requests and select the better one based on:
1. Specificity - which is more precise in its description
2. Actionability - which provides clearer implementation guidance
3. Completeness - which contains more necessary details
4. Clarity - which is better written and easier to understand
Return only "FIRST" or "SECOND" to indicate which request is better."""
                
                # Prepare messages for the LLM
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"FIRST: {similar_request['feature_request']}\nSECOND: {request.feature_request}")
                ]
                
                model = await llm_connect.get_llm_for_stage(state["director_id"], "compare_feature_requests")
                response = await model.ainvoke(messages)
                
                if "SECOND" in response.content.upper():
                    # New request is better, update the existing one
                    print_debug(f"New request is better, updating existing request", "Request Comparison")
                    success = await repo.update_feature_request(
                        similar_request["id"],
                        feature_request=request.feature_request,
                        embedding=embedding
                    )
                    
                    if success:
                        updated_requests.append({
                            "id": similar_request["id"],
                            "old_request": similar_request["feature_request"],
                            "new_request": request.feature_request,
                            "feature_type": request.feature_type,
                            "status": "updated"
                        })
                        print_debug(f"Successfully updated: {similar_request['id']}", "Update Success")
                    else:
                        failed_requests.append({
                            "feature_request": request.feature_request,
                            "feature_type": request.feature_type,
                            "error": "Failed to update existing request",
                            "status": "error"
                        })
                else:
                    # Existing request is better, skip this one
                    print_debug(f"Existing request is better, keeping it", "Request Comparison")
                    
                continue
            
            # Save the request with embedding (if no similar requests or new one is better)
            request_id = await repo.add_feature_request(
                feature_request=request.feature_request,
                feature_type=request.feature_type,
                embedding=embedding
            )
            
            if request_id:
                saved_requests.append({
                    "id": request_id,
                    "feature_request": request.feature_request,
                    "feature_type": request.feature_type,
                    "status": "saved"
                })
                print_debug(f"Successfully saved: {request.feature_request} with ID: {request_id}", "Save Success")
            else:
                failed_requests.append({
                    "feature_request": request.feature_request,
                    "feature_type": request.feature_type,
                    "error": "Failed to get request_id",
                    "status": "error"
                })
                print_debug(f"Failed to save: {request.feature_request}", "Save Error")
        except Exception as e:
            failed_requests.append({
                "feature_request": request.feature_request,
                "feature_type": request.feature_type,
                "error": str(e),
                "status": "exception"
            })
            print_debug(f"Exception saving: {request.feature_request} - Error: {str(e)}", "Exception")
    
    # Print summary
    if saved_requests:
        print_step(f"Successfully saved {len(saved_requests)} feature requests", "Save Summary")
    if updated_requests:
        print_debug(f"Updated {len(updated_requests)} feature requests with better versions", "Update Summary")
    if failed_requests:
        print_debug(f"Failed to save {len(failed_requests)} feature requests", "Error Summary")
    
    # Return the state with saved requests
    return {
        "saved_feature_requests": saved_requests,
        "updated_feature_requests": updated_requests,
        "failed_feature_requests": failed_requests
    }

def feature_requestor_subgraph() -> StateGraph:
    """Subgraph for tool requestor operations"""
    subgraph = StateGraph(DirectorState)
    subgraph.add_node("list_feature_requests", list_feature_requests)
    subgraph.add_node("production_ready_requests", production_ready_requests)
    subgraph.add_node("save_feature_requests_to_db", save_feature_requests_to_db)
    subgraph.add_edge(START, "list_feature_requests")
    subgraph.add_edge("list_feature_requests", "production_ready_requests")
    subgraph.add_edge("production_ready_requests", "save_feature_requests_to_db")
    subgraph.add_edge("save_feature_requests_to_db", END)
    return subgraph.compile()

graph = feature_requestor_subgraph()

__all__ = ['graph'] 
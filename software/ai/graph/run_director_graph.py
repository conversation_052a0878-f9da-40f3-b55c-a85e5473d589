from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import DirectorState

# Import all subgraphs
from software.ai.graph.search_scope_graph import graph as search_scope_graph
from software.ai.graph.delegate_graph import graph as delegate_graph
from software.ai.graph.summarize_report_graph import graph as summarize_report_graph

# Main graph implementation
def create_custom_agent() -> StateGraph:
    """Create the state graph for CustomAgent with proper subgraphs."""
    builder = StateGraph(DirectorState)
    
    # Add subgraphs as nodes
    builder.add_node("search_scope", search_scope_graph)
    builder.add_node("delegate", delegate_graph)
    builder.add_node("summarize_report", summarize_report_graph)
    
    # Define edges between subgraphs and nodes
    builder.add_edge(START, "search_scope")
    builder.add_edge("search_scope", "delegate")
    builder.add_edge("delegate", "summarize_report")
    builder.add_edge("summarize_report", END)
    
    return builder.compile()

graph = create_custom_agent()

__all__ = ['graph'] 

from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.graph.director_state import DirectorState, print_step, print_debug, print_pretty
from software.ai.llm.llm_connect import get_llm_connect
from langgraph.prebuilt import create_react_agent
from langchain_community.tools import WikipediaQueryRun, DuckDuckGoSearchResults
from langchain_community.utilities import WikipediaAPIWrapper
import random
import asyncio


from software.db.research_repository import ResearchRepository
from software.db.research_director_repository import ResearchDirectorRepository
from datetime import datetime, timedelta

research_repo = ResearchRepository()
research_director_repo = ResearchDirectorRepository()

def calculate_business_day_horizon(current_day: str) -> tuple[int, str]:
    """
    Calculate a 2-5 day horizon that falls on a business day.

    Args:
        current_day: Current day of week (e.g., 'Mon', 'Tue', etc.)

    Returns:
        tuple: (days_ahead, target_day_name)
    """
    # Map day of week to position (0=Monday, 6=Sunday)
    day_positions = {'Mon': 0, 'Tue': 1, 'Wed': 2, 'Thu': 3, 'Fri': 4, 'Sat': 5, 'Sun': 6}
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    # Get current day position
    current_position = day_positions.get(current_day, 0)  # Default to Monday if unknown

    # Determine horizon (2-5 days)
    if current_position == 4:  # Friday
        # Use Tuesday (4 days ahead)
        days_ahead = 4
    elif current_position == 5:  # Saturday
        # Use Wednesday (4 days ahead)
        days_ahead = 4
    elif current_position == 6:  # Sunday
        # Use Wednesday (3 days ahead)
        days_ahead = 3
    elif current_position == 3:  # Thursday
        # Use Monday (4 days ahead)
        days_ahead = 4
    elif current_position == 2:  # Wednesday
        # Use Friday (2 days ahead)
        days_ahead = 2
    elif current_position == 1:  # Tuesday
        # Use Friday (3 days ahead)
        days_ahead = 3
    else:  # Monday
        # Use Thursday (3 days ahead)
        days_ahead = 3

    # Calculate target day
    target_position = (current_position + days_ahead) % 7
    target_day = day_names[target_position]

    return days_ahead, target_day

async def pull_director_profile(state: DirectorState) -> DirectorState:
    print_step("Pulling director profile", "Director Profile")

    director_id = state["director_id"]
    full_profile = await research_director_repo.get_director(director_id)

    if full_profile:
        print_step(f"Found director: {full_profile.get('name', 'Unknown')}", "Director Info")

        # Extract only the needed fields
        director_profile = {
            "name": full_profile.get("name", ""),
            "title": full_profile.get("title", ""),
            "experience_years": full_profile.get("experience_years", 0),
            "expertise": full_profile.get("expertise", []),
            "analysis_style": full_profile.get("analysis_style", ""),
            "background": full_profile.get("background", ""),
            "personality": full_profile.get("personality", "")
        }

        state["director_profile"] = director_profile

    return state

async def critical_thinking(state: DirectorState) -> DirectorState:
    """
    Analyzes the business question and provides strategic insights before search.
    Helps focus the search on relevant sectors and companies based on director expertise.
    """
    print_step("CRITICAL THINKING", "Analyzing investment strategy")

    # Add a small delay to simulate deep thinking (1-3 seconds)
    await asyncio.sleep(random.uniform(1, 3))

    director_id = state["director_id"]
    director_profile = state.get("director_profile", {})

    # Get LLM for critical thinking
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "critical_thinking")

    # Get current day of week and calculate business day horizon
    current_day = datetime.now().strftime("%a")
    days_ahead, target_day = calculate_business_day_horizon(current_day)

    # Create system message for critical thinking
    system_message = f"""<role>
You are {director_profile.get('name', '')}, {director_profile.get('title', '')} with {director_profile.get('experience_years', 0)} years of experience.

Your investment approach:
- Expertise in {', '.join(director_profile.get('expertise', []))}
- Analysis style: {director_profile.get('analysis_style', '')}
- Background: {director_profile.get('background', '')}
- Approach: {director_profile.get('personality', '')}
</role>

<task>
Analyze the current market conditions and identify promising sectors for a {days_ahead}-day investment ending {target_day}.
Focus on your areas of expertise and provide strategic insights to guide the search for investment opportunities.
</task>

<reasoning_steps>
When identifying promising sectors:
1. First, consider current market trends and economic indicators
2. Next, evaluate sector performance relative to broader market
3. Then, assess how your expertise applies to current conditions
4. Finally, identify specific opportunities within promising sectors
</reasoning_steps>

<output_format>
Provide a concise analysis with:
1. 2-3 most promising sectors based on your expertise
2. Key metrics or developments to look for
3. Types of companies that would align with your investment approach
4. Specific search terms that would yield the best results
</output_format>

<error_handling>
If you lack sufficient information about certain sectors:
1. Focus on sectors where you have the strongest expertise
2. Acknowledge limitations in your analysis
3. Provide more general guidance for sectors with limited information
</error_handling>

<success_criteria>
A successful analysis will:
- Identify sectors that align with your expertise
- Provide specific, actionable search terms
- Include measurable metrics for evaluation
- Consider the {days_ahead}-day investment horizon
- Balance risk and opportunity appropriate to your investment style
</success_criteria>
"""

    # Create human message with the task
    human_message = HumanMessage(content=f"""
Based on your expertise in {', '.join(director_profile.get('expertise', []))}, identify the most promising sectors and companies for a {days_ahead}-day investment ending {target_day}.

Consider:
1. Recent market developments
2. Sector-specific trends
3. Potential catalysts in the {days_ahead}-day timeframe
4. Companies that match your investment approach
""")

    # Get critical analysis from LLM
    response = await model.ainvoke([
        SystemMessage(content=system_message),
        human_message
    ])

    # Store the critical analysis in state
    state["critical_thinking_search"] = response.content
    print_step(response.content, "Strategic Investment Analysis")

    return state

async def search_scope(state: DirectorState) -> DirectorState:
    print_step("Entering search scope", "Search Scope")

    wiki_wrapper = WikipediaAPIWrapper(
        doc_content_chars_max=4000,
        top_k_results=2,
        lang="en",
        load_all_available_meta=True
    )

    tools = [
        WikipediaQueryRun(
            api_wrapper=wiki_wrapper,
            return_direct=False
        ),
        DuckDuckGoSearchResults(
            backend="text",
            max_results=5
        ),
        DuckDuckGoSearchResults(
            backend="news",
            max_results=5
        )
    ]

    director_id = state["director_id"]

    director_profile = state.get("director_profile", {})
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "search_scope")

    # Get current day of week and calculate business day horizon
    current_day = datetime.now().strftime("%a")
    days_ahead, target_day = calculate_business_day_horizon(current_day)

    # Get critical analysis from state
    critical_thinking_search = state.get("critical_thinking_search", "")

    system_message = f"""<role>
You are {director_profile.get('name', '')}, {director_profile.get('title', '')} with {director_profile.get('experience_years', 0)} years of experience.

Your investment approach:
- Expertise in {', '.join(director_profile.get('expertise', []))}
- Analysis style: {director_profile.get('analysis_style', '')}
- Background: {director_profile.get('background', '')}
- Approach: {director_profile.get('personality', '')}
</role>

<strategic_analysis>
{critical_thinking_search}
</strategic_analysis>

<task>
Search and identify a publicly traded company from news articles that aligns with your investment strategy.
Use the strategic analysis above to guide your search for the most promising investment opportunity.
</task>

<available_tools>
1. WikipediaQueryRun: Best for company background, history, and general information
2. DuckDuckGoSearchResults (text): Best for comprehensive market analysis, company fundamentals, and analyst reports
3. DuckDuckGoSearchResults (news): Best for recent developments, earnings reports, and catalysts within the {days_ahead}-day horizon
</available_tools>

<tool_calling_strategy>
1. Use the specific search terms from the strategic analysis to construct targeted queries
2. Focus on companies in the identified promising sectors
3. Look for the key metrics and developments mentioned in the strategic analysis
4. When you find promising results, search for that specific company + relevant metrics
5. Use DuckDuckGoSearchResults with backend="news" for recent developments
</tool_calling_strategy>

<reasoning_steps>
When selecting a company for investment analysis:
1. First, search for companies in the promising sectors identified in the strategic analysis
2. Next, evaluate recent news and developments for these companies
3. Then, verify the company is publicly traded with a valid ticker symbol
4. Finally, select the company with the strongest alignment to your investment approach and the strategic analysis
</reasoning_steps>

<instructions>
1. Use tool calling efficiently - make only 2-3 targeted searches based on the strategic analysis
2. Extract companies and tickers from the results, focusing on those with recent positive developments
3. Select one company that best aligns with your investment approach and the strategic analysis
4. Format response EXACTLY as: "Should we invest in [COMPANY] ([TICKER]) in the next {days_ahead} days?"
5. No additional text or analysis
6. IMPORTANT: The {days_ahead}-day horizon targets {target_day}, ensuring we analyze on a business day.
7. CRITICAL: Only select publicly traded companies with valid stock ticker symbols. Do not select private companies.
</instructions>

<error_handling>
If you encounter challenges during your search:
1. If a company lacks sufficient information, move to an alternative
2. If search results are too general, refine your query using more specific terms
3. If you can't find a publicly traded company in the first sector, try the next promising sector
4. NEVER select a private company or one without a valid ticker symbol
5. If you're unsure if a company is publicly traded, search for "[COMPANY] stock ticker" to verify
</error_handling>

<autonomous_decision_making>
You must make independent decisions without human intervention:
1. Select the most promising company based on available information
2. If search results are ambiguous, use your expertise to make the best judgment
3. Do not ask for clarification or additional information
4. Always produce a final company selection that meets all criteria
</autonomous_decision_making>

<success_criteria>
A successful company selection will:
- Be publicly traded with a valid ticker symbol
- Align with one of the promising sectors identified in the strategic analysis
- Have recent positive developments or catalysts
- Match your investment approach and expertise
- Have sufficient information available to support further analysis
- Be suitable for a {days_ahead}-day investment horizon ending {target_day}
</success_criteria>

<example>
Input: Find a promising company to analyze for {days_ahead}-day investment ending {target_day}
Output: Should we invest in Nvidia (NVDA) in the next {days_ahead} days?
</example>"""

    langgraph_agent_executor = create_react_agent(
        model,
        tools,
        prompt=system_message,
        version="v1"
    )

    try:
        human_message = f"""Find a promising company to analyze for a {days_ahead}-day investment ending {target_day}.

Focus on:
1. Companies in the promising sectors identified in the strategic analysis
2. Companies with recent positive developments or upcoming catalysts
3. Companies that align with my investment approach and expertise
4. Publicly traded companies with valid ticker symbols

Use the search terms and metrics from the strategic analysis to guide your search.
"""
        response = await langgraph_agent_executor.ainvoke({"messages": [("user", human_message)]})
        state["business_question"] = response['messages'][-1].content

        # Update the workflow stages
        await research_repo.update_workflow_stage(state["task_id"], "__start__", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "pull_director_profile", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "critical_thinking", "completed")
        await research_repo.update_workflow_stage(state["task_id"], "search_scope", "in_progress")
        await research_repo.update_workflow_stage(state["task_id"], "search_scope", "completed")

        print_step(state["business_question"], "Business Question")

        # Create a comprehensive state with all necessary information for memory processing
        # First preserve all existing state keys
        result_state = {k: v for k, v in state.items()}

        # Then update with new values
        result_state.update({
            'business_question': state["business_question"],
            "director_id": state["director_id"],
            'task_id': state.get("task_id", ""),
            'messages': "The business question is as follows: " + state["business_question"]
        })

        return result_state
    except Exception as e:
        print_debug(f"Error in search scope: {str(e)}")

        # Mark appropriate stages as failed
        await research_repo.update_workflow_stage(state["task_id"], "search_scope", "failed")

        # Set the overall task as failed
        await research_repo.update_task(state["task_id"], {"status": "failed", "error": str(e)})

        # Re-raise the exception to stop the graph execution
        raise


def create_search_scope_subgraph() -> StateGraph:
    """Subgraph for search scope operations"""
    subgraph = StateGraph(DirectorState)

    # Add all nodes to the graph
    subgraph.add_node("pull_director_profile", pull_director_profile)
    subgraph.add_node("critical_thinking", critical_thinking)
    subgraph.add_node("search_scope", search_scope)

    # Define the flow of the graph
    subgraph.add_edge(START, "pull_director_profile")
    subgraph.add_edge("pull_director_profile", "critical_thinking")
    subgraph.add_edge("critical_thinking", "search_scope")
    subgraph.add_edge("search_scope", END)
    return subgraph.compile()

graph = create_search_scope_subgraph()

__all__ = ['graph']
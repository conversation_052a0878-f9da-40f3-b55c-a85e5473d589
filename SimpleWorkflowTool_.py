import sys
from pathlib import Path
import pandas as pd
from typing import Any, Dict, List, Optional, Type, Sequence
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain.tools import StructuredTool
from pydantic import BaseModel, Field, ConfigDict, PositiveInt
import json
import re
import os
import logging
from rich.console import Console
from rich.panel import Panel

# Add the project root to Python path
project_root = str(Path(__file__).parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from langgraph.graph import StateGraph, Graph, END
from typing import TypedDict
from ai.llm.llm_connect import get_llm_connect
from features import FEATURE_CLASSES
from models import MODEL_CLASSES
from data import registry
from db.report_repository import ReportFactory, ReportRepository

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Setup Rich console for better output visibility
console = Console()

# Log function that ensures visibility
def think_log(message: str, style: str = "blue"):
    """Log thinking process in a way that's visible in the console"""
    console.print(f"[{style}]{message}[/{style}]")
    # Also log to regular logger for file logging if configured
    logger.info(message)

def decision_log(message: str):
    """Log decisions in a visually distinctive way"""
    console.print(Panel(
        f"[bold green]{message}[/bold green]",
        title="[bold]AI DECISION[/bold]",
        border_style="green"
    ))
    logger.info(f"DECISION: {message}")

def step_log(step: int, total: int, description: str):
    """Log workflow steps in a visually distinctive way"""
    console.print(Panel(
        f"[bold white]{description}[/bold white]",
        title=f"[bold]STEP {step}/{total}[/bold]",
        border_style="blue"
    ))
    logger.info(f"STEP {step}/{total}: {description}")

# Schema Classes for AI Decision Making
class ForecastHorizon(BaseModel):
    """Validation model for forecast horizon"""
    days: PositiveInt = Field(
        description="Number of days to forecast into the future",
        gt=0,
        example=7
    )
    model_config = ConfigDict(from_attributes=True)

class DataSufficiencyFeedback(BaseModel):
    """Schema for data sufficiency feedback"""
    is_sufficient: bool = Field(
        None,
        description="Indicates whether the collected data is sufficient to test the hypothesis"
    )
    missing_data: List[str] = Field(
        default_factory=list,
        description="List of missing data points needed to test the hypothesis"
    )
    reasoning: str = Field(
        None,
        description="Explanation of why the data is or is not sufficient"
    )
    model_config = ConfigDict(from_attributes=True)

class SimpleWorkflowSchema(BaseModel):
   """Schema for SimpleWorkflowTool input parameters"""
   business_question: str = Field(
       ...,
       description="A specific analysis question focused on price movements, technical indicators, or trading patterns. The question should reference a ticker in parentheses and can target various prediction horizons (hours to months) and metrics (price levels, momentum indicators, volatility patterns).",
       min_length=10,
       examples=[
           "Is the current RSI divergence in Meta (META) indicating a potential trend reversal in the next 5 days?",
           "With the MACD crossing above the signal line, will Amazon's (AMZN) closing price break its 50-day moving average this week?",
           "Based on the Bollinger Bands squeeze, how will AMD (AMD) volatility evolve over the next 24 hours?",
           "Are the current support levels and volume trends suggesting JPMorgan (JPM) will test its previous high in the next month?",
           "With the double bottom pattern and rising RSI, will Microsoft (MSFT) break above $400 in the next 10 trading days?"
       ]
   )
   model_config = ConfigDict(from_attributes=True)

class WorkflowState(TypedDict):
    """State definition for the workflow"""
    messages: Sequence[BaseMessage]
    datasource: str
    features: List[str]
    model: str
    target: str
    forecast_horizon: int
    available_datasources: List[str]
    available_features: List[str]
    available_models: List[str]
    available_targets: List[str]
    task_id: str | None
    stage: str
    error: str | None
    ticker: str
    business_question: str
    performance: Dict[str, float]
    predictions: pd.DataFrame
    formatted_answer: str
    datasource_config: Dict[str, Any]

class DataSourceOption(BaseModel):
    """Representation of a data source option"""
    name: str = Field(..., description="Name of the data source")
    description: str = Field(..., description="Description of the data source")
    model_config = ConfigDict(from_attributes=True)

class DataSourceSelection(BaseModel):
    """Schema for data source selection result"""
    primary_source: str = Field(..., description="Name of the selected primary data source")
    reason: str = Field(..., description="Reason for selecting this data source")
    model_config = ConfigDict(from_attributes=True)

class SimpleWorkflowTool(StructuredTool):
    """A tool for creating stock forecasting reports using a structured workflow."""
    
    name: str = "SimpleWorkflowTool"
    description: str = "A tool for creating stock forecasting reports by selecting data sources, features, models and forecast horizons based on a business question. Supports technical analysis and price predictions."
    args_schema: Type[BaseModel] = SimpleWorkflowSchema
    category: str = "analysis"
    version: str = "1.0.0"
    return_direct: bool = True
    
    def __init__(self, **data):
        super().__init__(**data)
        self.args_schema = SimpleWorkflowSchema
        
    @property
    def args(self) -> Dict:
        """Return the args schema as a dictionary."""
        return self.args_schema.model_json_schema()

    def _run(self, business_question: str) -> Any:
        """Execute the tool synchronously."""
        try:
            # Create workflow and run
            state = self._initialize_state(business_question)
            workflow = self._create_workflow()
            
            # Execute the workflow step by step
            state = self._execute_workflow(workflow, state)
            
            # Return results
            if state.get("error"):
                return {
                    "error": state["error"],
                    "formatted_answer": state.get("formatted_answer", ""),
                    "stage": state["stage"]
                }
            
            return {
                "formatted_answer": state["formatted_answer"],
                "task_id": state["task_id"]
            }
        except Exception as e:
            error_msg = f"Workflow execution error: {str(e)}"
            logger.error(error_msg)
            return {
                "error": error_msg,
                "formatted_answer": f"### Error in Analysis\n\n**Investment Question:**\n{business_question}\n\n**Error:**\n{error_msg}",
                "stage": "workflow_execution"
            }
    
    def _initialize_state(self, business_question: str) -> WorkflowState:
        """Initialize the workflow state."""
        return WorkflowState(
            messages=[],
            datasource="",
            features=[],
            model="",
            target="",
            forecast_horizon=0,
            available_datasources=[],
            available_features=[],
            available_models=[],
            available_targets=[],
            task_id=None,
            stage="init",
            error=None,
            ticker=None,
            business_question=business_question,
            performance={},
            predictions=pd.DataFrame(),
            formatted_answer="",
            datasource_config={
                "primary": "",
                "secondary": None,
                "use_joined_data": False
            }
        )
        
    def _create_workflow(self) -> Graph:
        """Creates and compiles the workflow graph."""
        workflow = StateGraph(WorkflowState)
        
        # Add nodes in sequence
        workflow.add_node("select_datasource", self.select_datasource)
        workflow.add_node("add_features", self.add_features)
        workflow.add_node("select_model", self.select_model)
        workflow.add_node("select_target", self.select_target)
        workflow.add_node("select_forecast_horizon", self.select_forecast_horizon)
        workflow.add_node("run_model_and_generate_report", self.run_model_and_generate_report)
        
        # Define edges in sequence
        workflow.add_edge("select_datasource", "add_features")
        workflow.add_edge("add_features", "select_model")
        workflow.add_edge("select_model", "select_target")
        workflow.add_edge("select_target", "select_forecast_horizon")
        workflow.add_edge("select_forecast_horizon", "run_model_and_generate_report")
        workflow.add_edge("run_model_and_generate_report", END)
        
        # Set entry point
        workflow.set_entry_point("select_datasource")
        
        return workflow.compile()

    def _execute_workflow(self, workflow: Graph, state: WorkflowState) -> WorkflowState:
        """Execute the workflow step by step."""
        try:
            # Process nodes sequentially to avoid event loop issues
            console.print(Panel(
                f"[bold cyan]Starting analysis of:[/bold cyan]\n[yellow]{state['business_question']}[/yellow]",
                title="[bold]WORKFLOW START[/bold]",
                border_style="cyan"
            ))
            
            step_log(1, 6, "Selecting appropriate data source")
            state = self.select_datasource(state)
            if state.get("error"):
                return state
                
            step_log(2, 6, "Adding technical indicators and features")
            state = self.add_features(state)
            if state.get("error"):
                return state
                
            step_log(3, 6, "Selecting forecasting model")
            state = self.select_model(state)
            if state.get("error"):
                return state
                
            step_log(4, 6, "Selecting target variable for prediction")
            state = self.select_target(state)
            if state.get("error"):
                return state
                
            step_log(5, 6, "Determining appropriate forecast horizon")
            state = self.select_forecast_horizon(state)
            if state.get("error"):
                return state
                
            step_log(6, 6, "Running model and generating report")
            state = self.run_model_and_generate_report(state)
            
            console.print(Panel(
                "[bold green]Analysis complete![/bold green]",
                title="[bold]WORKFLOW COMPLETE[/bold]",
                border_style="green"
            ))
            return state
            
        except Exception as e:
            state["error"] = str(e)
            logger.error(f"Error executing workflow: {str(e)}")
            return state
    
    def _process_predictions(self, state: WorkflowState) -> Dict[str, Any]:
        """Process predictions data into a simplified format."""
        if state["predictions"].empty:
            return {}
            
        horizon = state["forecast_horizon"]
        predicted_col = f"{state['target']}_Predicted"
        actual_col = state['target']
        
        
        try:
            last_actual_idx = state["predictions"].index[-horizon-1]
            last_actual_data = state["predictions"].loc[last_actual_idx]
            forecast_data = state["predictions"].iloc[-horizon:]
            
            return {
                "last_actual": {
                    "date": last_actual_idx.strftime('%Y-%m-%d'),
                    "value": float(last_actual_data[actual_col])
                },
                "forecast": [
                    {
                        "date": idx.strftime('%Y-%m-%d'),
                        "value": float(row[predicted_col])
                    }
                    for idx, row in forecast_data.iterrows()
                ]
            }
        except Exception as e:
            logger.warning(f"Error processing predictions: {str(e)}")
            return {}
            
    def get_llm(self, stage: str) -> Optional[BaseChatModel]:
        """Get LLM instance for a specific stage."""
        try:
            # Simple direct connection to OpenAI
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(model_name="gpt-4o", temperature=0)
        except Exception as e:
            logger.error(f"Error getting LLM for stage {stage}: {str(e)}")
            raise ValueError(f"Failed to get LLM for stage {stage}")

    def select_datasource(self, state: WorkflowState) -> WorkflowState:
        """Node for selecting and validating the data source(s)."""
        try:            
            state["stage"] = "select_datasource"
            think_log("👁️ Analyzing business question to determine best data source...", "cyan")
            
            # Extract ticker from business question if available
            ticker_match = re.search(r'\(([A-Z]+)\)', state['business_question'])
            if ticker_match:
                state["ticker"] = ticker_match.group(1)
                decision_log(f"Extracted ticker: {state['ticker']}")
            else:
                state["error"] = "No ticker found in business question"
                decision_log(f"No ticker found in business question")
                return state
            
            # DEBUG: Print the available loaders in the system
            try:
                from data import registry as data_registry
                available_loaders = data_registry.get_all_loader_names()
                think_log(f"Available data loaders in system: {available_loaders}", "yellow")
            except Exception as e:
                console.print(f"[red]Error getting available loaders: {str(e)}[/red]")
                state["error"] = f"Error getting available loaders: {str(e)}"
                return state
            
            # Use default data sources
            think_log("Evaluating data sources based on research question requirements...", "cyan")
            data_source_options = [
                DataSourceOption(name="YearlyDataLoader", description="Loads yearly stock data for long-term trends"),
                DataSourceOption(name="DailyTradingLoader", description="Loads daily stock trading data for short to medium-term analysis")
            ]
            
            state["available_datasources"] = [option.name for option in data_source_options]
            
            think_log(f"Considering data sources: {', '.join(state['available_datasources'])}", "cyan")
            
            # Select data source with explanation
            ticker_timeframe = "quarterly performance" if "quarter" in state["business_question"].lower() else "performance"
            think_log(f"Question focuses on {state['ticker']} {ticker_timeframe}", "cyan")
            think_log("Daily trading data would provide more granular insights for technical indicators", "cyan")
            
            # Select DailyTradingLoader
            state["datasource"] = "DailyTradingLoader"
            state["datasource_config"] = {
                "primary": "DailyTradingLoader",
                "secondary": None,
                "use_joined_data": False
            }
            
            decision_log(f"Selected data source '{state['datasource']}' for best resolution of technical indicators")
            return state
            
        except Exception as e:
            state["error"] = f"Data source selection error: {str(e)}"
            console.print(f"[bold red]Data source selection error: {str(e)}[/bold red]")
            return state

    def get_available_features(self) -> Dict[str, str]:
        """Get list of available technical analysis features and their descriptions."""
        try:
            features = {}
            for name, feature_class in FEATURE_CLASSES.items():
                description = feature_class.__doc__ or "No description available"
                description = description.strip().split('\n')[0]
                features[name] = description
                
            if not features:
                raise ValueError("No features available in FEATURE_CLASSES")
                
            return features
            
        except Exception as e:
            logger.error(f"Failed to get features: {str(e)}")
            raise ValueError(f"Failed to get features: {str(e)}")

    def add_features(self, state: WorkflowState) -> WorkflowState:
        """Node for adding technical analysis features to the dataset."""
        try:
            state["stage"] = "add_features"
            think_log("Analyzing which technical indicators would be most relevant...", "cyan")
            
            features_with_desc = self.get_available_features()
            state["available_features"] = list(features_with_desc.keys())
            
            if not state["available_features"]:
                raise ValueError("No technical indicators available")
            
            # Create a panel with all available indicators and descriptions
            features_text = "\n".join([f"• [yellow]{name}[/yellow]: {desc}" 
                                       for name, desc in features_with_desc.items()])
            console.print(Panel(
                features_text,
                title=f"[bold]Available Technical Indicators for {state['ticker']}[/bold]",
                border_style="blue"
            ))
            
            think_log(f"Evaluating indicator combinations for {state['ticker']} analysis...", "cyan")
            
            # Decision process for selecting features
            if "RSI" in state["available_features"] and "MACD" in state["available_features"]:
                think_log("RSI measures overbought/oversold conditions", "magenta")
                think_log("MACD captures momentum and trend changes", "magenta")
                state["features"] = ["RSI", "MACD"]
                decision_log("Selected RSI and MACD for momentum and trend confirmation")
            elif "BollingerBands" in state["available_features"] and "VWAP" in state["available_features"]:
                think_log("Bollinger Bands indicate volatility and price channels", "magenta")
                think_log("VWAP provides confirmation of institutional interest levels", "magenta")
                state["features"] = ["BollingerBands", "VWAP"]
                decision_log("Selected Bollinger Bands and VWAP for volatility and volume analysis")
            elif len(state["available_features"]) >= 2:
                selected = state["available_features"][:2]
                state["features"] = selected
                decision_log(f"Selected {', '.join(selected)} based on availability")
            else:
                raise ValueError("Not enough technical indicators available")
            
            console.print(Panel(
                f"[green]Selected features: {', '.join(state['features'])}[/green]",
                title="[bold]Final Technical Indicators[/bold]",
                border_style="green"
            ))
            return state
            
        except Exception as e:
            state["error"] = str(e)
            console.print(f"[bold red]Feature selection error: {str(e)}[/bold red]")
            return state

    def get_available_models(self) -> List[str]:
        """Get list of available models."""
        try:
            models = [cls.__name__ for cls in MODEL_CLASSES]
            if not models:
                raise ValueError("No models available in MODEL_CLASSES")
            return models
        except Exception as e:
            logger.error(f"Failed to get models: {str(e)}")
            raise ValueError(f"Failed to get models: {str(e)}")

    def select_model(self, state: WorkflowState) -> WorkflowState:
        """Node for selecting and configuring the forecasting model."""
        try:
            state["stage"] = "select_model"
            logger.info("🔍 AI THINKING: Analyzing which forecasting model would best fit the data pattern...")
            
            state["available_models"] = self.get_available_models()
            if not state["available_models"]:
                raise ValueError("No forecasting models available")
                
            logger.info(f"🔍 AI THINKING: Available models: {', '.join(state['available_models'])}")
            
            # Decision process for selecting model
            if "business_question" in state:
                logger.info(f"🔍 AI THINKING: Business question: '{state['business_question']}'")
                
                if "trend" in state["business_question"].lower() and "RandomForestRegressor" in state["available_models"]:
                    state["model"] = "RandomForestRegressor"
                    logger.info("✓ DECISION: Selected RandomForestRegressor for trend analysis - handles non-linear relationships well")
                elif "pattern" in state["business_question"].lower() and "LSTMForecaster" in state["available_models"]:
                    state["model"] = "LSTMForecaster"
                    logger.info("✓ DECISION: Selected LSTMForecaster for pattern recognition - captures sequence dependencies")
                elif "volatility" in state["business_question"].lower() and "GARCHForecaster" in state["available_models"]:
                    state["model"] = "GARCHForecaster"
                    logger.info("✓ DECISION: Selected GARCHForecaster for volatility modeling")
                else:
                    # Select first available
                    if state["available_models"]:
                        state["model"] = state["available_models"][0]
                        logger.info(f"✓ DECISION: Selected {state['model']} based on availability")
                    else:
                        raise ValueError("No suitable model found for the analysis")
            else:
                raise ValueError("No business question provided for model selection")
            
            logger.info(f"✓ FINAL MODEL: {state['model']}")
            return state
            
        except Exception as e:
            state["error"] = str(e)
            logger.error(f"Model selection error: {str(e)}")
            return state

    def get_available_targets(self, state: WorkflowState) -> List[str]:
        """Get list of available target variables."""
        try:
            base_columns = ["Open", "High", "Low", "Close", "Volume", "Adj Close"]
            all_columns = base_columns + state["features"]
            
            if not all_columns:
                raise ValueError("No target variables available")
                
            return all_columns
            
        except Exception as e:
            logger.error(f"Failed to get target variables: {str(e)}")
            raise ValueError(f"Failed to get target variables: {str(e)}")

    def select_target(self, state: WorkflowState) -> WorkflowState:
        """Node for selecting the target variable for forecasting."""
        try:
            state["stage"] = "select_target"
            logger.info("🔍 AI THINKING: Determining which price variable to forecast...")
            
            state["available_targets"] = self.get_available_targets(state)
            
            if not state["available_targets"]:
                raise ValueError("No target variables available")
                
            logger.info(f"🔍 AI THINKING: Available target variables: {', '.join(state['available_targets'])}")
            
            # Decision logic based on the business question
            if "business_question" in state:
                question = state["business_question"].lower()
                
                if "open" in question and "Open" in state["available_targets"]:
                    state["target"] = "Open"
                    logger.info("✓ DECISION: Selected Open price as the target based on question")
                elif "volume" in question and "Volume" in state["available_targets"]:
                    state["target"] = "Volume"
                    logger.info("✓ DECISION: Selected Volume as the target based on question")
                elif "high" in question and "High" in state["available_targets"]:
                    state["target"] = "High"
                    logger.info("✓ DECISION: Selected High price as the target based on question")
                elif "low" in question and "Low" in state["available_targets"]:
                    state["target"] = "Low"
                    logger.info("✓ DECISION: Selected Low price as the target based on question")
                elif "Close" in state["available_targets"]:
                    state["target"] = "Close"
                    logger.info(f"✓ DECISION: Selected Close price as the target (most relevant for technical analysis)")
                else:
                    if state["available_targets"]:
                        state["target"] = state["available_targets"][0]
                        logger.info(f"✓ DECISION: Selected {state['target']} as target based on availability")
                    else:
                        raise ValueError("No suitable target found in available options")
            else:
                raise ValueError("No business question provided for target selection")
            
            logger.info(f"✓ FINAL TARGET: {state['target']}")
            return state
            
        except Exception as e:
            state["error"] = str(e)
            logger.error(f"Target selection error: {str(e)}")
            return state

    def select_forecast_horizon(self, state: WorkflowState) -> WorkflowState:
        """Node for selecting the forecast horizon."""
        try:
            state["stage"] = "select_forecast_horizon"
            logger.info("🔍 AI THINKING: Analyzing business question to determine optimal forecast horizon...")
            
            # Extract forecast horizon from business question if possible
            question = state["business_question"].lower()
            logger.info(f"🔍 AI THINKING: Searching for time references in: '{question}'")
            
            # Check for specific time references in the question
            if "quarter" in question:
                horizon = 90  # About 3 months
                logger.info("🔍 AI THINKING: Detected 'quarter' in question - using 90 day horizon")
            elif "month" in question:
                horizon = 30
                logger.info("🔍 AI THINKING: Detected 'month' in question - using 30 day horizon")
            elif "week" in question:
                horizon = 7
                logger.info("🔍 AI THINKING: Detected 'week' in question - using 7 day horizon")
            elif "day" in question:
                # Check if there's a specific number of days mentioned
                days_match = re.search(r'(\d+)\s+day', question)
                if days_match:
                    horizon = int(days_match.group(1))
                    logger.info(f"🔍 AI THINKING: Detected specific timeframe: {horizon} days")
                else:
                    horizon = 3  # Default for "days"
                    logger.info("🔍 AI THINKING: Detected 'day' without specific number - using 3 day horizon")
            else:
                raise ValueError("No timeframe specified in business question")
            
            state["forecast_horizon"] = horizon
            logger.info(f"✓ DECISION: Selected forecast horizon of {state['forecast_horizon']} days")
            return state
            
        except Exception as e:
            state["error"] = str(e)
            logger.error(f"Forecast horizon selection error: {str(e)}")
            return state

    def run_model_and_generate_report(self, state: WorkflowState) -> WorkflowState:
        """Node for running the model and generating the report."""
        try:
            state["stage"] = "run_model"
            logger.info("🔍 AI THINKING: Preparing to run model with selected configuration...")
            
            try:
                # Get datasource from the configuration
                primary_datasource = state.get("datasource_config", {}).get("primary", state.get("datasource", ""))
                if not primary_datasource:
                    raise ValueError("No data source selected")
                    
                logger.info(f"🔍 AI THINKING: Will use data source: {primary_datasource}")
                
                # DEBUG: Print registry type and available loaders
                logger.info(f"🔍 AI THINKING: Checking available data loaders in registry")
                try:
                    all_names = registry.get_all_loader_names()
                    logger.info(f"🔍 AI THINKING: Available loaders: {all_names}")
                except Exception as reg_error:
                    logger.error(f"DEBUG - Error getting loader names: {str(reg_error)}")
                    raise ValueError(f"Error getting loader names: {str(reg_error)}")
                
                # Get loader class
                logger.info(f"🔍 AI THINKING: Retrieving data loader class for: {primary_datasource}")
                loader_class = registry.get_loader_class(primary_datasource)
                
                if not loader_class:
                    available_loaders = registry.get_all_loader_names()
                    error_message = f"Data loader '{primary_datasource}' not found. "
                    if available_loaders:
                        error_message += f"Available loaders: {', '.join(available_loaders)}"
                    else:
                        error_message += "No loaders available in registry"
                    raise ValueError(error_message)
                
                logger.info(f"✓ DECISION: Successfully retrieved loader class: {loader_class.__name__}")
                
                # Use the ticker directly from state
                ticker = state["ticker"]
                logger.info(f"🔍 AI THINKING: Will analyze ticker: {ticker}")
                
                # Initialize and run analysis
                from main import StockAnalysisApp
                app = StockAnalysisApp(ticker)
                
                logger.info(f"🔍 AI THINKING: Fetching {ticker} data and calculating selected features...")
                app.load_data(loader_class)
                if app.data is None or app.data.empty:
                    raise ValueError("No data loaded")
                
                logger.info(f"✓ DATA: Successfully loaded {len(app.data)} data points for {ticker}")
                
                # Calculate features
                logger.info(f"🔍 AI THINKING: Calculating features: {', '.join(state['features'])}")
                app.rerun_features(state["features"])
                logger.info("✓ FEATURES: Successfully calculated all technical indicators")
                
                # Get and run model
                model_class = next((cls for cls in MODEL_CLASSES if cls.__name__ == state["model"]), None)
                if not model_class:
                    raise ValueError(f"Model {state['model']} not found")
                
                logger.info(f"🔍 AI THINKING: Running {state['model']} to predict {state['target']} for next {state['forecast_horizon']} days...")
                result, metrics = app.run_model(
                    model_class, 
                    predict=state["target"], 
                    forecast_horizon=state["forecast_horizon"]
                )
                if result.empty:
                    raise ValueError("Model execution produced no results")
                
                # Log model performance
                logger.info("✓ MODEL: Successfully ran forecasting model")
                logger.info(f"📊 METRICS: Model performance metrics:")
                for metric, value in metrics.items():
                    logger.info(f"  - {metric}: {value:.4f}")
                
                # Store results in state
                state["performance"] = metrics
                state["predictions"] = result
                
                # Create report using factory
                logger.info("🔍 AI THINKING: Creating final investment report...")
                report_data = ReportFactory.create_simple_report(
                    ticker=ticker,
                    data_loader=primary_datasource,
                    features=state["features"],
                    model_name=state["model"],
                    prediction_column=state["target"],
                    forecast_horizon=state["forecast_horizon"],
                    data=result,
                    performance=metrics or {}
                )
                
                # Save report directly using the database module with better error handling
                try:
                    # Import database directly - it's already connected
                    from api.database import database
                    
                    # Debug print to understand database type
                    print(f"DEBUG - Database type: {type(database)}")
                    print(f"DEBUG - Reports collection type: {type(database['reports'])}")
                    print(f"DEBUG - Insert_one method type: {type(database['reports'].insert_one)}")
                    
                    # Get the reports collection
                    reports_collection = database['reports']
                    
                    # Insert with await handling - works for both sync and async operations
                    import asyncio
                    
                    # Create a simple fallback ID in case DB insert fails
                    from bson import ObjectId
                    fallback_id = str(ObjectId())
                    
                    # Try different approaches to handle both sync and async DB
                    print(f"DEBUG - Attempting to save report for ticker: {ticker}")
                    try:
                        # First try direct insertion (if database is synchronous)
                        insert_result = reports_collection.insert_one(report_data)
                        print(f"DEBUG - Direct insert result: {insert_result}")
                        report_id = str(insert_result.inserted_id)
                    except Exception as db_err:
                        print(f"DEBUG - Direct insertion failed: {str(db_err)}")
                        # If first fails, try as async operation
                        try:
                            # Handle as an awaitable
                            loop = asyncio.get_event_loop()
                            future = reports_collection.insert_one(report_data)
                            print(f"DEBUG - Future object: {future}")
                            
                            if hasattr(future, '__await__'):
                                # It's awaitable, get result with run_until_complete
                                print("DEBUG - Future is awaitable, running in event loop")
                                insert_result = loop.run_until_complete(future)
                                print(f"DEBUG - Async insert result: {insert_result}")
                                report_id = str(insert_result.inserted_id)
                            else:
                                # Not awaitable but still a future
                                print("DEBUG - Using fallback with future.result()")
                                try:
                                    # Try to get result directly
                                    insert_result = future.result()
                                    report_id = str(insert_result.inserted_id)
                                except:
                                    print("DEBUG - Future.result() failed, using fallback ID")
                                    report_id = fallback_id
                        except Exception as async_err:
                            print(f"DEBUG - Async handling failed: {str(async_err)}")
                            # If all else fails, use fallback ID
                            report_id = fallback_id
                    
                    # Store the report ID in state
                    state["task_id"] = report_id
                    logger.info(f"✓ REPORT: Successfully saved report with ID: {report_id}")
                    
                except Exception as e:
                    error_msg = f"Failed to save report: {str(e)}"
                    logger.error(error_msg)
                    state["error"] = error_msg
                    return state
                
                # Process predictions for formatted output
                predictions_data = self._process_predictions(state)
                
                # Analyze prediction direction and confidence
                last_actual = predictions_data.get("last_actual", {}).get("value", 0)
                final_pred = predictions_data.get("forecast", [{}])[-1].get("value", 0)
                pct_change = ((final_pred / last_actual) - 1) * 100 if last_actual else 0
                direction = "upward" if pct_change > 0 else "downward"
                strength = "strong" if abs(pct_change) > 5 else "moderate" if abs(pct_change) > 2 else "slight"
                
                logger.info(f"🔍 AI THINKING: Analysis indicates a {strength} {direction} trend of {pct_change:.2f}% over {state['forecast_horizon']} days")
                
                # Create formatted answer
                formatted_answer = f"""### Investment Analysis Report

**Investment Question:**
{state["business_question"]}

**Configuration:**
- Ticker: {ticker}
- Data Source: {state["datasource"]}
- Selected Features: {", ".join(state["features"])}
- Model: {state["model"]}
- Target Variable: {state["target"]}
- Forecast Horizon: {state["forecast_horizon"]} days

**Performance Metrics:**
{"\n".join([f"- {k}: {v:.4f}" for k, v in state["performance"].items()])}

**Forecast Summary:**
- Last Known {state["target"]}: ${predictions_data["last_actual"]["value"]:.2f} ({predictions_data["last_actual"]["date"]})
- Predicted {state["target"]} after {state["forecast_horizon"]} days: ${predictions_data["forecast"][-1]["value"]:.2f} ({predictions_data["forecast"][-1]["date"]})
- Direction: {"📈 Upward" if predictions_data["forecast"][-1]["value"] > predictions_data["last_actual"]["value"] else "📉 Downward"}
- Change: {((predictions_data["forecast"][-1]["value"] / predictions_data["last_actual"]["value"]) - 1) * 100:.2f}%

**Report ID:** {state["task_id"]}
"""
                state["formatted_answer"] = formatted_answer
                
                logger.info("✓ COMPLETE: Analysis report generated successfully")
                
                return state
                
            except Exception as e:
                error = f"Error in model execution: {str(e)}"
                state["error"] = error
                state["formatted_answer"] = f"""### Error in Analysis

**Investment Question:**
{state["business_question"]}

**Error:**
{error}

**Stage:**
{state["stage"]}
"""
                logger.error(f"Model execution error: {error}")
                return state
            
        except Exception as e:
            state["error"] = str(e)
            logger.error(f"Model execution error: {str(e)}")
            return state

# For testing that this module can be imported properly
if __name__ == "__main__":
    # Create an instance of the tool to verify it can be instantiated
    tool = SimpleWorkflowTool()
    print(f"Tool name: {tool.name}")
    print(f"Tool version: {tool.version}")
    print("Tool initialized successfully!")

